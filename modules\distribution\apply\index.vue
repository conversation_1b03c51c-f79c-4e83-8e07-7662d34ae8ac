<template>
  <page>
    <view slot="content" class="page-content">
      <m-nav-bar title="申请成为分销员" :bg-overflow="false" />
      <scroll-view scroll-y="true" class="main">
        <view class="main-form">
          <title-input
            v-if="!config.userName.hidden"
            v-model="form.userName"
            :config="config.userName"
            horizontal
          ></title-input>
          <title-select-address
            v-if="!config.location.hidden"
            ref="locationRef"
            :config="config.location"
            :cData="cDataLocation"
            @updateForm="updateAddress"
          />
          <view class="form-item" v-if="!config.address.hidden" @tap="chooseLocation">
            <view class="form-title">
              <text class="star">*</text>
              {{ config.address.label }}
            </view>
            <view class="form-value">
              <template v-if="form.address">
                {{ form.address }}
              </template>
              <image v-else class="icon-gengduo" :src="file_ctx + 'static/image/business/icon-gengduo.png'" mode="aspectFit"></image>
            </view>
          </view>
          <view class="form-item" v-if="!config.identityCardFrontPath.hidden">
            <view class="form-title">
              <text class="star" v-if="config.identityCardFrontPath.required">*</text>
              {{ config.identityCardFrontPath.label }}
            </view>
            <title-img
              :config="config.identityCardFrontPath"
              @returnFn="
                (obj) => {
                  imgReturnFn(obj, 'identityCardFrontPath');
                }
              "
              :cData="cDataIdentityCardFrontPath"
            >
            </title-img>
          </view>
          <view class="form-item" v-if="!config.identityCardAfterPath.hidden">
            <view class="form-title">
              <text class="star" v-if="config.identityCardAfterPath.required">*</text>
              {{ config.identityCardAfterPath.label }}
            </view>
            <title-img
              :config="config.identityCardAfterPath"
              @returnFn="
                (obj) => {
                  imgReturnFn(obj, 'identityCardAfterPath');
                }
              "
              :cData="cDataIdentityCardAfterPath"
            >
            </title-img>
          </view>
        </view>

      </scroll-view>
      <view class="bottom-bar">
        <template v-if="!$validate.isNull(distributionapplyInfo)">
          <template v-if="distributionapplyInfo.auditStatus === 3">
            <view class="tips" v-if="distributionapplyInfo.auditRemark">{{ distributionapplyInfo.auditRemark }}</view>
            <button class="btn-bg submit-btn" type="default" @click="submit" :loading="submitLoading">
              申请被驳回，请检查填写的表单并重新提交申请
            </button>
          </template>
          <button v-else class="btn-bg submit-btn" type="default">
            已提交申请，请耐心等待审核结果
          </button>
        </template>
        <button v-else class="btn-bg submit-btn" type="default" @click="submit" :loading="submitLoading">
          提交申请
        </button>
      </view>
    </view>
  </page>
</template>

<script>
import { mapState } from 'vuex'
import titleInput from '@/components/business/module/v1/title-input/index.vue'
import titleSelector from '@/components/business/module/v1/title-selector/index'
import titleSelectAddress from '@/modules/distribution/components/title-select-address/index.vue'
import titleImg from '@/components/business/module/title-img/index.vue'
import titleTextarea from '@/components/business/module/v1/title-textarea/index'
const defaultForm = {
  distributionType: null,
  userName: null,
  location: [],
  address: null,
  identityCardFrontPath: [],
  identityCardAfterPath: []
}
const defaultConfig = {
  userName: {
    required: true,
    label: '真实姓名'
  },
  location: {
    name: 'location',
    label: '所在地区',
    required: true,
    isRequired: true,
    key: 'location',
    placeholder: '请选择所在地区',
    array: []
  },
  address: {
    required: true,
    label: '详细地址'
  },
  identityCardFrontPath: {
    label: '身份证正面（国徽面）',
    multiSelectCount: 1,
    count: 1,
    show: true,
    required: true,
    padding: '32rpx 0'
  },
  identityCardAfterPath: {
    label: '身份证正面（人像面）',
    multiSelectCount: 1,
    count: 1,
    show: true,
    required: true,
    padding: '32rpx 0'
  }
}
export default {
  components: {
    titleInput,
    titleSelector,
    titleSelectAddress,
    titleImg,
    titleTextarea
  },
  data() {
    return {
      file_ctx: this.file_ctx,
      $validate: this.$validate,
      form: JSON.parse(JSON.stringify(defaultForm)),
      config: JSON.parse(JSON.stringify(defaultConfig)),
      submitLoading: false,
      distributionapplyInfo: {},
      cDataIdentityCardFrontPath: [],
      cDataIdentityCardAfterPath: [],
      cDataLocation: []
    }
  },
  computed: {
    ...mapState('user', {
      curSelectUserInfo: state => state.curSelectUserInfo, // 当前选中的用户信息
      accountId: state => state.accountId
    })
  },
  onLoad() {
    this.init()
  },
  methods: {
    async chooseLocation() {
      const res = await this.$uniPlugin.chooseLocation()
      const { address, latitude, longitude } = res
      this.form.address = address
      this.form.latitude = latitude
      this.form.longitude = longitude
    },
    async init() {
      await this.getDistributionapplyUserApplyLog()
      if (!this.$validate.isNull(this.distributionapplyInfo)) {
        const { identityCardFrontPath, identityCardAfterPath, province = '', district = '', city = '' } = this.distributionapplyInfo
        Object.keys(this.form).forEach(key => {
          if (['identityCardFrontPath'].includes(key)) {
            if (identityCardFrontPath) {
              this.cDataIdentityCardFrontPath = identityCardFrontPath.split(',').map(item => {
                return { url: this.file_ctx + item, filePath: item, dir: item }
              })
            }
            this.form[key] = this.distributionapplyInfo[key] ? this.distributionapplyInfo[key].split(',') : []
          } else if (['identityCardAfterPath'].includes(key)) {
            if (identityCardAfterPath) {
              this.cDataIdentityCardAfterPath = identityCardAfterPath.split(',').map(item => {
                return { url: this.file_ctx + item, filePath: item, dir: item }
              })
            }
            this.form[key] = this.distributionapplyInfo[key] ? this.distributionapplyInfo[key].split(',') : []
          } else if (key === 'location') {
            this.cDataLocation = [province, city, district]
            this.form[key] = [province, city, district]
          } else {
            this.form[key] = this.distributionapplyInfo[key]
          }
        })
      }
    },
    async getDistributionapplyUserApplyLog() {
      const { centerUserId = '' } = this.curSelectUserInfo || {}
      const res = await this.$api.distribution.distributionapplyUserApplyLog({ userId: centerUserId })
      this.distributionapplyInfo = res.data
    },
    async submit() {
      // 校验
      for (const key in this.config) {
        if (this.config.hasOwnProperty.call(this.form, key)) {
          const item = this.config[key]
          if (item.required && this.$validate.isNull(this.form[key]) && !item.hidden) {
            return this.$uniPlugin.toast(`${item.label}不得为空`)
          }
        }
      }

      const [ provinceObj, cityObj, districtObj ] = this.$refs.locationRef.getLocationNameData() || []
      const formParams = JSON.parse(JSON.stringify(this.form))
      formParams.province = provinceObj.value
      formParams.city = cityObj.value
      formParams.district = districtObj.value
      formParams.location = provinceObj.text + cityObj.text + districtObj.text
      formParams.identityCardFrontPath = Array.isArray(formParams.identityCardFrontPath) ? formParams.identityCardFrontPath.join(',') : ''
      formParams.identityCardAfterPath = Array.isArray(formParams.identityCardAfterPath) ? formParams.identityCardAfterPath.join(',') : ''
      formParams.accountId = this.accountId
      const { centerUserId = '' } = this.curSelectUserInfo || {}
      formParams.userId = centerUserId
      let api = this.$api.distribution.distributionapplyInsert
      if (!this.$validate.isNull(this.distributionapplyInfo)) {
        formParams.id = this.distributionapplyInfo.id
        formParams.auditStatus = 1
        api = this.$api.distribution.distributionapplyUpdate
      }
      this.submitLoading = true
      await api(formParams).then(() => {
        this.submitLoading = false
        this.$uniPlugin.toast('已提交申请，请耐心等待审核结果')
        this.init()
      }).catch(() => { this.submitLoading = false })
    },
    updateAddress (obj) {
      this.form[obj.key] = obj.value
    },
    imgReturnFn(obj, key) {
      if (key === 'identityCardFrontPath') {
        this.cDataIdentityCardFrontPath = obj
      }
      if (key === 'identityCardAfterPath') {
        this.cDataIdentityCardAfterPath = obj
      }
      this.form[key] = obj.map(item => item.dir)
    }
  }
}
</script>

<style lang="scss" scoped>
.page-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #F4F6FA;
}

.main {
  flex: 1;
  padding: 20rpx 0;
  box-sizing: border-box;
}

.main-form {
  background-color: #fff;
  padding: 0 32rpx;
}

.bottom-bar {
  background: #FFFFFF;
  box-shadow: 0rpx -1rpx 0rpx 0rpx #EAEBF0;
  padding-bottom: calc(constant(safe-area-inset-bottom) + 26rpx);
  padding-bottom: calc(env(safe-area-inset-bottom) + 26rpx);
}

.tips {
  width: 100%;
  display: flex;
  align-items: center;
  background: #FFF0CC;
  padding: 16rpx 32rpx;
  font-size: 24rpx;
  color: #834F1A;
  line-height: 34rpx;
}

.submit-btn {
  margin-top: 12rpx;
}

::v-deep .title-radio,
  .title-input,
  .title-textarea,
  .title-jump {
  background-color: transparent;
}

::v-deep .title-input .l-r .input {
  background-color: transparent;
}
::v-deep .title-textarea .l-r uni-textarea {
  background-color: transparent !important;
}
::v-deep .upload-image .upload-image-main {
  background-color: transparent !important;
}
::v-deep .upload-image-main {
  padding: 0 !important;
}

.form-item {
  display: flex;
  justify-content: space-between;
  padding: 32rpx 0;
  &+.form-item {
    border-top: 1px solid #F2F3F7;
  }
}

.form-title {
  font-size: 30rpx;
  color: #1D2029;
  line-height: 42rpx;
}

.form-value {
  flex: 1;
  font-size: 30rpx;
  color: #1D2029;
  line-height: 42rpx;
  text-align: right;
  @include ellipsis(1);
}

.star {
  font-size: 30rpx;
  color: #FF5500;
  line-height: 42rpx;
}

.icon-gengduo {
  width: 32rpx;
  height: 32rpx;
}

</style>