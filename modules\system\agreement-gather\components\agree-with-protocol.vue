<template>
    <view>
      <view class="main-text" @tap="isCheckboxFn()">
        <view class="checkbox-view-text2 mgb16">
          <view class="checkbox-view" :class="[regForm.isCheckbox ? 'icon-yijianfankui-d-ok' : 'icon-yijianfankui-d']"></view>
          请阅读并勾选页面底部协议
        </view>
        <view class="checkbox-view-text2 mgb16">
          <!-- 我已阅读并同意 -->
          <!-- <br> -->
          查看
          <text class="tag" @tap.stop="navtoGo('UserAgreement')">《用户协议》</text>
          <!-- 和 -->
          <text class="tag" @tap.stop="navtoGo('SecrecyPolicy')">《隐私政策》</text>
          <!-- 以及 -->
          <!-- <text class="tag" @tap.stop="navtoGo('ExeceptionClause')">《免责条款》</text> -->
          <!-- <br> -->
        </view>
        <view class="checkbox-view-text2">
          若您的手机号未注册，将为您直接注册{{serverOptionsTitle}}账号
        </view>
      </view>
    </view>
</template>

<script>
import serverOptions from '@/config/env/options'
import { computed } from 'vue'
export default {
  name: "agree-with-protocol",
  components: {},
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $static_ctx: this.$static_ctx,
      regForm: {
        isCheckbox: false
      },
    }
  },
  watch: {
    /** 密码去首尾空监听 */
    'regForm.isCheckbox'() {
      this.$emit('returnFn', this.regForm.isCheckbox)
    }
  },
  computed: {
    serverOptionsTitle() {
      return serverOptions.title
    } 
  },
  methods: {
    isCheckboxToastFn() {
      return new Promise((resolve, reject) => {
        if(this.regForm.isCheckbox) {
          resolve(true)
          return
        }
        const that = this
        this.$uniPlugin.modal('服务协议和隐私政策及免责条款', '我已阅读并同意《服务协议》和《隐私政策》及《免责条款》', {
          showCancel: true, // 是否显示取消按钮，默认为 true
          cancelText: '取消', //  取消按钮的文字，默认为"取消"，最多 4 个字符
          cancelColor: '#000000', //  取消按钮的文字颜色，默认为"#000000"(H5、微信小程序、百度小程序)
          confirmText: '确定', //  确定按钮的文字，默认为"确定"，最多 4 个字符
          confirmColor: '#15c398', //  确定按钮的文字颜色，H5平台默认为"#007aff"，微信小程序平台默认为"#3CC51F"，百度小程序平台默认为"#3c76ff"（H5、微信小程序、百度小程序）
          fn: async (n) => {
            if (n) {
              that.isCheckboxFn()
              resolve(true)
            } else {
              this.$uniPlugin.toast('请同意协议！')
              reject(false)
            }
          }
        })
      })
    },
    isCheckboxFn() {
      this.regForm.isCheckbox = !this.regForm.isCheckbox
    },
    navtoGo(url = '', obj = {}) {
      this.$navto.push(url, obj)
    },
  },
}
</script>

<style lang="scss" scoped>
.main-text{
  position: fixed;
  left: 50%;
  bottom: 50upx;
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
  width: 600upx;
  margin: 0 auto;
  font-size: 24upx;
  line-height: 36upx;
  color: #999;
.checkbox-view {
  display: inline-block;
  vertical-align: middle;
  margin-right: 14upx;
}
.icon-yijianfankui-d{
  @include iconImg(36, 36, '/system/icon-yijianfankui-d.png');
}
.icon-yijianfankui-d-ok{
  @include iconImg(36, 36, '/system/icon-yijianfankui-d-ok.png');
}
.checkbox-view-text {
  text-align: center;
  display: inline-block;
  vertical-align: middle;
  width: calc(100% - 50upx);
  line-height: 36upx;
  font-size: 24upx;
  .tag{
    color: $topicC;
  }
}
.checkbox-view-text2 {
  line-height: 36upx;
  font-size: 24upx;
  display: flex;
  justify-content: center;
  color: #1D2029;
  .tag{
    color: $topicC;
  }
}
.mgb16 {
  margin-bottom: 16upx;
}
}
</style>
