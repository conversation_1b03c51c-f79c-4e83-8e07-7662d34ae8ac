<template>
  <view class="main">
    <view class="top-nav">
      <view class="top-nav-body">
        <!-- <em class="icon-back-left" @tap="$navto.back(1)"></em> -->
        <view class="input-view">
          <i class="icon-positioning-search"></i>
          <input confirm-type="search" placeholder="搜索你想要的信息" placeholder-style="color: #BFBFBF" class="input" type="text" :value="search" @input="searchInputFn" @confirm="searchFn">
        </view>
        <view class="click" @tap="searchFn">
          搜索
        </view>
      </view>
    </view>
    <!-- <tabs-sticky v-model="curIndex" :fixed="false" :tabs="tabs" @change="changeTab"></tabs-sticky> -->
    <tabs-sticky :fontBigger="true" :bdb="false" :overflowX="true" v-model="curIndex" :tabs="tabs" @change="changeTab"></tabs-sticky>
    <view class="search-main">
      <swiper class="swiper" :current="curIndex" @change="swiperChange">
        <!-- 帖子 -->
        <swiper-item>
          <scroll-refresh style="height: 100%;" :isShowEmptySwitch="true" :index="curIndex" :fixed="false" :isAbsolute="false" :up="upOption" :down="downOption"
            @returnFn="returnFn" @scrollInit="mescrollInit"
          >
            <view class="content">
              <view class="content-main">
                <nui-list :indexlist="indexlist" @cateClick="cateClick"></nui-list>
              </view>
            </view>
          </scroll-refresh>
        </swiper-item>
        <swiper-item>
          <service ref="myRefService" :index="curIndex" :search="search"></service>
        </swiper-item>
        <swiper-item>
          <hospital ref="myInstructionBook" :index="curIndex" :search="search"></hospital>
        </swiper-item>
      </swiper>
    </view>
  </view>
</template>

<script>
import TabsSticky from '@/components/basics/tabs-sticky-v3'
// import CourseBusinessList from '@/components/business/module/list/course-business-list'
// import StoresList from '@/components/business/module/list/stores-list'
import scrollRefresh from '@/components/uni/zPaging/index'
import nuiList from '@/components/community/nui-list/nui-list.vue'
import service from './service.vue'
import hospital from './hospital.vue'
import { isDomainUrl } from '@/utils/index'

export default {
  components: {
    TabsSticky,
    scrollRefresh,
    // CourseBusinessList,
    // StoresList,
    nuiList,
    // myInstructionBook,
    service,
    hospital,
  },
  data() {
    return {
      mescroll: null, // mescroll实例对象
      liveMescroll: null, // liveMescroll实例对象
      downOption: {
          auto: false // 不自动加载
      },
      upOption: {
          auto: false, // 不自动加载
          empty: {
            top: 0,
            zIndex: 999,
          }
      },
      scrollObj: {},
      courseScrollObj: {},
      storeList: [],
      courseList: [],
      cityInfo: {},
      curIndex: 0,
      tabs: [{ name: '帖子' },{ name: '服务' },{ name: '医院' },],
      courseListOne: [],
      storeListOne: [],
      search: '',
      indexlist: [],
      query: {},
      logConditionParams:{}, //直播的
      pdList: [], // 列表数据
    }
  },
  onLoad(paramsObj) {
    const query = this.$Route.query
    if (!this.$validate.isNull(query)) {
      this.search = query?.search || ''
      this.query = query.query
    }
    if (this.search) {
      this.init()
    } else {
      this.init()
    }
  },
  onShow() {
    // this.init()
  },
  watch: {
    // 监听下标的变化
    curIndex(val) {
      this.init()
    }
  },
  methods: {
    liveReturnFn(obj) {
		  console.log('触发加载',obj);
      const that = this
      function queryPage(pageNum, pageSize, fn) {
        const param = {
          current: pageNum,
          size: pageSize,
          condition: {
            activityStatusList: [
              2,3,5
            ],
            showType: 1,
            businessType: 7, // 直播活动
            title:that.search,
            // productId: that.productId
          }
        }

        // 在全部类型的请求中 加入排序参数
        if(!param.condition.classifyId){
          delete param.condition.businessType
          param.condition.businessTypeList = [3,7]
          param.condition.orderByActivityStatus = "5,2,3"
        }
        // 记录页面请求参数
        that.logConditionParams = {
          ...param.condition
        }

        that.$api.cloudClassroom.getMeetingQueryPage(param).then(res => {
           if (res && res.data.records) {
             for (const a in res.data.records) {
               const data = res.data.records[a]
               data.coverPathsUrl = isDomainUrl(data.coverPaths)
               // data.endTimeText = that.$common.formatDate(new Date(data.endTime), 'yyyy-MM-dd HH:mm')
             }
             fn(res.data.records)
           }
         })

      }



      setTimeout(function() {
        queryPage(obj.pageNum, obj.pageSize, (data) => {
          if (obj.pageNum === 1) {
            that.pdList = []
          }
          that.pdList = that.pdList.concat(data)
          obj.successCallback && obj.successCallback(data || [])
        })
      }, that.$constant.noun.scrollRefreshTime)
    },
    cateClick (data) {
        this.navtoGo('Circle', {cid: data.circleClassifyId})
    },
    navtoGo(url, obj = {}) {
        this.$navto.push(url, obj)
    },
    init() {
      // console.log('init---------------', this.curIndex)
      this.$nextTick(() => {
        let cityInfoStorage = this.$common.getKeyVal('system', 'cityInfoStorage', true)
        if (typeof cityInfoStorage === 'string' && cityInfoStorage === '') {
          cityInfoStorage = {}
        } else if (typeof cityInfoStorage === 'string' && cityInfoStorage !== '') {
          cityInfoStorage = JSON.parse(cityInfoStorage)
        }
        if (cityInfoStorage) {
          if (typeof cityInfoStorage === 'object' && JSON.stringify(cityInfoStorage) !== '{}') {
            this.cityInfo = cityInfoStorage
          } else {
            this.cityInfo = {}
          }
        } else {
          this.cityInfo = {}
        }
        if (this.curIndex === 0) {
          this.mescroll.triggerDownScroll()
        } else if (this.curIndex === 1) {
          // this.liveMescroll.triggerDownScroll()
          this.$refs.myRefService.init()
        } else if (this.curIndex === 2) {
          // this.courseScrollObj.triggerDownScroll()
          this.$refs.myInstructionBook.init()
        }
      })
    },
    searchInputFn(e) {
      this.search = e.target.value
    },
    changeTab(index) {
      this.curIndex = index
    },
    swiperChange(e) {
      this.changeTab(e.detail.current)
    },
    // courseScrollInit(scroll) {
    //   scroll.optUp.page.size = 10
    //   this.courseScrollObj = scroll
    // },
    // courseReturnFn(obj) {
    //   const that = this
    //   const query = that.$validate.isNull(that.query) ? {} : that.query
    //   function queryPage(pageNum, pageSize, fn) {
    //     const params = {
    //       current: pageNum,
    //       size: pageSize,
    //       condition: {
    //           keyword: that.search,
    //           ...query
    //       }
    //     }
    //     that.$api.community.postmessageSearch(params).then(res => {
    //       fn(res.data.records)
    //     })
    //   }
    //   setTimeout(function() {
    //     queryPage(obj.pageNum, obj.pageSize, (data) => {
    //       if (obj.pageNum === 1) {
    //         that.courseList = []
    //       }
    //       that.courseList = that.courseList.concat(data)
    //       obj.successCallback && obj.successCallback(data || [])
    //     })
    //   }, that.$constant.noun.scrollRefreshTime)
    // },
    scrollInit(scroll) {
      scroll.optUp.page.size = 10
      this.scrollObj = scroll
    },
    liveScrollInit(scroll) {
      scroll.optUp.page.num = 1
      scroll.optUp.page.size = 7
      this.liveMescroll = scroll
    },
    mescrollInit(scroll) {
      scroll.optUp.page.size = 10
      this.mescroll = scroll
    },
    returnFn(obj) {
      const that = this
      const query = that.$validate.isNull(that.query) ? {} : that.query
      function queryPage(pageNum, pageSize, fn) {
        const params = {
          current: pageNum,
          size: pageSize,
          condition: {
              keyword: that.search,
              ...query,
          }
        }
        that.$ext.community.postmessageSearch(params).then(res => {
          fn(res.data.records)
        })
      }
      setTimeout(function() {
        queryPage(obj.pageNum, obj.pageSize, (data) => {
          if (obj.pageNum === 1) {
            that.indexlist = []
          }
          that.indexlist = that.indexlist.concat(data)
          obj.successCallback && obj.successCallback(data || [])
        })
      }, that.$constant.noun.scrollRefreshTime)
    },
    navto(date) {
      // this.$common.setKeyVal('system', 'readOnlyStorage', { storeId: date.id, unitId: date.unitId }, true)
      this.$navto.push('StoreDetail', { storeId: date.id, unitId: date.unitId })
    },
    courseListReturnFn(o) {
      this.$navto.push('CourseBusinessDetail', { id: o.id, storeId: o.storeId, unitId: o.unitId })
    },
    searchFn() {
      if (this.search) {
        let arr = this.$common.getKeyVal('system', 'searchHistoryArrStorage', true)
        if (typeof arr === 'string' && arr === '') {
          arr = []
        } else if (typeof arr === 'string' && arr !== '') {
          arr = JSON.parse(arr)
        }
        if (arr) {
          if (typeof arr === 'object' && arr.length > 0) {
            let state = false
            let index = ''
            for (let i = 0; i < arr.length; i++) {
              if (this.search === arr[i]) {
                state = true
                index = i
                break
              }
            }
            if (state) {
              if (typeof index === 'number') {
                arr.splice(index, 1)
              }
            }
          }
          arr.unshift(this.search)
          this.$common.setKeyVal('system', 'searchHistoryArrStorage', arr, true)
        } else {
          this.$common.setKeyVal('system', 'searchHistoryArrStorage', [], true)
        }
        this.init()
      } else {
        this.init()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .m-t-40{
    margin-top:40upx;
  }
  .m-b-20{
    margin-bottom: 20upx;
  }
  .main{
    height: 100vh;
    background-color: #fff;
    .top-nav{
      padding: 10upx 30upx 10upx 30upx;
      background-color: #fff;
      .top-nav-body{
        display: flex;
        align-items: center;
        .icon-back-left{
          display: inline-block;
          vertical-align: middle;
          @include iconImg(64, 64, '/system/icon-back-left.png');
          margin-right: 16upx;
        }
        .input-view {
          flex: 1;
          display: inline-block;
          vertical-align: middle;
          width: calc(100% - 220upx);
          @include rounded(38upx);
          line-height: 64upx;
          height: 64upx;
          padding: 0 20upx;
          background: #F7F7F7;
          .icon-positioning-search{
            display: inline-block;
            vertical-align: middle;
            margin-right: 6upx;
            @include iconImg(32, 32, '/system/icon-positioning-search.png');
          }
          .input {
            width: calc(100% - 78upx);
            display: inline-block;
            vertical-align: middle;
            font-size: 28upx;
            line-height: 42upx;
            color: #333;
          }
        }
        .click{
          display: inline-block;
          vertical-align: middle;
          text-align: right;
          width: 100upx;
          line-height: 64upx;
          height: 64upx;
        }
      }
    }
    ::v-deep.tabs-sticky{
      padding:22rpx 32rpx 22rpx;
      // border-bottom: 4rpx solid #F5F5F5;
      .tabs-sticky-body{
        padding: 0;
        .tab{
          text{
            padding: 0;
          }
        }
      }
    }
    .search-main{
      height: calc(100% - 186upx);
      .swiper{
        height: 100%;
        .tabs-0{
          padding-top: 30upx;
          background-color: #fff;
          border-bottom: 30upx solid #f7f7f7;
          .title{
            font-size: 32upx;
            line-height: 48upx;
            padding: 10upx 30upx 0 30upx;
          }
          .no-data{
            font-size: 32upx;
            line-height: 48upx;
            text-align: center;
            margin: 40upx 0;
            color: #999;
            padding: 0 30upx;
          }
          .more{
            margin-left: 30upx;
            padding: 30upx 30upx 30upx 0;
            .icon-more-yellow{
              display: inline-block;
              vertical-align: middle;
              margin-right: 8upx;
              @include iconImg(32, 32, '/business/icon-more-yellow.png');
            }
            .name{
              width: calc(100% - 90upx);
              display: inline-block;
              vertical-align: middle;
              font-size: 28upx;
              line-height: 42upx;
              color: $topicC;
            }
            .icon-gengduo{
              display: inline-block;
              vertical-align: middle;
              margin-left: 18upx;
              @include iconImg(32, 32, '/business/icon-gengduo.png');
            }
          }
        }
        .tabs-0:last-of-type{
          border-bottom: none;
        }
      }
    }
  }
  .content {
    .content-main {
      overflow: hidden;
      // padding: 0 32rpx;
      background-color: #fff;
    }
  }
</style>
