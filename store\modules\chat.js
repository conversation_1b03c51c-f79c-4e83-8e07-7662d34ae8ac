/**
 * IM存储对象
 */
import common from '@/common/util/main'
import validate from '@/common/util/validate'
import navto from '@/router/config/nav-to'
import $env from '@/config/env'
const $static_ctx = $env.static_ctx
//       common.setKeyVal('chat', 'webSocketIsOpen', sceneResult, false)
const chat = {
  namespaced: true,
  state: {
    ws: null,
    webSocketIsOpen: false,
    webSocketWarningText:'',
    chatlist:[], // 聊天列表
    chatItem: {}, // 聊天对象
    messageList: [], // 消息列表
    showFunBtn: false, // 功能按钮
    messageListEl: false, // 消息列表 兼容小程序端获取元素需要访问组件this
    bottomBoxHeight: 0, // 底部输入框 引导框 高度
    pageIsShow: false, // 聊天页面是否在前台展示
    messageListLoadingStatus: 2, // 聊天列表上拉加载状态 1-加载中 2-加载完毕存在更多 3-加载完毕没有更多
    orderDetail: null, // 订单详情
    guideMode: '', // 当前引导的内容
    messageInit: false, // 聊天列表初始化
    departmentTags: [], // 选择科室标签列表
    consultTypeTags: [], // 资讯类型标签列表
    nodereplyconfig: [], // 咨询节点配置
    
  },
  mutations: {
    UPDATE_CONSULTTYPETAGS(state, data) {
      state.consultTypeTags = data
    },
    UPDATE_NODEREPLYCONFIG(state, data) {
      state.nodereplyconfig = data
    },
    UPDATE_DEPARTMENTTAGS(state, data) {
      state.departmentTags = data
    },
    /**
     * 更新 messageListEl对象
     */
    UPDATE_MESSAGEINIT(state, data) {
      state.messageInit = data
    },
    /**
     * 更新 messageListEl对象
     */
    UPDATE_GUIDEMODE(state, data) {
      state.guideMode = data
    },
    /**
     * 更新 messageListEl对象
     */
    UPDATE_ORDERDETAIL(state, data) {
      state.orderDetail = data
    },
    /**
     * 更新 messageListEl对象
     */
    UPDATE_MESSAGELISTLOADINGSTATUS(state, data) {
      state.messageListLoadingStatus = data
    },
    /**
     * 更新 messageListEl对象
     */
    UPDATE_PAGEISSHOW(state, data) {
      state.pageIsShow = data
    },
    /**
     * 更新 messageListEl对象
     */
    UPDATE_BOTTOMBOXHEIGHT(state, data) {
      state.bottomBoxHeight = data
    },
    /**
     * 更新 messageListEl对象
     */
    UPDATE_MESSAGELISTEL(state, data) {
      state.messageListEl = data
    },
    /**
     * 更新 showFunBtn对象
     */
    UPDATE_SHOWFUNBTN(state, data) {
      state.showFunBtn = data
    },
    /**
     * 更新 messageList对象
     */
    UPDATE_MESSAGELIST(state, data) {
      state.messageList = data
    },
    /**
     * 案例模板
     * @param state
     * @param data
     * @constructor
     */
    UPDATE_DEMO: (state, data) => {

    },
    /**
     * 更新 chatlist对象
     */
    UPDATE_CHATITEM(state, data) {
      state.chatItem = data
    },
    /**
     * 更新 chatlist对象
     */
    UPDATE_CHATLIST(state, data) {
      state.chatlist = data
    },
    /**
     * 更新 ws对象
     */
    UPDATE_WS(state, data) {
      state.ws = data
    },
    /**
     * 更新 ws是否连接状态
     */
    UPDATE_WEBSOCKETISOPEN(state, data) {
      state.webSocketIsOpen = data
    },
    /**
     * 更新 ws状态提示语
     */
    UPDATE_WEBSOCKETWARNINGTEXT(state, data) {
      state.webSocketWarningText = data
    }
  },
  actions: {
    UpdateConsultTypeTags(context, data) {
      context.commit('UPDATE_CONSULTTYPETAGS', data)
    },
    UpdateNodereplyconfig(context, data) {
      context.commit('UPDATE_NODEREPLYCONFIG', data)
    },
    UpdateDepartmentTags(context, data) {
      context.commit('UPDATE_DEPARTMENTTAGS', data)
    },
    /**
     * 更新 showFunBtn 对象
     */
    UpdateMessageInit(context, data) {
      context.commit('UPDATE_MESSAGEINIT', data)
    },
    /**
     * 更新 showFunBtn 对象
     */
    UpdateGuideMode(context, data) {
      context.commit('UPDATE_GUIDEMODE', data)
    },
    /**
     * 更新 showFunBtn 对象
     */
    UpdateOrderDetail(context, data) {
      context.commit('UPDATE_ORDERDETAIL', data)
    },
    /**
     * 更新 showFunBtn 对象
     */
    UpdateMessageListLoadingStatus(context, data) {
      context.commit('UPDATE_MESSAGELISTLOADINGSTATUS', data)
    },
    /**
     * 更新 showFunBtn 对象
     */
    UpdatePageIsShow(context, data) {
      context.commit('UPDATE_PAGEISSHOW', data)
    },
    /**
     * 更新 showFunBtn 对象
     */
    UpdateBottomBoxHeight(context, data) {
      context.commit('UPDATE_BOTTOMBOXHEIGHT', data)
    },
    /**
     * 更新 showFunBtn 对象
     */
    UpdateMessageListEl(context, data) {
      context.commit('UPDATE_MESSAGELISTEL', data)
    },
    /**
     * 更新 showFunBtn 对象
     */
    UpdateShowFunBtn(context, data) {
      context.commit('UPDATE_SHOWFUNBTN', data)
    },
    /**
     * 更新 messageList 对象
     */
    UpdateMessageList(context, data) {
      context.commit('UPDATE_MESSAGELIST', data)
    },
    /**
     * 案例模板
     * @param commit
     * @param data
     * @constructor
     */
    UpdateDemo({ commit }, data) {
      commit('UPDATE_DEMO', data)
    },
    /**
     * 更新 chatItem 对象
     */
    UpdateChatItem(context, data) {
      context.commit('UPDATE_CHATITEM', data)
    },
    /**
     * 更新 chatlist 对象
     */
    UpdateChatlist(context, data) {
      context.commit('UPDATE_CHATLIST', data)
    },
    /**
     * 更新 ws对象
     */
    UpdateWs(context, data) {
      context.commit('UPDATE_WS', data)
    },
    /**
     * 更新 ws是否连接状态
     */
    UpdateWebSocketIsOpen(context, data) {
      context.commit('UPDATE_WEBSOCKETISOPEN', data)
    },
    /**
     * 更新 ws状态提示语
     */
    UpdateWebSocketWarningText(context, data) {
      context.commit('UPDATE_WEBSOCKETWARNINGTEXT', data)
    }
  }
}

export default chat
