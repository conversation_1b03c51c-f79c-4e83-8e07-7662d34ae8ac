<template>
  <view class="page">
    <!-- 订单未取消状态 -->
    <inLinePay ref="inLinePay" :provinceValue='provinceValue' :insuredInfo='insuredInfo' :underInfo='underInfo' @ConfirmPurchaseInsurance='ConfirmPurchaseInsurance' @clearOrder='clearOrder' @inLinePay='inLinePay' @comboPay='comboPay' :accompanybookOne='accompanybookOne'></inLinePay>
    <modelPopup 
      :key="index" 
      v-for="(item,index) in accompanybookOne.accompanyOrderDTOList" 
      :ref="'modelPopup'+index"
      :isLocking="isLockingMap[index]" 
      :underInfo='insuredInfo'  
      :loadProductCodeFlag='loadProductCodeFlag' 
      @getProductCode='setProductCode' 
      :orderDetails='insuredInfo' 
      @finish='(res)=>finishModel(res,index)'
      @secondaryfinish='secondaryfinish' 
      :modelType='modelTypeMap[index]' 
      @change='changeModel'
      isList='true' 
      :openFlag='openFlagModelMap[index]'>
    </modelPopup>
  </view>
</template>

<script>
  import { mapState } from 'vuex'
  import common from '@/common/util/main'
  import inLinePay from './inLinePay.vue'
  import { getQueryObject } from '@/utils/index'
  import modelPopup from '../components/model.vue'
import serverOptions from "@/config/env/options";
  
  export default{
    components: {
      inLinePay,
      modelPopup
    },
    data(){
      return {
        currentServer:{},
        openServerFlag:false,
        accompanybookOne:{
          // accompanyOrderDTOList:[]
        },
        orderId:'',
        showPopup:false,
        showGuardDetail:false,
        currentServerId:null,
        oneByCity:null,
        cityRes:null,
        selectServerObj:null,
        system:null, //系统信息
        channelCode:'',
        openFlagModelMap:[],
        modelTypeMap:[],
        underInfo:{},
        insuredInfo:{},
        loadProductCodeFlag:false,
        productCode:'',
        isLockingMap:[],
        currentOrderIndex:0,
        provinceValue:{}
      }
    },
    computed: {
      ...mapState('user', {
        curSelectUserInfo: state => state.curSelectUserInfo
      })
    },
    watch:{

  },
    async onLoad(res) {
      let provinceValue = (await this.$api.accompanyDoctor.accompanyproviderQueryOne({id:serverOptions.providerId})).data;
      this.provinceValue = provinceValue;
      console.log(res,'res-000--')
      uni.showLoading({title:'加载中',mask:true}) //打开加载
      this.system = uni.getSystemInfoSync() //获取系统信息
      // 判断传递来的参数是否需要解析
      if(res.scene){
        let params = decodeURIComponent(decodeURIComponent(decodeURIComponent(res.scene)))
        console.log('params',params);
        let sceneObj = getQueryObject(params)
        console.log('sceneObj',sceneObj)
        sceneObj.gs && (this.channelCode = sceneObj.gs);
        sceneObj.i && (this.orderId = sceneObj.i);
        sceneObj.id && (this.orderId = sceneObj.id);
        this.orderId && this.loadOrderDetails();
      }
      if(res.orderId){ 
        this.orderId = res.orderId;
        this.loadOrderDetails()
      }
      uni.hideLoading()
     },
    methods:{
          // 仅检测自身可枚举属性
    isObjectNotEmpty(obj) {
      return Object.keys(obj).length > 0
    },
      changeModel(flag){
        this.$set(this.openFlagModelMap,this.currentOrderIndex,flag);
      },
      // 唤醒模块弹窗
      ConfirmPurchaseInsurance(type,isLocking = false,index = 0,item){
        this.currentOrderIndex = index;
        
        // 设置类型和状态
        this.$set(this.openFlagModelMap,index,true);
        this.$set(this.modelTypeMap,index,type);
        this.$set(this.isLockingMap,index,isLocking);
        
        // 确保弹窗完全打开后再设置保险信息
        this.$nextTick(() => {
          if(type === 'insureInfo' && item) {
            // 确保item.insuranceNum是数字类型
            if(item.insuranceNum) {
              item.insuranceNum = Number(item.insuranceNum);
            }
            this.$refs['modelPopup'+index][0].showListSelectInsureFn(item);
          }
        });
      },
      setProductCode({productCode}){
        this.productCode = productCode;
      },
      //选择保险模块
      async selectInsure(options,index){
        this.$refs.inLinePay.updateInsurance(options.insuranceNum)
        this.insuredInfo.insuranceNum = options.insuranceNum;
        this.$refs['modelPopup'+index][0].selectItem(options)
        this.$refs.inLinePay.selectInsurance({productCode:options.productCode},index); 
      },
      async finishModel({modelType,options},index){
        options = {accompanyId:this.accompanybookOne.id,...options}
        console.log('{modelType,options}',{modelType,options},index);
        let apiFunc;
        switch (modelType){
          // 添加购买人
          case 'under':
            let {insuranceNum,productCode,...underInfo} = options;
            this.productCode = productCode;
            // 更新支付模块的保险参数
            apiFunc = this.$api.accompanyDoctor.accompanyinsureAdd;
            console.log('this.insuredInfo',this.insuredInfo);
            
            if(Object.keys(this.insuredInfo).length > 3){
              apiFunc = this.$api.accompanyDoctor.accompanyinsureUpdate;
            }
            this.underInfo = underInfo;
            // 手动选择保险
            this.selectInsure(options,index)
            break;
            // 风险提示
            case 'riskAlert':
              return this.ConfirmPurchaseInsurance('insureInfo');
            break;
            // 保险内容
            case 'insureInfo':
            // 手动选择保险
              this.selectInsure(options,index)
            break;
            // 取消保障
            case 'surrender':
              apiFunc = this.$api.accompanyDoctor.accompanyinsureCancel;
            break;
            // 取消保障成功
            case 'surrender':
            break;
          default:
            break;
        }
        uni.showLoading({title:'加载中',mask:true})
        if(!apiFunc) apiFunc = ()=>{}
        let data = await apiFunc(options);
        if(modelType === 'under'){
          await this.loadAccompanyinsure();
          uni.showToast({title:'提交成功',icon:'none'})
        }
        // 取消保障成功重新刷新页面
        if(modelType === 'surrender'){
          this.finish(this.accompanybookOne.id)
        }

        uni.hideLoading()
        this.$set(this.openFlagModelMap,index,false);
        console.log('this.openFlagModelMap',this.openFlagModelMap);
        
        if(modelType === 'surrender'){
          //唤醒取消保障成功弹窗
          await this.ConfirmPurchaseInsurance('surrenderCG')
        }
      },
      secondaryfinish({modelType}){
        switch (modelType){
            case 'riskAlert':
              // this.$refs.inLinePay.setDirectPayment(true);
              // this.$refs.inLinePay.inLinePay();
              this.inLinePay()
              break;
            case 'surrender':
            this.$set(this.openFlagModelMap,index,false);

              break;
          default:
            break;
        }
      },
      async accompanyserviceQueryOneByCity(params){
        const res = await this.$api.accompanyDoctor.accompanyserviceQueryOneByCity(params)
        this.currentServer = res.data
      },
      getPosition(){
        let resFn;
        let promise = new Promise(res=>resFn = res);
        uni.getLocation({
          type: 'wgs84',
          geocode:true,
          	success: async (res)=> {
              console.log('res222222',res);
              let Position = await this.$ext.common.getPosition(res);
              console.log('Position',Position);
              resFn(Position)
          	}
        });
        return promise
      },
      clearOrder(){
        this.$refs.customerService.open();
      },
      finish(orderId){
        console.log('orderId',orderId);
        if(orderId) this.orderId = orderId;
        this.loadOrderDetails()
      },
      async loadOrderDetails(){
        let queryFunc = this.$api.accompanyDoctor.accompanycombineorderQueryCombineOrder
        let {data:accompanybookOne} = await queryFunc({id:this.orderId})
        this.loadAccompanyinsure()

        // 判断是否存在该订单
        if(!accompanybookOne){
          return uni.showModal({
            title:'订单信息有误，请联系客服',
            icon:'none',
            success: function (res) {
              if (res.confirm) {
              console.log('用户点击确定');
              uni.switchTab({url:'pages/accompany-home/index'})
              } else if (res.cancel) {
              console.log('用户点击取消');
              }
            }
            })
        }
        console.log('accompanybookOne',accompanybookOne);
        accompanybookOne.accompanyOrderDTOList = accompanybookOne.accompanyOrderDTOList.filter(e=>{
          return e.pay !== 1;
        })
        if(accompanybookOne.accompanyOrderDTOList.length === 0){
           uni.showModal({
            title:'提示',
            content:'订单已完成支付',
            confirmText:'返回订单',
            icon:'none',
            showCancel:false,
            success: function (res) {
              uni.switchTab({url:'pages/order/index'})
            }
          })
        }
        this.accompanybookOne = accompanybookOne;
        if(!this.accompanybookOne.star){
          this.showGuardDetail = true;
        }
      },
      // 查询购买人信息
      async loadAccompanyinsure(){
        let {data} = await this.$api.accompanyDoctor.accompanyinsureQuery({accompanyId:this.orderId})
        if(!data) return
        let {name,sex,certfType,certfNo,birthDate,id} = data;
        console.log('{name,sex,certfType,certfNo,birthDate}',{name,sex,certfType,certfNo,birthDate});
        birthDate = this.changeTime(birthDate)
        this.underInfo = {name,sex,certfType,certfNo,birthDate};
        data.birthDate = birthDate;
        this.insuredInfo = data;
        this.insuredInfo.insuranceNum = 5;
        console.log('this.insuredInfo1',this.insuredInfo);
        this.loadProductCodeFlag = true;
      },
      changeTime(time){
        let endDate = new Date(time);
        let Y = endDate.getFullYear() + "-";
        let M = (endDate.getMonth() + 1 < 10 ? "0" + (endDate.getMonth() + 1) : endDate.getMonth() + 1) + "-";
        let D = endDate.getDate() < 10 ? "0" + endDate.getDate() : endDate.getDate() + "";
        return Y + M + D
      },
      back() {
        let pages = getCurrentPages() // 获取栈实例
        if (pages.length > 1) {
          this.$navto.back()
        } else {
          this.$navto.replaceAll('AccompanyHome')
        }
      },
      selectServer(options){
        this.selectServerObj = options
        // this.currentServer = options;
        this.accompanyserviceQueryOneByCity({id:options?.id,city: this.oneByCity || this.cityRes?.cityname})
      },
      openChangeServer(){
        this.openServerFlag = true
      },
      changeExchange(flag){
        this.openServerFlag = flag
      },
      // 套餐支付方法
      async comboPay({comboId}){
        const codeUserInfo = common.getKeyVal('user', 'codeUserInfo',true)
        let queryOptions = {
          id:this.orderId,
          userId:codeUserInfo.id,
          comboId
        }
        let data = await this.$api.accompanyDoctor.accompanybookPay(queryOptions);
        this.$uniPlugin.toast('支付成功');
        this.loadOrderDetails()

      },
      async allinpaydetailGetOrderDetail(){
        const res = await this.$api.accompanyDoctor.allinpaydetailGetOrderDetail({bizOrderNo:this.orderId})
        return Promise.resolve(res)
      },
      flush(){
        // 查询当前页面栈 如果没有上一页则去到订单列表
        let pages = getCurrentPages() // 获取栈实例
        setTimeout(() => {
          if (pages.length > 1) {
            return uni.navigateBack();
          }else {
            this.$navto.replaceAll('OrderIndex') 
          }
        }, 500);
      },
      // 在线支付方法
      async inLinePay(isSelectInsurance){
        let accompanyCombineInsureList = this.$refs.inLinePay.getAccompanyCombineInsureList();
        let options = {bizOrderNo:this.orderId,productCode:this.productCode,accompanyCombineInsureList,successCb:this.flush,failCb:()=>{
          uni.showToast({title:'支付失败',icon:'none'})
        },type:4};
        if(!isSelectInsurance){
          delete options.productCode
        }
        console.log('options',options);
        
        this.$ext.user.accompanyPay(options)
      }
    }
  }
</script>

<style lang="scss">
  .page{
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    background: #F4F6FA;
  }
</style>
