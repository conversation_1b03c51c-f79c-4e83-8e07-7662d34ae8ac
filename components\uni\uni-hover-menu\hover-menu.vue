<template>
  <view>
    <view v-if="!$validate.isNull(btnList)">
      <!-- 遮罩 -->
      <view class="mask" v-if="show" @tap.stop="show = false" @touchmove.stop.prevent></view>
      <!-- 按钮 -->
      <view class="major-box">
<!--        <view class="click-btn" v-if="!show" @tap.stop="showFn()" draggable="true" @touchstart.stop="touchstart" @touchmove.stop.prevent="touchmove">-->
        <view class="click-btn" v-if="!show" @tap.stop="showFn()">
          <view class="click-btn-text" :style="{'width': defaultConfig.titleWidth, 'height': defaultConfig.titleHeight, 'line-height': defaultConfig.titleLineHeight, 'font-size': defaultConfig.titleSize, 'color': defaultConfig.titleColor}">
            {{defaultConfig.title}}
          </view>
        </view>
        <view class="nav-box" v-if="show">
          <view class="nav-btn" v-for="(item, index) in btnList" :key="index" @tap.stop="clickBtn(index)">
            <view class="nav-icon" v-if="item.icon">
              <image class="icon" :src="item.icon"></image>
            </view>
            <view class="nav-text" :style="{'color': defaultConfig.itemColor, 'font-size': defaultConfig.itemSize}" v-if="item.title">{{item.title}}</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
	export default {
		props: {
      // 初始值
      cData: {
        type: Array,
        default() {
          return []
        }
      },
      // 参数设置
      config: {
        type: Object,
        required: false,
        default: () => {
          return {}
        }
      }
		},
    watch: {
      cData: {
        handler(val) {
          this.watchDataMain(val)
        },
        deep: true
      },
      config: {
        handler(val) {
          this.copyConfig()
        },
        deep: true
      }
    },
		data() {
			return {
        // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
        $constant: this.$constant,
        $static_ctx: this.$static_ctx,
        $common: this.$common,
        $accurateConversion: this.$accurateConversion,
        file_ctx: this.file_ctx,
        $timePlugin: this.$timePlugin,
        $validate: this.$validate,
				show: false, // 是否显示
				deviationTop: 0, // 偏移量
				windowHeight: uni.getSystemInfoSync().windowHeight, // 视图高度
        // 所有按钮 [{title: 标题，icon:图片全路径}]
        // 当isNavToGo为true，需要增加key，navtoName: 路由名称，navtoParam:{}
				btnList: [],
        defaultConfig: {
          title: '《《 快捷导航', // 导航总标题
          titleSize: '14px', // 导航总标题字体大小（如：'14px'）
          titleColor: '#fff', // 导航总标题字体色号（如：'#fff'）
          titleWidth: '100px', // 导航总标题宽度（如：'100px'）
          titleHeight: '40px', // 导航总标题高度（如：'40px'）
          titleLineHeight: '40px', // 导航总标题行高（如：'40px'）
          isNavToGo: false, // 是否开启组件内部路由直接跳转
          itemSize: '14px', // 每个导航的字体大小（如：'14px'）
          itemColor: '#fff' // 每个导航的字体颜色（如：'#fff'）
        }
			};
		},
    mounted() {
      this.copyConfig()
      this.watchDataMain(this.cData)
    },
		methods: {
      navtoGo(name, paramObj) {
        this.$navto.push(name, paramObj)
      },
      // 初始化拷贝config对象
      copyConfig() {
        const that = this
        const obj = that.config
        Object.keys(obj).forEach(function(key) {
          that.defaultConfig[key] = obj[key]
        })
      },
      // 导航数组初始化
      watchDataMain(val) {
        this.btnList = val
      },
		  showFn() {
		    if (this.$validate.isNull(this.btnList)) return
        this.show = !this.show
      },
			// 点击按钮
			clickBtn(index) {
				this.show = false
        if (this.defaultConfig.isNavToGo) {
          if (!this.$validate.isNull(this.btnList[index].navtoName)) {
            if (!this.$validate.isNull(this.btnList[index].navtoParam)) {
              this.navtoGo(this.btnList[index].navtoName, this.btnList[index].navtoParam)
            } else {
              this.navtoGo(this.btnList[index].navtoName)
            }
          }
        } else {
          const obj = {
            index: index,
            list: this.btnList
          }
          this.$emit('returnFn', obj)
        }
			},
			// 拖动开始，记录一下偏移量
			touchstart(e) {
				const touch = e.touches[0] || e.changedTouches[0]
				this.deviationTop = touch.clientY - this.top
			},
			// 上下拖动时
			touchmove(e) {
				const touch = e.touches[0] || e.changedTouches[0]
				let top = touch.clientY
				top = top - this.deviationTop
				if (top < 0) {
					top = 0
				}
				if (top > this.windowHeight - 40) {
					top = this.windowHeight - 40
				}
				this.top = top
				return false
			}
		}
	}
</script>

<style lang="scss" scoped>
	/* 遮罩 */
	.mask {
		position: fixed;
		width: 100%;
		height: 100%;
		top: 0;
		left: 0;
		z-index: 99;
		background: rgba(248, 248, 248, 0.8);
	}

	/* 总盒子 */
	.major-box {
		border: 1px 0 solid;
		z-index: 100;
		position: fixed;
		bottom: 20%;
		right: 0;
		transition: left 0.5s;
		overflow: hidden;

	}

	.click-btn,
	.nav-box {
		float: left;
	}

	/* 按钮样式 */
	.nav-box {
		background-color: #FFF;
    @include rounded(0 0 0 5px);
	}

	.click-btn {
		background-color: #999;
		opacity: 0.8;
    @include rounded(50px 0 0 50px);
    padding: 10px 0 10px 10px;
    box-sizing: border-box;
    overflow: hidden;
    .click-btn-text {
      text-align: center;
    }
	}

	/* 按钮盒子 */
	.nav-box {
		display: flex;
		flex-wrap: wrap;
		text-align: center;
		justify-content: center;
		background-color: #999;
    @include opacity(0.8);
    @include rounded(50px 0 0 50px);
		padding: 10px 0 10px 10px;
	}

	.nav-btn {
		flex: 1;
		border: 0px #000 solid;
		min-width: 75px;
    padding: 0 4px;
	}

	.icon {
		margin: 0 auto;
		width: 20px;
		height: 20px;
	}

	.nav-text {
		font-size: 14px;
	}
</style>
