<template>
  <page>
    <view slot="content" class="main-body">
      <view class="main">
        <scroll-view scroll-y="true" class="scroll-view-main">
          <view class="m-main">
            <text class="content">
              由于微信官方推送规则变更，服务消息通知变为单次订阅消息。
              因此，为了及时地推送最新消息给到您，保证您不会遗漏重要消息和推送，请用户们扫码关注服务号！
              操作流程：点击下方按钮给客服发送“1”即可关注服务号了喔~赶紧去添加吧~
            </text>
            <view class="img" @tap="previewImage(0)">
              <image mode="scaleToFill" :src="imageUrl"/>
            </view>
            <button class="btn-bg btn" open-type="contact">点击给客服发送“1”</button>

            <!-- 微信绑定或解绑 -->
            <view class="wx-main" v-if="isWeiXinView">
              <view class="wx-m-main">
                <view class="title">已绑定推送微信公众号</view>
                <view class="wx-m-main-body">
                  <view class="li-view l">
                    <!--                    <default-img :config="config.avatar" :cData="'图片全路径，如果是半路径就会拿取我们的前缀（file_ctx）'"/>-->
                  </view>
                  <view class="li-view m">
                    <!--                    微信名称-->
                  </view>
                  <view class="li-view r" :class="[isWeiXin ? 'bg-green' : 'bg-red']" @tap="wxUnbundling()">
                    {{isWeiXin ? '解绑微信' : '绑定微信'}}
                  </view>
                </view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>
  </page>
</template>

<script>
import { mapState } from 'vuex'
export default {
  components: {

  },
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $accurateConversion: this.$accurateConversion,
      file_ctx: this.file_ctx,
      $static_ctx: this.$static_ctx,
      $timePlugin: this.$timePlugin,
      imageUrl: this.$static_ctx + 'image/system/qrcode.jpg',
      isWeiXinView: true, // 控制是否出现微信面板
      isWeiXin: false, // 是否绑定微信
      config: {
        avatar: {
          widthHeightAuto: true
        }
      }
    }
  },
  computed: {
    ...mapState('user', {
      codeUserInfo: state => state.codeUserInfo,
      isLogin: state => state.isLogin
    })
  },
  onLoad() {
    const that = this
    const openId = that.$common.getCache('openId')
    if (!that.$validate.isNull(openId)) {
      that.isWeiXin = true
      that.isWeiXinView = true
    } else {
      that.isWeiXin = false
      that.isWeiXinView = false
    }
  },
  methods: {
    // 解绑微信
    wxUnbundling() {
      const that = this
      const openId = that.$common.getCache('openId')
      if (!that.$validate.isNull(openId)) {
        that.$ext.wechat.unbundlingWechatUser().then(() => {
          that.$uniPlugin.toast('解绑成功')
          that.isWeiXin = false
          that.isWeiXinView = false
        }).catch(() => {
          that.$uniPlugin.toast('解绑失败')
        })
      }
    },
    previewImage(index) {
      const that = this
      const imageList = []
      imageList.push(that.imageUrl)
      uni.previewImage({
        current: index,
        urls: imageList
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  .main-body{
    height: 100%;
    .main {
      height: 100%;
      .scroll-view-main {
        height: 100%;
      }
    }
  }
  .m-main {
    overflow: hidden;
    .content{
      margin-top: 20upx;
      padding:30upx;
      display: inline-block;
      background: #FFFFFF;
      font-size: 28upx;
      line-height: 56upx;
    }
    .wx-main {
      margin-top: 20upx;
      padding: 30upx;
      .wx-m-main {
        background: #fff;
        @include rounded(20upx);
        box-sizing: border-box;
        overflow: hidden;
        padding: 30upx;
        .title {
          text-align: center;
          font-size: 32upx;
          line-height: 48upx;
        }
        .wx-m-main-body {
          margin-top: 20upx;
          .li-view {
            display: inline-block;
            vertical-align: middle;
            font-size: 28upx;
            line-height: 42upx;
          }
          .l {
            height: 88upx;
            width: 88upx;
            @include rounded(50%);
            margin-right: 12upx;
          }
          .m {
            width: calc(100% - 240upx);
          }
          .r {
            text-align: right;
            width: 140upx;
          }
        }
      }
    }
    .img{
      width: 100%;
      background: #FFFFFF;
      text-align: center;
      image{
        width: 400upx;
        height: 400upx;
        display: inline-block;
      }
    }
    .btn{
      margin-top: 50upx;
    }
  }

</style>
