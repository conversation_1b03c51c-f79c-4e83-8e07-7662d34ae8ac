import serverOptions from '@/config/env/options'
export default [
{
  path: '/modules/activity/pages/diagnosis/list',
  name: 'diagnosisComment',
  meta: {
    index: 2,
    headerObj: {
      title: '诊后点评',
      isShow: true
    }
  }
},
{
  path: '/modules/activity/pages/diagnosis/add',
  name: 'diagnosisCommentAdd',
  meta: {
    index: 2,
    headerObj: {
      title: '诊后点评',
      isShow: true
    }
  }
},
{
  path: '/modules/activity/pages/diagnosis/subscribemessage',
  name: 'diagnosisSubscribemessage',
  meta: {
    index: 2,
    headerObj: {
      title: '医院服务满意度调查',
      isShow: true
    }
  }
},
{
  path: '/modules/activity/research/index',
  name: 'Research',
  meta: {
    index: 2,
    headerObj: {
      title: '学术调研',
      isShow: true
    }
  }
},
{
  path: '/modules/activity/research/detail',
  name: 'ResearchDetail',
  meta: {
    index: 2,
    headerObj: {
      title: '调研详情',
      isShow: true
    }
  }
},
{
  path: '/modules/activity/questionnaire/index',
  name: 'questionnaireIndex',
  meta: {
    index: 2,
    headerObj: {
      title: '问卷详情',
      isShow: true
    }
  }
},
{
  path: '/modules/activity/questionnaire/components/fillin',
  name: 'questionnaireFillin',
  meta: {
    index: 2,
    headerObj: {
      title: '编辑问卷',
      isShow: true
    }
  }
},
{
  path: '/modules/activity/pages/accurate-promotion-of-small-gourd/index',
  name: 'businessAccuratePromotionOfSmallGourd',
  meta: {
    index: 2,
    headerObj: {
      title: '问卷精准地推',
      isShow: true
    }
  }
},
{
  path: '/modules/activity/hospital-ranking/index',
  name: 'HospitalRanking',
  meta: {
    index: 2,
    headerObj: {
      title: '医院点评',
      isShow: true
    }
  }
},
{
  path: '/modules/activity/hospital-ranking/hospital-detail/index',
  name: 'HospitalDetail',
  meta: {
    index: 2,
    headerObj: {
      title: '医院详情',
      isShow: true
    }
  }
},
{
  path: '/modules/activity/hospital-ranking/doctor-detail/index',
  name: 'DoctorDetail',
  meta: {
    index: 2,
    headerObj: {
      title: '医生详情',
      isShow: true
    }
  }
},
{
  path: '/modules/activity/hospital-ranking/remark/index',
  name: 'Remark',
  meta: {
    index: 2,
    headerObj: {
      title: '点评',
      isShow: true
    }
  }
},
{
  path: '/modules/activity/health-testing/index',
  name: 'HealthTesting',
  meta: {
    index: 2,
    headerObj: {
      title: '健康自测',
      isShow: true
    }
  }
},
{
  path: '/modules/activity/health-testing/testing-detail',
  name: 'TestingDetail',
  meta: {
    index: 2,
    headerObj: {
      title: '健康自测详情',
      isShow: true
    }
  }
},
{
    // 优德医webview
    path: '/modules/activity/calabash/webview',
    name: 'calabashWebview',
    meta: {
      index: 1,
      headerObj: {
        title: serverOptions.title
      }
    }
  },
  {
    // 优德医
    path: '/modules/activity/calabash/index',
    name: 'calabash',
    meta: {
      index: 1,
      headerObj: {
        title: serverOptions.title
      }
    }
  },
  {
    // 我的福币
    path: '/modules/activity/calabash/myLuckyCoin',
    name: 'myLuckyCoin',
    meta: {
      index: 1,
      headerObj: {
        title: '我的福币'
      }
    }
  },
  {
    // 福币任务
    path: '/modules/activity/calabash/luckyCoinaTask',
    name: 'luckyCoinaTask',
    meta: {
      index: 1,
      headerObj: {
        title: '福币任务'
      }
    }
  },
  {
    // 积分商城(兑换好礼)
    path: '/modules/activity/calabash/exchangeWings/integrationShop',
    name: 'integrationShop',
    meta: {
      index: 1,
      headerObj: {
        title: '福币任务'
      }
    }
  },
  {
    // 积分商城-兑换记录
    path: '/modules/activity/calabash/exchangeWings/exchangeRecords',
    name: 'exchangeRecords',
    meta: {
      index: 1,
      headerObj: {
        title: '福币任务'
      }
    }
  },
  {
    // 兑换详情
    path: '/modules/activity/calabash/exchangeWings/integrationGoods',
    name: 'integrationGoods',
    meta: {
      index: 1,
      headerObj: {
        title: '兑换详情'
      }
    }
  },
  {
    // 兑换详情
    path: '/modules/activity/calabash/exchangeWings/meAddress',
    name: 'meAddress',
    meta: {
      index: 1,
      headerObj: {
        title: '我的地址'
      }
    }
  },
  {
    // 兑换详情
    path: '/modules/activity/calabash/exchangeWings/addAddress',
    name: 'addAddress',
    meta: {
      index: 1,
      headerObj: {
        title: '新增地址'
      }
    }
  },
]
