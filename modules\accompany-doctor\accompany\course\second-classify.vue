<template>
  <view class='second-classify'>
    <scroll-refresh class="scroll-refresh-main" :isShowEmptySwitch="true" :fixed="false" :up="upOption" :down="downOption" @returnFn="returnFn" @scrollInit="scrollInit">
      <view class="top-nav">
        <view class="input-view">
          <i class="icon-positioning-search"></i>
          <input confirm-type="search" placeholder="搜索课程名称" placeholder-style="color: #A5AAB8" class="input" type="text" :value="courseName" @input="searchInputFn"  @confirm="searchFn">
        </view>
        <view class="click" @tap="searchFn">搜索</view>
      </view>
      <view class="classify-content">
        <view class="content-item" v-for="item in contentList" :key="item.id" @click="handleClickJump(item.id)">
          <view class="classify-content-l"><image :src="file_ctx + item.listImg"></image></view>
          <view class="classify-content-r">
            <view class="tilte">{{ item.courseName }}</view>
            <view class="tags">共 {{ item.chapter }} 课时</view>
          </view>
        </view>
      </view>
    </scroll-refresh>
  </view>
</template>

<script>
  export default {
    data(){
      return{
        file_ctx:this.file_ctx,
        mescroll: null, // mescroll实例对象
        downOption: {
          auto: false // 不自动加载
        },
        upOption: {
          auto: false, // 不自动加载
          empty: {
            top: 0,
            zIndex: 999,
          }
        },
        contentList:[],
        classifyId:null,
        courseName:'',
      }
    },
    onLoad(option){
      const query = this.$validate.isNull(this.$Route.query) ? option : this.$Route.query
      this.classifyId = query.id
      // this.accompanycourseQueryPage(query.id)
      if(query?.name){
        uni.setNavigationBarTitle({
          title: query?.name // 设置的新标题
        })
      }
    },
    mounted(){
      this.$nextTick(() => {
        this.init()
      })
    },
    methods:{
      searchInputFn(e){
        this.courseName = e.detail.value
      },
      searchFn(){
        this.init()
      },
      handleClickJump(id){
        this.$navto.push('CourseDetail', {id})
      },
      // accompanycourseQueryPage(id){
      //   this.$api.accompanyDoctor.accompanycourseQueryPage({current:1,size:30,condition:{state:1,classifyId:id}}).then(res=>{
      //     console.log(res,'res00000')
      //   })
      // },
      returnFn(obj) {
        const that = this
        setTimeout(function () {
          let params = {
            current: obj.pageNum,
            size: obj.pageSize,
            condition: {
              state:1,
              classifyId:that.classifyId,
              courseName:that.courseName,
            }
          }
          that.$api.accompanyDoctor.accompanycourseQueryPage(params).then(res => {
            let data = res.data.records
            // let data = res.data.records.map(item=>(
            //   {
            //     ...item,
            //   }))
            if (obj.pageNum === 1) {
              that.contentList = []
            }
            that.contentList = [...that.contentList, ...data]
            obj.successCallback && obj.successCallback(data)
          })
        }, that.$constant.noun.scrollRefreshTime)
      },

      scrollInit(scroll) {
        scroll.optUp.page.num = 1
        scroll.optUp.page.size = 10
        this.mescroll = scroll
      },
      init() {
        this.$nextTick(() => {
          this.mescroll.triggerDownScroll()
        })
      },
    },
 }
</script>

<style lang='scss' scoped>
  .second-classify{
    height: 100vh;
    // padding: 0 32rpx;
    background: #F4F6FA;
  }
  .top-nav{
    display: flex;
    .input-view {
      display: flex;
      align-items: center;
      vertical-align: middle;
      width: 626rpx;
      line-height: 72rpx;
      height: 72rpx;
      padding: 0 20upx;
      margin-left: 32rpx;
      background: #FFFFFF;
      border-radius: 36rpx;
      border: 2rpx solid #D9DBE0;
      box-sizing: border-box;
      .icon-positioning-search{
        display: inline-block;
        vertical-align: middle;
        margin-right: 6upx;
        @include iconImg(32, 32, '/system/icon-positioning-search.png');
      }
      .input {
        width: calc(100% - 78upx);
        display: inline-block;
        vertical-align: middle;
        font-size: 28upx;
        line-height: 42upx;
        color: #333;
      }
    }
    .click{
      display: inline-block;
      vertical-align: middle;
      text-align: center;
      width: 100upx;
      line-height: 64upx;
      height: 64upx;
    }
  }
  .scroll-refresh-main{
    height: 100%;
    ::v-deep .mescroll-empty-box{
      min-height: 0% !important;
      position: absolute !important;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
  .classify-content{
    height: 100%;
    // margin-top: 32rpx;
    margin: 32rpx 32rpx 0;
    .content-item{
      display: flex;
      padding: 32rpx 24rpx;
      background-color: #fff;
      border-radius: 16rpx;
      margin-bottom: 20rpx;
      .classify-content-l{
        display: flex;
        flex-shrink: 0;
        width: 224rpx;
        height: 128rpx;
        border-radius: 12rpx;
        overflow: hidden;
        // background: skyblue;
        margin-right: 16rpx;
        image{
          width: 100%;
          height: 100%;
        }
      }
      .classify-content-r{
        .tilte{
          font-size: 28rpx;
          color: #1D2029;
          line-height: 40rpx;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          word-wrap: break-word;
          -webkit-box-orient: vertical;
        }
        .tags{
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 22rpx;
          color: #4E5569;
          padding: 4rpx 12rpx;
          background: #F4F6FA;
          border-radius: 8rpx;
          margin-top: 12rpx;
          width:fit-content;
        }
      }
      &:last-child{
        margin-bottom: 0;
      }
    }
  }
</style>