import common from '@/common/util/main'
import api from '@/service/api'
import validate from '@/common/util/validate'
import ext from '@/service/ext'
import navto from '@/router/config/nav-to'
const launchOptions = uni.getLaunchOptionsSync()
/**
 * 系统请求工具类，
 * 注意：API接口只做请求数据和格式化数据的事情，其他事务一概不处理
 */
export default {
  /**
   * 圈子-当前用户已关注圈子列表
   * @param resolve
   * @param reject
   */
  curCircleclassifyQuerySubscribeList() {
    return new Promise((resolve, reject) => {
      const param = {
        accountId: common.getKeyVal('user', 'accountId', true)
      }
      if(launchOptions.scene === 1154)  param.accountId = 0;
      api.circleclassify.circleclassifyQuerySubscribeList(param).then((res) => {
        // 当前用户存在已关注圈子列表
        if (!validate.isNull(res.data)) {
          common.setKeyVal('business', 'openGuideSubscribe', false) // 不需要弹出订阅引导
        }
        resolve(res)
      }).catch(error => {
        reject(error)
      })
    })
  },
  /**
   * 是否打开引导订阅圈子页面
   */
  initOpenGuideSubscribe(scene = 1001) {
    return new Promise((resolve, reject) => {
      let openGuideSubscribe = common.getKeyVal('business', 'openGuideSubscribe')
      // 是否打开引导订阅
          // if(scene == 1154){
          //   resolve()
          // }
      if (!openGuideSubscribe) {
        resolve()
      } else {
        ext.circleclassify.curCircleclassifyQuerySubscribeList().then(() => {
          openGuideSubscribe = common.getKeyVal('business', 'openGuideSubscribe')
          if (!openGuideSubscribe) {
            resolve()
          } else {
            navto.push('CircleGuideSubscribe')
          }
        })
      }
    })
  }
}
