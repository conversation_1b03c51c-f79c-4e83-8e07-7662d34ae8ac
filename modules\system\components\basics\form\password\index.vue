<!-- 密码表单组件封装 -->
<template>
  <view class="password-main">
    <view :class="inputPhone ? 'bdb-1-ccc' : 'bdb-1-topic-c'">
      <input
        v-model="pwd"
        password="true"
        maxlength="18"
        :placeholder="pwdConfig.placeholder"
        placeholder-class="f-w-medium"
        @focus="onFocusPWD($event, 'inputPhone')"
        @blur="onBlurPWD($event, 'inputPhone')"
        @input="phoneInput($event)"
      >
      <view class="icon" v-if="iconDelete" @click="deletePwd()">
        <image style="display: block" class="width-height-atuo" :src="$static_ctx + 'image/system/logo/icon-backone.png'"/>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    pwdConfig: {
      type: Object,
      default: function() {
        return {
          name: 'pwd', // 对应的是父组件 data 的密码变量名字
          placeholder: '请输入密码(6-20位)'
        }
      }
    }
  },
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      // $constant: this.$constant,
      // $common: this.$common,
      $static_ctx: this.$static_ctx,
      pwd: '',
      inputPhone: true,
      iconDelete: false
    }
  },
  watch: {
    /** 密码去首尾空监听 */
    pwd() {
      this.pwd = this.$validate.trim(this.pwd)
      const obj = {}
      obj[this.pwdConfig.name] = this.pwd
      this.$emit('updatePwd', obj)
    }
  },
  created() {
    this.isShowPWD = !!this.pwdConfig.isShowPWD
  },
  methods: {
    /** 失去焦点后触发校验 */
    onBlurPWD(e, type) {
      const obj = this.$validate.checkPWD(this.pwd)
      this.pwd = obj.pwd
      this[type] = !this[type]
    },
    onFocusPWD(e, type) {
      const obj = this.$validate.checkPWD(this.pwd)
      this.pwd = obj.pwd
      this[type] = !this[type]
      if (this.pwd) {
        this.iconDelete = true
      } else {
        this.iconDelete = false
      }
    },
    deletePwd() {
      if (this.pwd) {
        this.pwd = ''
        this.iconDelete = false
      }
    },
    phoneInput(e) {
      if (e.detail.value) {
        this.iconDelete = true
      } else {
        this.iconDelete = false
      }
    }

  }
}
</script>

<style lang="scss" scoped>
.password-main{
  height: 56upx;
  position: relative;
  box-sizing: border-box;
  .icon{
    position: absolute;
    right: 0;
    top: 4upx;
    height: 40upx;
    width: 40upx;
    z-index: 2;
  }
  input{
    font-size: 32upx !important;
    line-height: 48upx
  }
}
.f-w-medium{
  color: #bfbfbf;
  font-size: 32upx;
  line-height: 48upx;
  font-weight: Medium;
}
.width-height-atuo{
  width: 100%;
  height: 100%;
}
.bdb-1-topic-c {
  border-bottom: 2upx solid $topicC;
}
.bdb-1-ccc {
  border-bottom: 2upx solid #ccc;
}
</style>
