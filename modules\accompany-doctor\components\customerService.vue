<template>
  <view class="">
    <view class="major-box" :style="{top: top + 'px', left: left + 'px', transition: touchStatus ? '' : 'all 0.3s' }">
    	<view class="click-btn"
    		@tap="$refs.uniPopup.open()" @touchstart="touchstart" @touchend="touchend" @touchmove.stop.prevent="touchmove"
    	>
       <image :src="getIcon" class="customerService" mode=""></image>
       <view class="clickTitle">{{getIconValue}}</view>
    	</view>
    </view>
    <uniPopup type="bottom" ref='uniPopup' @change="changeShow">
      <view class="confirm">
        <view class="confirmTitle">{{popupTitle || '联系客服'}}</view>
        <!-- 保险服务类型 -->
        <template v-if="serviceType === 'insurance'">
          <view class="confirmBox">
            <view class="flex">
              <image class="customerServiceOpen" :src="customerServiceOpen" mode=""></image>
              <view class="confirmContent">服务热线</view>
            </view>
            <view class="confirmBtn" @click="callServicePhone">
              拨打电话
            </view>
          </view>
          <view class="confirmBox">
            <view class="flex">
              <image class="customerServiceOpen" :src="customerServiceOpen" mode=""></image>
              <view class="confirmContent">理赔热线</view>
            </view>
            <view class="confirmBtn" @click="callManagementPhone">
              拨打电话
            </view>
          </view>
        </template>
        <!-- 默认服务类型 -->
        <template v-else>
          <view class="confirmBox" v-if="onlineCustomerButton === 1">
            <view class="flex">
              <image class="customerServiceOpen" :src="customerServiceOpen" mode=""></image>
              <view class="confirmContent">在线客服</view>
            </view>
            <button class="confirmBtn" open-type="contact" bindcontact="handleContact">在线联系</button>
          </view>
          <view class="confirmBox">
            <view class="flex">
              <image class="customerServiceOpen" :src="customerServiceOpen" mode=""></image>
              <view class="confirmContent">电话客服</view>
            </view>
            <view class="confirmBtn" @click="callBack">
              拨打电话
            </view>
          </view>
          <view class="confirmBox" v-if="codeButton === 1">
            <view class="flex">
              <image class="customerServiceOpen" :src="Consultation" mode=""></image>
              <view class="confirmContent">客服咨询</view>
            </view>
            <view class="confirmBtn" @click="previewImage(qrCodeUrl)">
              点击咨询
            </view>
          </view>
        </template>
      </view>
    </uniPopup>
  </view>
</template>

<script>
  import common from '@/common/util/main'
  import uniPopup from '@/components/uni/uni-popup/uni-popup.vue'
  import options from '@/config/env/options'
  export default{
    components: {
        uniPopup
    },
    props: {
      // 是否可以移动
      isMove: {
        type: Boolean,
        default: true
      },
      defaultTop: {
        type: Number,
        default: 300
      },
      popupTitle:{
        type: String,
        default: ''
      },
      showPopup:{
        type:Boolean,
        default:false
      },
      pageIndex:{
        type:Number,
        default:0,
      },
      customTitle:{
        type: String,
        default: '客服'
      },
      // 服务类型：normal(默认)、insurance(保险)
      serviceType: {
        type: String,
        default: 'normal'
      },
      // 服务热线电话
      servicePhone: {
        type: String,
        default: '**********'
      },
      // 理赔热线电话
      managementPhone: {
        type: String,
        default: '956030'
      }
    },
    watch:{
      showPopup(n){
        if(n){
          this.open()
        }else{
          this.close()
        }
      }
    },
    async mounted(){
      try {
        const codeUserInfo = common.getKeyVal('user', 'codeUserInfo',true);
        let {data} = await this.$api.accompanyDoctor.accompanyproviderQueryOne({id:options.providerId})
        this.contactPhone = data.contactPhone;
        this.codeButton = data.codeButton;
        this.onlineCustomerButton = data.onlineCustomerButton
        console.log('在线客服',data);

        // 保存二维码地址
        this.qrCodeUrl = this.file_ctx + data.codeUrl
      } catch (error) {
        //TODO handle the exception
        console.error(error)
      }
    },
    data(){
      return {
        customerService: this.$static_ctx + "image/business/hulu-v2/customerService.png",
        customerServiceOpen: this.$static_ctx + "image/business/hulu-v2/customerServiceOpen.png",
        callPhone: this.$static_ctx + "image/business/hulu-v2/callPhone.png",
        Consultation: this.$static_ctx + "image/business/hulu-v2/Consultation.png",
        touchStatus: false, // 当前是否正在被拖动
        show: false ,// 是否显示
        top: this.defaultTop,	// 顶端距离
        left: uni.getSystemInfoSync().windowWidth-66, // 左侧距离
        deviationTop: 0,	// 偏移量
        deviationLeft: 0,	// 偏移量
        windowHeight: uni.getSystemInfoSync().windowHeight,	// 视图高度
        windowWidth: uni.getSystemInfoSync().windowWidth,	// 视图高度
        boxPosition: 'right',
        contactPhone:'',
        qrCodeUrl: '',
        codeButton: '',
        onlineCustomerButton: '',
      }
    },
    computed:{
      getIconValue(){
        // 特殊处理：当serviceType为insurance时，显示"客服"文字
        if(this.serviceType === 'insurance') {
          return this.customTitle || '客服'
        }
        return ['','急速预约','客服','客服','客服','客服'][this.pageIndex]
      },
      getIcon(){
        // 使用默认图标
        return this.callPhone
      },
    },
    methods:{
      close() {
        this.$refs.uniPopup.close()
      },
      open(){
        this.$refs.uniPopup.open()
      },
      callBack(){
        uni.makePhoneCall({
        	phoneNumber: this.contactPhone
        });
      },
      // 拨打服务热线
      callServicePhone(){
        uni.makePhoneCall({
        	phoneNumber: this.servicePhone
        });
      },
      // 拨打理赔热线
      callManagementPhone(){
        uni.makePhoneCall({
        	phoneNumber: this.managementPhone
        });
      },
      // 客服咨询
      previewImage(url) {
        if (!url || url === 'https://file.greenboniot.cn/' || !url.split('greenboniot.cn/')[1]) {
          uni.showToast({ title: '二维码未配置', icon: 'none' });
          return;
        }
        uni.previewImage({
          urls: [url],    // 需要预览的图片地址列表
          current: 0,     // 当前显示图片的索引
          indicator: 'number',
          loop: true
        })
      },
      changeShow(){

      },
      // 拖动开始，记录一下偏移量
      touchstart: function(e) {
      	this.touchStatus = true
      	let touch = e.touches[0] || e.changedTouches[0];
      	this.deviationTop = touch.clientY - this.top;
      	this.deviationLeft = touch.clientX - this.left;
      	console.log(this.deviationTop);
      },
      // 上下拖动时
      touchmove: function(e) {
        if(!this.isMove) return
      	let touch = e.touches[0] || e.changedTouches[0];
      	let top = touch.clientY;
      	let left = touch.clientX;
      	top = top - this.deviationTop;
      	left = left - this.deviationLeft;
      	if (this.boxPosition === 'right') {
      		if (top < 0) {
      			top = 0;
      		}
      		if (top > this.windowHeight - 50) {
      			top = this.windowHeight - 50;
      		}
      		if (left < 0) {
      			left = 0;
      		}
      		if (left > this.windowWidth - 50) {
      			left = this.windowWidth - 50;
      		}
      	} else {
      		if (top < 0) {
      			top = 0;
      		}
      		if (top > this.windowHeight - 50) {
      			top = this.windowHeight - 50;
      		}
      		if (left > 0) {
      			left = 0;
      		}
      		if (left < (-this.windowWidth + 50)) {
      			left = 0;
      		}
      	}
      	this.top = top;
      	this.left = left;
      	return false;
      },
      // 停止触摸
      touchend() {
      	this.touchStatus = false
      	this.$nextTick(() => {
      		if (this.boxPosition === 'right') {
      				this.boxPosition = 'right'
      				this.left = this.windowWidth-50
      		} else {
      			if (this.left <= (-this.windowWidth/2 + 25)) {
      				this.boxPosition = 'left'
      				this.left = -this.windowWidth + 50
      			} else {
      				this.boxPosition = 'right'
      				this.left = this.windowWidth - 50
      			}
      		}
      	})
      }
    }
  }
</script>

<style lang="scss">
  .major-box {
  	border: 1px 0 solid;
  	z-index: 100;
    width: 112rpx;
    height: 112rpx;
    position: fixed;
  	left: 650rpx;
  	transition: none;
    border-radius: 50%;
    z-index: 101;
  }
  .click-btn {
    width: 130rpx;
    height: 130rpx;
    background: linear-gradient( 180deg, #FF9226 0%, #FF5B09 100%);
  	text-align: center;
  	overflow: hidden;
    padding-top: 20rpx;
    box-sizing: border-box;
    .clickTitle{
      box-sizing: border-box;
      font-weight: 400;
      font-size: 20rpx !important;
    }
    border-radius: 50%;
    color: #FFFFFF;
    .customerService{
      width: 40rpx;
      height: 44rpx;
    }
  }
  .menuIcon{
    width: 104upx;
    height: 88upx;
  }
  .confirm{
    width: 750rpx;
    height: 740rpx;
    background: #FFFFFF;
    border-radius: 24rpx 24rpx 0rpx 0rpx;
    padding-top: 32rpx;
    position: fixed;
    bottom: 0;
    .confirmTitle{
      font-weight: 500;
      font-size: 32rpx;
      color: #1D2029;
      line-height: 44rpx;
      text-align: center;
      margin-bottom: 64rpx;
    }
    .confirmBox{
      width: 686rpx;
      height: 168rpx;
      background: #F4F6FA;
      border-radius: 16rpx;
      padding: 40rpx 32rpx;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 0 auto 32rpx;
    }
    .customerServiceOpen{
      width: 88rpx;
      height: 88rpx;
      margin-right: 24rpx;
    }
    .flex{
      display: flex;
      align-items: center;
    }
    .confirmContent{
      font-weight: 400;
      font-size: 30rpx;
      color: #1D2029;
      text-align: center;
    }
    .confirmBtn{
      width: 208rpx;
      height: 80rpx;
      background: #00B484;
      border-radius: 40rpx;
      line-height: 88rpx;
      font-weight: 500;
      font-size: 32rpx;
      color: #FFFFFF;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0;
      .callPhone{
        width: 28rpx;
        height: 30rpx;
        margin-right: 8rpx;
      }
    }
  }
</style>
