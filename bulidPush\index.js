// 常用指令格式如下 idSet用来指定具体运行发布哪些小程序
 // npm run publish description=修复医院数据筛选错误 idList=1,2,3,4 version=1.2.3
import {
  exec
} from 'child_process';
import optionsMap from "../config/env/optionsMap.js"
import {
  ParsingOptions,
  createInstructionSet,
  runTimeMain
} from "./utils.js"
// 获取指令参数
let parameters = ParsingOptions()
console.log('parameters',parameters);
// 生成编译指令集合
let instructionSet = createInstructionSet(optionsMap, parameters);
console.log('生产指令集合', instructionSet);
// // 运行主函数
runTimeMain(instructionSet)

