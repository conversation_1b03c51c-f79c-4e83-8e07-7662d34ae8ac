/**
 * 系统路由
 */
export default [
{
  path: '/pages/circle-home/index',
  name: 'CircleHome',
  meta: {
    index: 2,
    headerObj: {
      title: '交流',
      isShow: true
    }
  }
},
{
  path: '/pages/post-message/index',
  name: 'PostMessage',
  meta: {
    index: 2,
    headerObj: {
      title: '发帖',
      isShow: true
    }
  }
},
{
  path: '/modules/community/posts/detail/index',
  name: 'PostsDetail',
  meta: {
    index: 2,
    headerObj: {
      title: '帖子详情',
      isShow: true
    }
  }
},
{
  path: '/modules/community/posts/edit/index',
  name: 'PostsEdit',
  meta: {
    index: 2,
    headerObj: {
      title: '编辑帖子',
      isShow: true
    }
  }
},
{
  path: '/modules/community/posts/invite-reply/index',
  name: 'PostsInviteReply',
  meta: {
    index: 2,
    headerObj: {
      title: '邀请医生解答',
      isShow: true
    }
  }
},
{
  path: '/modules/community/posts/reply/index',
  name: 'PostsReply',
  meta: {
    index: 2,
    headerObj: {
      title: '帖子回复',
      isShow: true
    }
  }
},
{
  path: '/modules/community/posts/invite-record/index',
  name: 'InviteRecord',
  meta: {
    index: 2,
    headerObj: {
      title: '邀请记录',
      isShow: true
    }
  }
},
{
  path: '/modules/community/news/reply/index',
  name: 'NewsReply',
  meta: {
    index: 2,
    headerObj: {
      title: '回复',
      isShow: true
    }
  }
},
{
  path: '/modules/community/news/like-collect/index',
  name: 'NewsLikeCollect',
  meta: {
    index: 2,
    headerObj: {
      title: '点赞和收藏',
      isShow: true
    }
  }
},
{
  path: '/modules/community/enterprise-wechat-group/index',
  name: 'EnterpriseWechatGroup',
  meta: {
    index: 2,
    headerObj: {
      title: '企业微信群',
      isShow: true
    }
  }
},
{
  path: '/modules/community/enterprise-wechat-group/check/index',
  name: 'EnterpriseWechatGroupCheck',
  meta: {
    index: 2,
    headerObj: {
      title: '查看群二维码',
      isShow: true
    }
  }
},
{
  path: '/modules/community/circle/more/index',
  name: 'CircleMoreIndex',
  meta: {
    index: 2,
    headerObj: {
      title: '更多圈子',
      isShow: true
    }
  }
},
{
  path: '/modules/community/personal/my-comment/index',
  name: 'PersonalMyComment',
  meta: {
    index: 2,
    headerObj: {
      title: '我的评论',
      isShow: true
    }
  }
},
{
  path: '/modules/community/personal/my-like/index',
  name: 'PersonalMyLike',
  meta: {
    index: 2,
    headerObj: {
      title: '我的点赞',
      isShow: true
    }
  }
},
{
  path: '/modules/community/circle/index',
  name: 'Circle',
  meta: {
    index: 2,
    headerObj: {
      title: '圈子详情',
      isShow: true
    }
  }
},
{
  path: '/modules/community/personal/my-collect/index',
  name: 'PersonalMyCollect',
  meta: {
    index: 2,
    headerObj: {
      title: '我的收藏',
      isShow: true
    }
  }
},
{
  path: '/modules/community/personal/my-posts/index',
  name: 'PersonalMyPosts',
  meta: {
    index: 2,
    headerObj: {
      title: '我的帖子',
      isShow: true
    }
  }
},
{
  path: '/modules/community/personal/home-page/index',
  name: 'PersonalHomePage',
  meta: {
    index: 2,
    headerObj: {
      title: '个人主页',
      isShow: true
    }
  }
},
{
  path: '/modules/community/patronsaint/index',
  name: 'Patronsaint',
  meta: {
    index: 2,
    headerObj: {
      title: '健康保护神',
      isShow: true
    }
  }
},
{
  path: '/modules/community/circle/guide-subscribe/index',
  name: 'CircleGuideSubscribe',
  meta: {
    index: 2,
    headerObj: {
      title: '引导订阅圈子',
      isShow: true
    }
  }
},
]
