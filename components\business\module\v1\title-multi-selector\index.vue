<!--
*
*config  [Object] 参数设置
bdt: true,              上划线border-top
bdb: true,              下划线border-bottom
label: '下拉框',         左边name值
name: 'select',         字段名
placeholder: '请选择',   提示
required: false,        是否必填
array: [],              展示数组
dicKey: '',             查询字典字段key
disabled: false         是否禁用
oneArr: []              第一维数组
twoArr: []              第二维数组
split: ''               字符转数组的分割值
-------------------------------------------

*returnFn    [function]     回调函数
*cData        [String]       (默认选中)key传入方式
*getData     [String]      做数据处理首次默认值watch执行监听
*
*字典选择器(已经放到全局使用) <dictionary-selector  :config="xxx" :cData="xxx"  @returnFn = "xxx"></dictionary-selector>
 -->
<template>
  <view class="time-quantum" :class="{'bdt':defaultConfig.bdt,'bdb':defaultConfig.bdb}">
    <view class="time-l" :style="{'color': defaultConfig.titleColor}">
      {{defaultConfig.label}}
      <text class="star" v-if="defaultConfig.required">*</text>
    </view>
    <view class="time-r">
      <picker-multi-selector class="time-r-r" :disabled="disabled" :config="defaultConfig" @returnFn="returnFn" :cData="form.data.select"></picker-multi-selector>
    </view>
    <em class="jump"></em>
  </view>
</template>

<script>
import PickerMultiSelector from '@/components/business/module/title-multi-selector/picker-multi-selector'
export default {
  components: {
    PickerMultiSelector
  },
  data() {
    return {
      form: {
        data: {
          // select: []
          select: ''
        }
      },
      array: [],
      index: 0,
      defaultConfig: {
        bdt: false,
        bdb: true,
        titleColor: '#333',
        label: '二维下拉',
        name: 'select',
        placeholder: '请选择',
        required: false,
        oneArr:[],
        twoArr:[],
        split: ''
      }
    }
  },
  watch: {
    cData: {
      handler(val) {
        this.watchDataMain(val)
      },
      deep: true
    },
    config: {
      handler(val) {
        this.copyConfig()
      },
      deep: true
    }
  },
  props: {
    // 数组下标
    dIndex: {
      type: [String, Number],
      default() {
        return ''
      }
    },
    // 是否禁用disable
    disabled: {
      type: Boolean,
      default() {
        return false
      }
    },
    // 初始值传值，用于回显
    cData: {
      type: String,
      required: false,
      default() {
        return ''
      }
    },
    // 参数设置
    config: {
      type: Object,
      required: false,
      default: () => {
        return {}
      }
    }
  },
  computed: {

  },
  created() {

  },
  mounted() {
    this.copyConfig()
    if (!this.$validate.isNull(this.cData)) {
      this.watchDataMain(this.cData)
    }
  },
  methods: {
    /**
       * 初始化拷贝config对象
       */
    copyConfig() {
      const that = this
      const obj = that.config
      const defaultConfig = that.defaultConfig
      Object.keys(obj).forEach(function(key) {
        defaultConfig[key] = obj[key]
      })
      that.defaultConfig = Object.assign({}, defaultConfig)
      if (!that.$validate.isNull(that.cData)) {
        that.watchDataMain(that.cData)
      }
    },
    /**
       * 监听Data主逻辑方法
       */
    watchDataMain(val) {
      this.form.data.select = val.toString()
    },
    /**
       * picker触发选中事件
       * @param v
       */
    returnFn(v) {
      this.form.data.select = v
      let params = {
        key: '' + this.config.name,
        value: '' + this.form.data.select
      }
      if(!this.$validate.isNull(this.dIndex)){
        params.index = this.dIndex
      }
      this.$emit('returnFn', params)
    }
  }
}
</script>

<style lang="scss" scoped>
  .color-999{
    color: #999999!important;
  }
  .time-quantum{
    background-color: #fff;
    height: 88upx;
    padding: 0 30upx;
    .time-l{
      line-height: 88upx;
      font-size: 32upx;
      display: inline-block;
      vertical-align: middle;
      width: 300upx;
    }
    .time-r{
      line-height: 88upx;
      font-size: 32upx;
      display: inline-block;
      vertical-align: middle;
      width: calc(100% - 334upx);
      text-align: right;
      .time-r-r{
        display: inline-block;
      }
    }
    .jump{
      @include iconImg(34,34,'/business/icon-gengduo.png');
      display: inline-block;
      vertical-align: middle;
    }
  }
  .star{
    color: #F85E4C;
    padding-top: 6upx;
    font-size: 32upx;
    display: inline-block;
  }
</style>
