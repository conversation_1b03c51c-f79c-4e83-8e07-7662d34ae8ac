import common from '@/common/util/main'
import api from '@/service/api'
import ext from '@/service/ext'
import constant from '@/constant'
import env from '@/config/env'
import validate from '@/common/util/validate'
import uniPlugin from '@/common/util/uni-plugin'
import md5 from 'js-md5'
import store from '@/store'
import navto from '@/router/config/nav-to'
import rsa from '@/common/util/rsa'
import timePlugin from '@/common/util/time-plugin'
import $validate from '@/common/util/validate'

const fansVTypeArr = [
  { value: 1, label: '医生' },
  { value: 2, label: '企业' },
  { value: 3, label: 'KOC' }
]

const replyStatusList = [
  { value: 1, label: '已答复' },
  { value: 2, label: '已邀请' }
]

/**
 * 系统请求工具类，
 * 注意：API接口只做请求数据和格式化数据的事情，其他事务一概不处理
 */
export default {
  /**
   * 帖子推荐-分页列表查询
   * @param resolve
   * @param reject
   */
   postmessageQueryRecommendPage(param) {
    const that = this
    return new Promise((resolve, reject) => {
      api.postmessage.postmessageQueryRecommendPage(param).then((res) => {
        let data = res.data.records || []
        data = data.map(item => {
		  // if(item.intro && item.intro != ''){
			 //  item.introTip = common.rexFilter(item.intro);
		  // }
		  // if(item.content)
		  // item.content = item.
          // let content = common.rexFilter(item.content)
		  // if(!)
          item.content = item.intro || ''
		  // console.log('item.content',item.content)
          return {
            ...item,
            putawayTimeText: timePlugin.formatDate(new Date().valueOf(), item.putawayTime)
          }
        })
        res.data.records = data
        resolve(res)
      }).catch(error => {
        reject(error)
      })
    })
  },
  /**
   * 首页搜索-分页查询
   * @param resolve
   * @param reject
   */
   postmessageSearch(param) {
    const that = this
    return new Promise((resolve, reject) => {
      api.postmessage.postmessageSearch(param).then((res) => {
        let data = res.data.records || []
        data = data.map(item => {
          // item.content = common.rexFilter(item.content)
          item.content = item.intro || ''
          return {
            ...item,
            putawayTimeText: timePlugin.formatDate(new Date().valueOf(), item.putawayTime)
          }
        })
        res.data.records = data
        resolve(res)
      }).catch(error => {
        reject(error)
      })
    })
  },
  /**
   * 名医热点-分页查询
   * @param resolve
   * @param reject
   */
   postmessageQueryPhysicianHotPage(param) {
    const that = this
    return new Promise((resolve, reject) => {
      api.postmessage.postmessageQueryPhysicianHotPage(param).then((res) => {
        let data = res.data.records || []
        data = data.map(item => {
          // item.content = common.rexFilter(item.content)
		  item.content = item.intro || ''
          return {
            ...item,
            putawayTimeText: timePlugin.formatDate(new Date().valueOf(), item.putawayTime)
          }
        })
        res.data.records = data
        resolve(res)
      }).catch(error => {
        reject(error)
      })
    })
  },
  /**
   * 精华帖子-分页查询
   * @param resolve
   * @param reject
   */
   postmessageQueryEssencePage(param) {
    const that = this
    return new Promise((resolve, reject) => {
      api.postmessage.postmessageQueryEssencePage(param).then((res) => {
        let data = res.data.records || []
        data = data.map(item => {
          // item.content = common.rexFilter(item.content)
		  item.content = item.intro || ''
          return {
            ...item,
            putawayTimeText: timePlugin.formatDate(new Date().valueOf(), item.putawayTime)
          }
        })
        res.data.records = data
        resolve(res)
      }).catch(error => {
        reject(error)
      })
    })
  },
  /**
   * 帖子列表-分页查询
   * @param resolve
   * @param reject
   */
   postmessageQueryPage(param) {
    const that = this
    return new Promise((resolve, reject) => {
      api.postmessage.postmessageQueryPage(param).then((res) => {
        let data = res.data.records || []
        data = data.map(item => {
          // item.content = common.rexFilter(item.content)
          // item.content = common.rexFilter(item.content)
          item.content = item.intro || ''
          return {
            ...item,
            putawayTimeText: timePlugin.formatDate(new Date().valueOf(), item.putawayTime)
          }
        })
        res.data.records = data
        resolve(res)
      }).catch(error => {
        reject(error)
      })
    })
  },
  /**
   * 我的收藏帖子-分页查询
   * @param resolve
   * @param reject
   */
  commoncollectlikesQueryMyCollectionPage(param) {
    const that = this
    return new Promise((resolve, reject) => {
      api.community.commoncollectlikesQueryMyCollectionPage(param).then((res) => {
        let data = res.data.records || []
        data = data.map(item => {
          // item.content = common.rexFilter(item.content)
          item.content = item.intro || ''
          return {
            ...item,
            putawayTimeText: timePlugin.formatDate(new Date().valueOf(), item.putawayTime),
            id: item.businessId,
            isDel: item.postMessageStatus == -1 || [2,4].includes(item.putawayStatus)
          }
        })
        res.data.records = data
        resolve(res)
      }).catch(error => {
        reject(error)
      })
    })
  },
  /**
   * 帖子没有被邀请过的医生（随机获取list）
   */
  async fansrecordNotInviteDoctorList(param) {
    try {
      const res = await api.community.fansrecordNotInviteDoctorList(param)
      let data = res.data || []
      res.data = data.map(item => {
        return {
          ...item,
          vTypeText: common.getEnumText(item.vType, fansVTypeArr),
          inviteStatus: 2,
          headPath: common.getHeadImage(item.headPath)
        }
      })
      return Promise.resolve(res)
    } catch (err) {
      return Promise.reject(err)
    }
  },
  /**
   * 已邀请评论记录
   */
  async cfpostinvitelogQueryPage(param) {
    try {
      const res = await api.community.cfpostinvitelogQueryPage(param)
      let data = res.data.records || []
      res.data.records = data.map(item => {
        // 评论数据
        let commentInfo = item.commentInfo
        if(!$validate.isNull(commentInfo)) {
          commentInfo = {
            ...commentInfo,
            imagePathList: commentInfo.imagePath ? commentInfo.imagePath.split(',') : commentInfo.imagePath
          }
        }
        // 帖子详情数据
        let postMessageDetailVo = item.postMessageDetailVo
        if(!$validate.isNull(postMessageDetailVo)) {
          postMessageDetailVo = {
            ...postMessageDetailVo,
            showReadNumber: +postMessageDetailVo.virtualReadNumber + postMessageDetailVo.readNumber,
            showCommentNumber: +postMessageDetailVo.virtualCommentNumber + postMessageDetailVo.commentNumber,
            showLikeNumber: +postMessageDetailVo.virtualLikeNumber + postMessageDetailVo.likeNumber,
            headPath: common.getHeadImage(postMessageDetailVo.headPath),
          }
        }
        return {
          ...item,
          headPath: common.getHeadImage(item.headPath),
          vTypeText: common.getEnumText(item.vType, fansVTypeArr),
          replyStatusText: common.getEnumText(item.replyStatus, replyStatusList),
          commentInfo,
          postMessageDetailVo,
          inviteDurationText: timePlugin.formatDate(Date.now(), item.createTime)
        }
      })
      return Promise.resolve(res)
    } catch (err) {
      return Promise.reject(err)
    }
  },
  /**
   * 我的收藏帖子-分页查询
   * @param resolve
   * @param reject
   */
  commoncollectlikesQueryMyLikePage(param) {
    const that = this
    return new Promise((resolve, reject) => {
      api.community.commoncollectlikesQueryMyLikePage(param).then((res) => {
        let data = res.data.records || []
        data = data.map(item => {
          // item.content = common.rexFilter(item.content)
          item.content = item.intro || ''
          return {
            ...item,
            putawayTimeText: timePlugin.formatDate(new Date().valueOf(), item.putawayTime),
            id: item.businessId,
            isDel: item.postMessageStatus == -1 || [2,4].includes(item.putawayStatus)
          }
        })
        res.data.records = data
        resolve(res)
      }).catch(error => {
        reject(error)
      })
    })
  },
  /**
   * 帖子列表-分页查询
   * @param resolve
   * @param reject
   */
  postmessageQueryAppPage(param) {
    const that = this
    return new Promise((resolve, reject) => {
      api.postmessage.postmessageQueryAppPage(param).then((res) => {
        let data = res.data.records || []
        data = data.map(item => {
          // item.content = common.rexFilter(item.content)
          // item.content = common.rexFilter(item.content)
          item.content = item.intro || ''
          return {
            ...item,
            putawayTimeText: timePlugin.formatDate(new Date().valueOf(), item.putawayTime)
          }
        })
        res.data.records = data
        resolve(res)
      }).catch(error => {
        reject(error)
      })
    })
  },
}
