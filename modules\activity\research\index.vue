<template>
  <!--  <scroll-view
    :style="{
      height: height
    }"
    :scroll-y="true"
    scroll-with-animation
    :scroll-into-view="scrollintotargetview"

  > -->
  <!-- top="92" -->
  <!-- <view > -->
  <scroll-refresh :fixed="true" :bgcolor="'#f0f0f0'" top="0" :up="upOption" :down="downOption" @returnFn="returnFn" @scrollInit="scrollInit">
    <view class="main-content">
        <title-search :value="searchinput" @update="searchFn"></title-search>
      <case-list-item :type="activeindex" :list="caselist"></case-list-item>

      <!-- <nomore  v-if="caselist.length == 0"></nomore> -->
    </view>
  </scroll-refresh>
  <!-- </view> -->

  <!-- </scroll-view> -->
</template>

<script>
import caseListItem from './listitem.vue';
import nomore from '@/modules/activity/components/nomore/index.vue';
import titleSearch from '@/modules/activity/components/title-search/index.vue';

import titleTimer from '@/modules/activity/components/title-timer/index.vue';

export default {
  name: 'caselist',
  components: {
    caseListItem,
    nomore,
    titleSearch,
    titleTimer
  },
  props: {
    tenantId: {
      type: String,
      default: '1'
    },
    height: {
      type: String,
      default: '100vh'
    }
  },
  data() {
    return {
      updatenav: true,
      isInit: false, // 列表是否已经初始化
      mescroll: null, // mescroll实例对象
      downOption: {
        auto: false // 不自动加载
      },
      upOption: {
        auto: false // 不自动加载
        // use:false,
      },
      searchinput: '',
      // 1 2
      moretype: 2,
      caselist: [
        // {
        //   title: '张三',
        //   code: '344106',
        //   createTime: new Date().getTime(),
        //   updateTime: new Date().getTime()
        // }
        // {
        //   title: '消炎镇痛',
        //   desc: '患者性别:男，年龄:15岁，患有消炎镇席，现病史;dhdbs，现病时长: udbd在宜宾市第五人民医院心胸',
        //   reason: '拒绝原因: 患者姓名为空，需要填写患者姓名',
        //   createTime: new Date().getTime(),
        //   createTimeinfo: '2022-10-22'
        // }
      ],
      scrollintotargetview: '',
      activeindex: 0,
      noReq: false,
      // createTime:null,
      startCreateTime: null,
      endCreateTime: null,
      searchTitle: '',
      timetype: null,
      activitytype:2,
    };
  },
  mounted() {
    this.init()
  },
  methods: {
    removeitem(idx) {
      this.caselist.splice(idx, 1);
    },
    queryTimer(obj) {
      console.log(obj.item);
      if (obj.item.starttimer) {
        this.startCreateTime = obj.item.starttimer;
        this.endCreateTime = obj.item.endtimer;
        this.timetype = null;
        // this.createTime = obj.item.starttimer + '-' + obj.item.endtimer;
      } else {
        this.startCreateTime = null;
        this.endCreateTime = null;
        this.timetype = 'all';
        // this.createTime = null;
      }
      this.mescroll.resetUpScroll();
    },
    // 获取本月时间
    getTime() {
      let date = new Date();

      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      let days = date.getDate();
      let nowtimer = date.getTime();

      // 本月
      let str = year + '-' + month + '-' + '01' + '  ' + '00:00:00';
      // let lastmonth = month + 1;
      let endstr;
      if (month + 1 > 12) {
        // lastmonth = '01'
        endstr = year + 1 + '-' + '01' + '-' + '01' + '  ' + '00:00:00';
      } else {
        endstr = year + '-' + (month + 1) + '-' + '01' + '   ' + '00:00:00';
      }

      // let startimer = new Date(str).getTime()
      // let endtimer = new Date(endstr).getTime()

      this.startCreateTime = str;
      this.endCreateTime = endstr;
      // return startimer + '-' + endtimer;
    },
    cannelTimer() {
      this.mescroll.lockDownScroll(false);
      this.mescroll.lockUpScroll(false);
      // this.noReq = false;
    },
    toggleTimer(obj) {
      if (obj.item.type == 'other') {
        console.log('进来了');
        this.mescroll.lockDownScroll(true);
        this.mescroll.lockUpScroll(true);
        // this.noReq = true;
        // this.upOption.use = false;
      }
      console.log('obj', obj);
    },

    init(val) {
      this.$nextTick(() => {
        this.isInit = true; // 标记为true
        // this.regForm.search = ''
        this.mescroll.triggerDownScroll();
      });
    },
    scrollInit(scroll) {
      console.log('scroll', scroll);
      scroll.optUp.page.num = 1;
      scroll.optUp.page.size = 7;
      this.mescroll = scroll;
    },
    returnFn(obj) {
      const that = this;
      function queryPage(pageNum, pageSize, fn) {
        if (!that.startCreateTime && that.timetype != 'all') {
          that.getTime();
          // that.createTime =
        }
        const accountId = that.$common.getKeyVal('user', 'accountId', true)
        const param = {
          current: pageNum,
          size: pageSize,
          condition: {
            // openStatus:2,
            type: 1, // 流程类型：1-征集，2-回访
            activityType:that.activitytype,
            accountId,
            notSelfHealth:1, //过滤健康自测
            // createTime:that.createTime,
          }
        };

        if (that.activeindex == 3) {
          param.condition.startCreateTime = that.startCreateTime;
          param.condition.endCreateTime = that.endCreateTime;

          // startCreateTime:that.startCreateTime,
          // endCreateTime:that.endCreateTime
        }

        if (that.activeindex == 2 && that.searchTitle != '') {
          param.condition.title = that.searchTitle;
        }

        // if (that.regForm.search) {
        //   param.condition.title = that.regForm.search
        // }
        that.$api.activity.casecollectsubmitlogquerylistV2(param).then(res => {
          if (res && res.data.records) {
            for (const a in res.data.records) {
              const data = res.data.records[a];
              data.endTimeText = that.$common.formatDate(new Date(data.endTime), 'yyyy-MM-dd HH:mm');
              data.updateTimeText = that.$common.formatDate(new Date(data.updateTime), 'yyyy-MM-dd HH:mm');
              data.progressText = (data.progress - 0) * 100;
              data.createTimeText = that.$common.formatDate(new Date(data.createTime), 'yyyy-MM-dd HH:mm');
              data.issueDateText = that.$common.formatDate(new Date(data.issueDate), 'yyyy-MM-dd HH:mm');
            }
            // debugger
            fn(res.data.records);
          }
        });
      }
      setTimeout(function() {
        queryPage(obj.pageNum, obj.pageSize, data => {
          if (obj.pageNum === 1) {
            that.caselist = [];
          }
          // data = [{}]
          that.caselist = that.caselist.concat(data);
          console.log('that.caselist-----------', that.caselist)
          obj.successCallback && obj.successCallback(data || []);
        });
      }, that.$constant.noun.scrollRefreshTime);
      // obj.successCallback && obj.successCallback(data || [])
    },
    searchFn(obj) {
      this.caselist = [];
      this.searchTitle = obj.value;
      this.init();
      // console.log('obj',obj)
    }
  }
};
</script>

<style lang="scss" scoped>
.main-content {
  background-color: #f0f0f0;
}
</style>
