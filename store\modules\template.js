import common from '@/service/api/modules/common.js'

const campus = {
  namespaced: true,
  state: {
    fileList: [],
    paramsList: {},
    // 科室数据
    departmentPromise:null,
    // 地址数据
    addressDataPromise:null,

  },

  mutations: {
    UPDATE_ADDRESSDATA: (state, data) => {
      state.addressDataPromise = new Promise((resolve,reject) => {
        common.getAreaListArea({
        }).then(res => {
          resolve(res.data)
        }).catch(e => {
          resolve([])
        });
      })
    },
    UPDATE_DEPARTMENTRESULT: (state, data) => {
      state.departmentPromise = new Promise((resolve,reject) => {
        common.findByParentId({
          parentId:120003
        }).then(res => {
          resolve(res.data)
        }).catch(e => {
          resolve([])
        });
      })
    },
    /**
     * 案例模板
     * @param state
     * @param data
     * @constructor
     */
    UPDATE_DEMO: (state, data) => {

    },
    /**
     * 更新主页数据
     * @param state
     * @param data
     * @constructor
     */
    UPDATE_FILELIST: (state, data) => {
      state.fileList = data
    },
    /**
     * 更新主页数据
     * @param state
     * @param data
     * @constructor
     */
    UPDATE_PARAMSLIST: (state, data) => {
      state.paramsList = data
    }
  },

  actions: {
    getAddressData({commit,state}, data){
       if(!state.addressDataPromise){
         commit('UPDATE_ADDRESSDATA', data)
       }
       return state.addressDataPromise;
    },
    getDepartmentResult({commit,state}, data) {
      if(!state.departmentPromise){
        commit('UPDATE_DEPARTMENTRESULT', data)
      }
      return state.departmentPromise;
    },
    /**
     * 案例模板
     * @param commit
     * @param data
     * @constructor
     */
    UpdateDemo({ commit }, data) {
      commit('UPDATE_DEMO', data)
    },
    UpdateFiles(context, data) {
      context.commit('UPDATE_FILELIST', data)
    },
    UpdateParams(context, data) {
      context.commit('UPDATE_PARAMSLIST', data)
    }
  }
}

export default campus
