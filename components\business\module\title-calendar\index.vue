<!-- 内容模块标题配件组件 <title-jump :title="xx" :text="xx" @returnFn = "returnFn"></title-jump> -->
<template>
  <view class="univalent" :class="{'bdt':bdt,'bdb':bdb}">
    <view class="univalent-padding" @click="open">
      <text class="l-t"><text class="xingxing" v-if="isRequired">*</text>{{title}}</text>
      <em v-show="iconState" class="icon-gengduo jump"></em>
      <text :class="{'l-r': true, 'm-r-30': !iconState}" :style="{'color':color}">{{text}}</text>
    </view>
    <uni-calendar
      ref="calendar"
      :multiSelectDate="multiSelectDate"
      :insert="insert"
      :disableBefore="disableBefore"
      :multiSelect="multiSelect"
      @confirm="confirm"
    ></uni-calendar>
  </view>
</template>

<script>
import uniCalendar from '@/components/uni/uni-calendar/uni-calendar'
export default {
  components: {
    uniCalendar
  },
  props: {
    /**
     * 是否开启多选日期
     */
    multiSelect: {
      type: Boolean,
      default: false
    },
    /**
     * 是否开启多选日期初始值
     */
    multiSelectDate: {
      type: Array,
      default() {
        return []
      }
    },
    /**
     * 插入
     */
    insert: {
      type: Boolean,
      default: false
    },
    /**
     * 是否禁用今天之前的日期
     */
    disableBefore: {
      type: Boolean,
      default: false
    },
    // 控制icon是否显示
    iconState: {
      type: Boolean,
      default() {
        return true
      }
    },
    // 显示文本
    text: {
      type: String,
      default() {
        return '请选择'
      }
    },
    // 是否禁用disable
    disabled: {
      type: Boolean,
      default() {
        return false
      }
    },
    // 标题名称
    title: {
      type: String,
      default() {
        return ''
      }
    },
    // border-top
    bdt: {
      type: Boolean,
      default() {
        return true
      }
    },
    // border-bottom
    bdb: {
      type: Boolean,
      default() {
        return true
      }
    },
    color: {
      type: String,
      default() {
        return '#666666'
      }
    },
    isRequired: {
      type: Boolean,
      default() {
        return false
      }
    }
  },
  data() {
    return {
      val: 0
    }
  },
  watch: {},
  methods: {
    open() {
      if (!this.disabled) {
        this.$refs.calendar.open()
      }
    },
    confirm(e) {
      if (!this.disabled) {
        this.$emit('returnFn', e)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .m-r-30{
    margin-right: 30upx;
  }
  .univalent{
    width:100%;
    box-sizing: border-box;
    overflow: hidden;
    height: 88upx;
    background-color:#ffffff;
    .univalent-padding {
      height: 100%;
    }
    .l-t{
      margin-left: 30upx;
      color:#333333;
      font-size:32upx;
      line-height: 88upx;
      float: left;
      @include ellipsis();
      width: 280upx;
    }
    .l-r{
      float: right;
      color:#333333;
      font-size:32upx;
      line-height: 88upx;
    }
  }
  .xingxing{
    color: #F85E4C;
    padding-top: 6upx;
    font-size: 32upx;
    display: inline-block;
  }
  .jump{
    width:34upx;
    height: 34upx;
    float: right;
    margin: 26upx 20upx 0 6upx;
  }
</style>
