const CryptoJS = require('crypto-js') // 引用AES源码js
const key = CryptoJS.enc.Utf8.parse('53A1F8076893B4B9FC2C4293558DB5AA') // 十六位十六进制数作为密钥
const iv = CryptoJS.enc.Utf8.parse('EA6B2EFBDD4255A9F1B3BBC6399B58F4') // 十六位十六进制数作为密钥偏移量

/**
 * 加密方法
 * @param word
 * @returns {string}
 * @constructor
 */
export function Encrypt(word) {
    const srcs = CryptoJS.enc.Utf8.parse(word)
    const encrypted = CryptoJS.AES.encrypt(srcs, key, {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
    })
    return encrypted.ciphertext.toString().toUpperCase()
}

/**
 * 解密方法
 * @param word
 * @returns {string}
 * @constructor
 */
export function Decrypt(word) {
    const encryptedHexStr = CryptoJS.enc.Hex.parse(word)
    const srcs = CryptoJS.enc.Base64.stringify(encryptedHexStr)
    const decrypt = CryptoJS.AES.decrypt(srcs, key, {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
    })
    const decryptedStr = decrypt.toString(CryptoJS.enc.Utf8)
    return decryptedStr.toString()
}