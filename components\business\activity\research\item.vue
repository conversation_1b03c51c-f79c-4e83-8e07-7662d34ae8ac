<template>
  <view>
    <view class="portrait-list">
      <!-- @tap="navtoGo('ResearchDetail',{id:itemA.id})" -->
          <view
            class="li"
            v-for="(itemA,indexA) in pdList"
            :key="indexA"
            
            @tap="navtoGo('questionnaireIndex',{id:itemA.id})">
            <view class="img">
              <image mode="aspectFill" :src="itemA.coverPath ? file_ctx + itemA.coverPath : $static_ctx + 'image/system/avatar/icon-default-v2.png'"  class="role-image"/>
            </view>
            <view class="content">
              <view class="title">
                {{itemA.title || '暂无名称'}}
              </view>
              <view class="title-t">
                <text>{{ itemA.desc.replace(/<[^>]+>/g,"").substring(0,10) + '...'}}</text>
              </view>
              <view class="footer">
                <view class="icon-time"></view>
                <view class="time">截止到 {{ itemA.endTimeText}}</view>
                <!--<text class="btn-bg btn" @tap="navtoGo('ResearchDetail',{id:itemA.id})">马上参加</text>-->
              </view>
            </view>
          </view>
        </view>
  </view>
</template>

<script>
export default {
  components: {

  },
  props: {
    // 参数设置
    pdList: {
      type: Array,
      required: false,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $accurateConversion: this.$accurateConversion,
      file_ctx: this.file_ctx,
      $static_ctx: this.$static_ctx,
      $timePlugin: this.$timePlugin,
      $validate: this.$validate,
    }
  },
  watch: {
    // 监听下标的变化
  },
  onLoad(paramsObj) {
    const query = this.$Route.query
    if (!this.$validate.isNull(query)) {
    }
  },
  mounted() {
  },
  methods: {
    navtoGo(url = '', obj = {}) {
      this.$navto.push(url, obj)
    }
  }
}
</script>
<style lang="scss" scoped>
  .portrait-list{
      /*margin-top: 10upx;*/
      .li{
        @include rounded(20upx);
        background-color:#FFFFFF;
        padding:16upx;
        margin-bottom: 24upx;
        .img{
          width: 240upx;
          height: 180upx;
          display: inline-block;
          vertical-align: middle;
          @include rounded(20upx);
          .role-image{
            width: 100%;
            height: 100%;
          }
        }
        .content{
          height: 180upx;
          position: relative;
          width: calc(100% - 266upx);
          padding-left: 24upx;
          display: inline-block;
          vertical-align: middle;
          .title{
            font-size: 32upx;
            color: #333333;
            display: block;
            font-weight: 600;
            /*line-height: 40upx;*/
            @include ellipsis(2);
            /*margin-bottom: 16upx;*/
          }
          .title-t{
            font-size: 28upx;
            color: #999999;
            display: block;
          }
          .footer{
            position: absolute;
            width: 100%;
            bottom: 0;
            .icon-time{
              display: inline-block;
              margin-right: 10upx;
              @include iconImg(26, 26, '/system/icon-time.png');
            }
            .time{
              color: #999999;
              font-size: 24upx;
              display: inline-block;
            }
            .btn{
              display: inline-block;
              right: 0;
              top: 0;
              width: 108upx;
              height: 40upx;
              line-height: 40upx;
              font-size: 24upx;
            }
          }
        }
      }
    }
</style>
