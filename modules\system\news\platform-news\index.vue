<template>
  <view class="main-body">
    <scroll-refresh class="scroll-refresh-main" :fixed="false" :up="upOption" :down="downOption" @returnFn="returnFn" @scrollInit="scrollInit">
      <view class="mian">
        <view class="list" v-for="(item,index) in pdList" :key="index" @tap="navtoGo('PlatformNewsDetails', {id: item.id})">
          <view class="list-body" v-if="item.PlatformMessage">
            <view v-if="item.isRead == 0" class="offiCialone"></view>
            <view class="t">
              <view class="l">
                <image mode="scaleToFill" :src="$static_ctx + 'image/business/icon-news-logo.png'"/>
              </view>
              <view class="m">
                {{item.PlatformMessage.author}}
              </view>
              <view class="r">
                {{$timePlugin.formatDate(new Date().valueOf(), item.PlatformMessage.createTime)}}
              </view>
            </view>
            <view class="m">
              <view class="title">
                {{item.PlatformMessage.title}}
              </view>
              <view class="content">
                {{item.PlatformMessage.content}}
              </view>
            </view>
            <view class="b" v-if="item.PlatformMessage.coverPic">
              <image mode="scaleToFill" :src="file_ctx + item.PlatformMessage.coverPic"/>
            </view>
          </view>
        </view>
      </view>
    </scroll-refresh>
  </view>
</template>

<script>
import { mapState } from 'vuex'
import TabsSticky from '@/components/basics/tabs-sticky'
export default {
  components: {
    TabsSticky
  },
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $accurateConversion: this.$accurateConversion,
      file_ctx: this.file_ctx,
      $timePlugin: this.$timePlugin,
      $static_ctx: this.$static_ctx,
      regForm: {},
      mescroll: null, // mescroll实例对象
      downOption: {
        auto: false // 不自动加载
      },
      upOption: {
        auto: false // 不自动加载
      },
      pdList: [] // 列表数据
    }
  },
  computed: {
    ...mapState('user', {
      curSelectUserInfo: state => state.curSelectUserInfo, // 当前选中的用户信息
      curSelectUserInfoId: state => state.curSelectUserInfoId, // 当前选中的档案的ID
    })
  },
  onLoad(paramsObj = {}) {
    if (paramsObj.query) {}
  },
  onShow() {
    this.init()
  },
  methods: {
    init() {
      this.$nextTick(() => {
        this.pdList = []
        this.mescroll.triggerDownScroll()
      })
    },
    navtoGo(url, obj) {
      const parameter = obj || {}
      this.$navto.push(url, parameter)
    },
    scrollInit(scroll) {
      scroll.optUp.page.num = 1
      scroll.optUp.page.size = 10
      this.mescroll = scroll
    },
    returnFn(obj) {
      const that = this
      function queryPage(pageNum, pageSize, fn) {
        const param = {
          current: pageNum,
          size: pageSize,
          condition: {
          }
        }
        // that.$api.common.platformmessageitemQueryPage(param).then(res => {
        //   if (res && res.data.records) {
        //     fn(res.data.records)
        //   }
        // })
        fn([])
      }
      setTimeout(function() {
        queryPage(obj.pageNum, obj.pageSize, (data) => {
          if (obj.pageNum === 1) {
            that.pdList = []
          }
          that.pdList = that.pdList.concat(data)
          obj.successCallback && obj.successCallback(data || [])
        })
      }, that.$constant.noun.scrollRefreshTime)
    }
  }
}
</script>

<style lang="scss" scoped>
  .main-body{
    height: 100%;
    background: #f7f7f7;
    .scroll-refresh-main{
      height: 100%;
      .mian{
        padding: 30upx;
        .list{
          background-color: #fff;
          @include rounded(20upx);
          margin-bottom: 30upx;
          .list-body{
            padding: 30upx;
            position: relative;
            overflow: hidden;
            .t{
              margin-bottom: 30upx;
              .l{
                vertical-align: middle;
                display: inline-block;
                width: 90upx;
                height: 90upx;
                overflow: hidden;
                @include rounded(50%);
                margin-right: 20upx;
                overflow: hidden;
                image{
                  width: 100%;
                  height: 100%;
                }
              }
              .m{
                display: inline-block;
                width: calc(100% - 290upx);
                color: #666666;
                font-size: 32upx;
                line-height: 48upx;
                @include ellipsis(1);
                vertical-align: middle;
              }
              .r{
                vertical-align: middle;
                margin-left: 20upx;
                display: inline-block;
                width: 160upx;
                font-size: 24upx;
                line-height: 36upx;
                color: #999;
                text-align: right;
                @include ellipsis(1);
              }
            }
            .m{
              .title{
                color: #333;
                font-size: 32upx;
                line-height: 48upx;
                font-weight:500;
                @include ellipsis(1);
                display: block;
              }
              .content{
                font-size: 28upx;
                line-height: 42upx;
                color: #666;
                @include ellipsis(3);
                display: block;
              }
            }
            .b{
              margin-top: 20upx;
              width: 100%;
              height: 200upx;
              @include rounded(20upx);
              overflow: hidden;
              image{
                width: 100%;
                height: 100%;
              }
            }
            .offiCialone{
              position: absolute;
              top: 12upx;
              right: 12upx;
              width:18upx;
              height:18upx;
              background: #FF4A4A;
              @include rounded(50%);
              overflow: hidden;
            }
          }
        }
      }
    }
  }
</style>
