<template>
  <view class="title-input clear-float" :class="{'bdt':defaultConfig.bdt,'bdb':defaultConfig.bdb && !child}">
    <view class="l-l" :class="defaultConfig.titleClass" :style="{'color': defaultConfig.titleColor}">
      {{defaultConfig.label}}
      <!-- <text class="star" v-if="defaultConfig.required">*</text> -->
    </view>

<view class="l-r">
  <!-- 我是内容 -->
  <template v-for="(item, index) in defaultConfig.config.config">
    <!--<slot v-if="item.slot" :name="item.name"></slot>-->
    <!-- <view> -->
      <!--类型：1-单选题(radio)，2-多选题(checkbox)，3-文本题，4-数值题(slider)，5-文件上传(file)，6-共用题干题（嵌套题），7-手机号码，8-邮寄地址-->
      <!--文本题类型：1-单行输入框(input)，2-日期，3-时间，4-日期时间，5-多行输入框(textarea)-->
      <template v-if="item.type === 'radio'">
        <title-radio :child='true' :disabled="item.disabled" :config="item" :cData="defaultConfig.config.regForm[item.name]"
                     @updateForm="updateForm"></title-radio>
      </template>
      <template v-if="item.type === 'checkbox'">
        <title-checkbox :child='true' :disabled="item.disabled" :config="item" :cData="defaultConfig.config.regForm[item.name]"
                        @updateForm="updateForm"></title-checkbox>
      </template>
      <template v-if="item.type === 'slider'">
        <title-slider :child='true' :disabled="item.disabled" :config="item" :cData="defaultConfig.config.regForm[item.name]"
                      @updateForm="updateForm"></title-slider>
      </template>
      <template v-if="item.type === 'file'">
        <title-lvfile :child='true' :config="item" :attachmentList="defaultConfig.config.regForm[item.name]"
                     @updateForm="updateForm" @returnFn="imgReturnFn">

        </title-lvfile>
            <!-- <title-file :config="{padding: 0, background: 'none'}" :disabled="true" ></title-file> -->
      </template>
      <template v-if="item.type === 'input'">
        <title-input :child='true' :disabled="item.disabled" :config="item" :cData="defaultConfig.config.regForm[item.name]"
                     @updateForm="updateForm"></title-input>
      </template>
      <template v-if="item.type === 'textarea'">
        <title-textarea :child='true' :disabled="item.disabled" :config="item" :cData="defaultConfig.config.regForm[item.name]"
                        @updateForm="updateForm"></title-textarea>
      </template>
      <template v-if="item.type === 'phone'">
        <title-phone :child='true' :disabled="item.disabled" :config="item" :cData="defaultConfig.config.regForm[item.name]"
                     @updateForm="updateForm"></title-phone>
      </template>
      <template v-if="item.type === 'address'">
        <title-address :child='true' :disabled="item.disabled" :config="item" :cData="defaultConfig.config.regForm[item.name]"
                        @updateForm="updateForm"></title-address>
      </template>
       <template v-if="item.type === 'lvSlider'">
         <!-- lvSlider -->
          <title-lvsliver :child='true'  :config='item' :cdata="defaultConfig.config.regForm[item.name]" :onlykey='item.name' @update='updateValue'></title-lvsliver>
        </template>

        <template v-if="item.type === 'rate'">
             <!-- 667 -->
             <!-- <title-input ></title-input> -->
             <title-rate :disabled="item.disabled" :config="item"  :cData="defaultConfig.config.regForm[item.name]" @updateForm="updateForm"></title-rate>
            <!-- <uni-rate :margin="8" :size="36" :value="5" :readonly='item.readonly' /> -->
        </template>
        <template v-if='item.type === "pointFunctionName"'>
          <title-name :child="true"  :disabled="item.disabled" :config="item" :cData="defaultConfig.config.regForm[item.name]" @updateForm="updateForm"></title-name>
        </template>
        <!-- </view> -->
        </template>
  </view>

  </view>
</template>


  <script>
    import TitleRadio from '@/modules/activity/components/title-radio/index'
    import TitleCheckbox from '@/modules/activity/components/title-checkbox/index'
    import TitleInput from "@/modules/activity/components/title-input/index";
    import TitleTextarea from "@/modules/activity/components/title-textarea/index";
    import TitleSlider from "@/modules/activity/components/title-slider/index";
    import TitleFile from "@/modules/activity/components/title-file/index";
    import TitlePhone from "@/modules/activity/components/title-phone/index";
    import TitleAddress from "@/modules/activity/components/title-address/index";
    import titleLvsliver from '@/modules/activity/components/title-lvsliver/index.vue'
    import titleLvfile from '@/modules/activity/components/title-lvfile/index.vue'
    import titleRate from '@/modules/activity/components/title-rate/index.vue';
    import titleName from '@/modules/activity/components/title-name/index.vue'

    export default {
      name:"titleLvquestion",
      components: {
        TitleInput,
        TitleRadio,
        TitleCheckbox,
        TitleTextarea,
        TitleSlider,
        TitleFile,
        TitlePhone,
        TitleAddress,
        titleLvsliver,
        titleLvfile,
        titleRate,
        titleName
      },
      props:{
        // 初始值传值，用于回显
        cdata: {
          type: [String, Number],
          required: false,
          default() {
            return ''
          }
        },
        // 参数设置
        config: {
          type: Object,
          required: false,
          default: () => {
            return {}
          }
        },
        onlykey:{
          type:[String,Number,Object],
        }
      },
      data(){
        return {
        spaceTemp:[],
        lvdescrection:"",
        cvalue:"",
        // form: {
        //   data: {
        //     val: ''
        //   }
        // },
        // array: [],
        index: 0,
        defaultConfig: {
          bdt: false,
          bdb: true,
          titleColor: '#333',
          textColor: '#333',
          label: '单行输入框',
          name: 'input',
          required: false,
          config:{
            config:{

            },
            regForm:{

            }
          }
        },
        // step:1,
        // max:100,
        }
      },
      mounted(){
          this.copyConfig();
      },
      watch:{
        config: {
          handler(val) {
            this.copyConfig()
          },
          deep: true
        },
        cdata(n){
          this.cvalue = cdata;
        },
      },
      methods:{

        /**
         * 上传图片
         * @param v
         */
        imgReturnFn(v) {
          console.log(v)
          console.log('v----')
          this.$emit('returnFn',{
            arr:v.arr,
            name:v.name,
            onlykey:this.onlykey,
            child:true,

          })
          // this.regForm.attachmentList = v
        },
        updateValue(obj){
            this.$emit('updateValue',{
              ...obj,
              child:true,
              onlykey2:this.onlykey,
            })
            // this.$emit('')
        },
        updateForm(obj){
          this.$emit('updateForm',{
            ...obj,
            lvchild:true,
            onlykey:this.onlykey,

          })
        },
      /**
         * 初始化拷贝config对象
         */
      copyConfig() {
        const that = this
        const obj = that.config

        Object.keys(obj).forEach(function(key) {
          that.defaultConfig[key] = obj[key]
        })
        console.log("that.defaultConfig")
        console.log(that.defaultConfig)
      },
      },
    }
</script>

<style lang="scss" scoped>
 .title-input{
    background-color:#ffffff;
    .l-l{
      line-height: 88upx;
      color: #333333;
      font-weight: 600;
      font-size:30upx;
    }
    .l-r{
      margin-bottom: 5px;
      padding: 20upx;
      // border: 1rpx solid #dbdbdb;
      // input {
      //   height: 80upx;
      //   line-height: 80upx;
      //   color: #333;
      //   font-size: 32upx;
      //   text-align: left;
      //   display: inline-block;
      //   vertical-align: middle;
      //   width: calc(100% - 64upx);
      //   border: 2upx solid #ede9e9;
      //   border-radius: 10upx;
      // }
    }
  }
  .star{
    color: #F85E4C;
    padding-top: 6upx;
    font-size: 32upx;
    display: inline-block;
    margin-left: 10rpx;
  }
</style>
