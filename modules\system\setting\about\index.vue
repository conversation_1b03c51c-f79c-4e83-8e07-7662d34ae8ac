<template>
  <page>
    <view slot="content" class="main-body">
      <scroll-view scroll-y="true" class="main">
        <view class="m-main">
          <view class="img-view">
            <view class="img-logo-view"></view>
            <view class="img-logo-text">基本产品介绍</view>
            <view class="img-logo-text">让医院环保与便民同行</view>
          </view>
          <view class="list m-t-20" @tap="helpCenter">
            <view class="title">产品官网</view>
            <view class="icon">
              <image class="width-height-atuo" :src="$static_ctx + 'image/business/icon-more.png'"/>
            </view>
          </view>
          <view class="list m-t-20" @tap="navtoGo('SecrecyPolicy')">
            <view class="title">隐私声明</view>
            <view class="icon">
              <image class="width-height-atuo" :src="$static_ctx + 'image/business/icon-more.png'"/>
            </view>
          </view>
          <view class="list m-t-20" @tap="navtoGo('UserAgreement')">
            <view class="title">用户服务协议</view>
            <view class="icon">
              <image class="width-height-atuo" :src="$static_ctx + 'image/business/icon-more.png'"/>
            </view>
          </view>
          <view class="list m-t-20" @tap="navtoGo('PlatformAgreement')">
            <view class="title">平台服务协议</view>
            <view class="icon">
              <image class="width-height-atuo" :src="$static_ctx + 'image/business/icon-more.png'"/>
            </view>
          </view>
          <view class="list m-t-20" @tap="phoneOpen()">
            <view class="title">联系我们</view>
            <view class="icon">
              <image class="width-height-atuo" :src="$static_ctx + 'image/business/icon-more.png'"/>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
  </page>
</template>

<script>
import serverOptions from '@/config/env/options'
export default {
  components: {

  },
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      version_ctx: this.version_ctx,
      $constant: this.$constant,
      $common: this.$common,
      $accurateConversion: this.$accurateConversion,
      file_ctx: this.file_ctx,
      $static_ctx: this.$static_ctx,
      $timePlugin: this.$timePlugin,
      $validate: this.$validate
    }
  },
  methods: {
    navtoGo(url = '', obj = {}) {
      this.$navto.push(url, obj)
    },
    helpCenter() {
      this.$navto.push('WebHtmlView', { src: 'http://www.greenbon.cn/', title: serverOptions.title })
    },
    phoneOpen() {
      const that = this
      uni.makePhoneCall({
        // 手机号
        phoneNumber: '020-88520820',

        // 成功回调
        success: (res) => {
          // console.log('调用成功!')
        },

        // 失败回调
        fail: (res) => {
          // console.log('调用失败!')
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  .main-body{
    height: 100%;
    .main{
      height: 100%;
    }
  }
  .m-main {
    overflow: hidden;
    background-color: #f7f7f7;
    .img-view {
      padding: 30upx;
      .img-logo-view {
        @include iconImg(300,160,'/system/logo/icon-logo.png');
        display: block;
        margin: 0 auto;
        @include rounded(20upx);
      }
      .img-logo-text {
        font-size: 24upx;
        line-height: 36upx;
        color: #666;
        text-align: center;
      }
    }
    .list{
      background-color: #fff;
      padding: 0 30upx;
      height: 88upx;
      .title{
        display: inline-block;
        float: left;
        height: 88upx;
        line-height: 88upx;
        font-size: 32upx;
        color: #333;
      }
      .text{
        display: inline-block;
        float: right;
        height: 88upx;
        line-height: 88upx;
        font-size: 30upx;
        color: #999999;
        margin-right: 20upx;
      }
      .icon{
        display: inline-block;
        float: right;
        height: 44upx;
        width: 44upx;
        margin-top: 22upx;
      }
    }
  }
  .width-height-atuo{
    width: 100%;
    height: 100%;
  }
  .m-t-20{
    margin-top: 20upx;
  }
  .p-t-20{
    padding-top: 20upx;
  }
</style>
