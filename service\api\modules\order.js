import request from '@/common/util/request'
import env from '@/config/env'

/**
 * 系统请求工具类，
 * 注意：API接口只做请求数据和格式化数据的事情，其他事务一概不处理
 */
export default {
    /**
     * 订单-极速资讯
     * @param resolve
     * @param reject
     */
    orderInitiatorUserCheck (param) {
        const url = env.ctx + 'im/api/v1/order/initiator/user/check'
        return request.get(url, param)
    },
    // 创建新咨询订单（上次咨询已结束）
    orderEndInitiatorUserCheck (param) {
        const url = env.ctx + 'im/api/v1/order/again/create'
        return request.postJson(url, param)
    }
}
