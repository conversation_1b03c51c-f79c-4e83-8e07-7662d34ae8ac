import common from './modules/common.js'
import lights from './modules/lights.js'
import user from './modules/user'
import sys from './modules/sys'
import pay from './modules/pay'
import wechat from './modules/wechat'
import information from './modules/information'
import record from './modules/record'
import patientinfo from './modules/patientinfo'
import chat from './modules/chat'
import order from './modules/order'
import community from './modules/community.js'
import postmessage from './modules/postmessage'
import circleclassify from './modules/circleclassify'
import wishingwell from './modules/wishingwell'
import packet from './modules/packet'
import hospital from './modules/hospital'
import drugBook from './modules/drugBook'

import cloudClassroom from './modules/cloudClassroom.js'
import activity from './modules/activity.js'
import medicineclassify from './modules/medicineclassify'
import redpacket from './modules/redpacket'
import dll from './modules/dll'
import accompanyDoctor from './modules/accompanyDoctor'
import distribution from './modules/distribution'

const api = {
  common,
  lights,
  pay,
  user,
  sys,
  wechat,
  information,
  record,
  patientinfo,
  chat,
  order,
  community,
  postmessage,
  circleclassify,
  wishingwell,
  packet,
  cloudClassroom,
  activity,
  hospital,
  medicineclassify,
  drugBook,
  redpacket,
  dll,
  accompanyDoctor,
  distribution
}
console.log('apiapiapi',api);
export default api
