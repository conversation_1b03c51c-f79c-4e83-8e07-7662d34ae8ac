<template>
  <page>
    <view slot="content" class="page-content">
      <m-nav-bar title="分销记录" :bg-overflow="false" />
      <view style="padding: 32rpx 32rpx 32rpx">
        <search :fixed="true" top="88" v-model="regForm.nickName" placeholder="搜索" :isJumpSearch="false" @search="changeSearch" :isSearchBtn="false"></search>
      </view>
      <view style="flex: 1;">
        <scroll-refresh
          :fixed="false"
          :isAbsolute="false"
          :up="upOption"
          :down="downOption"
          bgColor="transparent"
          @returnFn="returnFn"
          @scrollInit="scrollInit"
        >
          <view class="list-header">
            <view class="total-num">
              总分佣：
              <text class="active">¥{{ distributionrecordSum || 0 }}</text>
            </view>
            <filter-dropdown wrapperStyle="padding:0" :config="filterConfig" @change="filterChange" />
          </view>
          <view class="content">
            <log-list :list="indexlist" />
          </view>
        </scroll-refresh>
      </view>
    </view>
  </page>
</template>

<script>
import { mapState } from 'vuex'
import { isDomainUrl } from '@/utils/index'
import { getDistributionType } from '@/utils/enumeration'
import search from '@/components/basics/search-v1/index.vue'
import filterDropdown from '@/components/basics/filter-dropdown/index.vue'
import logList from '@/modules/distribution/components/log-list.vue'
const distributionTypeList = getDistributionType().filter(item => [2, 4].includes(item.value))
export default {
  components: {
    search,
    filterDropdown,
    logList
  },
  data() {
    return {
      file_ctx: this.file_ctx,
      mescroll: null, // mescroll实例对象
      downOption: {
        auto: false // 不自动加载
      },
      upOption: {
        auto: false, // 不自动加载
        empty: {
          top: 0,
          zIndex: 999,
        }
      },
      isInit: false, // 列表是否已经初始化
      indexlist: [],
      regForm: {
        name: ''
      },
      filterConfig: {
        type: {
          keyName:'type',
          height:'608rpx',
          determine:false,
          type: 'selector',
          range: [
            { label: '全部', value: '' },
            ...distributionTypeList
          ],
          rangeKey: 'label',
          rangeValueKey: 'value',
          placeholder: '全部',
          tabTitle:'筛选',
          style: ''
        }
      },
      filterData: {},
      distributionrecordSum: 0
    }
  },
  computed: {
    ...mapState('user', {
      codeUserInfo: state => state.codeUserInfo, // 当前登录用户信息
      curSelectUserInfo: state => state.curSelectUserInfo, // 当前选中的用户信息
      curSelectUserInfoId: state => state.curSelectUserInfoId, // 当前选中的档案的ID
      recordUserInfo: state => state.recordUserInfo,
      accountId: state => state.accountId
    })
  },
  onLoad() {
    this.init()
  },
  methods: {
    async getDistributionrecordSum() {
      const res = await this.$api.distribution.distributionrecordQueryMySalesRecordSum({})
      this.distributionrecordSum = res.data
    },
    filterChange(e) {
      if(e.keyName) {
        this.filterConfig[e.keyName].placeholder = e.label
        let o = {};
        o[e.keyName] = e.id
        e = o;
      }
      this.filterData = {...this.filterData,...e}
      this.mMainScrollObj.triggerDownScroll()
    },
    changeSearch() {
      this.mescroll.optUp.page.num = 1
      this.mescroll.optUp.page.size = 7
      this.mescroll.triggerDownScroll()
    },
    navtoGo(url, obj = {}) {
      this.$navto.push(url, obj)
    },
    init() {
      this.getDistributionrecordSum()
      this.$nextTick(() => {
        this.mescroll.triggerDownScroll()
      })
    },
    scrollInit(scroll) {
      scroll.optUp.page.num = 1
      scroll.optUp.page.size = 10
      this.mescroll = scroll
    },
    returnFn(obj) {
      const that = this
      const { centerUserId = '' } = this.curSelectUserInfo || {}
      setTimeout(function () {
        const params = {
          current: obj.pageNum,
          size: obj.pageSize,
          condition: {
            ...that.filterData, userId: centerUserId, name: that.regForm.name
          }
        }
        that.$ext.distribution.distributionrecordQueryPage(params).then(res => {
          let data = res.data.records || []
          data = data.map(item => {
            return {
              ...item,
              headPath: isDomainUrl(item.headPath),
              typeText: that.$common.getEnumText(item.type, distributionTypeList)
            }
          })
          if (obj.pageNum === 1) {
            that.indexlist = []
          }
          that.indexlist = [...that.indexlist, ...data]
          obj.successCallback && obj.successCallback(data)
        })
      }, that.$constant.noun.scrollRefreshTime)

    },
  }
}
</script>

<style lang="scss" scoped>
.page-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.list-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx 24rpx;
}

.total-num {
  font-weight: 500;
  font-size: 30rpx;
  color: #000000;
  line-height: 42rpx;
  .active {
    color: $topicC;
  }
}

.content {
  background-color: #FFFFFF;
  padding: 0 32rpx;
}

::v-deep .search .input {
  border: none;
}
</style>
