<template>
  <view class="page">
    <!-- 搜索框 -->
    <view class="accompany-content" v-if="navCurrent == 0">
      <view :style="'height:' + statusBarHeight + 'px;'"></view>
      <view class="top-nav">
        <view class="top-nav-c">{{serverOptions.title}}</view>
      </view>
      <search :fixed="true" top="88" placeholder="搜索帖子、服务、医院" v-model="search" @handleCityFn="$refs.guardDetailPopup.open()">
        <template #cityName>{{cityName}}</template>
      </search>

      <!-- banner部分 -->
      <view class="banner">
        <swiper class="swiper" circular :indicator-dots="true" :autoplay="true" :interval="5000"
          :duration="500" indicator-active-color="#00B484">
          <swiper-item class="swiper-item" v-for="item in carouselList" :key="item.id" @tap="$navto.pushPath(item.skipUrl)">
            <view class="item"><image class="img" :src="item.image"></image></view>
          </swiper-item>
        </swiper>
      </view>

      <!-- 专业陪诊部分 -->
      <view class="specialty-accompany" :style="{backgroundImage:'url(' + file_ctx + 'static/image/business/accompany-doctor/icon-accompany-doctor-specialty.png' + ')','background-repeat':'no-repeat','background-size': '100%'}">
        <view class="accompany-head">
          <view class="accompany-head-l">
            <view class="teacher"><image class="img" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-doctor-specialty-teacher.png'"></image></view>
            <view class="info">
              <view class="info-t"><image class="img" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-doctor-specialty-text.png'"></image></view>
              <view class="info-b">1000+陪诊师</view>
            </view>
          </view>
          <view class="accompany-head-r" @tap="gotoOrder(accompanyMap[0])">
            <button>立即预约</button>
          </view>
        </view>
        <view class="accompany-bott">
          <view class="accompany-bott-item" @tap="gotoOrder(item)" v-for="(item,index) in accompanyMap" :key="item.id" :style="{backgroundImage:'url(' + file_ctx + 'static/image/business/accompany-doctor/icon-accompany-doctor-era-examine-bg'+ (index+1) +'.png' + ')','background-repeat':'no-repeat','background-size': '100%'}">
            <view class="title">{{item.serviceName}}</view>
            <view class="text">{{item.description}}</view>
          </view>
        </view>
      </view>

      <!-- 服务项目部分 -->
      <view class="service-project" v-if="serviceList.length">
        <view class="service-project-head">
          <view class="service-project-head-l">服务项目</view>
          <view class="service-project-head-r" @tap="handleServiceMore" v-if="serviceList.length > 1">更多<image class="head-r-img" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-service-right-arrows.png'"></image></view>
        </view>
        <view class="service-project-item" v-for="item in serviceList" :key="item.id" @tap="handleJumpService(item.id)">
          <view class="project-item-l"><image class="img" :src="file_ctx + item.listImg"></image></view>
          <view class="project-item-r">
            <view class="project-item-r-title">{{ item.comboName }}</view>
            <view class="project-item-r-info">{{ item.comboDesc }}</view>
            <view class="project-item-r-box" v-if="item.tag && item.tag.split(',').length > 1">
              <view class="project-item-r-tag" v-for="(item2,index) in item.tag.split(',')" :key="index">{{ item2 }}</view>
            </view>
            <view class="project-item-r-tag" v-else-if="item.tag && item.tag.split(',').length == 1">{{ item.tag }}</view>
            <view class="project-item-r-money">¥<span>{{ item.price / 100 }}</span></view>
          </view>
        </view>
      </view>

      <!-- 了解陪诊师部分 -->
      <view class="understand-accompany">
        <view class="service-project-head">
          <view class="service-project-head-l">了解陪诊师</view>
          <view class="service-project-head-r" v-if="postList.length" @tap="$navto.push('accompanyTeacher')">更多<image class="head-r-img" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-service-right-arrows.png'"></image></view>
        </view>
        <template v-if="postList.length">
          <view class="service-project-item" v-for="item in postList" :key="item.id" @tap="$navto.push('PostsDetail', {id: item.id,isShowBtn:false})">
            <view class="accompany-item-l">
              <view class="title">{{ item.title }}</view>
              <view class="user-box">
                <view class="user-profile"><image class="img" :src="file_ctx + item.headPath"></image></view>
                <view class="user-name">{{ item.nickName }}</view>
              </view>
            </view>
            <view class="accompany-item-r" v-if="item.imagesPath"><image class="img" :src="(item.imagesPath.split(',')[0])"></image></view>
          </view>
        </template>
        <view class="empty" v-else>
          <view class="empty-img"><image class="img" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-empty.png'"></image></view>
          暂无数据~
        </view>
      </view>

      <!-- 热门医院部分 -->
      <view class="service-project" style="margin:20rpx 0rpx 88rpx;">
        <view class="service-project-head">
          <view class="service-project-head-l">热门医院</view>
          <view class="service-project-head-r" v-if="hospitalList.length > 1" @tap="$navto.push('HospitalRanking',{isShow:true,cityName:cityName})">更多<image class="head-r-img" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-service-right-arrows.png'"></image></view>
        </view>
        <template v-if="hospitalList.length">
          <view class="service-project-item" v-for="item in hospitalList" :key="item.id" @tap="$navto.push('HospitalDetail',{id:item.id})">
            <view class="project-item-l" style="border-radius:50%"><image class="img" mode="aspectFit" :src="item.logo"></image></view>
            <view class="project-item-r">
              <view class="project-item-r-title">{{ item.hospitalName }}</view>
              <view class="project-item-r-box">
                <view class="project-item-r-hospital" v-if="item.type">{{ item.type }}</view>
                <view v-if="item.level && toString(item.level).split(',').length > 1">
                  <view class="project-item-r-hospital" v-for="(item2,index) in toString(item.level).split(',')" :key="index">{{ item2 }}</view>
                </view>
                <view v-else-if="item.level && toString(item.level).split(',').length == 1" class="project-item-r-hospital">{{ item.level }}</view>
              </view>
              <view class="project-item-r-address">
                <view class="info-img"><image class="img" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-hospital-address.png'"></image></view>
                <view class="address-text">{{ item.address }}</view>
              </view>
            </view>
          </view>
        </template>
        <view class="empty" v-else>
          <view class="empty-img"><image class="img" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-empty.png'"></image></view>
          暂未录入~
        </view>
      </view>
    </view>

    <!-- 服务页面 -->
    <ServiceIndex v-if="navCurrent == 1" :serviceCurrent="serviceCurrent" />

    <!-- 订单页面 -->
    <OrderIndex v-if="navCurrent == 2" />

    <!-- 我的页面 -->
    <MyIndex v-if="navCurrent == 3" />

    <!-- 底部部分 -->
    <view class="accompany-bottom">
      <view class="bottom-item" v-for="(item,index) in navList" :key="index" @tap="handletapJump(index)">
        <view class="bottom-item-img" v-if="navCurrent == index"><image class="img" :src="file_ctx + item.activeUrl"></image></view>
        <view class="bottom-item-img" v-else><image class="img" :src="file_ctx + item.url"></image></view>
        <view :class="navCurrent == index ? 'bottom-item-name active' : 'bottom-item-name'">{{item.name}}</view>
      </view>
    </view>

    <!-- 城市套餐 -->
    <uni-popup ref="guardDetailPopup" type="bottom">
      <view class="guard-detail-content">
        <view class="title">切换城市</view>
        <view class="error" @tap="$refs.guardDetailPopup.close()"><image :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-service-error.png'"></image></view>
        <view class="city-tilte">已开通服务的城市</view>
        <view class="city-list">
          <view class="city-item" v-for="(item,index) in accompanyproviderList" :key="index" @tap="handleChangeCity(index)">{{item.city}}</view>
        </view>
        <view class="info">其他城市详请期待…</view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  import serverOptions from '@/config/env/options'
  import { mapState } from 'vuex'
  // import uniNavBar from '@/components/uni/uni-nav-bar/uni-nav-bar.vue'
  import search from '../components/search'
  // import UniIcons from '@/components/uni/uni-icons/uni-icons'
  import uniPopup from '@/components/uni/uni-popup'
  // import ServiceIndex from './components/service/index'
  // import OrderIndex from './components/order/index'
  // import MyIndex from './components/my/index'
  import { isDomainUrl } from '@/utils/index.js'
  export default {
    components: {
      // uniNavBar,
      search,
      // UniIcons,
      uniPopup,
      // ServiceIndex,
      // OrderIndex,
      // MyIndex,
    },
    data(){
      return {
        serverOptions,
        file_ctx: this.file_ctx,
        statusBarHeight: 0,
        navList:[
          {name:'首页',url:'static/image/business/accompany-doctor/icon-home.png',activeUrl:'static/image/business/accompany-doctor/icon-home-active.png'},
          {name:'服务',url:'static/image/business/accompany-doctor/icon-accompany-bottom-service.png',activeUrl:'static/image/business/accompany-doctor/icon-accompany-bottom-service-active.png'},
          {name:'订单',routerName:'accompanyOrder',url:'static/image/business/accompany-doctor/icon-accompany-bottom-order.png',activeUrl:'static/image/business/accompany-doctor/icon-accompany-bottom-order-active.png'},
          {name:'我的',url:'static/image/business/accompany-doctor/icon-my.png',activeUrl:'static/image/business/accompany-doctor/icon-my-active.png'}
        ],
        search:'',
        cityName:'广州',
        serviceList:[],
        postList:[],
        hospitalList:[],
        navCurrent:0,
        accompanyMap:[],
        accompanyproviderList:[],
        accompanyproviderId:null,
        carouselList:[],
        serviceCurrent:0,
        providerId:'',
      }
    },
    mounted() {

    },
    computed: {
      ...mapState('user', {
        accountId: state => state.accountId
      }),
    },
    async onLoad(){
      const userId = serverOptions.getUserId(this);
      if(!userId){
        this.$uniPlugin.toast('请先登录')
        return
      } else {
        let {data:id} = await this.$api.accompanyDoctor.accompanyproviderUserProvider({userId})
        this.providerId = id;
        this.$common.setKeyVal('user','providerId',this.providerId,true)
        let {data:{records}} = await this.$api.accompanyDoctor.getAccompanyservicePage({current:0,size:4,condition:{providerId:this.providerId}})
        this.accompanyMap = records
        this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight // 获取设备的状态栏高度
        this.accompanyproviderQueryPage()
        this.accompanycomboQueryPage()
        this.bannerQueryPage()
      }
      // this.hospitalQueryPage()
    },

    methods:{
      handleJumpService(id){
        this.$navto.push('comboDetail',{id})
      },
      handleBack(){
        this.$navto.back(1)
      },
      handleServiceMore(){
        // this.$navto.push('ServiceIndex')
        this.navCurrent = 1
        this.serviceCurrent = 2
      },
      handletapJump(index){
        this.navCurrent = index
      },
      gotoOrder(options){
        this.$navto.push('serviceReservation',{id:options?.id});
      },
      handleChangeCity(index){
        this.cityName = this.accompanyproviderList[index].city
        this.accompanyproviderId = this.accompanyproviderList[index].id
        this.postmessageQueryRecommendPage(this.accompanyproviderId)
        this.hospitalQueryPage()
        this.$refs.guardDetailPopup.close()
      },
      handleTip(){
        this.$uniPlugin.modal('提示', '您当前的城市未开通陪诊服务，已为你切换就?', {
          showCancel: false, // 是否显示取消按钮，默认为 true
          cancelText: '取消', //  取消按钮的文字，默认为"取消"，最多 4 个字符
          cancelColor: '#00B484', //  取消按钮的文字颜色，默认为"#000000"(H5、微信小程序、百度小程序)
          confirmText: '我知道了', //  确定按钮的文字，默认为"确定"，最多 4 个字符
          fn: async (n) => {
            // resolve(n)
            if(n){
            }
          },
        })
      },
      // 套餐列表
      accompanycomboQueryPage(){
        this.$api.accompanyDoctor.accompanycomboQueryPage({current:1,size:2,condition:{providerId:this.providerId}}).then(res=>{
          this.serviceList = res.data.records
        })
      },

      // 首页弹窗
      bannerQueryPage(){
        let params = {
          current:1,
          size:10,
          condition:{
            openStatus: 1,
            useType: 8,
            useTypes: [1, 5, 6,8],
            providerId:this.providerId
          }
        }
        this.$api.drugBook.bannerQueryPage(params).then(res=>{
          this.carouselList = res.data.records.map(item=>({...item,image:isDomainUrl(item.image)}))
        })
      },

      // 服务商列表
      accompanyproviderQueryPage(){
        this.$api.accompanyDoctor.accompanyproviderQueryPage({current:1,size:20,condition:{providerId:this.providerId}}).then(res=>{
          let data = res.data.records.map(item=>({city:item.city.substr(0,2),id:item.id}))
          let seenNames = {}
          let accompanyproviderId = null
          data.forEach(item => {
            if(item.city == '广州'){
              accompanyproviderId = item.id
            }
            if (!seenNames[item.city]) {
              seenNames[item.city] = true
              this.accompanyproviderList.push(item)
            }
          })
          this.postmessageQueryPage(accompanyproviderId)
          this.hospitalQueryPage()
        })
      },

      // 推荐帖子列表
      postmessageQueryPage(id){
        const param = {
          current: 1,
          size: 2,
          condition: {
            accountId: this.accountId,
            entryType: 5,
            providerId:this.providerId
          }
        }
        this.$ext.community.postmessageQueryRecommendPage(param).then(res=>{
        // this.$ext.community.postmessageQueryPage(param).then(res=>{
          this.postList = res.data.records.slice(0, 2)
        })
      },

      // 医院列表
      hospitalQueryPage(){
        let params = {
          current: 1,
          size: 2,
          ascs:'top',
          condition: {
            city: this.cityName,
          }
        }
        this.$api.hospital.hospitalQueryPage(params).then(res => {
          this.hospitalList = res.data.records.map(item=>({...item,logo:isDomainUrl(item.logo)})) || []
        })
      },
    },
  }
</script>

<style lang="scss">
  .img{
    width: 100%;
    height: 100%;
  }
  .page{
    position: fixed;
    width: 100vw;
    height: 100vh;
    background: #F4F6FA;
    // padding: 20rpx 0;
    box-sizing: border-box;
    overflow-y: auto;
    overflow-x: hidden;
  }
  .accompany-content{
    padding-bottom: 166rpx;
    margin:0 32rpx;
  }
  .top-nav{
    // position: fixed;
    width: calc(100% - 16rpx);
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 40px;
    line-height: 40px;
    // padding: 0 16rpx;
    // z-index: 999;
    // padding: 0 32rpx 0 24rpx;
    .top-nav-l{
      display: flex;
      width: 48rpx;
      height: 48rpx;
      image{
        width: 100%;
        height: 100%;
      }
    }
    .top-nav-c{
      flex: 1;
      text-align: center;
      height: 44rpx;
      font-weight: 500;
      font-size: 32rpx;
      color: #2D2F38;
      line-height: 44rpx;
    }
  }
  .banner{
    width: 686rpx;
    height: 208rpx;
    // background: #DAE6FF;
    border-radius: 16rpx;
    margin: 32rpx 0;
    .swiper{
      height: 100%;
      border-radius: 16rpx;
      overflow: hidden;
      .swiper-item{
        width: 100%;
        height: 100%;
        .item{
          width: 100%;
          height: 100%;
        }
      }
    }
  }
  .specialty-accompany{
    position: relative;
    width: 686rpx;
    // height: 312rpx;
    padding: 0 24rpx 24rpx;
    box-sizing: border-box;
    background-color: #fff;
    border-radius: 16rpx;
    .accompany-head{
      display: flex;
      height: 150rpx;
      align-items: center;
      justify-content: space-between;
      border-bottom: 2rpx solid #e5e5e5;
      margin-bottom: 24rpx;
      .accompany-head-l,.accompany-head-r{
        display: flex;
        align-items: center;
        .teacher{
          position: absolute;
          top: -10rpx;
          width: 114rpx;
          height: 160rpx;
        }
        .info{
          display: flex;
          flex-direction: column;
          margin-left: 128rpx;
          .info-t{
            width: 144rpx;
            height: 36rpx;
            margin-bottom: 10rpx;
          }
          .info-b{
            font-size: 28rpx;
            color: #1D2029;
            line-height: 40rpx;
          }
        }
      }
      .accompany-head-r{
        button{
          display: flex;
          justify-content: center;
          align-items: center;
          width: 160rpx;
          height: 72rpx;
          background: #00B484;
          box-shadow: 0rpx 0rpx 8rpx 0rpx #FFFFFF;
          border-radius: 36rpx;
          font-weight: 600;
          font-size: 24rpx;
          color: #FFFFFF;
          line-height: 34rpx;
          &::after{
            border: none !important;
          }
        }
      }
    }
    .accompany-bott{
      display: flex;
      flex-wrap: wrap;
      .accompany-bott-item{
        height: 136rpx;
        width: 310rpx;
        margin-right: 18rpx;
        margin-bottom: 18rpx;
        padding: 24rpx;
        box-sizing: border-box;
        .title{
          font-size: 30rpx;
          color: #1D2029;
          line-height: 42rpx;
          font-weight: 600;
        }
        .text{
          margin-top: 2rpx;
          font-size: 22rpx;
          color: #A5AAB8;
          line-height: 32rpx;
        }
        &:nth-child(2n){
          margin-right: 0;
        }
      }
    }
  }
  .service-project,.understand-accompany{
    margin: 20rpx 0;
    padding:24rpx 24rpx 0;
    background: #FFFFFF;
    border-radius: 16rpx;
    .service-project-head{
      display: flex;
      align-items: center;
      justify-content: space-between;
      .service-project-head-l{
        font-size: 32rpx;
        color: #1D2029;
        line-height: 44rpx;
      }
      .service-project-head-r{
        display: flex;
        align-items: center;
        font-size: 24rpx;
        color: #868C9C;
        line-height: 34rpx;
        .head-r-img{
          display: flex;
          width: 32rpx;
          height: 32rpx;
        }
      }
    }
    .service-project-item{
      display: flex;
      padding: 24rpx 0;
      border-bottom: 1rpx solid #EAEBF0;
      width: 100%;
      box-sizing: border-box;
      overflow: hidden;
      .project-item-l{
        width: 144rpx;
        height: 144rpx;
        border-radius: 12rpx;
        // background-color: skyblue;
        margin-right: 20rpx;
        overflow: hidden;
      }
      .project-item-r{
        display: flex;
        flex: 1;
        flex-direction: column;
        .project-item-r-title{
          font-size: 30rpx;
          color: #1D2029;
          line-height: 42rpx;
        }
        .project-item-r-info{
          width: 474rpx;
          font-size: 22rpx;
          color: #868C9C;
          line-height: 32rpx;
          margin: 4rpx 0 10rpx;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .project-item-r-box{
          display: flex;
          .project-item-r-tag{
            padding: 2rpx 8rpx;
            font-size: 20rpx;
            color: #00A277;
            line-height: 28rpx;
            margin-right: 8rpx;
            border-radius: 4rpx;
            border: 1rpx solid rgba(0,180,132,0.4);
            &:last-child{
              margin-right: 0;
            }
          }
          .project-item-r-hospital{
            padding: 2rpx 8rpx;
            margin: 8rpx 0 12rpx;
            background: #FCF0DA;
            border-radius: 4rpx;
            font-size: 20rpx;
            color: #693E13;
            line-height: 28rpx;
            margin-right: 8rpx;
            &:last-child{
              margin-right: 0;
            }
          }
        }
        .project-item-r-address{
          display: flex;
          align-items: center;
          font-size: 22rpx;
          color: #4E5569;
          line-height: 32rpx;
          .info-img{
            display: flex;
            width: 24rpx;
            height: 24rpx;
          }
          .address-text{
            width: 438rpx;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-left: 8rpx;
          }
        }
        .project-item-r-money{
          color: #FF5500;
          span{
            font-size: 36rpx;
            line-height: 50rpx;
          }
        }
      }
      .accompany-item-l{
        width: 474rpx;
        margin-right: 20rpx;
        .title{
          font-size: 30rpx;
          color: #1D2029;
          line-height: 42rpx;
          margin-bottom: 16rpx;
        }
        .user-box{
          display: flex;
          align-items: center;
          .user-profile{
            width: 32rpx;
            height: 32rpx;
            // background-color: purple;
            margin-right: 8rpx;
            border-radius: 50%;
            overflow: hidden;
          }
          .user-name{
            font-size: 22rpx;
            color: #1D2029;
            line-height: 32rpx;
          }
        }
      }
      .accompany-item-r{
        width: 144rpx;
        height: 144rpx;
        border-radius: 12rpx;
        border: 1rpx solid #D9DBE0;
        overflow: hidden;
      }
      &:last-child{
        border-bottom: none;
      }
    }
    .empty{
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 50rpx 0;
      .empty-img{
        width: 286rpx;
        height: 212rpx;
        margin-bottom: 20rpx;
      }
    }
  }
  .accompany-bottom{
    position: fixed;
    width: 100%;
    bottom: 0;
    left:0;
    display: flex;
    z-index: 999;
    // align-items: center;
    justify-content: space-around;
    // height: 98rpx;
    height: 166rpx;
    background-color: #fff;
    .bottom-item{
      display: flex;
      flex-direction: column;
      align-items: center;
      // justify-content: center;
      padding-top: 8rpx;
      .bottom-item-img{
        width: 56rpx;
        height: 56rpx;
        margin-bottom: 4rpx;
      }
      .bottom-item-name{
        font-size: 20rpx;
        color: #868C9C;
      }
      .active{
        color: #00B484;
      }
    }
  }
  .guard-detail-content{
    position: relative;
    background: #fff;
    padding: 32rpx 32rpx 386rpx;
    border-radius: 24rpx 24rpx 0rpx 0rpx;
    .title{
      display: flex;
      justify-content: center;
      font-size: 32rpx;
      color: #1D2029;
      line-height: 44rpx;
    }
    .error{
      position: absolute;
      right: 32rpx;
      top: 38rpx;
      width: 32rpx;
      height: 32rpx;
      image{
        width: 100%;
        height: 100%;
      }
    }
    .city-tilte{
      font-size: 28rpx;
      color: #1D2029;
      line-height: 40rpx;
      margin: 40rpx 0 24rpx;
    }
    .city-list{
      display: flex;
      flex-wrap: wrap;
      .city-item{
        display: flex;
        align-items: center;
        justify-content: center;
        width: 156rpx;
        height: 72rpx;
        background: #F4F6FA;
        border-radius: 12rpx;
        font-size: 26rpx;
        color: #1D2029;
        margin-right: 20rpx;
        margin-bottom: 20rpx;
        &:nth-child(4n){
          margin-right: 0;
        }
      }
    }
    .info{
      font-size: 24rpx;
      color: #868C9C;
      line-height: 34rpx;
      margin-top: 12rpx;
    }
  }
</style>
