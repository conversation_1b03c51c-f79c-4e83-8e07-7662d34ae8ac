<template>
    <uniPopup type="bottom" ref='uniPopup' @change="changeShow">
      <view class="confirm">
        <view class="">请选择服务</view>
        <scroll-view :scroll-top="scrollTop" scroll-y="true" class="scroll-Y" @scrolltolower="upper">
          <view v-for="(item,index) in serverMap" :key="item.id">
            <view class="headerTab" @click="selectServer(item)">
              <image class="serverIcon" :src="file_ctx + item.detailImg" mode=""></image>
              <!-- 服务名称 -->
              <view class="serviceName">
                <view class="">{{item.serviceName}}</view>
                <view class="">
                  <text class="signal">¥</text>
                  <text class="serverNum">{{item.price / 100}}</text><text class="tag">/次起</text>
                  </view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </uniPopup>
</template>

<script>
  import { mapState } from "vuex";
  import uniPopup from '@/components/uni/uni-popup/uni-popup.vue'
  export default{
    components: {
        uniPopup
    },
    props:{
      openFlag:{
        type:Boolean,
        default:false
      }
    },
    watch:{
      openFlag(n){
          if(n){
            this.$refs.uniPopup.open()
          }else{
            this.$refs.uniPopup.close()
          }
      }
    },
    data(){
      return {
        file_ctx:this.file_ctx,
        serverMap:[],
        current:0,
        loadEnd:false
      }
    },
    computed: {
      ...mapState("user", {
        accountId: (state) => state.accountId,
        fansRecord: (state) => state.fansRecord,
      }),
    },
     mounted() {
      this.getServerData();
    },
    methods:{
      changeShow(res){
        !res.show && this.$emit('change',res.show)
      },
      selectServer(options){
        this.$emit('selectServer',options)
        this.$emit('change',false)
      },
      close(){
        this.$emit('change',false)
      },
      upper(){
        console.log('触底');
        if(this.loadEnd) return
        this.current++;
        this.getServerData();
      },
      async getServerData(){
        let queryOptons = {current:this.current,size:10,condition:{}}
        let {data:{records,total}} = await this.$api.accompanyDoctor.getAccompanyservicePage(queryOptons);
        this.serverMap.push(...records);
        if(total <= this.serverMap.length){
          this.loadEnd = true
        }
      }
    }
  }
</script>

<style lang="scss">
.confirm{
  position: fixed;
  bottom: 0;
  width: 750rpx;
  height: 796rpx;
  background: #F4F6FA;
  border-radius: 24rpx 24rpx 0rpx 0rpx;
  padding: 32rpx 32rpx 88rpx 32rpx;
  box-sizing: border-box;
  .headerTab{
    width: 686rpx;
    height: 192rpx;
    background: #FFFFFF;
    border-radius: 16rpx;
    padding: 24rpx;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    .serverIcon{
      width: 144rpx;
      height: 144rpx;
      background: #D8D8D8;
      border-radius: 12rpx;
      border: 1rpx solid #D9DBE0;
      margin-right: 20rpx;
    }
    .serviceName{
      font-weight: 500;
      font-size: 32rpx;
      color: #1D2029;
      .signal{
        font-weight: 400;
        font-size: 22rpx;
        color: #FF5500;
      }
      .serverNum{
        font-weight: 500;
        font-size: 36rpx;
        color: #FF5500;
      }
      .tag{
        font-weight: 400;
        font-size: 20rpx;
        color: #868C9C;
      }
    }
    .changeServer{
      width: 148rpx;
      height: 52rpx;
      background: #FFFFFF;
      border-radius: 36rpx;
      border: 1rpx solid #D9DBE0;
      margin-left: auto;
    }
  }
}
.scroll-Y{
  height: 600rpx;
}
</style>
