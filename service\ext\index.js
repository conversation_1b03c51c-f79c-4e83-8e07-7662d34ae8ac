import user from './modules/user'
import wechat from './modules/wechat'
import pay from './modules/pay'
import utility from './modules/utility'
import common from './modules/common'
import VueWebSocket from './modules/websocket/index'
import chat from './modules/chat'
import systemMenuList from './modules/system-menu-list'
import community from './modules/community'
import circleclassify from './modules/circleclassify'

/**
 * 框架扩展目录，主要放置 数据接口扩展逻辑，针对接口做ETL或者封装，隔离接口
 * @type {{etl: {}}}
 */
const ext = {
    wechat,
    user,
    pay,
    utility,
    common,
    systemMenuList,
    // webSocket: VueWebSocket,
    webSocket: new VueWebSocket(),
    chat,
    community,
    circleclassify
}
export default ext
