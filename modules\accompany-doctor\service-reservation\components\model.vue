<template>
  <view class="">
    <uniPopup :zIndex='99' type="bottom" ref='uniPopup' @change="changeShow">
          <view :class="classMap[modelType]" class="confirm">
            <!-- 标题 -->
            <view class="confirmTitle">
              {{titleMap[modelType]}}
              <view @click="close" class="iconCloseBox">
                <image class="iconClose" v-if="!currentOptions.hiddenClose" @click="close" :src="iconClose" mode=""></image>
              </view>
            </view>
            <!-- 模块分区 -->
            <formList @emitChange='emit' :serverMap='currentOptions' @select='select' @emitInput="emitInput">
              <view :class="{lineHide:modelType !== 'insureInfo'}">
                <insureInfoBox :isLocking="isLocking" @selectItem="selectItem" ref="insureInfoBox" :initialInsuranceNum="insuranceNum"></insureInfoBox>
              </view>
              <view :class="{lineHide:modelType !== 'surrender'}">
                <view class="describe">
                    就医导诊
                    <image class="Check" :src="clearRed" mode=""></image>
                    就医提醒
                    <image class="Check" :src="clearRed" mode=""></image>
                    法律咨询
                    <image class="Check" :src="clearRed" mode=""></image>
                </view>
              </view>
              <view :class="{lineHide:modelType !== 'surrenderCG'}" style="width: 100%;">
                <view class="surrenderCGTitle">支付费用将原路返回</view>
              </view>
              <view :class="{lineHide:modelType !== 'invoiceOpenCG'}" style="width: 100%;">
                <view class="surrenderCGTitle">将于5个工作日完成开票并通过短信发送，请留意短信通知</view>
              </view>
            </formList>

            <!-- 确认按钮 -->
            <view class="buttonMap">
              <view class="notice" v-if="isList && modelType === 'insureInfo'" :class="{shake:isShake}">
                <image
                  class="selectorButton"
                  @click="setIsNotice"
                  :src="isNotice ? iconPostSucess : Ellipse"
                  >
                </image>
                我已阅读并同意
                <text class="specification">
                  <text>【服务须知】</text>
                  <text @click="$navto.push('instruction',{insuranceNum:insuranceNum})">【服务细则】</text>
                  <text @click="$navto.push('instruction',{showText:true})">【信息安全】</text>
                </text>
              </view>
              <view class="secondaryButton" v-if="secondaryButtonMap[modelType]" @click="secondary">{{secondaryButtonMap[modelType].buttonText}}</view>
              <view :class="btnClassMap[modelType]" class="actionsConfirm" @click="trigger">{{buttonTitleMap[modelType]}}</view>
            </view>
            <template v-if="!isList">
              <view v-if="showSelectInsure" class="buttonMap selectInsure">
                <view class="title">
                  <image class="icon" :src='shieldMini'></image>
                  就诊已获保障！
                </view>
              </view>
            </template>
            <template v-else>
              <view v-if="showListSelectInsure" class="buttonMap selectInsure langSelectInsure">
                <view class="title">
                  <image class="icon" :src='shieldMini'></image>
                  就诊已获保障！
                </view>
              </view>
            </template>

          </view>
        </uniPopup>
        <!-- 弹窗模块 -->
        <view class="lineHide">
          <selectData ref="selectHospital" placeholder="输入医院名称，模糊匹配搜索"  :localdata="hospitalQuery" popup-title="请选择就诊医院" @change="onchangeHospital"></selectData>
        </view>
        <view class="lineHide">
          <dataPicker ref="selectData" :localdata="currentData" :popup-title="'请选择'" @change="onchangeMode"></dataPicker>
        </view>
        <view class="lineHide">
          <timePicker ref="selectTime" type="date" @change="changeTime"></timePicker>
        </view>
  </view>
</template>

<script>
  import common from '@/common/util/main'
  import { mapState } from "vuex";
  import uniPopup from '@/components/uni/uni-popup/uni-popup.vue'
  import timePicker from '../../components/uni-datetime-picker/uni-datetime-picker'
  import dataPicker from '../../components/uni-data-picker/uni-data-picker.vue'
  import selectData from '../../components/select-data.vue'
  import formList from './formList.vue'
  import TitleImg from "@/components/business/module/title-img/index.vue"
  import serverOptions from '@/config/env/options'
  import listOptions from './listOptions.js'
  import insureInfoBox from './insureInfo.vue'
  // 验证是否是中文
  function isChinese({value}) {
    var reg = /^[\u4E00-\u9FA5]+$/;
    return reg.test(value);
  }
  // 验证是否是电话号码
  function isPhoneNumber({value}) {
    var reg = /^1[3-9]\d{9}$/;
    return reg.test(value);
  }
  // 验证是否是邮箱
  function isEmail({value}) {
    var reg = /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/;
    return reg.test(value);
  }
  // 验证是否是身份证 只验证身份证号码 如果是别的证件直接忽略返回true
  function isIdCard({value},currentOptions) {
    // let index = currentOptions.findIndex(e=>e.textType === 'certfType');
    // let certfType = currentOptions[index].value
    // if(certfType && certfType !== '01') return true;
    var reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
    return reg.test(value);
  }
  export default{
    components: {
        uniPopup,
        timePicker,
        dataPicker,
        formList,
        TitleImg,
        selectData,
        insureInfoBox
    },
    props:{
      openFlag:{
        type:Boolean,
        default:false
      },
      modelType:{
        type:String,
        default:''
      },
      orderDetails:{
        type:Object,
        default:{}
      },
      loadProductCodeFlag:{
        type:Boolean,
        default:false
      },
      underInfo:{
        type:Object,
        default:null
      },
      isLocking:{
        type:Boolean,
        default:false
      },
      isList:{
        type:Boolean,
        default:false
      },
      productCode:{
        type:String,
        default:''
      },
      modelOptions:{
        type:Object,
        default:() => ({})
      }
    },
    watch:{
      loadProductCodeFlag(newVal,oldVal) {
          if(this.loadProductCodeFlag){
            console.log('触发前调');

            this.$emit('getProductCode',this.$refs.insureInfoBox.getProductCode())
          }
        },
      async openFlag(n){
          if(n){
            this.$refs.uniPopup.open();
            if(this.modelType !== 'under') return
            this.currentOptions.map(({textType:key})=>{
              console.log('this.orderDetails',this.orderDetails,key);
              let newValue = this.orderDetails[key];
              let newText = this.orderDetails[key];
              // 判断是否有默认值
              if(this.getForm(key).value && newValue === undefined){
                newValue = this.getForm(key).value
              }
              if(key === 'certfType'){
                this.setForm('birthDate','hidden',newValue === '01')
              }
              if(listOptions[key]){
                let optionsItem = listOptions[key].filter(e=>e.value === newValue)[0];
                if(optionsItem){
                  newValue = optionsItem.value
                  newText = optionsItem.text
                }
              }
              this.setForm(key,'value',newValue)
              this.setForm(key,'text',newText)
            })
            console.log('this.currentOptions',this.currentOptions);
            if(!this.currentOptions) return;
          }else{
            this.$refs.uniPopup.close()
          }
      },
      async modelType(n){
        if(n === 'server'){
          return
        }

      },
      modelOptions: {
        handler(val) {
          console.log('modelOptions changed:', val);
        },
        immediate: true,
        deep: true
      },
    },
    data(){
      return {
        underMap:[
          {required:true,checkFn:isChinese,title:'姓名',value:'',text:'',noValue:'请输入',valueType:'name',textType:'name',type:'input'},
          {required:true,title:'性别',value:'',text:'',noValue:'请选择',valueType:'sex',textType:'sex'},
          // {required:true,title:'证件类型',value:'01',text:'身份证',noValue:'请选择',valueType:'certfType',textType:'certfType'},
          {required:true,title:'身份证号码',checkFn:isIdCard,value:'',text:'',noValue:'请输入',valueType:'certfNo',textType:'certfNo',type:'input'},
          // {required:true,title:'出生日期',value:'',text:'',noValue:'请输入',valueType:'birthDate',textType:'birthDate',showType:'time'},
        ],
        riskAlertMap:[
          {title:'',value:'',text:`在陪诊服务过程中，我们始终将您的安全与权益置于首位。为全面应对门（急）诊场景中可能发生的突发健康风险（如检查过程中的身体不适、诊疗环节的临时状况等），现推出​「门诊无忧增值服务包」​，通过​“健康风险预案+医疗支持”双轨机制，为您提供更安心的就诊服务与保障。`,noValue:'',valueType:'',textType:'',type:'box'},
        ],
        insureInfoMap:[
          {title:'',noValue:'',valueType:'',textType:'',type:'box'},
        ],
        surrenderMap:[
          {title:'',noValue:'',valueType:'',textType:'',type:'noPaddingBox'},
        ],
        surrenderCGMap:[
          {title:'',noValue:'',valueType:'',textType:'',type:'noPaddingBox',hiddenClose:true},
        ],
        invoiceOpenCGMap:[
          {title:'',noValue:'',valueType:'',textType:'',type:'noPaddingBox',hiddenClose:true},
        ],
        invoiceOpenMap:[
          {required:true,title:'发票类型',value:'pc',text:'普通发票',noValue:'请选择',valueType:'invoiceLine',textType:'invoiceLine',type:'radio'},
          {required:true,title:'抬头类型',value:false,text:'个人',noValue:'请选择',valueType:'company',textType:'company',type:'radio'},
          {required:true,title:'购方名称',value:'',text:'',noValue:'请输入',valueType:'buyerName',textType:'buyerName',type:'input'},
          {required:true,title:'购方税号',hidden:true,value:'',text:'',noValue:'请输入',valueType:'buyerTaxNum',textType:'buyerTaxNum',type:'input'},
          {required:true,title:'手机号码',checkFn:isPhoneNumber,value:'',text:'',noValue:'请输入',valueType:'buyerTel',textType:'buyerTel',type:'input'},
          {required:true,title:'邮箱地址',checkFn:isEmail,value:'',text:'',noValue:'请输入',valueType:'email',textType:'email',type:'input'},
        ],
        // 副按钮集合
        secondaryButtonMap:{
          riskAlert:{
            buttonText:'不需要',
          },
          surrender:{
            buttonText:'取消',
          }
        },
        // 参数映射
        mappingList:{
          // 设置保险人信息
          under:'underMap',
          // 风险提示
          riskAlert:'riskAlertMap',
          // 查看保险详情
          insureInfo:'insureInfoMap',
          // 取消保障
          surrender:'surrenderMap',
          // 取消保障成功
          surrenderCG:'surrenderCGMap',
          // 开发票
          invoiceOpen:'invoiceOpenMap',
          invoiceOpenCG:'invoiceOpenCGMap'
        },
        titleMap:{
          under:'填写购买人',
          riskAlert:'门诊意外风险提示',
          insureInfo:'门诊无忧服务',
          surrender:'确定要取消保障吗',
          surrenderCG:'取消保障成功',
          invoiceOpen:'开具发票',
          invoiceOpenCG:'提交成功',
        },
        buttonTitleMap:{
          under:'提交',
          riskAlert:'购买门诊意外保障',
          insureInfo:'添加保障',
          surrender:'我要取消保障',
          surrenderCG:'我知道了',
          invoiceOpen:'提交',
          invoiceOpenCG:'我知道了',
        },
        classMap:{
          under:'underBox',
          surrender:'surrenderBox',
          surrenderCG:'surrenderCGBox',
          invoiceOpenCG:'surrenderCGBox',
        },
        btnClassMap:{
          code:'codeBtn'
        },
        apiMap:{
          server:'accompanybookUpdateOrder',
        },
        providerId:'',
        openServerFlag:false,
        file_ctx: this.file_ctx,
        iconClose: this.$static_ctx + "image/business/hulu-v2/icon-close2.png",
        iconRightArrow: this.$static_ctx + "image/business/hulu-v2/icon-right-arrow.png",
        CheckGreen: this.$static_ctx + "image/business/hulu-v2/CheckGreen.png",
        clearRed: this.$static_ctx + "image/business/hulu-v2/clearRed.png",
        shieldMini: this.$static_ctx + "image/business/hulu-v2/shieldMini.png",
        Ellipse: this.$static_ctx + "image/business/hulu-v2/Ellipse.png",
        iconPostSucess:
        this.$static_ctx + "image/business/hulu-v2/icon-post-sucess.png",
        isNotice:false,
        ProviderIdMap:[],
        listOptions,
        currentData:[],
        popupTitle:'',
        currentType:'',
        insuranceNum:5,
        listInsuranceNum:0,
        isShake:false,

      }
    },
    computed: {
      ...mapState("user", {
        accountId: (state) => state.accountId,
        fansRecord: (state) => state.fansRecord,
      }),
      currentOptions(){
        return this[this.mappingList[this.modelType]]
      },
      showSelectInsure(){
        if(this.modelType !== 'insureInfo') return false;
        if (this.underInfo && this.underInfo.insuranceNum === this.insuranceNum && this.productCode) {
          return true
        }
        return false
      },
      showListSelectInsure(){
        if(this.modelType !== 'insureInfo') return false;
        console.log('this.listInsuranceNum === this.insuranceNum',this.listInsuranceNum === this.insuranceNum,this.listInsuranceNum , this.insuranceNum);

        return this.listInsuranceNum === this.insuranceNum;
      }
    },
    async mounted() {
      console.log('加载数据',this.orderDetails);
    },
    methods:{
      setIsNotice(){
        if(this.isLocking) return uni.showToast({title:this.lockText,icon:'none'})
        this.isNotice = !this.isNotice;
        this.$emit('changesetIsNotice');
      },
      showListSelectInsureFn(item){
        this.listInsuranceNum = Number(item.insuranceNum);
        this.insuranceNum = Number(item.insuranceNum);
        // 确保组件更新
        this.$forceUpdate();

        // 延迟一帧再设置insureInfoBox的保险类型，确保initialInsuranceNum已经被传递
        this.$nextTick(() => {
          if(this.$refs.insureInfoBox) {
            this.$refs.insureInfoBox.setInsuranceType();
          }
        });
      },
      selectItem({insuranceNum}){
        this.insuranceNum = insuranceNum;
      },
      getForm(itemName,flag){
        return this.currentOptions.filter(e=>e.textType === itemName)[0] || (flag ? null : {})
      },
      setForm(itemName,className,value){
        this.currentOptions.map(e=>{
          if(e.textType === itemName){
            this.$set(e,className,value)
          }
        })
      },
      timestampToDateTime(timestamp) {
          if(typeof timestamp !== 'number') return timestamp
          // 创建一个 Date 对象
          const date = new Date(timestamp); // 时间戳通常是以秒为单位，Date 对象需要毫秒

          // 获取年、月、日、时、分
          const year = date.getFullYear();
          const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要加1，并补零
          const day = String(date.getDate()).padStart(2, '0');
          const hours = String(date.getHours()).padStart(2, '0');
          const minutes = String(date.getMinutes()).padStart(2, '0');

          // 拼接成所需的格式
          return `${year}-${month}-${day} ${hours}:${minutes}`;
      },
      async trigger(){
        // 检测必填项
        let options = this.checkRequired()
        if(typeof options === 'string') return uni.showToast({
          title:options,
          icon:'none'
        })
        if(!options) return uni.showToast({
          title:'请填入完整参数',
          icon:'none'
        })
        if(this.modelType === 'insureInfo'){
          // 如果当前未填同意须知
          if(this.isList && !this.isNotice){
            uni.showModal({
              title: '服务须知和产品服务细则',
              content: '我已阅读并同意【服务须知】，【服务细则】，【信息安全】',
              success: (res)=> {
                if (res.confirm) {
                  this.setIsNotice()
                  this.trigger()
                }
              }
            });
            this.isShake = true;
            setTimeout(() => {this.isShake = false},1000)
            return
          }
          options = this.$refs.insureInfoBox ? this.$refs.insureInfoBox.getProductCode() : {
            productCode: '',
            insuranceNum: 0
          };
        }
        if(this.modelType === 'under'){
          const insureBoxData = this.$refs.insureInfoBox ? this.$refs.insureInfoBox.getProductCode() : {
            productCode: '',
            insuranceNum: 0
          };
          options = {...insureBoxData, ...options, certfType:'01'}
        }
        // 先保存当前数据，不直接引用可能会被清空的对象
        const savedOptions = JSON.parse(JSON.stringify(options || {}));
        const savedModelType = this.modelType;

        // 立即显示loading遮罩整个界面
        uni.showLoading({title: '', mask: true});

        // 1. 直接关闭弹窗
        this.close();

        // 2. 等待弹窗完全关闭
        await new Promise(resolve => setTimeout(resolve, 500));

        // 3. 弹窗已完全关闭，再触发数据处理事件
        await this.$emit('finish', {
          modelType: savedModelType,
          options: savedOptions
        });
        // 4. 数据处理完毕，隐藏loading
        uni.hideLoading();
      },
      secondary(){
        this.$emit('secondaryfinish',{modelType:this.modelType})
      },
      checkRequired(){
        let options = {};
        for (var index = 0; index < this.currentOptions.length; index++) {
          var element = this.currentOptions[index];
          if(element.checkFn && !element.checkFn(element,this.currentOptions)){
            return `请输入正确的${element.title}`
          }
          if(element.required && !element.hidden){
            if(element.value === '' || element.value === null || element.value === undefined){
              return false
            }
          }
          !element.hidden && (options[element.valueType] = element.value)
        }
        return options
      },
      onchangeMode({detail:{value:[data]}}){
        if(this.currentType === 'certfType'){
          this.setForm('birthDate','hidden',data.value === '01')
        }
        this.setForm(this.currentType,'value',data.value)
        this.setForm(this.currentType,'text',data.text)
      },
      emit(type){
        if(type === 'certfType') return
        this.currentData = [];
        this.currentType = type;
        this.listOptions[type] && (this.currentData = this.listOptions[type]);
        this.popupTitle = '';
        if(this.getForm(type).showType === 'time'){
          this.$refs.selectTime.show()
        }else{
          this.$refs.selectData.show()
        }
        switch (type){
          case 'hospitalName':
            break;
          default:
            break;
        }
      },
      changeTime(time){
        this.setForm(this.currentType,'value',time)
        this.setForm(this.currentType,'text',time)
      },
      select({type,value}){
        this.setForm(type,'value',value)
        console.log(type);
        if(type === "company"){
          this.setForm('buyerTaxNum','required',value)
          this.setForm('buyerTaxNum','hidden',!value)
        }
        console.log(this.currentOptions);
      },
      emitInput({type,res:{detail:{value}}}){
        this.setForm(type,'value',value)
      },
      // 预览图片
      previewImage(urls){
        uni.previewImage({urls:[urls]});
      },

      changeShow(res){
        !res.show && this.$emit('change',res.show)
      },
      close(){
        this.$emit('change',false)
      },
    }
  }
</script>
<style lang="scss">
  .surrenderCGTitle{
    font-weight: 400;
    font-size: 28rpx;
    color: #777777;
    margin-top: 24rpx 0 28rpx 0;
    text-align: center;
  }
  .lineHide{
    width: 0;
    overflow: hidden;
    height: 0;
  }
  .underBox{
    background: white !important;
  }
  .shake{
    animation: shake 0.5s;
    animation-iteration-count: infinite;
  }
  // 写的简短一点
  @keyframes shake {
    0% {
      transform: translate(1px, 1px) rotate(0deg);
    }
    50% {
      transform: translate(-1px, -2px) rotate(-1deg);
    }
    100% {
      transform: translate(1px, -1px) rotate(1deg);
    }
  }
  .notice{
    font-weight: 500;
    font-size: 24rpx;
    color: #777777;
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
    width: 100%;
    .selectorButton {
      width: 32rpx;
      height: 32rpx;
      margin-top: auto;
      margin-right: 8rpx;
    }
    .specification{
      color: #1687F7;
    }
  }
  .surrenderBox{
    top: -48vh !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    width: 654rpx !important;
    border-radius: 16rpx 16rpx 16rpx 16rpx !important;
    overflow: hidden !important;
    bottom: unset !important;
    background-color: white !important;
    padding-bottom: 32rpx !important;
    .buttonMap{
      position: unset !important;
      padding: 0 0 16rpx 0 !important;
      padding-top: 0 !important;
      .secondaryButton{
        width: 292rpx;
        background: #00B484 !important;
        color: #FFFFFF !important;
        text-align: center;
      }
      .actionsConfirm{
        width: 292rpx;
        background: #FFFFFF !important;
        color: #1D2029 !important;
        text-align: center;
        border: 2rpx solid #D9DBE0;
      }
    }
     .server{
      margin-top: 0 !important;
      .serverItem{
        padding: 0 !important;
        .serverBox{
          padding-top: 38rpx !important;
          padding-bottom: 34rpx !important;
        }
      }
    }
  }
  .surrenderCGBox{
    top: -48vh !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    width: 654rpx !important;
    border-radius: 16rpx 16rpx 16rpx 16rpx !important;
    overflow: hidden !important;
    bottom: unset !important;
    background-color: white !important;
    padding-bottom: 32rpx !important;
    .buttonMap{
      position: unset !important;
      padding: 0 !important;
      padding-top: 0 !important;
      .actionsConfirm{
        border-top: 2rpx solid #EAEBF0;
        width: 100%;
        background: #FFFFFF !important;
        color: #00B484 !important;
        text-align: center;
        border-radius: 0 !important;
      }
    }
  }
  .codeBtn{
    background: #FFFFFF !important;
    color: #00B484 !important;
  }
  .describe {
    width: 590rpx;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    margin-top: 8rpx;
    font-weight: 400;
    font-size: 28rpx;
    color: #777777;
    .Check {
      width: 32rpx;
      height: 32rpx;
      &:last-child {
        margin: 8rpx 0 0 0 !important;
      }
      &:not(:last-child){
        margin: 0 20rpx 0 0rpx;
      }
    }
  }
  .confirm{
    width: 100vw;
    background: #F4F6FA;
    padding: 32rpx;
    box-sizing: border-box;
    border-radius: 24rpx 24rpx 0rpx 0rpx;
    position: fixed;
    bottom: 0;
    padding-bottom: 230rpx;
    .confirmTitle{
      font-weight: 500;
      font-size: 32rpx;
      color: #1D2029;
      text-align: center;
      position: relative;
      .iconCloseBox{
        width: 64rpx;
        position: absolute;
        right: 0;
        top: 6rpx;
      }
      .iconClose{
        width: 32rpx;
        height: 32rpx;
      }
    }

    .clear{
      .clearTitle{
        font-weight: 400;
        font-size: 28rpx;
        color: #1D2029;
      }
      .clearInput{
        font-weight: 400;
        font-size: 28rpx;
        margin-top: 24rpx;
        width: 686rpx;
        height: 88rpx;
        background: #FFFFFF;
        border-radius: 16rpx;
        border: 1rpx solid #D9DBE0;
        padding: 24rpx;
        box-sizing: border-box;
      }
      .miniTitle{
        font-weight: 400;
        font-size: 24rpx;
        color: #4E5569;
        margin-top: 36rpx;
      }
      .clearValue{
        width: 686rpx;
        height: 288rpx;
        background: #FFFFFF;
        border-radius: 16rpx;
        border: 1rpx solid #D9DBE0;
        margin-top: 24rpx;
        padding: 24rpx;
        box-sizing: border-box;
      }
    }
    .code{
      width: 638rpx;
      height: 766rpx;
      background-image: url($static_ctx + 'image/business/hulu-v2/orderCodeBg.png');
      background-size: 100%;
      margin: 48rpx auto;
      padding: 0 32rpx;
      box-sizing: border-box;
      .codeTitle{
        font-weight: 500;
        font-size: 28rpx;
        color: #1D2029;
        height: 136rpx;
        width: 100%;
        line-height: 136rpx;
        text-align: center;
        border-bottom: 2rpx solid #BCDDD4;
      }
      .payCode{
        width: 390rpx;
        height: 390rpx;
        background: #FFFFFF;
        border-radius: 40rpx;
        border: 12rpx solid #9DE3D0;
        margin: 110rpx auto;
        .codeImg{
          width: 100%;
          height: 100%;
          border-radius: 40rpx;
        }
      }
    }
    .langSelectInsure{
      .title{
        height: 164rpx !important;
      }
    }
    .selectInsure{
      background-color: #C1EFE3 !important;
      padding: 26rpx 32rpx 68rpx !important;
      .title{
        width: 750rpx;
        height: 112rpx;
        background: #C1EFE3;
        font-weight: 500;
        font-size: 34rpx;
        color: #00B484;
        display: flex;
        justify-content: center;
        align-items: center;
        .icon{
          width: 48rpx;
          height: 48rpx;
          margin-right: 8rpx;
        }
      }
    }
    .langButton{

    }
    .buttonMap{
        display: flex;
        margin-top: 50rpx;
        padding:24rpx 32rpx 68rpx;
        background-color: white;
        width: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
        box-sizing: border-box;
        flex-wrap: wrap;
        // 设置子元素的间隔
        row-gap: 24rpx; // 行间隔
      .secondaryButton{
        padding: 22rpx 34rpx;
        height: 88rpx;
        background: #FFFFFF;
        border-radius: 44rpx 44rpx 44rpx 44rpx;
        border: 1rpx solid #D9DBE0;
        box-sizing: border-box;
        margin-right: 36rpx;
      }
      .actionsConfirm{
        flex: 1;
        height: 88rpx;
        background: #00B484;
        border-radius: 44rpx;
        font-weight: 500;
        font-size: 32rpx;
        color: #FFFFFF;
        text-align: center;
        line-height: 88rpx;
      }
    }

  }
</style>
