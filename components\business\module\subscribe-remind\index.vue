<!--
<subscribe-remind></subscribe-remind>
-->
<template>
  <view v-show="isShowSubscribePanel&&isLogin">
    <view class="subscribe-remind" :class="defaultConfig.class" :style="{'background': defaultConfig.backgroundColor}">
      <text class="icon-warning-red-bg"></text>
      <view class="hint">
        您还没开启日程提醒，重要消息会收不到
      </view>
      <view class="btn" @tap="returnFn()">
        马上开启
      </view>
    </view>
    <uni-popup ref="showPopup" type="center">
      <view class="show-popup">
        <view class="popup-t">
          开启消息提醒
        </view>
        <view class="popup-m">
          <text>
            您还没开启日程提醒，重要消息会接收不到哦！
          </text>
        </view>
        <view class="popup-b">
          <view class="l" @tap="showAuditPopupCancel()">
            取消
          </view>
          <view class="r" @tap="showAuditPopupConfirm()">
            马上开启
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { mapState } from 'vuex'
import uniPopup from '@/components/uni/uni-popup'
export default {
  components: {
    uniPopup
  },
  computed: {
    ...mapState('user', {
      codeUserInfo: state => state.codeUserInfo,
      isLogin: state => state.isLogin
    }),
    ...mapState('system', {
      isShowSubscribePanel: state => state.isShowSubscribePanel
    })
  },
  props: {
    // 参数设置
    config: {
      type: Object,
      required: false,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      defaultConfig: {
        class: '',
        backgroundColor: '#fff'
      },
      isShow: false
    }
  },
  watch: {
    config: {
      handler(val) {
        this.copyConfig()
      },
      deep: true
    }
  },
  mounted() {
    this.copyConfig()
    if (this.isShowSubscribePanel) {
      this.subNumberFn()
    }
    // this.$uniPlugin.getSetting((res) => {
    //   const itemSettings = res.subscriptionsSetting.itemSettings
    //   if (itemSettings) {
    //     if (itemSettings[this.$constant.system.tmplIds[0].toString()] === 'accept') {
    //       this.isShow = false
    //     } else {
    //       this.isShow = true
    //     }
    //     // if (res.subscriptionsSetting.mainSwitch) {
    //     //   this.isShow = false
    //     // } else {
    //     //   this.isShow = true
    //     // }
    //     this.subNumberFn()
    //   } else {
    //     this.isShow = true
    //     this.subNumberFn()
    //   }
    // }, (res) => {})
  },
  methods: {
    /**
       * 初始化拷贝config对象
       */
    copyConfig() {
      const that = this
      const obj = that.config
      Object.keys(obj).forEach(function(key) {
        that.defaultConfig[key] = obj[key]
      })
    },
    subNumberFn() {
      // 初始化弹窗次数，提醒订阅消息
      const that = this
      if (that.isShow) {
        const subNumber = Number(this.$common.getCache('subNumber') === '' ? 0 : this.$common.getCache('subNumber'))
        if (subNumber < 1) {
          that.$refs.showPopup.open()
          this.$common.setCache('subNumber', 1)
        }
      }
    },
    showAuditPopupCancel() {
      this.$refs.showPopup.close()
    },
    showAuditPopupConfirm() {
      this.returnFn()
      this.$refs.showPopup.close()
    },
    returnFn() {
      const that = this
      // that.$uniPlugin.loading('订阅中...', true)
      // const openId = this.$common.getCache('openId')
      // if (this.$validate.isNull(openId)) { // 为空
      //   that.$uniPlugin.login((res) => {
      //     const params = { js_code: res.code }
      //     that.$ext.wechat.jscode2session(params).then((res) => {
      //       console.log('jscode2session:', res)
      //       this.extInsertProgram(res.openId)
      //     }).catch(error => {
      //       that.$uniPlugin.toast('初始化信息失败，请重试')
      //     })
      //   }, () => {})
      // } else { // 不为空
      //   console.log('openId:', openId)
      //   this.extInsertProgram(openId)
      // }
      this.$navto.push('ReceiptInform', {})
    },
    /**
       * 绑定小程序openId
       * @param openId
       */
    extInsertProgram(openId) {
      const params = {}
      params.userId = this.codeUserInfo.id
      params.openId = openId
      this.$api.wechat.insertProgram(params, (res) => {
        this.requestSubscribeMessage()
      }, () => {
        this.$uniPlugin.toast('初始化信息失败，请重试')
      })
    },
    requestSubscribeMessage() {
      this.$uniPlugin.requestSubscribeMessage(this.$constant.system.tmplIds, (res) => {
        if (res[this.$constant.system.tmplIds[0].toString()] === 'accept') {
          this.isShow = false
          this.$uniPlugin.toast('订阅成功')
        } else {
          this.isShow = true
          this.$uniPlugin.toast('订阅失败')
        }
      })
      this.$uniPlugin.hideLoading()
    }
  }
}
</script>
<style lang="scss" scoped>
  .subscribe-remind{
    background-color: #ffffff;
    color: #f56c6c;
    border-radius: 10upx;
    padding: 14upx;
    margin: 5rpx 30rpx 20rpx 30rpx;
    text.icon-warning-red-bg{
      @include iconImg(36, 36, '/business/icon-warning-red-bg.png');
      display: inline-block;
      vertical-align: middle;
      margin-right: 14upx;
    }
    .hint{
      display: inline-block;
      vertical-align: middle;
      font-size: 24upx;
      line-height: 36upx;
      width: calc(100% - 184upx);
    }
    .btn{
      display: inline-block;
      vertical-align: middle;
      font-size: 22upx;
      line-height: 34upx;
      width: 110upx;
      text-align: center;
      color: #ffffff;
      border: 2upx solid #fd9e14;
      @include rounded(6upx);
      margin-left: 20upx;
      background: #fd9e14;
    }
  }
  .uni-popup{
    z-index: 999!important;
  }
  .show-popup{
    background-color: #fff;
    width: 560upx;
    @include rounded(6upx);
    overflow: hidden;
    .popup-t{
      text-align: center;
      font-size: 36upx;
      line-height: 48upx;
      padding: 30upx 0 20upx 0;
    }
    .popup-m-img{
      display: inline-block;
      margin-bottom: 20upx;
      .l{
        vertical-align: middle;
        display: inline-block;
        width: 90upx;
        height: 90upx;
        overflow: hidden;
        @include rounded(50%);
        margin-right: 20upx;
        overflow: hidden;
        image{
          width: 100%;
          height: 100%;
        }
      }
      .m{
        display: inline-block;
        color: #333;
        font-size: 28upx;
        line-height: 42upx;
        vertical-align: middle;
      }
    }
    .popup-m{
      display: block;
      margin-bottom: 20upx;
      font-size: 28upx;
      line-height: 42upx;
      color: #333;
      padding: 0 30upx;
    }
    .popup-b{
      border-top: 2upx solid $contentDdt;
      view {
        line-height: 88upx;
        height: 88upx;
        font-size: 36upx;
        text-align: center;
        display: inline-block;
      }
      .l{
        width: calc(50% - 2upx);
        border-right: 2upx solid $contentDdt;
      }
      .r{
        width: 50%;
        color: $topicC;
      }
    }
  }
</style>

