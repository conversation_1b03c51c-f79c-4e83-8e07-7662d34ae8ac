/**
 * 导航存储对象
 */
import common from '@/common/util/main'
import validate from '@/common/util/validate'
import navto from '@/router/config/nav-to'
import $env from '@/config/env'
const $static_ctx = $env.static_ctx
const navigation = {
  namespaced: true,
  state: {
    /**
     * 原生对象属性说明
     */
    headerObj: { // 页面标题
      isDefault: true,
      isShow: false, // 默认为不显示
      title: '加载中', // 中间标题
      classStyle: '', // :style的样式
      optionsLeft: {//  左边模块对象
        isShow: true, // 是否显示
        iconStyle: { // icon对象数据
          value: '', // 值
          classStyle: {
            'background': 'url(' + $static_ctx + 'image/system/logo/icon-back.png) no-repeat'
          }, // :style的样式
          clickEvent: () => { // 点击事件
            navto.back()
          }
        }
      },
      optionsRight: {//  右边模块对象
        isShow: true, // 是否显示
        iconStyle: [{ // icon对象数据
          value: '', // 值
          classStyle: '', // :style的样式
          clickEvent: () => {}
        }]
      }
    },
    /**
     * 备份重置
     */
    headerObjCopy: { // 页面标题
      isShow: false, // 默认为不显示
      title: '加载中', // 中间标题
      classStyle: '', // :style的样式
      optionsLeft: {//  左边模块对象
        isShow: true, // 是否显示
        iconStyle: { // icon对象数据
          value: '', // 值
          classStyle: {
            'background': 'url(' + $static_ctx + 'image/system/logo/icon-back.png) no-repeat'
          }, // :style的样式
          clickEvent: () => { // 点击事件
            navto.back()
          }
        }
      },
      optionsRight: {//  右边模块对象
        isShow: true, // 是否显示
        iconStyle: [{ // icon对象数据
          value: '', // 值
          classStyle: '', // :style的样式
          clickEvent: () => {}
        }]
      }
    }
  },

  mutations: {
    /**
     * 案例模板
     * @param state
     * @param data
     * @constructor
     */
    UPDATE_DEMO: (state, data) => {

    },
    /**
     * 更新 页面标题
     */
    UPDATE_HEADEROBJ(state, data) {
      if (!data) return
      const headerObj = data.headerObj
      if (validate.isNull(headerObj)) {
        return
      }
      state.headerObj = common.deepCloneObj(state.headerObjCopy)

      for (const i in state.headerObj) {
        if (headerObj.hasOwnProperty(i)) {
          if (validate.judgeTypeOf(headerObj[i]) === 'Object') {
            for (const j in headerObj[i]) {
              if (validate.judgeTypeOf(headerObj[i][j]) === 'Object') {
                for (const k in headerObj[i][j]) {
                  state.headerObj[i][j][k] = headerObj[i][j][k]
                }
              } else {
                state.headerObj[i][j] = headerObj[i][j]
              }
            }
          } else {
            state.headerObj[i] = headerObj[i]
          }
        }
      }
    }
  },
  actions: {
    /**
     * 案例模板
     * @param commit
     * @param data
     * @constructor
     */
    UpdateDemo({ commit }, data) {
      commit('UPDATE_DEMO', data)
    },
    /**
     * 更新 页面标题
     */
    UpdateHeaderObj(context, headerObj) {
      context.commit('UPDATE_HEADEROBJ', headerObj)
    }
  }
}

export default navigation
