export default [
  {
    path: '/modules/pharmacy/pharmacy-cyclopedia/index',
    name: 'PharmacyCyclopedia',
    meta: {
      index: 2,
      headerObj: {
        title: '用药指南',
        isShow: true
      }
    }
  },
  {
    path: '/modules/pharmacy/pharmacy-cyclopedia/doctor-question/index',
    name: 'DoctorQuest<PERSON>',
    meta: {
      index: 2,
      headerObj: {
        title: '药师问答',
        isShow: true
      }
    }
  },
  {
    path: '/modules/pharmacy/pharmacy-cyclopedia/pharmacy-evaluate/index',
    name: 'PharmacyEvaluate',
    meta: {
      index: 2,
      headerObj: {
        title: '病友分享',
        isShow: true
      }
    }
  },
  {
    path: '/modules/pharmacy/pharmacy-cyclopedia/look-more/index',
    name: 'LookMore',
    meta: {
      index: 2,
      headerObj: {
        title: '查看更多',
        isShow: true
      }
    }
  },
  {
    path: '/modules/pharmacy/pharmacy-cyclopedia/enterprise/index',
    name: 'Enterprise',
    meta: {
      index: 2,
      headerObj: {
        title: '企业介绍',
        isShow: true
      }
    }
  },
  {
    path: '/modules/pharmacy/pharmacy-cyclopedia/drugstore/index',
    name: 'Drugstore',
    meta: {
      index: 2,
      headerObj: {
        title: '附近药店',
        isShow: true
      }
    }
  },
  {
    path: '/modules/pharmacy/pharmacy-cyclopedia/pharmacy-remind/index',
    name: 'PharmacyRemind',
    meta: {
      index: 2,
      headerObj: {
        title: '用药提醒',
        isShow: true
      }
    }
  },
  {
    path: '/modules/pharmacy/pharmacy-cyclopedia/pharmacy-remind/components/add',
    name: 'PharmacyAdd',
    meta: {
      index: 2,
      headerObj: {
        title: '创建提醒',
        isShow: true
      }
    }
  },
  {
    path: '/modules/pharmacy/pharmacy-cyclopedia/pharmacy-remind/components/pharmacy-detail',
    name: 'PharmacyDetail',
    meta: {
      index: 2,
      headerObj: {
        title: '用药详情',
        isShow: true
      }
    }
  },
  {
    path: '/modules/pharmacy/pharmacy-cyclopedia/publish-post/index',
    name: 'PublishPost',
    meta: {
      index: 2,
      headerObj: {
        title: '发帖',
        isShow: true
      }
    }
  },
  {
    path: '/modules/pharmacy/pharmacy-cyclopedia/history/index',
    name: 'History',
    meta: {
      index: 2,
      headerObj: {
        title: '我的说明书',
        isShow: true
      }
    }
  },
  {
    path: '/modules/pharmacy/pharmacy-cyclopedia/electronic-book/index',
    name: 'ElectronicBook',
    meta: {
      index: 2,
      headerObj: {
        title: '电子说明书',
        isShow: true
      }
    }
  },
  {
    path: '/modules/pharmacy/pharmacy-cyclopedia/electronic-book/detail',
    name: 'ElectronicDetail',
    meta: {
      index: 2,
      headerObj: {
        title: '药品说明书',
        isShow: true
      }
    }
  },
  {
    path: '/modules/pharmacy/pharmacy-cyclopedia/everyday-rumour/index',
    name: 'EverydayRumour',
    meta: {
      index: 2,
      headerObj: {
        title: '每日辟谣',
        isShow: true
      }
    }
  },
  {
    path: '/modules/pharmacy/pharmacy-cyclopedia/everyday-rumour/more-rumour',
    name: 'EverydayMoreRumour',
    meta: {
      index: 2,
      headerObj: {
        title: '更多辟谣',
        isShow: true
      }
    }
  }
]
