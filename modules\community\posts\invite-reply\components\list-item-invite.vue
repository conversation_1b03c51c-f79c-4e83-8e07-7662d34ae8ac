<template>
  <view class="main">
    <view class="fast-invite-box">
      <text class="fast-invite-text">已成功邀请{{ count }}人</text>
    </view>
    <view style="flex: 1;">
      <scroll-refresh
        :fixed="false"
        :up="upOption"
        :down="downOption"
        @returnFn="returnFn"
        @scrollInit="scrollInit"
      >
        <view class="main-content">
          <view
            class="item"
            v-for="item in list"
            :key="item.id"
            hover-class="message-hover-class"
          >
            <image
              class="avatar"
              mode="aspectFill"
              :src="item.headPath || defaultAvatar"
            ></image>
            <view class="user-box-content">
              <text class="user-name">{{ item.nickName }}</text>
              <text class="user-type">{{ item.vTypeText }}</text>
            </view>
            <button type="primary" size="mini" class="btn" disabled>
              {{ item.replyStatusText }}
            </button>
          </view>
        </view>
      </scroll-refresh>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    params: {
      type: Object,
      default: () => {
        return null
      }
    }
  },
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $accurateConversion: this.$accurateConversion,
      file_ctx: this.file_ctx,
      $static_ctx: this.$static_ctx,
      $timePlugin: this.$timePlugin,
      $validate: this.$validate,
      mescroll: null, // mescroll实例对象
      downOption: {
        auto: false // 不自动加载
      },
      upOption: {
        auto: false // 不自动加载
      },
      isInit: false, // 列表是否已经初始化
      list: [],
      defaultAvatar: this.file_ctx + 'static/image/system/avatar/icon-default-v2.png',
      count: 0
    }
  },
  methods: {
    init(val) {
      this.mescroll.triggerDownScroll()
    },
    scrollInit(scroll) {
      scroll.optUp.page.num = 1
      scroll.optUp.page.size = 10
      this.mescroll = scroll
    },
    returnFn(obj) {
      const that = this
      setTimeout(function () {
        let params = {
          current: obj.pageNum,
          size: obj.pageSize,
          condition: {}
        }
        Object.keys(that.params).forEach(key => {
          if (!['name'].includes(key)) {
            params.condition[key] = that.params[key]
          }
        })
        that.$ext.community.cfpostinvitelogQueryPage(params).then(res => {
          const data = res.data.records || []
          if (obj.pageNum === 1) {
            that.list = []
          }
          that.list = that.list.concat(data)
          that.count = res.data.total
          obj.successCallback && obj.successCallback(data || [])
        })
      }, that.$constant.noun.scrollRefreshTime)
    },
    navtoGo(url, obj = {}) {
      this.$navto.push(url, obj)
    }
  }
}
</script>

<style lang="scss" scoped>
.main {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  padding-bottom: env(safe-area-inset-bottom);
}
.main-content {
  flex: 1;
  width: 100%;
  padding: 0 32rpx;
  background-color: #fff;
  box-sizing: border-box;
  overflow-y: auto;
}
.item {
  width: 100%;
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid #f5f5f5;
  padding: 32rpx 0;
  &:last-child {
    padding-bottom: 32rpx;
  }
  .avatar {
    width: 90rpx;
    height: 90rpx;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 24rpx;
  }
  .user-box-content {
    height: 90rpx;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  .user-name {
    font-size: 32rpx;
    line-height: 46rpx;
    font-weight: 500;
    color: #000;
  }
  .user-type {
    font-size: 26rpx;
    line-height: 36rpx;
    color: #333;
  }
  .btn {
    margin: 0;
    padding: 0 32rpx;
    display: flex;
    align-items: center;
    background-color: #4cd964;
    border: none;
    height: 56upx;
    line-height: 56upx;
    border-radius: 28rpx;
    font-size: 28rpx;
  }
}
.fast-invite-box {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #e4e4e4;
  box-sizing: border-box;
  .fast-invite-text {
    flex: 1;
    font-size: 26rpx;
    color: #333;
    line-height: 42rpx;
    text-align: center;
  }
}
</style>