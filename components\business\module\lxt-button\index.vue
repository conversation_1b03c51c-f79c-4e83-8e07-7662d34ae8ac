<!-- 内容模块标题配件组件 <lxt-button @returnFn = "returnFn"></lxt-button> -->
<template>
  <view class="lxt-button" :class="defaultConfig.class" @tap.stop="returnFn">
    {{text}}
  </view>
</template>

<script>
export default {
  props: {
    // 是否禁用disable
    disabled: {
      type: Boolean,
      default() {
        return false
      }
    },
    // 按钮文本
    cText:{
      type: String,
      default() {
        return ''
      }
    },
    // 参数设置
    config: {
      type: Object,
      required: false,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      defaultConfig: {
        class: ''
      },
      text: '',
      timer: undefined
    }
  },
  watch: {
    cText: {
      handler(val) {
        this.watchDataMain(val)
      },
      deep: true
    },
    config: {
      handler(val) {
        this.copyConfig()
      },
      deep: true
    }
  },
  mounted() {
    this.copyConfig()
    this.watchDataMain(this.cText)
  },
  methods: {
    /**
       * 初始化拷贝config对象
       */
    copyConfig() {
      const that = this
      const obj = that.config
      Object.keys(obj).forEach(function(key) {
        that.defaultConfig[key] = obj[key]
      })
    },
    watchDataMain(val) {
      this.text = val
    },
    returnFn() {
      if (this.timer) {
        clearTimeout(this.timer)
      }
      this.timer = setTimeout(() => {
        this.$emit('returnFn')
      }, 500)
    }
  }
}
</script>

<style lang="scss" scoped>
.lxt-button{

}
</style>
