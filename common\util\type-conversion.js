import constant from '@/constant'
const dictionaryLibrary = constant.noun
export default {
  /**
   * key转换text --- 对象方法体处理
   * @param textval
   * @returns {boolean}
   */
  switchKeyOrValue(type, key) {
    let value = ''
    if (type && key && dictionaryLibrary[type]) {
      value = dictionaryLibrary[type][key]
    }
    return value
  },
  /**
   * key转换text --- 数组方法体处理
   * @param textval
   * @returns {boolean}
   */
  switchKeyOrValueArr(type, key) {
    let value = ''
    if (type && key && dictionaryLibrary[type]) {
      for (const k in dictionaryLibrary[type]) {
        if (key == dictionaryLibrary[type][k].key) {
          value = dictionaryLibrary[type][k].value
          break
        }
      }
    }
    return value
  }
}
