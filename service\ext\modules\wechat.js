import common from '@/common/util/main'
import api from '@/service/api'
import store from '@/store'
import wechatPlugin from '@/common/util/wechat-plugin'
import uniPlugin from '@/common/util/uni-plugin'
import ext from '@/service/ext'
import env from '@/config/env'
import validate from '@/common/util/validate'

/**
 * 针对获取 微信公众号 H5 进行二次数据清洗、加工、转化，特别指API
 */
export default {
    getPhone(params) {
        uniPlugin.loading('登录中')
        return new Promise((resolve, reject) => {
          params.appId = env.appId
            api.wechat.getPhone(params).then(res => {
                uniPlugin.hideLoading()

                resolve(res)
            }).catch(err => {
                uniPlugin.hideLoading()

                reject(err)
            })
        })

    },
    quickLogin({ phone }) {
        uniPlugin.loading('登录中')

        return new Promise((resolve, reject) => {
            api.wechat.quickLogin({ phone }).then(res => {
                const data = res.data
                // 先给个中央用户id 防止没有档案拿不到中央用户id
                common.setKeyVal('user', 'curSelectUserInfo', { centerUserId: data.loginId}, true)
                common.setKeyVal('user', 'codeUserInfo', { id: data.loginId }, true)
                // #ifdef MP-WEIXIN
                ext.user.loginBindWeixinAccount()
                // #endif

                uniPlugin.hideLoading()
                common.setKeyVal('user', 'token', data.tokenValue, true)
                common.setKeyVal('user', 'isLogin', true, true)
                common.setKeyVal('user', 'mpWxUserInfo', res.data, true)
                resolve(res)
            }).catch(err => {
                uniPlugin.hideLoading()

                common.setKeyVal('user', 'mpWxUserInfo', {}, true)

                reject(err)
            })
        })

    },
    /**
     * 检查是否绑定微信公众号Unionid关系，控制提醒面板
     */
    checkWechatConcern(resolve = () => {}, reject = () => {}) {
        const openId = common.getCache('openId')
        api.wechat.checkWechatConcern(openId, (res) => {
            if (res.result) {
                common.setKeyVal('system', 'isShowSubscribePanel', false)
            } else {
                common.setKeyVal('system', 'isShowSubscribePanel', true)
            }
            resolve()
        }, (res) => {
            reject()
        })
    },
    /**
     * 获取微信公众号 Unionid
     * @returns {Promise<unknown>}
     */
    getUnionid() {
        return new Promise((resolve, reject) => {
            const unionid = common.getCache('unionid')
            const openId = common.getCache('openId')
            if (unionid.length === 0 && openId.length === 0) {
                uniPlugin.login((res) => {
                    const params = {
                        js_code: res.code
                    }
                    ext.wechat.jscode2session(params).then((res) => {
                        // console.log('初始化 unionid & openId ', res.unionid, res.openId)
                        ext.wechat.updateWechatUser()
                    }).catch(error => {})
                }, () => {})
            } else {
                ext.wechat.updateWechatUser()
                    // console.log('已存在 unionid & openId ', unionid, openId)
            }
        })
    },
    /**
     * 更新微信用户信息
     * @param userId
     */
    updateWechatUser() {
        if (!validate.isNull(store.state.user.codeUserInfo)) {
            // if (validate.isNull(common.getCache('isBindUnionIdStatus'))){
            return new Promise((resolve, reject) => {
                    const unionid = common.getCache('unionid')
                    const openId = common.getCache('openId')
                    const param = {
                        unionId: unionid,
                        openId: openId,
                        appId: env.appId,
                        createUser: store.state.user.codeUserInfo.id
                    }
                    if (!validate.isNull(unionid) && !validate.isNull(openId)) {
                        api.wechat.updateWechatUser(param, (res) => {
                            common.setCache('isBindUnionIdStatus', true)
                            resolve()
                        }, (error) => {
                            reject(error)
                        })
                    }
                })
                // }
        }
    },
    // 解绑微信用户
    unbundlingWechatUser() {
        return new Promise((resolve, reject) => {
            const unionid = common.getCache('unionid')
            const openId = common.getCache('openId')
            const param = {
                unionId: unionid,
                openId: openId,
                appId: env.appId,
                createUser: ''
            }
            if (!validate.isNull(unionid) && !validate.isNull(openId)) {
                api.wechat.updateWechatUser(param, (res) => {
                    common.setKeyVal('system', 'isShowSubscribePanel', true)
                    common.setCache('unionid', '')
                    common.setCache('openId', '')
                    resolve()
                }, (error) => {
                    reject(error)
                })
            }
        })
    },
    /**
     * 获取微信公众号用户信息
     * @returns {Promise<any>}
     */
    getWechatInfo() {
        return new Promise((resolve, reject) => {
            api.user.getWechatInfo().then((weChatInfo) => {
                    const isBind = !!store.state.user.curSelectStore.openId
                    weChatInfo.isBind = isBind // 通过 openId 判断是否已绑定
                    weChatInfo.isShow = true
                    common.setKeyVal('user', 'weChatInfo', weChatInfo)
                    resolve(weChatInfo)
                }).catch(error => {
                    reject(error)
                })
                // }
        })
    },
    /**
     * 是否微信公众号JSSDK初始化成功状态
     */
    isWechatConfig() {
        return wechatPlugin.isWechatConfig()
    },
    /**
     * 判断是否在微信公众号环境中
     * @returns {*|boolean}
     */
    isWechatEnv() {
        let state = false
            // #ifdef MP-WEIXIN
            // #endif
            // #ifdef H5
        state = wechatPlugin.isWechatEnv()
            // #endif
        return state
    },
    /**
     * 解析微信公众号JSSDK权限验证配置
     * @param callback
     * @returns {Promise<any>}
     */
    handlerJSsdkConfig() { // wx.config  微信注入权限验证配置
        return new Promise((resolve, reject) => {
            const urlparam = common.getKeyVal('user', 'firstUrlParam', true) || {}
            if (urlparam && !urlparam.appId) {
                return
            }
            let url = location.href.split('#')[0]
            url = encodeURIComponent(url)
            const params = {
                appId: urlparam.appId,
                url: url
            }
            api.wechat.handlerJSsdkConfig(params, (res) => {
                wechatPlugin.initiConfig(urlparam, res, () => {
                    resolve()
                })
            }, () => {
                reject()
            })
        })
    },
    /**
     * 微信小程序登录授权并获取手机号
     * @returns {Promise<unknown>}
     */
    decryptPhoneNumber(param) {
        return new Promise((resolve, reject) => {
            api.wechat.decryptPhoneNumber(param, (res) => {
                resolve(res.result.phoneNumber)
            }, () => {
                reject()
            })
        })
    },
    /**
     * 登录凭证校验
     * @param param
     * @returns {Promise<unknown>}
     */
    jscode2session(param) {
        return new Promise((resolve, reject) => {
            api.wechat.jscode2session(param, (res) => {
                common.setCache('session_key', res.result.session_key)
                common.setCache('openId', res.result.openId)
                common.setCache('unionid', res.result.unionid)
                resolve(res.result)
            }, () => {
                reject()
            })
        })
    },
    // 获取openid
    getOpenId () {
      return new Promise((resolve,reject) => {
        const openId = common.getCache('openId')
        if(openId) {
          resolve(openId)
          return
        }
        // #ifdef MP-WEIXIN
        uni.login({
          provider: 'weixin',
          scopes: 'auth_base',
          success: async (res) => {
            const {code} = res
            const resp = await api.user.getMiniCode2Session({ appId: env.appId, secret: 1, jsCode: code }).catch(err => {
              reject(err)
            })
            common.setCache('openId', resp.data.openid)
            resolve(resp.data.openid)
          },
          fail: (err) => {
            console.log('微信授权失败',err)
          }
        })
        // #endif
        //#ifndef MP-WEIXIN
        resolve('');
        // #endif
      })
    },
    // 获取unionid
    getUnionId () {
      return new Promise((resolve,reject) => {
        const unionid = common.getCache('unionid')
        if(unionid) {
          resolve(unionid)
          return
        }
        // #ifdef MP-WEIXIN
        uni.login({
          provider: 'weixin',
          scopes: 'auth_base',
          success: async (res) => {
            const {code} = res
            const resp = await api.user.getMiniCode2Session({ appId: env.appId, secret: 1, jsCode: code }).catch(err => {
              reject(err)
            })
            common.setCache('unionid', resp.data.unionid)
            resolve(resp.data.unionid)
          },
          fail: (err) => {
            console.log('微信授权失败',err)
          }
        })
        // #endif
        //#ifndef MP-WEIXIN
        resolve('');
        // #endif
      })
    },
}
