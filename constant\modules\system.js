/**
 * 系统级别的的静态描述
 */
import serverOptions from '@/config/env/options'
export default {
  /**
   * 终端类型
   */
  terminal: {
    h5: 'h5-ps',
    app: 'app-ps',
    // miniProgram: 'miniProgram-ps',
    // miniProgram: 'accompany-ps',
    miniProgram: serverOptions.miniProgram,
    alipay: 'alipay-ps'
  },
  shareTitle: '优德医陪诊',
  shareLogo: '',
  shareDesc: '',
  /**
   * 新增缩略图配置
   * 261993644112740352/78cc384c96614d50bd4c0c81786c9aa5.jpg?x-oss-process=image/auto-orient,1/resize,p_50/quality,q_90
   * http://dev-oss.lezhide.cn/261993644112740352/d8eacf8179ee41cabee8f6ea4c17ab8b.jpg?x-oss-process=image/auto-orient,1/resize,p_80/quality,q_90
   * x-oss-process=image/auto-orient,1/resize,p_80/quality,q_90
   */
  thumbnail: {
    smaller: '?x-oss-process=image/resize,p_20/quality,q_90',
    small: '?x-oss-process=image/resize,p_50/quality,q_90',
    large: '?x-oss-process=image/resize,p_80/quality,q_90'
  },
  /**
   * 支持上传视频格式
   * h5 video 只支持播放 Ogg MPEG4 WebM 这三种格式
   * swf|avi|flv|mpg|rm|mov|wav|asf|3gp|mkv|rmvb|wmv|mp4|ogg
   * WebM|MPEG4|mp4|ogg|mov
   */
  // videoReg: 'WebM|MPEG4|mp4|ogg|mov',
  videoReg: new RegExp('\.(WebM|MPEG4|mp4|ogg|mov)$', 'i'),
  /**
   * 路由白名单配置
   * @type {string[]}
   */
  whiteRouterList: [
    'AccompanyHome',
    'PacketDll',
    'PacketDllResult',
    'RedpacketReceive',
    'Redpacket',
    'PagesWebHtmlView',
    'liveComputeEndPage',
    'postVideoList',
    'directseedingVideoListIndex',
    'questionnaireFillin',
    'questionnaireIndex',
    'ResearchDetail',
    'Research',
    'InviteRecord',
    'PostsReply',
    'PostsInviteReply',
    'diagnosisComment',// 诊后点评
    'diagnosisCommentAdd',// 诊后点评
    'diagnosisSubscribemessage',// 医院服务满意度调查表
    'AppJumpView',
    'shortVideoList',
    'CircleGuideSubscribe',
    'Packet',
    'PacketPay',
    'PacketMiddlePage',
    'Patronsaint',
    'PostsEdit',
    'User',
    'CommonSystemSearchData',
    'CircleHome',
    'PostMessage',
    'PostsDetail',
    'NewsReply',
    'NewsLikeCollect',
    'EnterpriseWechatGroup',
    'EnterpriseWechatGroupCheck',
    'CircleMoreIndex',
    'PersonalMyComment',
    'PersonalMyLike',
    'Circle',
    'PersonalMyCollect',
    'PersonalMyPosts',
    'PersonalHomePage',
    'CommonSystemSearch',
    'LightsPacketResult',
    'ResultLoading',
    'Result',
    'FreeOut',
    'Lights',
    'ChatLogin',
    'InformationList',
    'InformationDetail',
    'Home',
    'Index',
    'Store',
    // 'News',
    'Personal',
    'WebHtmlView',
    'Login',
    'Register',
    'VerificationCode',
    'Password',
    'UserAgreement', // 用户协议
    'OrderAgreement', // 下单协议
    'ArrivalAgreement', // 入驻协议
    'ExeceptionClause',//免责条款
    'SecrecyPolicy', // 隐秘政策
    'PlatformAgreement', // 平台服务协议
    'HospitalRanking', //医院点评
    'HospitalDetail', //医院详情
    'PharmacyCyclopedia', //医院详情
    'accompanyDoctorSystemSearch', // 陪诊医生搜索
    'accompanyDoctorSystemSearchSystem', // 陪诊医生搜索结果页面
    'serviceReservation', // 预约挂号
    'comboDetail', // 套餐详情
    'DoctorDetail', // 医生详情
    'ServiceIndex', // 服务首页
    'doctorList', // 医生列表
    'accompanyTeacher', // 陪诊师
    'accompanyList', // 陪诊列表
    'accompanyDoctorDetails', // 陪诊详情
    'ServiceDetail', // 服务详情
    'Application', // 陪诊师申请
    'ApplicationIntro', // 陪诊师申请详情
  ],
  // 日程提醒
  tmplIds: ['K-I4YKa4AGpjGRCvTOQKfw_y36b0pf4xsMDCEnZncKQ'],
  postsBeLikeTmplIds: ['TadP-limeo9-5ezzab6Ixj2dCHUHwVaFt9CxSFBQNbA'], // 帖子被赞
  commentReplyTmplIds: ['ZDFx4ynEbPGxPSNbsVAcc-kkjrNCGot1nqUNIvbGjCw'], // 评论回复
  commentReplyOtherTmplIds: ['M1ed03bV4VC_u44nv8-EBQtd04ap6Lnb1ctLGTd6IJY'], // 帖子评论回复
  diagnosisTmplIds: ['K07IXYzbGFELTRykXL8aJ55hi9HQl3H_LLhkNpXkkEg'], // 诊后订阅
  packetTmplIds: ['pXql1fSTwUF74Csc8pl6qRDBQXH-JqFKuCnBVt-MCOo'] // 出袋订阅
}
