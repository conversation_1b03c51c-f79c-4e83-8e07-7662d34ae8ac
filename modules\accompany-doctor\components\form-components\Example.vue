<template>
  <view class="container">
    <view class="test-form">
      <view class="form-title">表单组件测试</view>
      
      <!-- 使用formFields属性统一渲染表单 -->
      <app-form
        v-model="form"
        :form-fields="formItems"
        :valid-rules="validRules"
        :static-ctx="$static_ctx"
        submit-text="提交表单"
        @submit="submitForm"
      >
        <!-- 自定义插槽示例 -->
        <template v-slot:custom-field>
          <view class="custom-field">
            <view class="custom-field-content">
              <view class="custom-buttons">
                <view 
                  v-for="(tag, idx) in customTags" 
                  :key="idx" 
                  class="custom-tag"
                  :class="{ active: selectedTags.includes(tag) }"
                  @click="toggleTag(tag)"
                >
                  {{tag}}
                </view>
              </view>
              <view class="selected-tags-preview" v-if="selectedTags.length > 0">
                已选择: {{selectedTags.join(', ')}}
              </view>
            </view>
          </view>
        </template>
      </app-form>
    </view>
  </view>
</template>

<script>
import AppForm from '@/modules/accompany-doctor/form-components/AppForm.vue'

export default {
  components: {
    AppForm
  },
  data() {
    return {
      form: {
        name: '',
        age: '',
        gender: '',
        province: '',
        city: '',
        avatar: '',
        experience: '',
        idCardFront: '',
        idCardBack: '',
        phone: '',
        idcard: '',
        language: '',
        customTags: [],
        isAvailable: true,
        receiveNotifications: false
      },
      // 自定义标签数据
      customTags: ['专业', '耐心', '准时', '贴心', '热情', '认真', '细致'],
      selectedTags: [],
      // 表单项配置
      formItems: [
        {
          type: 'input',
          name: 'name',
          label: '姓名',
          placeholder: '请输入真实姓名',
          required: true
        },
        {
          type: 'input',
          name: 'age',
          label: '年龄',
          placeholder: '请输入年龄',
          required: true,
          inputType: 'number'
        },
        {
          type: 'picker',
          name: 'gender',
          label: '性别',
          placeholder: '请选择',
          required: true,
          mode: 'selector',
          range: ['男', '女']
        },
        // {
        //   type: 'picker',
        //   name: 'regionValue',
        //   label: '服务城市',
        //   placeholder: '请选择',
        //   required: true,
        //   mode: 'region',
        //   level: 'city',
        //   provinceField: 'province',
        //   cityField: 'city'
        // },
        {
          type: 'picker',
          name: 'language',
          label: '擅长语言',
          placeholder: '请选择',
          required: true,
          mode: 'selector',
          range: ['普通话', '粤语', '英语', '客家话', '潮汕话', '五邑话', '高州话', '湖南方言']
        },
        {
          type: 'input',
          name: 'phone',
          label: '手机号',
          placeholder: '请输入手机号码',
          required: true,
          inputType: 'number'
        },
        // {
        //   type: 'input',
        //   name: 'idcard',
        //   label: '身份证号',
        //   placeholder: '请输入身份证号码',
        //   required: true
        // },
        {
          type: 'image',
          name: 'avatar',
          label: '形象照',
          required: true
        },
        {
          type: 'textarea',
          name: 'experience',
          label: '经验描述',
          placeholder: '请描述您的陪诊经验（200字以内）',
          maxlength: 200,
          showCounter: true
        },
        {
          type: 'idcard',
          label: '身份证',
          required: true,
          frontField: 'idCardFront',
          backField: 'idCardBack'
        },
        {
          type: 'switch',
          name: 'isAvailable',
          label: '启用状态',
          color: '#07C160'
        },
        {
          type: 'switch',
          name: 'receiveNotifications',
          label: '接收通知',
          color: '#1687F7'
        },
        {
          type: 'slot',
          slotName: 'custom-field',
          label: '个人特点标签',
          name: 'customTags',
          required: true
        }
      ],
      // 表单验证规则
      validRules: {
        name: {
          required: true,
          message: '请输入姓名'
        },
        age: {
          required: true,
          validator: value => value && parseInt(value) > 0 && parseInt(value) < 120,
          message: '请输入有效年龄'
        },
        gender: {
          required: true,
          message: '请选择性别'
        },
        // province: {
        //   required: true,
        //   message: '请选择服务城市'
        // },
        // city: {
        //   required: true,
        //   message: '请选择服务城市'
        // },
        language: {
          required: true,
          message: '请选择擅长语言'
        },
        phone: {
          required: true,
          validator: value => value && /^1[3-9]\d{9}$/.test(value),
          message: '请输入正确的手机号码'
        },
        // idcard: {
        //   required: true,
        //   validator: value => value && /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(value),
        //   message: '请输入正确的身份证号码'
        // },
        avatar: {
          required: true,
          message: '请上传形象照',
          type: 'image'
        },
        idCardFront: {
          required: true,
          message: '请上传身份证人像面',
          type: 'image'
        },
        idCardBack: {
          required: true,
          message: '请上传身份证国徽面',
          type: 'image'
        }
      }
    }
  },
  methods: {
    // 表单提交
    submitForm(formData) {
      console.log('表单提交数据:', formData);
      
      // 添加自定义标签到表单数据
      formData.customTags = this.selectedTags;
      
      // 显示提交结果
      uni.showModal({
        title: '表单数据',
        content: JSON.stringify(formData, null, 2),
        showCancel: false
      });
    },
    
    // 切换标签选择状态
    toggleTag(tag) {
      if(this.selectedTags.includes(tag)) {
        this.selectedTags = this.selectedTags.filter(item => item !== tag);
      } else {
        this.selectedTags.push(tag);
      }
      // 更新表单数据
      this.form.customTags = this.selectedTags;
    }
  }
}
</script>

<style lang="scss">
.container {
  background: #F4F6FA;
  min-height: 100vh;
  padding: 30rpx 0;
}

.test-form {
  width: 686rpx;
  margin: 0 auto;
  background: #FFFFFF;
  border-radius: 16rpx;
  padding: 30rpx;
  box-sizing: border-box;
}

.form-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #1D2029;
  margin-bottom: 30rpx;
  text-align: center;
}

/* 自定义字段样式 */
.custom-field {
  width: 100%;
}

.custom-field-title {
  font-weight: 500;
  font-size: 28rpx;
  color: #1D2029;
  margin-bottom: 20rpx;
}

.custom-field-content {
  width: 100%;
}

.custom-buttons {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20rpx;
}

.custom-tag {
  height: 60rpx;
  padding: 0 20rpx;
  line-height: 60rpx;
  background: #F4F6FA;
  border-radius: 30rpx;
  font-size: 26rpx;
  color: #666;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
}

.custom-tag.active {
  background: #E6F7F2;
  color: #00B484;
  border: 1px solid #00B484;
}

.selected-tags-preview {
  font-size: 26rpx;
  color: #666;
  padding: 10rpx 0;
}
</style>
