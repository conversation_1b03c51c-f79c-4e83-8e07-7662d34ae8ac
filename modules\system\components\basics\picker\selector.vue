<template>
  <view>
    <picker @change="bindPickerChange" :value="indexEq" :range="list" range-key="name">
      <slot></slot>
    </picker>
  </view>
</template>

<script>
export default {
  name: 'Selector',
  data() {
    return {
      index: 0,
      array: [{ name: '中国' }, { name: '美国' }, { name: '巴西' }, { name: '日本' }]
    }
  },
  props: {
    // 标题名称
    title: {
      type: String,
      default() {
        return ''
      }
    },
    value: {
      type: String,
      default() {
        return ''
      }
    },

    list: {
      type: Array,
      default() {
        return ''
      }
    },
    indexEq: {
      type: Number,
      default() {
        return 0
      }
    }
  },
  methods: {
    bindPickerChange: function(e) {

      if (!e.target.value) {
        return false
      }
      this.$emit('returnFn', this.list[e.target.value])
      this.$emit('value', this.list[e.target.value])
      this.index = e.target.value
    }
  }
}
</script>

<style scoped>

</style>
