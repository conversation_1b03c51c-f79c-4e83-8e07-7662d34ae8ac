<template>
  <view>
    <page>
      <view slot="content">
        <view class="header bdb">
          <view class="back" @tap="$navto.back(1)"></view>
          <view class="title">姓名</view>
          <view class="success">完成</view>
        </view>
        <view class="title-one">
          <input type="text" placeholder="请输入名字" v-model="name">
          <view class="details" @tap="detail"></view>
        </view>
      </view>
    </page>
  </view>
</template>

<script>
export default {
  data() {
    return {
      name: ''
    }
  },
  methods: {
    detail() {
      this.name = ''
    }
  }
}
</script>

<style lang="scss" scoped>
  .header{
    background: #fff;
    display: flex;
    justify-content: space-between;
    padding: 30upx;
    align-items: center;
    .back{
      @include iconImg(30, 32, '/system/logo/icon-back.png');
    }
    .title{
      font-size: 36upx;
    }
    .success{
      font-size: 30upx;
      color: #FF9051FF;
    }
  }
  .title-one{
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: #fff;
    input{
    font-size: 30upx;
    }
    .details{
      @include iconImg(30, 30, '/system/logo/icon-backone.png');
    }
  }

</style>
