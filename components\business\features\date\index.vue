<!-- 下拉日期组件<title-jump-date title="日期" :date="regForm.date" @returnFn="dateReturnFn"></title-jump-date>
></title-jump-date> -->
<!--停止使用-->
<template>
  <view class="title-jump" :class="{ bdt: bdt, bdb: bdb }">
    <text class="l-t"
      >{{ title }}<em class="xingxing" v-if="isRequired">*</em></text
    >
    <picker
      :disabled="disabled"
      mode="date"
      :value="date"
      :start="startDate"
      :end="endDate"
      @change="returnFn"
    >
      <em class="icon-gengduo jump"></em>
      <text class="l-r">
        {{ dateText }}
      </text>
    </picker>
  </view>
</template>

<script>

export default {

  data() {
    return {
      startDate: '',
      endDate: '',
      dateText: ''
    }
  },
  props: {
    // 是否禁用disable
    disabled: {
      type: <PERSON>olean,
      default() {
        return false
      }
    },
    // 标题名称
    title: {
      type: String,
      required: false,
      default() {
        return ''
      }
    },
    // 返回content
    date: {
      type: String,
      required: false,
      default() {
        return ''
      }
    },
    // border-top
    bdt: {
      type: Boolean,
      required: false,
      default() {
        return true
      }
    },
    // border-bottom
    bdb: {
      type: Boolean,
      required: false,
      default() {
        return true
      }
    },
    isRequired: {
      type: Boolean,
      default() {
        return false
      }
    },
    obj: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  watch: {
    date: {
      handler(val) {
        console.log(val, '1')

        this.watchDataMain(val)
      },
      deep: true
    }
  },
  mounted() {
    this.watchDataMain(this.date)
  },
  methods: {
    returnFn(e) {
      if (JSON.stringify(this.obj) === '{}') {
        this.dateText = e.target.value
        this.$emit('returnFn', e.target.value)
      } else {
        this.obj.date = e.target.value
        this.dateText = e.target.value

        this.$emit('returnFn', this.obj)
      }
    },
    watchDataMain(val) {
      console.log(val, '2', this)
      this.dateText = val
      this.date = val
    }
  }
}
</script>

<style lang="scss" scoped>
.title-jump {
  height: 88upx;
  overflow: hidden;
  background-color: #ffffff;
  .l-t {
    float: left;
    margin-left: 30upx;
    line-height: 88upx;
    color: #333333;
    font-size: 32upx;
    @include ellipsis();
    width: 280upx;
  }
  .l-r {
    float: right;
    font-size: 32upx;
    color: #000000;
    line-height: 88upx;
  }
  .jump {
    width: 34upx;
    height: 34upx;
    float: right;
    margin: 26upx 20upx 0 6upx;
  }
  .xingxing {
    color: #f85e4c;
    padding-top: 6upx;
    font-size: 32upx;
    display: inline-block;
  }
}
</style>
