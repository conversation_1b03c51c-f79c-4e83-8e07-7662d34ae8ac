/**
 * 对日期操作方法：
 * 获取日期，转换日期格式等等
 */
export default {

    /**
     * Parse the time to string
     * @param {(Object|string|number)} time
     * @param {string} cFormat
     * @returns {string | null}
     */
    parseTime(time, cFormat) {
        if (arguments.length === 0 || !time) {
            return null
        }
        const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
        let date
        if (typeof time === 'object') {
            date = time
        } else {
            if ((typeof time === 'string')) {
                if ((/^[0-9]+$/.test(time))) {
                    // support "1548221490638"
                    time = parseInt(time)
                } else {
                    // support safari
                    // https://stackoverflow.com/questions/4310953/invalid-date-in-safari
                    time = time.replace(new RegExp(/-/gm), '/')
                }
            }

            if ((typeof time === 'number') && (time.toString().length === 10)) {
                time = time * 1000
            }
            date = new Date(time)
        }
        const formatObj = {
            y: date.getFullYear(),
            m: date.getMonth() + 1,
            d: date.getDate(),
            h: date.getHours(),
            i: date.getMinutes(),
            s: date.getSeconds(),
            a: date.getDay()
        }
        const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {
            const value = formatObj[key]
                // Note: getDay() returns 0 on Sunday
            if (key === 'a') {
                return ['日', '一', '二', '三', '四', '五', '六'][value]
            }
            return value.toString().padStart(2, '0')
        })
        return time_str
    },
    /**
     * 获取时间戳
     * @param {date} 日期
     */
    getStamp(date) {
        const d = date.replace(new RegExp(/-/gm), '/') // '/'为兼容ios
        return (new Date(d)).getTime()
    },
    /**
     * 单字符格式化
     * @param str
     * @returns {string}
     */
    dateFormatter(str) {
        if (!str) return '00'
        const s = str.toString()
        return s.length > 1 ? s : '0' + s
    },

    /** 时间转换
     *
     * @param timeStamp
     * @param format
     * @returns {{year: number, month: (*|string), day: (*|string), hours: (*|string), minutes: (*|string), seconds: (*|string), week: number, weekDay: *, timeStamp: *}}
     */
    turnDate(timeStamp, format) {
        timeStamp = timeStamp || ''
        let date = ''
        if (!timeStamp) {
            date = new Date()
            timeStamp = date.getTime()
        } else {
            date = new Date(timeStamp)
        }
        const year = date.getFullYear() + ''
        const month = this.dateFormatter((date.getMonth() + 1))
        const day = this.dateFormatter(date.getDate())
        const hours = this.dateFormatter(date.getHours())
        const minutes = this.dateFormatter(date.getMinutes())
        const seconds = this.dateFormatter(date.getSeconds())
        const week = date.getDay() // 星期
        const weekArr = {
            1: '星期一',
            2: '星期二',
            3: '星期三',
            4: '星期四',
            5: '星期五',
            6: '星期六',
            0: '星期日'
        }

        const obj = {
            year: year,
            month: month,
            day: day,
            hours: hours,
            minutes: minutes,
            seconds: seconds,
            week: week,
            weekDay: weekArr[week],
            timeStamp: timeStamp
        }
        return obj
    },
    /**
     *
     * 日期格式转成文字描述，比如：10分钟前
     *
     * @param timeStamp 时间戳（当前的时间）
     * @param currentStamp 时间戳（固定的时间）
     * @returns {string}
     */
    formatDate(timeStamp, currentStamp) {
        if (timeStamp && timeStamp.toString().length !== 13) {
            timeStamp = +new Date(timeStamp)
        }
        const thatDateObj = this.turnDate(timeStamp)
        const curDateObj = this.turnDate(currentStamp)
        const thatDate = thatDateObj.year + '-' + thatDateObj.month + '-' + thatDateObj.day
        const curDate = curDateObj.year + '-' + curDateObj.month + '-' + curDateObj.day
        let ts = '此刻'

        if (thatDate === curDate) { // 当天
            const hour = Math.abs(parseInt((timeStamp - currentStamp) / 1000 / 3600)) // 小时
            const minutes = Math.abs(parseInt((timeStamp - currentStamp) / 1000 / 60)) // 分钟
            const seconds = Math.abs(parseInt((timeStamp - currentStamp) / 1000)) // 秒
            if (seconds > 0 && seconds < 60) {
                ts = parseInt(seconds) + '秒前'
            } else if (minutes > 0 && minutes < 60) {
                ts = parseInt(minutes) + '分钟前'
            } else if (hour > 0 && hour < 24) {
                ts = parseInt(hour) + '小时前'
            }
        } else { // 一天之后的
            const day = Math.abs(parseInt((timeStamp - currentStamp) / 1000 / 3600 / 24)) // 日期
            if (day < 2) {
                // ts = '昨天'
              // hours: hours,
              //   minutes: minutes,
              //   seconds: seconds,
                const curDate = this.turnDate(currentStamp)
                ts = curDate.year + '-' + curDate.month + '-' + curDate.day + ' '+ curDate.hours + ":" + curDate.minutes
            } else if (day < 30) {
                // ts = parseInt(day) + '天前'
                const curDate = this.turnDate(currentStamp)
                ts = curDate.year + '-' + curDate.month + '-' + curDate.day + ' '+ curDate.hours + ":" + curDate.minutes
            } else {
                const curDate = this.turnDate(currentStamp)
                ts = curDate.year + '-' + curDate.month + '-' + curDate.day + ' '+ curDate.hours + ":" + curDate.minutes
            }
        }
        return ts
    },
    /**
     * 两个时间比较大小
     *
     * @param date1 格式：Date对象 如：Thu Dec 05 2019 11:40:50 GMT+0800
     * @param date2 格式：Date对象 如：Thu Dec 05 2019 11:40:50 GMT+0800
     * @returns 1: 代表date2大； 2：代表date1等于代表date2； 3：代表date1大; false: 代表格式有误
     */
    compareDate(date1, date2) {
        if (date1 instanceof Date && date2 instanceof Date) {
            const oDate1 = date1
            const oDate2 = date2
            if (oDate1.getTime() < oDate2.getTime()) {
                return '1' // 第二个大
            } else if (oDate1.getTime() === oDate2.getTime()) {
                return '2'
            } else if (oDate1.getTime() > oDate2.getTime()) {
                return '3' // 第一个大
            }
        } else {
            return false
        }
    },
  /**
   * 获取本月第一天和最后一天（月末和月初）
   */
  getSameMonthOneAndLast(){
      var nowDate = new Date();
      var cloneNowDate = new Date();
      var fullYear = nowDate.getFullYear();
      var month = nowDate.getMonth() + 1; // getMonth 方法返回 0-11，代表1-12月
      var endOfMonth = new Date(fullYear, month, 0).getDate(); // 获取本月最后一天
      function getFullDate(targetDate) {
        var D, y, m, d;
        if (targetDate) {
          D = new Date(targetDate);
          y = D.getFullYear();
          m = D.getMonth() + 1;
          d = D.getDate();
        } else {
          y = fullYear;
          m = month;
          d = date;
        }
        m = m > 9 ? m : '0' + m;
        d = d > 9 ? d : '0' + d;
        return y + '-' + m + '-' + d;
      };
      var endDate = getFullDate(cloneNowDate.setDate(endOfMonth)) + ' 23:59:59';//当月最后一天
      var starDate = getFullDate(cloneNowDate.setDate(1)) + ' 00:00:00';//当月第一天
      return [starDate,endDate]
    },
  /**
   * 获取昨天的日期
   */
  getYesterday(){
    var date = new Date();
    var y = date.getFullYear();
    var m = date.getMonth() + 1;
    var d = date.getDate() - 1;
    if (d == "0") {
      m = m - 1;
      var temp = new Date(y, m, d);
      d = temp.getDate();
    }
    var Yesterday = [y, m, d];
    Yesterday = Yesterday.join("-");
    return  Yesterday
  }
}
