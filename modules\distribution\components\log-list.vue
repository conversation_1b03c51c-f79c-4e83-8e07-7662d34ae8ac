<template>
  <view>
    <view class="item" v-for="item in list" :key="item.id">
      <view class="item-left">
        <view class="item-title">{{ item.remark }}</view>
        <view class="item-time">{{ item.createTimeText }}</view>
      </view>
      <view class="item-right">
        <view class="item-price">+¥{{ item.rewardAmount }}</view>
        <view class="item-ratio">比例：{{ item.ratioText }}%</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    list: {
      type: Array,
      default: () => ([])
    }
  }
}
</script>

<style lang="scss" scoped>
.item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  &+.item {
    border-top: 1px solid #F2F3F7;
  }
}

.item-left {
  width: 0;
  flex: 1;
}

.item-title {
  font-weight: 500;
  font-size: 28rpx;
  color: #000000;
  line-height: 40rpx;
  padding-bottom: 6rpx;
}

.item-time {
  font-size: 24rpx;
  color: #868C9C;
  line-height: 34rpx;
}

.item-right {
  text-align: right;
}

.item-price {
  font-weight: 500;
  font-size: 36rpx;
  color: #FF553B;
  line-height: 50rpx;
}

.item-ratio {
  font-size: 24rpx;
  color: #4E5569;
  line-height: 34rpx;
}
</style>