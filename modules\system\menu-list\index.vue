<template>
  <page>
    <view slot="content" class="main-html">
      <view class="main">
        <view class="content">
          <view class="main-empty" v-if="$validate.isNull(permission)">
            <view class="middle">
              <em class="icon-empty-data"></em>
              <text>
                暂无权限
              </text>
            </view>
          </view>
          <view class="main-content" v-if="!$validate.isNull(permission)">
            <view class="men-search" v-if="!isSwitch">
              <search placeholder="搜索名称" :placeholderStyle="'text-align: center;'" :fixed="false" v-model="searchName" @changeSearch="changeSearch"></search>
              <view class="anchor-main-tabs">
                <tabs-sticky :overflowX="true" v-model="anchorCurIndex" :fixed="false" :tabs="anchorTabs" @change="anchorChangeTab"></tabs-sticky>
              </view>
            </view>
            <view class="m-main">
              <scroll-view scroll-y="true" class="scroll-view-main" :scroll-into-view="scrollIntoView">
                <view class="m-main-content">
                  <view class="main-menu-list" v-if="!searchName">
                    <view class="selected-menu-list" >
                      <view class="title clear-float">
                        <text class="l">我的应用</text>
                        <text class="r" v-if="!isSwitch&&selectedMenuList.length > 0" @tap="edit">编辑</text>
                      </view>
                      <view class="content-main">
                        <view class="menu" v-if="selectedMenuList.length > 0">
                          <view class="menu-list">
                            <view class="ul">
                              <view class="list clear-float" v-for="(lItem, lIndex) in selectedMenuList" :key="lIndex">
                                <view class="li" v-for="(item,index) in lItem" :key="index" @tap="jumpNavto(item)">
                                  <!--                      <view class="li" v-for="(item,index) in lItem" :key="index" @tap="jumpNavto(item)">-->
                                  <view class="bg-img" :style="{backgroundImage: 'url(' + item.imgUrl + ')'}"></view>
                                  <text>{{item.name}}</text>
                                  <text v-if="isSwitch" class="icon-close-black-circle" @tap.stop="delSelectedMenu(item)"></text>
                                </view>
                              </view>
                            </view>
                          </view>
                        </view>
                      </view>
                    </view>
                    <view class="all-menu-list">
                      <view class="content-main">
                        <view class="menu" :id="'anchor' + value.code" :key="key" v-for="(value,key) in systemMenuList" v-if="value.list.length >0">
                          <view class="title">
                            <view class="title-li">
                              <text>{{value.name}}</text>
                            </view>
                          </view>
                          <view class="menu-list">
                            <view class="ul">
                              <view class="list clear-float" v-for="(lItem, lIndex) in value.list" :key="lIndex">
                                <view class="li" v-for="(item,index) in lItem" :key="index" @tap="jumpNavto(item)">
                                  <!--                      <view class="li" v-for="(item,index) in lItem" :key="index" @tap="jumpNavto(item)">-->
                                  <view class="bg-img" :style="{backgroundImage: 'url(' + item.imgUrl + ')'}"></view>
                                  <text>{{item.name}} </text>
                                  <text class="icon-close-black-circle add" v-if="isSwitch" @tap.stop="addSelectedMenu(item)"></text>
                                </view>
                              </view>
                            </view>
                          </view>
                        </view>
                      </view>
                    </view>
                  </view>
                  <view class="main-menu-list" v-if="searchName">
                    <view class="selected-menu-list search-content" v-if="searchMenuList.length > 0">
                      <view class="content-main">
                        <view class="menu">
                          <view class="menu-list">
                            <view class="ul">
                              <view class="list clear-float" v-for="(lItem, lIndex) in searchMenuList" :key="lIndex">
                                <view class="li" v-for="(item,index) in lItem" :key="index" @tap="jumpNavto(item)">
                                  <view class="bg-img" :style="{backgroundImage: 'url(' + item.imgUrl + ')'}"></view>
                                  <text>{{item.name}} </text>
                                </view>
                              </view>
                            </view>
                          </view>
                        </view>
                      </view>
                    </view>
                  </view>
                </view>
              </scroll-view>
            </view>
          </view>
          <view class="operating-btn clear-float" v-if="isSwitch">
            <text class="btn-bg cancel" @tap="close">取消</text>
            <text class="btn-bg submit" @tap="submit">完成</text>
          </view>
        </view>
      </view>
    </view>
  </page>
</template>

<script>
import { mapState } from 'vuex'
import search from '@/components/basics/form/search'
import TabsSticky from '@/components/basics/tabs-sticky'

export default {
  components: {
    search,
    TabsSticky
  },
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $validate: this.$validate,
      $static_ctx: this.$static_ctx,
      scrollIntoView: '',
      anchorCurIndex: 0,
      anchorTabs: [],
      menuListInit: {}, // 菜单初始数据---剩余菜单
      menuListInitTemporary: {}, // 菜单初始数据---剩余菜单-临时存储
      systemMenuList: {}, // 剩余菜单---二维数组
      selectedMenu: [], // 已选菜单,
      selectedMenuTemporary: [], // 已选菜单--临时存储,
      selectedMenuList: [], // 已选菜单-二位数组
      isSwitch: false, // 控制开关
      searchName: '', // 搜索内容
      searchMenuList: [] // 搜索出来的菜单列表
    }
  },
  onShow() {
    this.sysuserentryQueryList()
  },
  computed: {
    ...mapState('user', {
      curSelectUserInfo: state => state.curSelectUserInfo,
      codeUserInfo: state => state.codeUserInfo, // 当前登录用户信息
      permission: state => state.permission
    })
  },
  methods: {
    /**
     * 返回上一级页面
     */
    navBack() {
      this.$navto.back(1)
    },
    /**
     * 进入菜单页面
     */
    jumpNavto(obj) {
      if (this.isSwitch) {
        return
      }
      this.close()
      // this.$common.setKeyVal('system', 'isOnShow', true)
      // this.$navto.push(obj.url)
      if (obj.url) {
        this.$navto.push(obj.url, obj.obj)
      } else {
        this.$uniPlugin.toast('暂不开放此功能')
      }
    },
    anchorChangeTab(index) {
      this.scrollIntoView = `anchor${this.anchorTabs[index].code}`
    },
    /**
     * 添加我的应用
     */
    addSelectedMenu(item) {
      if (this.selectedMenu.length === 9) {
        this.$uniPlugin.toast('最多选择9个我的应用')
        return
      }
      /**
       * 我的应用
       */
      this.selectedMenu.push(item)
      this.selectedMenuList = this.$common.arrayType(this.selectedMenu, 5)

      /**
       * 剩余应用
       */
      for (const a in this.menuListInit) {
        for (const b in this.menuListInit[a].list) {
          if (this.menuListInit[a].list[b].eq === item.eq && this.menuListInit[a].list[b].module === item.module) {
            this.menuListInit[item.module].list.splice(b, 1)
          }
        }
      }

      const menuListInit = this.$common.deepCloneObj(this.menuListInit)
      for (const value in menuListInit) {
        menuListInit[value].list = this.$common.arrayType(menuListInit[value].list, 5)
      }
      this.systemMenuList = menuListInit
    },
    /**
     * 编辑
     */
    edit() {
      this.isSwitch = true
    },
    /**
     * 取消
     */
    close() {
      // this.isSwitch = false
      // this.selectedMenu = this.selectedMenuTemporary
      // this.menuListInit = this.menuListInitTemporary
      this.isSwitch = false
      // 已选应用初始数据
      this.selectedMenu = this.$common.deepCloneObj(this.selectedMenuTemporary)
      this.selectedMenuList = this.$common.arrayType(this.selectedMenu, 5)

      // 剩余应用数据
      this.menuListInit = this.$common.deepCloneObj(this.menuListInitTemporary)
      this.menuListInitTemporary = this.$common.deepCloneObj(this.menuListInit)
      const systemMenuListObj = this.$common.deepCloneObj(this.menuListInit)
      for (const value in systemMenuListObj) {
        systemMenuListObj[value].list = this.$common.arrayType(systemMenuListObj[value].list, 5)
      }
      this.systemMenuList = systemMenuListObj
    },
    // 锚点数组组装
    anchorDataFn() {
      const that = this
      const listObj = JSON.parse(JSON.stringify(that.systemMenuList))
      const anchorObj = {}
      const anchorTabs = []
      for (const key in listObj) {
        if (listObj[key].list.length > 0) {
          anchorObj[key] = listObj[key]
        }
      }
      for (const key in anchorObj) {
        anchorTabs.push(anchorObj[key])
      }
      that.anchorTabs = anchorTabs
    },
    /**
     * 保留操作应用
     */
    submit() {
      /**
       * 自动添加满9个我的应用
       */
      const menuListItem = this.$common.deepCloneObj(this.menuListInit)
      if (this.selectedMenu.length < 9) {
        for (const a in this.menuListInit) {
          for (const b in this.menuListInit[a].list) {
            if (this.selectedMenu.length === 9) {
              break
            } else {
              const obj = this.menuListInit[a].list[b]
              this.selectedMenu.push(obj)
              for (const c in menuListItem[a].list) {
                if (obj.code === menuListItem[a].list[c].code) {
                  menuListItem[a].list.splice(c, 1)
                }
              }
            }
          }
        }
        this.selectedMenuList = this.$common.arrayType(this.selectedMenu, 5)
        this.menuListInit = this.$common.deepCloneObj(menuListItem)
        const menuListInit = this.$common.deepCloneObj(menuListItem)
        for (const value in menuListInit) {
          menuListInit[value].list = this.$common.arrayType(menuListInit[value].list, 5)
        }
        this.systemMenuList = menuListInit
      }

      this.isSwitch = false

      const menuListParams = []
      const that = this
      if (that.selectedMenu.length === 0) {
        this.$uniPlugin.toast('请添加应用')
        return
      }
      for (let a = 0; a < that.selectedMenu.length; a++) {
        menuListParams.push({
          code: that.selectedMenu[a].code,
          order: a + 1,
          type: that.curSelectUserInfo.type && that.curSelectUserInfo.type.key === that.$constant.noun.staffCode.toString() ? that.$constant.noun.staffCode : that.$constant.noun.parentsCode,
          userId: that.codeUserInfo.id
        })
      }
      // 去除重复
      const paramsList = that.removeDuplicates(menuListParams, 'code')
      that.$api.sys.sysuserentryInsertList(paramsList).then(res => {
        that.$uniPlugin.toast('我的应用保存成功')
        that.$common.setKeyVal('system', 'isOnShow', false, true)

        // 已选应用初始数据
        that.selectedMenuTemporary = that.$common.deepCloneObj(that.selectedMenu) || {}
        // 剩余应用数据
        that.menuListInitTemporary = that.$common.deepCloneObj(that.menuListInit)
        that.anchorDataFn()
      })
    },
    /**
     * 去除重复
     * @returns {Promise<unknown>}
     */
    removeDuplicates(arr, field) {
      const hash = {}
      arr = arr.reduce((item, next) => {
        hash[next[field]] ? '' : hash[next[field]] = true && item.push(next)
        return item
      }, [])
      return arr || []
    },
    /**
     * 删除我的应用
     * @param item
     */
    delSelectedMenu(item) {
      // if (this.selectedMenu.length === 4) {
      //   this.$uniPlugin.toast('至少保留4个我的应用')
      //   return
      // }
      /**
       * 返回剩余应用
       */
      this.menuListInit[item.module].list.splice(item.eq - 1, 0, item)
      /**
       * eq排序
       */
      function up(x, y) {
        return x.eq - y.eq
      }
      this.menuListInit[item.module].list.sort(up)
      const menuListInit = this.$common.deepCloneObj(this.menuListInit)
      for (const value in menuListInit) {
        menuListInit[value].list = this.$common.arrayType(menuListInit[value].list, 5)
      }
      this.systemMenuList = menuListInit

      /**
       * 删除我的应用
       */
      for (const i in this.selectedMenu) {
        if (this.selectedMenu[i].eq === item.eq && this.selectedMenu[i].module === item.module) {
          this.selectedMenu.splice(i, 1)
        }
      }
      this.selectedMenuList = this.$common.arrayType(this.selectedMenu, 5)
    },
    /**
     * 查詢快捷入口
     */
    sysuserentryQueryList() {
      const that = this
      const systemMenuListItem = that.$common.getKeyVal('system', 'systemMenuList', true)
      const systemMenuList = {}
      for (const a in systemMenuListItem) {
        systemMenuList[a] = systemMenuListItem[a]
      }
      const menuListInit = that.$common.deepCloneObj(systemMenuList)
      const selectedMenu = []
      for (const a in systemMenuList) {
        for (const b in systemMenuList[a].list) {
          selectedMenu.push(systemMenuList[a].list[b])
        }
      }
      // 已选应用初始数据
      that.selectedMenu = that.$common.deepCloneObj(selectedMenu)
      that.selectedMenuTemporary = that.$common.deepCloneObj(that.selectedMenu) || {}
      that.selectedMenuList = that.$common.arrayType(that.selectedMenu, 5)

      // 剩余应用数据
      that.menuListInit = that.$common.deepCloneObj(menuListInit)
      that.menuListInitTemporary = that.$common.deepCloneObj(that.menuListInit)
      const systemMenuListObj = that.$common.deepCloneObj(that.menuListInit)
      for (const value in systemMenuListObj) {
        systemMenuListObj[value].list = that.$common.arrayType(systemMenuListObj[value].list, 5)
      }
      that.systemMenuList = systemMenuListObj
      that.anchorDataFn()
    },
    /**
     * 快捷入口搜索
     */
    changeSearch(obj) {
      const that = this
      that.searchName = obj.name
      that.close()
      const systemMenuListItem = that.$common.getKeyVal('system', 'systemMenuList', true)
      const systemMenuList = {}
      for (const a in systemMenuListItem) {
        if (a !== 'nurseryClientStoreMessagePanel') {
          systemMenuList[a] = systemMenuListItem[a]
        }
      }
      const searchMenuList = []
      for (const a in systemMenuList) {
        for (const b in systemMenuList[a].list) {
          if (systemMenuList[a].list[b].name.indexOf(that.searchName) > -1) {
            searchMenuList.push(systemMenuList[a].list[b])
          }
        }
      }
      that.searchMenuList = this.$common.arrayType(searchMenuList, 5)
    }
  }
}
</script>

<style lang="scss" scoped>
.h-194{
  height: 194upx;
}
.main-html{
  height: 100%;
  .main{
    height: 100%;
    position: relative;
    background-color: #f7f7f7;
    .content{
      height: 100%;
      .main-content{
        height: 100%;
        .men-search{

        }
        .m-main {
          height: calc(100% - 194upx);
          .scroll-view-main {
            height: 100%;
            .m-main-content {
              overflow: hidden;
              background-color: #f7f7f7;
            }
          }
        }
        .main-menu-list{
          padding-bottom: 20upx;
          height: 100%;
          box-sizing: border-box;
          &:last-of-type {
            padding-bottom: 0;
          }
          .selected-menu-list, .all-menu-list{
            background: #FFFFFF;
            .title{
              padding:30upx;
              font-size: 32upx;
              color: #333333;
              font-weight: bold;
            }
            .content-main{
              .menu{
                background: #fff;
                .title{
                  /*line-height: 54upx;*/
                  font-size: 32upx;
                  color: #333333;
                  font-weight: bold;
                  padding: 30upx;
                  background-color: #fff;
                  /*margin-bottom: 30upx;*/
                  .title-li{
                    font-size: 32upx;
                    display: inline-block;
                  }
                  em{
                    margin-left: 8upx;
                    vertical-align: middle;
                  }
                  .icon-logo-down{
                    @include iconImg(28, 28, '/business/icon-logo-down.png');
                  }
                  .icon-logo-right{
                    @include iconImg(28, 28, '/business/icon-logo-right.png');
                  }
                }
                .menu-list{
                  width:100%;
                  background-color: #ffffff;
                  .ul{
                    width: 100%;
                    padding: 10upx 40upx 30upx 40upx;
                    box-sizing: border-box;
                    overflow: hidden;
                    .list{
                      margin-bottom: 40upx;
                      .li{
                        float: left;
                        width: 100upx;
                        margin-right: 40upx;
                        text-align: center;
                        font-size: 0;
                        position: relative;
                        .icon-close-black-circle{
                          position: absolute;
                          right: 0;
                          top: 14upx;
                          -webkit-transform: translateY(-50%);
                          transform: translateY(-50%);
                          @include iconImg(40,40,'/system/icon-menu-list-del.png');
                        }
                        .class-tag{
                          position: absolute;
                          right: 8upx;
                          top: 50upx;
                          width: 30upx;
                          height: 30upx;
                          line-height: 30upx;
                          text-align: center;
                          @include rounded(50%);
                          background-color: #187fff;
                          color: #ffffff;
                          font-size: 16upx;
                          border:2upx solid #ffffff;
                        }
                        .add{
                          @include iconImg(40, 40, '/system/icon-menu-list-add.png');
                        }
                        image{
                          width: 90upx;
                          height: 90upx;
                          display: inline-block;
                        }
                        .bg-img{
                          width: 90upx;
                          height: 90upx;
                          background-size: 100%;
                          background-repeat: no-repeat;
                          display: block;
                          margin: 0 auto;
                        }
                        text{
                          font-size: 24upx;
                          line-height: 36upx;
                          color: #333333;
                          display: block;
                          text-align: center;
                          margin-top: 12upx;
                        }
                      }
                      .li:last-of-type{
                        margin-right: 0;
                      }
                    }
                    .list:last-of-type{
                      margin-bottom: 0;
                    }
                  }
                }
              }
              .menu:last-of-type{
                .title{
                  margin-bottom: 0;
                }
              }
            }
          }
          .selected-menu-list{
            .title{
              .l{
                float: left;
              }
              .r{
                float: right;
                color: #3270F9;
              }
            }
            .content-main{
              position: relative;
              .main-empty{
                height: 300upx;
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: #fff;
                .middle{
                  width: 100%;
                  position: absolute;
                  left: 0;
                  top: 50%;
                  -webkit-transform: translateY(-50%);
                  transform: translateY(-50%);
                  .icon-no-data{
                    margin: 0 auto;
                    display: block !important;
                    @include iconImg(300,200,'/system/invalid/icon-no-data.png');
                  }
                  text{
                    text-align: center;
                    color: #999;
                    font-size: 24upx;
                    line-height: 36upx;
                    display: inline-block;
                    width: 100%;
                    margin-top: 16upx;
                  }
                }
              }
            }
          }
          .all-menu-list{
            margin-top: 20upx;
          }
        }
        .search-content{
          padding-top:20upx;
          margin-top: 30upx;
        }
      }
      .operating-btn{
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 194upx;
        padding: 30upx;
        box-sizing: border-box;
        background: #FFFFFF;
        @include downBoxShadow(-4upx, 0, 6upx, 1, 222, 222, 222);
        .cancel{
          width: 40%;
          float: left;
          background: #FFFFFF;
          border: 2upx solid #999999;
          color: #999999;
        }
        .submit{
          width: 40%;
          float: right;
        }
      }
    }
  }
}
.main-empty{
  /*height: calc(100vh - 100upx);*/
  height: 100%;
  position: relative;
  background-color: #fff;
  .middle{
    width: 100%;
    position: absolute;
    left: 0;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    .icon-empty-data{
      margin: 0 auto;
      display: block !important;
      @include iconImg(200,200,'/system/invalid/icon-empty-data.png');
    }
    text{
      text-align: center;
      color: #999;
      font-size: 24upx;
      line-height: 36upx;
      display: inline-block;
      width: 100%;
    }
  }
}
</style>
