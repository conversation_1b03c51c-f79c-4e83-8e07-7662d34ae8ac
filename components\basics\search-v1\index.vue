<!-- 搜索栏组件 <search :placeholder="***"  :value="***" @input="onKeyInput" ></search> -->
<template>
  <view class="search" :class="{ fixed: fixed }" :style="{ top: top + 'upx' }" @tap="searcFn()">
    <view
      class="input"
      :class="{ 'width-auto': !rightText && !isIcon }"
    >
      <em class="icon-view-l icon-search"></em>
      <swiper class="input-view" circular vertical :autoplay="autoplay" :interval="interval"
        :duration="duration">
        <swiper-item class="item-view" v-for="(item,index) in hotSearchList" :key="index" @touchmove.stop="">
          <view class="swiper-item uni-bg-red">{{ item.word }}</view>
        </swiper-item>
      </swiper>

      <view class="line"></view>
      <view class="search-text" @tap="returnFn">
        <view class="text">搜索</view>
        <view v-if="isIcon" class="icon icon-screen"></view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'Search',
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $accurateConversion: this.$accurateConversion,
      file_ctx: this.file_ctx,
      $static_ctx: this.$static_ctx,
      $timePlugin: this.$timePlugin,
      $validate: this.$validate,
      inputValue: '',
      timer: undefined,
      width: 0,
      autoplay: true,
      interval: 3000,
      duration: 1000
    }
  },
  props: {
    hotSearchList:{ // 热搜列表
      type: Array,
      default() {
        return []
      }
    },
    // 提示文本style
    placeholderStyle: {
      type: String,
      default() {
        return ''
      }
    },
    isIcon: {
      type: Boolean,
      default() {
        return false
      }
    },
    rightText: {
      type: String,
      default() {
        return ''
      }
    },
    value: {
      type: String,
      required: false,
      default: ''
    },
    placeholder: {
      type: String,
      default() {
        return '搜索'
      }
    },
    k: { // 字段名称
      type: String,
      default() {
        return 'name'
      }
    },
    top: {
      type: String,
      default() {
        return '88'
      }
    },
    fixed: {
      type: [String, Boolean],
      default() {
        return true
      }
    }
  },
  watch: {
    value: {
      handler(val) {
        this.inputValue = val
      },
      deep: true
    },
    /** 监听手机输入 */
    inputValue() {
      if (this.timer) {
        clearTimeout(this.timer)
      }
      this.timer = setTimeout(() => {
        this.inputValue = this.$validate.trim(this.inputValue)
        const obj = {}
        obj[this.k] = this.inputValue
        this.$emit('changeSearch', obj)
      }, 500)
    }
  },
  mounted() {
    // this.getRightWidth()

  },
  methods: {
    searcFn() {
      this.$navto.push('CommonSystemSearch')
    },
    // 清除输入框
    remove() {
      this.inputValue = ''
    },
    onKeyInput(e) {
      this.inputValue = e.target.value
    },
    returnFn() {
      this.$emit('returnFn', this.inputValue)
    },
    getRightWidth() {
      this.$nextTick(() => {
        const that = this
        let width = 0
        // #ifdef H5
        width = this.$el.querySelector('.right-text').clientWidth;
        that.width = width
        // #endif
        // #ifdef MP
        var query = this.createSelectorQuery();
        query.select('.right-text').boundingClientRect(
          function (rect) {
            // console.log(rect)
            if (rect.width) {
              that.width = rect.width;
            }
          }
        ).exec();
        // #endif
        // this.width = width

      })


    }
  }
}
</script>

<style lang="scss" scoped>
.fixed {
  //position: fixed;
}
.search {
  /*position: fixed;*/
  /*top:200upx;*/
  left: 0;
  right: 0;
  height: 102upx;
  // padding: 16upx 32rpx;
  padding: 16upx 0;
  //background-color: $pageBg;
  z-index: 9999;
  box-sizing: border-box;
  .input {
    background-color: #fff;
    @include rounded(50upx);
    //border: 2upx solid #efefef;
    position: relative;
    width: calc(100% - 140upx);
    vertical-align: middle;
    display: inline-block;
    box-sizing: border-box;
    border: 2rpx solid #00B484;
    .icon-view-l {
      position: absolute;
      top: 50%;
      -webkit-transform: translateY(-50%);
      transform: translateY(-50%);
      left: 30upx;
      width: 34upx;
      height: 34upx;
    }
    .input-view {
      display: flex;
      align-items: center;
      width: 100%;
      height: 72upx;
      line-height: 72upx;
      // padding: 0 60upx 0 60upx;
      padding: 0 114rpx 0 78rpx;
      box-sizing: border-box;
      .item-view{
        display: flex;
        align-items: center;
        .swiper-item{
          font-size: 28rpx;
          color: #A5AAB8;
        }
      }
    }
    .icon-view-r {
      @include iconImg(34, 34, "/business/icon-close-black-circle.png");
      position: absolute;
      top: 50%;
      -webkit-transform: translateY(-50%);
      transform: translateY(-50%);
      right: 18upx;
    }
  }
  .placeholder-f-s-28 {
    font-size: 28upx;
  }
  .width-auto {
    width: 100%;
  }
  .line{
    position: absolute;
    right: 112rpx;
    top: 50%;
    transform: translateY(-50%);
    width: 2rpx;
    height: 40rpx;
    z-index: 9999;
    background: #DBDDE0;
  }
  .search-text{
    display: flex;
    justify-content: center;
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    // padding: 0 32rpx 0 24rpx;
    // width: 56rpx;
    width: 112rpx;
    height: 40rpx;
    z-index: 9999;
    .text{
      font-size: 28rpx;
      color: #00B484;
      margin-top: -2rpx;
    }
  }
  .right-text {
    vertical-align: middle;
    display: inline-block;
    // width: 140upx;
    .text {
      text-align: right;
      font-size: 32upx;
      line-height: 48upx;
      color: #666;
      display: inline-block;
      vertical-align: middle;
      width: calc(100% - 40upx);
    }
    .icon {
      display: inline-block;
      vertical-align: middle;
      margin-left: 8upx;
    }
    .icon-screen {
      @include iconImg(32, 32, "/business/icon-screen.png");
    }
  }
}
</style>
