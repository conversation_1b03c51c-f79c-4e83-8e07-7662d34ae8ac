import MessageDefault from './MessageDefault'

export default class HandleOrderEnd extends MessageDefault {
    constructor (websocket) {
        super()
        this.websocket = websocket
    }

    match (dataJson) {
        const { ORDER_END_CMD } = this.chatCmd
        return dataJson.cmd === ORDER_END_CMD
    }

    processMessage (dataJson) {
        const data = dataJson.data
        const { ORDER_GUIDE } = this.chatCmd
        let orderDetail = this.$common.getKeyVal('chat', 'orderDetail', false)
        const chatItem = this.$common.getKeyVal('chat', 'chatItem', false)
        this.$common.setKeyVal('chat', 'guideMode', '')

        // 是否为当前聊天对象
        if (this.$validate.isNull(orderDetail) || data.id !== orderDetail.id) return
        data.endTime = typeof data.endTime === 'string' ? Number(data.endTime) : data.endTime
        // 更新订单详情
        this.$common.setKeyVal('chat', 'orderDetail', data, false)
        setTimeout(() => {
            uni.pageScrollTo({
                scrollTop: 99999,
                duration: 0
            })
        }, 0)

        // 是否为转接触发的订单结束cmd
        if (data.transferOrderEnd == 1) return

        // 执行问诊后咨询节点
        const nodereplyconfig = this.$common.getKeyVal('chat', 'nodereplyconfig').filter(item => item.nodeType === 2)
        if (!this.$validate.isNull(nodereplyconfig)) {
            let dto =  {
                cmd: ORDER_GUIDE,
                data: {
                    orderId: chatItem.orderId,
                    userId: chatItem.userId,
                    chatUserId: chatItem.chatUserId,
                    nodeConfigId: nodereplyconfig[0].id
                }
            }
            this.websocket.webSocketSend(ORDER_GUIDE, dto)
        }
    }
}