<template>
  <view class="main">
    <view class="fast-invite-box">
      <text class="fast-invite-text">一键邀请，获取更多解答</text>
      <button
        type="primary"
        size="mini"
        class="btn"
        @tap="fastInvite"
        :disabled="$validate.isNull(list)"
      >
        一键邀请
      </button>
    </view>
    <view style="flex: 1;">
      <scroll-refresh
        :fixed="false"
        :up="upOption"
        :down="downOption"
        @returnFn="returnFn"
        @scrollInit="scrollInit"
        :no-page="true"
        :zPageDefault="{
          loadingMoreEnabled: false,
        }"
      >
        <view class="main-content">
          <view
            class="item"
            v-for="item in list"
            :key="item.id"
            hover-class="message-hover-class"
          >
            <image
              class="avatar"
              mode="aspectFill"
              :src="item.headPath || defaultAvatar"
            ></image>
            <view class="user-box-content">
              <text class="user-name">{{ item.nickName }}</text>
              <text class="user-type">{{ item.vTypeText }}</text>
            </view>
            <button
              type="primary"
              size="mini"
              class="btn"
              @tap="invite(item)"
              :disabled="item.inviteStatus === 1"
            >
              {{ item.inviteStatus === 1 ? "已邀请" : "邀请" }}
            </button>
          </view>
        </view>
      </scroll-refresh>
    </view>
  </view>
</template>

<script>
import { param } from '../../../../../utils'
export default {
  props: {
    params: {
      type: Object,
      default: () => {
        return null
      }
    }
  },
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $accurateConversion: this.$accurateConversion,
      file_ctx: this.file_ctx,
      $static_ctx: this.$static_ctx,
      $timePlugin: this.$timePlugin,
      $validate: this.$validate,
      mescroll: null, // mescroll实例对象
      downOption: {
        auto: false // 不自动加载
      },
      upOption: {
        auto: false // 不自动加载
      },
      isInit: false, // 列表是否已经初始化
      list: [],
      defaultAvatar: this.file_ctx + 'static/image/system/avatar/icon-default-v2.png'
    }
  },
  methods: {
    // 一键邀请
    fastInvite() {
      this.$uniPlugin.modal('', '是否确认一键邀请？', {
        showCancel: true, // 是否显示取消按钮，默认为 true
        cancelText: '取消', //  取消按钮的文字，默认为"取消"，最多 4 个字符
        cancelColor: '#000000', //  取消按钮的文字颜色，默认为"#000000"(H5、微信小程序、百度小程序)
        confirmText: '确定', //  确定按钮的文字，默认为"确定"，最多 4 个字符
        confirmColor: '#15c398', //  确定按钮的文字颜色，H5平台默认为"#007aff"，微信小程序平台默认为"#3CC51F"，百度小程序平台默认为"#3c76ff"（H5、微信小程序、百度小程序）
        fn: async (n) => {
          if (n) {
            this.$uniPlugin.loading('请求中...')
            const params = {
              messageId: this.params.postMessageId,
              isAssociatedUser: 2, // 是否选择关联的用户 1-是 2-否
              accountIdList: this.list.map(item => item.beInviterAccountId)
            }
            const res = await this.$api.postmessage.inviteBatch(params)
            this.$uniPlugin.hideLoading()
            this.$uniPlugin.toast(res.msg)
            this.init()
          }
        }
      })
    },
    async invite(e) {
      this.$uniPlugin.loading('请求中...')
      const params = {
        messageId: this.params.postMessageId,
        isAssociatedUser: 2, // 是否选择关联的用户 1-是 2-否
        userType: e.type,
        accountIdList: [e.accountId]
      }
      const res = await this.$api.postmessage.inviteBatch(params)
      this.$uniPlugin.hideLoading()
      this.$uniPlugin.toast(res.msg)
      // 修改列表邀请状态
      this.list = this.list.map(item => {
        return {
          ...item,
          inviteStatus: item.id === e.id ? 1 : item.inviteStatus
        }
      })

      // 全部已邀请时 刷新列表
      const isAllInvite = this.list.some(item => item.inviteStatus !== 1)
      if (!isAllInvite) {
        this.init()
        return
      }
    },
    init(val) {
      this.mescroll.triggerDownScroll()
    },
    scrollInit(scroll) {
      scroll.optUp.page.num = 1
      scroll.optUp.page.size = 10
      this.mescroll = scroll
    },
    returnFn(obj) {
      const that = this
      setTimeout(function () {
        that.$ext.community.fansrecordNotInviteDoctorList({ postMessageId: that.params.postMessageId }).then(res => {
          that.list = res.data || []
          obj.successCallback && obj.successCallback(res.data || [])
        })
      }, that.$constant.noun.scrollRefreshTime)
    },
    navtoGo(url, obj = {}) {
      this.$navto.push(url, obj)
    }
  }
}
</script>

<style lang="scss" scoped>
.main {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  padding-bottom: env(safe-area-inset-bottom);
}
.main-content {
  flex: 1;
  width: 100%;
  padding: 0 32rpx;
  background-color: #fff;
  box-sizing: border-box;
  overflow-y: auto;
}
.item {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1rpx solid #f5f5f5;
  padding: 32rpx 0;
  box-sizing: border-box;
  .avatar {
    width: 90rpx;
    height: 90rpx;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 24rpx;
  }
  .user-box-content {
    height: 90rpx;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  .user-name {
    font-size: 32rpx;
    line-height: 46rpx;
    font-weight: 500;
    color: #000;
  }
  .user-type {
    font-size: 26rpx;
    line-height: 36rpx;
    color: #333;
  }
}
.btn {
  margin: 0;
  padding: 0 32rpx;
  display: flex;
  align-items: center;
  background-color: #4cd964;
  border: none;
  height: 56upx;
  line-height: 56upx;
  border-radius: 28rpx;
  font-size: 28rpx;
}
.fast-invite-box {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #e4e4e4;
  box-sizing: border-box;
  .fast-invite-text {
    flex: 1;
    font-size: 26rpx;
    color: #333;
    line-height: 42rpx;
  }
}
</style>