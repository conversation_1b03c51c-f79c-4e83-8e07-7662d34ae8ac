<template>
  <view class='work-bench'>
    <template v-if="navCurrent == 0">
      <view class="my-data">
        <view :style="'height:' + statusBarHeight + 'px;'"></view>
        <view class="top-nav">
          <view class="top-nav-l">
            <image mode="aspectFit" @click.stop="handleBack" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-service-left-arrows.png'" class="header-search-img"/>
            <view class="nav-img">
              <image class="img" :src="file_ctx + detailObj.logo"></image>
            </view>
            <view class="nav-text">{{ detailObj.providerName }}</view>
          </view>
          <view class="top-nav-c"></view>
        </view>
        <view class="data-overview">
          <view class="overview-head">
            <view class="overview-head-l">
              <view class="title">今日数据概览</view>
              <view class="time">数据时间：{{ timeMap[1] }}</view>
            </view>
            <view class="overview-head-r" @tap="handleClickDataOverview">更多<image class="head-r-img" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-service-right-arrows.png'"></image></view>
          </view>
          <view class="overview-content">
            <view class="overview-item" @click="handleClickPopup">
              <view class="title">{{ orderStatisticList[0].name }}</view>
              <view class="num">{{ orderStatisticList[0].count }}</view>
              <!-- <view class="percentage" :style="{color:orderStatisticList[0].yesterdayChain.includes('+') ? '#008763' : '#FF5500'}">{{ orderStatisticList[0].yesterdayChain }}<uni-icons class="my-icons" :size="10" color="#A5AAB8" type="right" /></view> -->
            </view>
            <view class="overview-item">
              <!-- <view class="title">{{ postReadCountStatisticObj.name }}</view> -->
              <view class="title">帖子阅读次数</view>
              <view class="num">{{ postReadCountStatisticObj.count }}</view>
              <!-- <view class="percentage" :style="{color:postReadCountStatisticObj.yesterdayChain.includes('+') ? '#008763' : '#FF5500'}">{{ postReadCountStatisticObj.yesterdayChain }}<uni-icons class="my-icons" :size="10" color="#A5AAB8" type="right" /></view> -->
            </view>
            <view class="overview-item">
              <!-- <view class="title">{{ incomeStatisticObj.name }}</view> -->
              <view class="title">收益</view>
              <view class="num">{{ incomeStatisticObj.count }}</view>
              <!-- <view class="percentage" :style="{color:incomeStatisticObj.yesterdayChain.includes('+') ? '#008763' : '#FF5500'}">{{ incomeStatisticObj.yesterdayChain }}<uni-icons class="my-icons" :size="10" color="#A5AAB8" type="right" /></view> -->
            </view>
          </view>
        </view>
      </view>
      <scroll-refresh class="scroll-refresh-main" :isShowEmptySwitch="true" :fixed="false" :up="upOption" :down="downOption" @returnFn="returnFn" @scrollInit="scrollInit">
        <view class="order-list">
          <view class="order-header">
            <view class="order-header-l">订单中心</view>
            <view class="order-header-r" @tap="handleClickOrderMore">详情<image class="head-r-img" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-service-right-arrows.png'"></image></view>
          </view>
          <view class="l-main">
            <scroll-view class="tabs-sticky" :scroll-x="true">
              <view class="tabs-sticky-body white-space">
                <view :class="tabCurrent == index ? 'tab active' :'tab'" v-for="(item,index) in tabs" :key="item.id" @click.stop.prevent="changeTab(index)">{{item.name}}</view>
              </view>
            </scroll-view>
          </view>
          <view class="order-item" v-for="item in contentList" :key="item.id" @tap="handleClickOrderItem(item.id)">
            <view class="order-item-t">
              <view class="order-item-t-l">{{ item.serviceName }}</view>
              <view
                class="order-item-t-r"
                :style="{color:item.orderState == 1 ? '#1687F7' : item.orderState == 2 ? '#FF5500' : (item.orderState == 5 || item.orderState == 6) ? '#00B484' : item.orderState == 7 ? '#1D2029' : '#868C9C'}">
                {{ handleFilterOrderState(item.orderState) }}
              </view>
            </view>
            <view class="order-item-b">
              <view class="cost" v-if="item.bookName">就诊人：<span>{{ item.bookName }}</span></view>
              <view class="cost" v-if="item.payPrice">服务费用：<span>￥{{ item.payPrice / 100 }}</span></view>
              <view class="time">陪诊时间：<span>{{ item.startTime }}~{{ item.endTime }}</span></view>
              <view class="hospital">就诊医院：<span>{{ item.hospitalName }}</span></view>
              <view class="hospital">订单号：<span>{{ item.id }}</span></view>
              <view class="accompany-teacher" v-if="item.employeeId">陪诊师：<view class="teacher-box"><view class="img"><image :src="file_ctx + item.avatar"></image></view> <span>{{ item.employeeName }}</span></view></view>
              <!-- <button class="evaluate" v-if="item.orderState === 7 && item.commentState !== 2">前往评价</button> -->
              <view class="my-evaluate" v-if="item.commentState == 2">
                用户评价：
                <view class="evaluate-img1" v-if="item.star == 1"><image class="img" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-very-evaluate1.png'"></image></view>
                <view class="evaluate-img2" v-if="item.star == 2"><image class="img" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-very-evaluate2.png'"></image></view>
                <view class="evaluate-img3" v-if="item.star == 3"><image class="img" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-very-evaluate3.png'"></image></view>
                <view class="evaluate-img4" v-if="item.star == 4"><image class="img" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-very-evaluate4.png'"></image></view>
                <view class="evaluate-img5" v-if="item.star == 5"><image class="img" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-very-evaluate5.png'"></image></view>
              </view>
            </view>
          </view>

        </view>
      </scroll-refresh>
    </template>

    <MyData v-else :isShow="isShow" :detailObj="detailObj"></MyData>

    <view class="work-bench-bott">
      <view class="bench-bott-item" v-for="(item,index) in bottomList" :key="item.id" @click="handleClickJump(index)">
        <view class="bottom-item-img" v-if="navCurrent == index"><image class="img" :src="file_ctx + item.activeUrl"></image></view>
        <view class="bottom-item-img" v-else><image class="img" :src="file_ctx + item.url"></image></view>
        <view :class="navCurrent == index ? 'bottom-item-name active' : 'bottom-item-name'">{{item.name}}</view>
      </view>
    </view>
  </view>
</template>

<script>
  import TabsSticky from '@/components/basics/tabs-sticky-v3'
  import MyData from './components/my'
  import UniIcons from '@/components/uni/uni-icons/uni-icons'
  import options from '@/config/env/options'
  export default {
    components: {
      TabsSticky,
      MyData,
      UniIcons,
    },
    data(){
      return{
        file_ctx:this.file_ctx,
        mescroll: null, // mescroll实例对象
        downOption: {
          auto: false // 不自动加载
        },
        upOption: {
          auto: false, // 不自动加载
          empty: {
            top: 0,
            zIndex: 999,
          }
        },
        contentList:[],
        statusBarHeight:0,
        curIndex:0,
        tabs:[{name:'全部',id:null},{name:'待接入',id:1},{name:'待支付',id:2},{name:'待派单',id:3},{name:'待接单',id:4},{name:'待服务',id:5},{name:'服务中',id:6},{name:'已完成',id:7},{name:'已取消',id:8}],
        orderState:null,
        bottomList:[
          {id:1,name:'工作台',url:'static/image/business/accompany-doctor/icon-accompany-bottom-workbench-default.png',activeUrl:'static/image/business/accompany-doctor/icon-accompany-bottom-workbench.png'},
          {id:2,name:'我的',url:'static/image/business/accompany-doctor/icon-my.png',activeUrl:'static/image/business/accompany-doctor/icon-my-active.png'}
          ],
        navCurrent:0,
        tabCurrent:0,
        timeMap:[],
        orderStatisticList:null,
        postReadCountStatisticObj:null,
        incomeStatisticObj:null,
        isShow:false,
        detailObj:null,
        isShowPopup:false,
      }
    },
    onLoad(option){
      const query = this.$validate.isNull(this.$Route.query) ? option : this.$Route.query
      if(query?.isShow){
        this.isShow = query.isShow
        this.navCurrent = 1
        this.accompanyproviderQueryOne()
      }
      this.timeMap = this.getDatesOfTodayAndLastWeek()
      if(this.timeMap.length){
        this.accompanyBookTotalOrderStatistic()
        this.accompanyBookPostReadCountStatistic()
        this.accompanyBookIncomeStatistic()
        this.accompanyproviderQueryOne()
      }
    },
    onShow() {
      this.mescroll.triggerDownScroll()
    },
    mounted(){
      this.$nextTick(() => {
        this.init()
      })
      this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight // 获取设备的状态栏高度
    },
    methods:{
      handleClickOrderItem(id){
        this.$navto.push('accompanyDetails',{id:id,isServer:true})
      },
      handleClickOrderMore(){
        this.$navto.push('orderCenter')
      },
      handleClickDataOverview(){
        this.$navto.push('DataPanel')
      },
      handleClickPopup(){

      },
      formatDate(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要加1
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
      },
      getDatesOfTodayAndLastWeek() {
        const dates = [];
        const today = new Date();
        const lastMonth = new Date(today);
        lastMonth.setMonth(lastMonth.getMonth() - 1);

        if (lastMonth.getMonth() === 11 && today.getMonth() === 0) {
          lastMonth.setFullYear(lastMonth.getFullYear() - 1);
        }

        lastMonth.setDate(1);
        dates.push(this.$common.formatDate(lastMonth, 'yyyy-MM-dd HH:mm:ss'))
        dates.push(this.$common.formatDate(today, 'yyyy-MM-dd HH:mm:ss'))
        return dates;
      },

      accompanyproviderQueryOne(){
        // const providerId = this.$common.getKeyVal('user', 'providerId',true)
        this.$api.accompanyDoctor.accompanyproviderQueryOne({id:options.providerId}).then(res=>{
          this.detailObj = {...res.data,createTime:this.$common.formatDate(new Date(res.data.createTime), 'yyyy-MM-dd').replace(/-/g, '.')}
        })
      },
      // 数据统计-订单统计
      accompanyBookTotalOrderStatistic(){
        const providerId = this.$common.getKeyVal('user', 'providerId',true)
        let params = {
          // startTime:this.timeMap[0] + ' 00:00:00',
          startTime:this.timeMap[0],
          // endTime:this.timeMap[1] + ' 23:59:59',
          endTime:this.timeMap[1],
          providerId:providerId
        }
        this.$api.accompanyDoctor.accompanyBookTotalOrderStatistic(params).then(res=>{
          this.orderStatisticList = res.data
        })
      },
      // 数据统计-帖子阅读数统计
      accompanyBookPostReadCountStatistic(){
        const providerId = this.$common.getKeyVal('user', 'providerId',true)
        let params = {
          startTime:this.timeMap[0],
          endTime:this.timeMap[1],
          providerId:providerId
        }
        this.$api.accompanyDoctor.accompanyBookPostReadCountStatistic(params).then(res=>{
          this.postReadCountStatisticObj = res.data
        })
      },
      // 数据统计-收益统计
      accompanyBookIncomeStatistic(){
        const providerId = this.$common.getKeyVal('user', 'providerId',true)
        let params = {
          startTime:this.timeMap[0],
          endTime:this.timeMap[1],
          providerId:providerId
        }
        this.$api.accompanyDoctor.accompanyBookIncomeStatistic(params).then(res=>{
          this.incomeStatisticObj = res.data
        })
      },
      handleClickJump(index){
        this.navCurrent = index
      },
      handleFilterOrderState(type){
        switch(type){
          case 1:
            return '等待人工导诊'
          case 2:
            return '待支付'
          case 3:
            return '待派单'
          case 4:
            return '待接单'
          case 5:
            return '待服务'
          case 6:
            return '服务中'
          case 7:
            return '已完成'
          case 8:
            return '已取消'
        }
      },
      returnFn(obj) {
        const that = this
        const { centerUserId: userId = '' } = this.curSelectUserInfo || {}
        setTimeout(function () {
          let params = {
            current: obj.pageNum,
            size: obj.pageSize,
            condition: {
              orderState:that.orderState,
              // userId:userId,
            }
          }
          that.$api.accompanyDoctor.accompanybookQuery(params).then(res => {
            // let data = res.data.records.map(item=>({...item,listCover:isDomainUrl(item.listCover)}))
            // if(res.data)
            let data = res.data.records.map(item=>(
              {
                ...item,
                startTime:that.$common.formatDate(new Date(item.startTime), 'yyyy-MM-dd').replace(/-/g, '.'),
                endTime: item.endTime ? that.$common.formatDate(new Date(item.endTime), 'yyyy-MM-dd').replace(/-/g, '.') : '',
              }))
            if (obj.pageNum === 1) {
              that.contentList = []
            }
            that.contentList = [...that.contentList, ...data]
            obj.successCallback && obj.successCallback(data)
          })
        }, that.$constant.noun.scrollRefreshTime)
      },

      scrollInit(scroll) {
        scroll.optUp.page.num = 1
        scroll.optUp.page.size = 10
        this.mescroll = scroll
      },
      init() {
        this.$nextTick(() => {
          this.mescroll.triggerDownScroll()
        })
      },
      changeTab(index) {
        this.tabCurrent = index
        this.orderState = this.tabs[index].id
        this.init()
      },
      handleBack(){
        this.$navto.back(1)
      },
    },
 }
</script>

<style lang='scss' scoped>
  .work-bench{
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow-x: hidden;
    background-color: #F4F6FA;
    .scroll-refresh-main{
      flex: 1;
      // height: calc(100% - 60rpx);
      overflow-x: hidden;
      padding-bottom: 166rpx;
      background-color: #F4F6FA;
      ::v-deep .mescroll-empty-box{
        min-height: 0% !important;
        position: absolute !important;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
      .order-list{
        height: 100%;
        overflow-x: hidden;
        margin: 0 32rpx;
        background-color: #fff;
        border-radius: 16rpx;
        .order-header{
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 32rpx 24rpx;
          .order-header-l{
            font-size: 32rpx;
            font-weight: 600;
            color: #1D2029;
          }
          .order-header-r{
            display: flex;
            align-items: center;
            font-size: 24rpx;
            color: #868C9C;
            .head-r-img{
              display: flex;
              width: 32rpx;
              height: 32rpx;
            }
          }
        }
        .l-main{
          // position: sticky;
          // top: 0;
          // z-index: 999;
          padding:0 24rpx;
          .tabs-sticky{
            height: 60upx;
            .tabs-sticky-body{
              .tab{
                display: flex;
                justify-content: center;
                align-items: center;
                flex-shrink: 0;
                margin-right: 20rpx;
                width: 104rpx;
                height: 56rpx;
                background: #F4F6FA;
                border-radius: 8rpx;
                font-size: 24rpx;
                color: #1D2029;
              }
              .active{
                background: #D7FAF1;
                border: 1rpx solid #00B484;
                color: #00B484;
              }
            }
          }
        }
        .order-item{
          padding:32rpx 24rpx;
          margin-top: 20rpx;
          background-color: #fff;
          border-radius: 16rpx;
          .order-item-t{
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            .order-item-t-l{
              flex: 1;
              font-size: 32rpx;
              color: #1D2029;
              line-height: 44rpx;
              margin-right: 20rpx;
              /* 限制最多显示2行，超出显示省略号 */
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 2;
              overflow: hidden;
              text-overflow: ellipsis;
              word-break: break-all;
            }
            .order-item-t-r{
              font-size: 28rpx;
              color: #1687F7;
              line-height: 40rpx;
              flex-shrink: 0;
              min-width: 100rpx;
              text-align: right;
            }
          }
          .order-item-b{
            margin-top: 16rpx;
            .cost,.time,.hospital,.accompany-teacher{
              font-size: 24rpx;
              color: #4E5569;
              line-height: 34rpx;
              span{
                color: #1D2029;
              }
            }
            .time{
              margin: 8rpx 0;
            }
            .hospital{}
            .accompany-teacher{
              display: flex;
              .teacher-box{
                display: flex;
                align-items: center;
                .img{
                  width: 32rpx;
                  height: 32rpx;
                  border-radius: 50%;
                  overflow: hidden;
                  image{
                    width: 100%;
                    height: 100%;
                  }
                }
                span{
                  margin-left: 8rpx;
                  font-size: 22rpx;
                  color: #1D2029;
                  line-height: 32rpx;
                }
              }
            }
            .evaluate{
              display: flex;
              align-items: center;
              justify-content: center;
              height: 72rpx;
              width: 100%;
              margin-top: 24rpx;
              font-size: 26rpx;
              color: #00B484;
              background: #FFFFFF;
              border-radius: 36rpx;
              border: 1rpx solid #00B484;
              &::after{
                border: none !important;
              }
            }
            .my-evaluate{
              display: flex;
              align-items: center;
              height: 72rpx;
              background: #F4F6FA;
              border-radius: 8rpx;
              padding: 18rpx 16rpx 20rpx;
              box-sizing: border-box;
              margin-top: 24rpx;
              // .evaluate-img{
              //   display: flex;
              //   width: 132rpx;
              //   height: 32rpx;
              // }
              .evaluate-img1{
                width: 204rpx;
                height: 48rpx;
              }
              .evaluate-img2{
                width: 156rpx;
                height: 48rpx;
              }
              .evaluate-img3{
                width: 132rpx;
                height: 48rpx;
              }
              .evaluate-img4{
                width: 132rpx;
                height: 48rpx;
              }
              .evaluate-img5{
                width: 180rpx;
                height: 48rpx;
              }
            }
          }
        }
      }
    }
  }
  .white-space{
    white-space: nowrap;
    display: flex;
  }
  .img{
    width: 100%;
    height: 100%;
  }
  .my-data{
    position: relative;
    height: 520rpx;
    background: linear-gradient( 180deg, #00B484 0%, #F4F6FA 100%);
    margin-bottom: 40rpx;
  }
  .top-nav{
    // position: fixed;
    width: calc(100% - 16rpx);
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 40px;
    line-height: 40px;
    padding: 0 32rpx;
    // z-index: 999;
    // padding: 0 32rpx 0 24rpx;
    font-size: 32rpx;
    color: #FFFFFF;
    .top-nav-l{
      display: flex;
      align-items: center;
      .header-search-img{
        display: flex;
        width: 48rpx;
        height: 48rpx;
      }
      .nav-img{
        display: flex;
        width: 64rpx;
        height: 64rpx;
        margin-right: 12rpx;
        border-radius: 50%;
        overflow: hidden;
        image{
          width: 100%;
          height: 100%;
        }
      }
    }
    .top-nav-c{
      flex: 1;
      text-align: center;
      height: 44rpx;
      font-weight: 500;
      font-size: 32rpx;
      color: #2D2F38;
      line-height: 44rpx;
      margin-right: 48rpx;
    }
  }
  .data-overview{
    position: absolute;
    top: 204rpx;
    width: calc(100% - 116rpx);
    padding: 32rpx 24rpx;
    background-color: #fff;
    border-radius: 16rpx;
    margin: 0 32rpx;
    .overview-head{
      display: flex;
      justify-content: space-between;
      .overview-head-l{
        .title{
          font-size: 32rpx;
          color: #1D2029;
          line-height: 44rpx;
          font-weight: 600;
          margin-bottom: 8rpx;
        }
        .time{
          font-size: 24rpx;
          color: #A5AAB8;
          line-height: 34rpx;
        }
      }
      .overview-head-r{
        display: flex;
        align-items: center;
        font-size: 24rpx;
        color: #868C9C;
        line-height: 34rpx;
        .head-r-img{
          display: flex;
          width: 32rpx;
          height: 32rpx;
        }
      }
    }
    .overview-content{
      display: flex;
      flex-wrap: wrap;
      margin-top: 32rpx;
      padding: 0 22rpx 0 28rpx;
      .overview-item{
        display: flex;
        flex: 33.33333%;
        flex-direction: column;
        margin-bottom: 48rpx;
        .title{
          font-size: 24rpx;
          color: #1D2029;
          line-height: 34rpx;
        }
        .num{
          font-size: 32rpx;
          font-weight: 600;
          color: #1D2029;
          line-height: 44rpx;
        }
        .percentage{
          font-size: 24rpx;
          color: #008763;
          line-height: 34rpx;
        }
      }
    }
  }
  .work-bench-bott{
    position: fixed;
    width: 100%;
    bottom: 0;
    left:0;
    display: flex;
    // align-items: center;
    justify-content: space-around;
    // height: 98rpx;
    height: 166rpx;
    background-color: #fff;
    .bench-bott-item{
      display: flex;
      flex-direction: column;
      align-items: center;
      // justify-content: center;
      padding-top: 8rpx;
      .bottom-item-img{
        width: 56rpx;
        height: 56rpx;
        margin-bottom: 4rpx;
      }
      .bottom-item-name{
        font-size: 20rpx;
        color: #868C9C;
      }
      .active{
        color: #00B484;
      }
    }
  }
</style>
