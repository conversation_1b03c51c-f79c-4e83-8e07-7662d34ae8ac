<template>
  <page>
    <view slot="content" class="content">
      <!-- tab菜单-->
      <tabs-sticky
        v-model="curIndex"
        :fixed="false"
        :tabs="tabs"
        @change="changeTab"
      ></tabs-sticky>
      <view class="main">
        <swiper class="swiper" :current="curIndex" @change="swiperChange">
          <swiper-item>
            <list-item-recommend
              ref="listRecommendRef"
              :index="curIndex"
              :params="{postMessageId}"
            />
          </swiper-item>
          <swiper-item>
            <list-item-invite
              ref="listInviteRef"
              :index="curIndex"
              :params="{messageId: postMessageId}"
            />
          </swiper-item>
        </swiper>
      </view>
    </view>
  </page>
</template>

<script>
import TabsSticky from '@/components/basics/tabs-sticky'
import ListItemRecommend from './components/list-item-recommend.vue'
import ListItemInvite from './components/list-item-invite.vue'
export default {
  components: {
    TabsSticky,
    ListItemRecommend,
    ListItemInvite
  },
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $accurateConversion: this.$accurateConversion,
      file_ctx: this.file_ctx,
      $static_ctx: this.$static_ctx,
      $timePlugin: this.$timePlugin,
      $validate: this.$validate,
      curIndex: 0, // 当前菜单下标,
      tabs: [{ name: '推荐' }, { name: '已邀请' }],
      postMessageId: ''
    }
  },
  onLoad() {
    const query = this.$Route.query
    this.postMessageId = query.postMessageId
    this.init()
  },
  onShow() {
    
  },
  methods: {
    // 轮播菜单
    swiperChange(e) {
      this.changeTab(e.detail.current)
    },
    changeTab(index) {
      this.curIndex = index
    },
    navtoGo(url = '', obj = {}) {
      this.$navto.push(url, obj)
    },
    init(val) {
      this.$nextTick(() => {
        const curIndex = this.curIndex
        this.changeTab(curIndex)
        this.$refs.listRecommendRef.init()
        this.$refs.listInviteRef.init()
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
}
.main {
  flex: 1;
  .swiper {
    height: 100%;
  }
}
</style>
