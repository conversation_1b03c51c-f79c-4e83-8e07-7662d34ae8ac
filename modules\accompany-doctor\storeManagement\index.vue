<template>
    <view class="page">
        <!-- 门店管理 S -->
        <view class="storeBox" v-for="(item,index) in pageLiat" :key="index">
            <view class="title">门店管理</view>
            <view class="storeBoxChild">
                <view class="storeBoxSon" v-for="(son,k) in item.children" :key="k" @click="gotoPage(son.routerName)">
                    <image :src="file_ctx + son.icon" class="storeBoxSon-icon"></image>
                    <view class="storeBoxSon-title">{{son.title}}</view>
                </view>
            </view>
        </view>
        <!-- 门店管理 E -->
    </view>
</template>
<script>
export default {
    data() {
        return {
            file_ctx: this.file_ctx,
            pageLiat:[
                {title:'门店管理',
                    children:[
                        {title:'一键装修',icon:'static/image/business/accompany-doctor/fixtures.png',routerName:'oneKeyFixtures'},
                    ]
                },
            ]
        }
    },
    methods: {
        // 跳转订单详情页
        gotoPage(routerName) {
            this.$navto.push(routerName)
        },
    }
}
</script>
<style lang="scss" scoped>
.page{
    width: 100vw;
    height: 100vh;
    background-color: #F4F6FA;
    padding: 32rpx;
    box-sizing: border-box;
    .storeBox{
        width: 100%;
        height: 218rpx;
        background: #FEFEFE;
        border-radius: 24rpx 24rpx 24rpx 24rpx;
        margin-bottom: 24rpx;
        padding: 20rpx;
        box-sizing: border-box;
        .title{
            font-weight: bold;
            font-size: 28rpx;
            color: #333333;
            &::before{
                content: '';
                display: inline-block;
                width: 8rpx;
                height: 28rpx;
                background: #00B484;
                border-radius: 8rpx 8rpx 8rpx 8rpx;
                margin-right: 8rpx;
                transform: translateY(4rpx);
            }
        }
        .storeBoxChild{
            width: 100%;
            display: flex;
            padding: 24rpx 16rpx;
            column-gap: 80rpx;
            .storeBoxSon{
                text-align: center;
                .storeBoxSon-icon{
                    width: 56rpx;
                    height: 56rpx;
                    margin: 0rpx auto 8rpx;
                }
               .storeBoxSon-title{
                font-weight: 500;
                font-size: 24rpx;
                color: #333333;
                }
            }
        }
    }
}
</style>