<!-- 内容模块标题配件组件 <title-jump :title="xx" :text="xx" @returnFn = "returnFn"></title-jump> -->
<template>
  <view :style="'height:' + statusBarHeight + 'px;'"></view>
</template>

<script>
  export default {
    props: {

    },
    data() {
      return {
        statusBarHeight: 0
      }
    },
    watch: {},
    mounted() {
      this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight
    },
    methods: {

    }
  }
</script>

<style lang="scss" scoped>

</style>
