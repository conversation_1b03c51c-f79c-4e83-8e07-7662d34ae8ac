<template>
  <view class="m-main-body">
    <scroll-refresh class="scroll-refresh-main" :isShowEmptySwitch="true" :fixed="false" :up="upOption" :down="downOption" @returnFn="returnFn" @scrollInit="scrollInit">
      <view class="wrapper" :style="{height:contentList.length==0 && '0'}">
        <view class="wrapper-item" v-for="(item,index) in contentList" :key="index" @click="handleClickJump(item.id)">
          <view class="item-l">
            <image :src="item.logo"></image>
          </view>
          <view class="item-r">
            <view class="hospital">{{ item.hospitalName }}</view>
            <view class="type-list" v-if="item.service && item.service.split(',').length > 1">
              <view class="list-item" v-for="(item,index) in item.service" :key="index">医院类型</view>
            </view>
            <view class="list-item" v-else-if="item.service && item.service.split(',').length == 1">{{ item.service }}</view>

            <view class="address">
              <view class="address-img"><image class="img" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-hospital-address.png'"></image></view>
              <view class="text">{{ item.address }}</view>
            </view>
          </view>
        </view>
      </view>
    </scroll-refresh>
  </view>
</template>

<script>
  import { mapState } from 'vuex'
  import { isDomainUrl } from '@/utils/index.js'
  export default {
    props: {
      search:{
        type:String,
        default:''
      }
    },
    data(){
      return{
        file_ctx: this.file_ctx,
        $constant: this.$constant,
        mescroll: null, // mescroll实例对象
        downOption: {
          auto: false // 不自动加载
        },
        upOption: {
          auto: false, // 不自动加载
          empty: {
            top: 0,
            zIndex: 999,
          },
          onScroll: true
        },
        contentList:[],
      }
    },
    computed: {
      ...mapState('user', {
        accountId: state => state.accountId,
      }),
    },
    onLoad(){
      this.$nextTick(() => {
        this.init()
      })
    },
    mounted(){},
    methods:{
      handleClickJump(id){
        this.$navto.push('HospitalDetail', {id})
      },
      returnFn(obj) {
        const that = this
        setTimeout(function () {
          let params = {
            current: obj.pageNum,
            size: obj.pageSize,
            // ascs:'top',
            condition:{
              hospitalName:that.search
            }
          }
          that.$api.hospital.hospitalQueryPage(params).then(res => {
            // let data = res.data.records.map(item=>({...item,banner:item.banner.split(',').map(item=>(isDomainUrl(item))),listCover:isDomainUrl(item.listCover)}))
            let data = res.data.records
            if (obj.pageNum === 1) {
              that.contentList = []
            }
            that.contentList = [...that.contentList, ...data]
            obj.successCallback && obj.successCallback(data)
          })
        }, that.$constant.noun.scrollRefreshTime)
      },
      scrollInit(scroll) {
        scroll.optUp.page.num = 1
        scroll.optUp.page.size = 10
        this.mescroll = scroll
      },
      init() {
        this.$nextTick(() => {
          this.mescroll.triggerDownScroll()
        })
      },
    },
 }
</script>

<style lang='scss' scoped>
  .img{
    width: 100%;
    height: 100%;
  }
  .m-main-body{
    height: 100%;
    .scroll-refresh-main{
      height: 100%;
      .wrapper{
        height: 100%;
        // padding:24rpx 32rpx;
        .wrapper-item{
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 32rpx;
          background-color: #fff;
          border-bottom: 1rpx solid #EAEBF0;
          .item-l{
            display: flex;
            align-items: center;
            width: 144rpx;
            height: 144rpx;
            border-radius: 50%;
            image{
              width: 100%;
              height: 100%;
            }
          }
          .item-r{
            display: flex;
            flex-direction: column;
            flex: 1;
            justify-content: center;
            margin-left: 20rpx;
            .hospital{
              font-size: 30rpx;
              color: #1D2029;
              line-height: 42rpx;
            }
            .type-list{
              display: flex;
              margin: 8rpx 0 12rpx;
              .list-item{
                padding: 2rpx 8rpx;
                background: #FCF0DA;
                border-radius: 4rpx;
                margin-right: 8rpx;
                font-size: 20rpx;
                color: #693E13; 
                line-height: 28rpx;
                &:last-child{
                  margin-right: 0;
                }
              }
            }
            .address{
              display: flex;
              align-items: center;
              .address-img{
                display: flex;
                width: 24rpx;
                height: 24rpx;
              }
              .text{
                flex: 1;
                font-size: 22rpx;
                color: #4E5569;
                line-height: 32rpx;
              }
            }
          }
          &:last-child{
            margin-bottom: 0;
          }
        }
      }
    }
  }
</style>