<template>
  <page>
    <view slot="content" class="main-body">
      <view class="main">
        <scroll-view scroll-y="true" class="scroll-view-main">
          <view class="m-main">
            <!-- #ifndef MP-ALIPAY -->
            <view class="list m-t-20" @tap="navtoGo('About')">
              <view class="title">关于{{serverOptionsTitle}}</view>
              <view class="icon">
                <image class="width-height-atuo" :src="$static_ctx + 'image/business/icon-more.png'"/>
              </view>
            </view>
            <!-- #endif -->
            <!--    <view class="list m-t-20" @tap="clearCache">-->
            <!--      <view class="title">清除缓存</view>-->
            <!--      <view class="icon">-->
            <!--        <image class="width-height-atuo" :src="$static_ctx + 'image/business/icon-more.png'"/>-->
            <!--      </view>-->
            <!--      <view class="text">150M</view>-->
            <!--    </view>-->
            <view class="list m-t-20">
              <view class="title">发布版本</view>
              <view class="text">
                {{version_ctx}}
              </view>
            </view>
            <view class="list m-t-20" @tap="loginOut">
              <view class="title">退出登录</view>
              <view class="icon">
                <image class="width-height-atuo" :src="$static_ctx + 'image/business/icon-more.png'"/>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>
  </page>
</template>

<script>
import serverOptions from '@/config/env/options'
export default {
  name: 'Personal',
  components: {

  },
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $static_ctx: this.$static_ctx,
      version_ctx: this.version_ctx,
      roleList: [{ name: '' }]
    }
  },
  computed: {
    serverOptionsTitle(){
      return serverOptions.title
    }
  },
  methods: {
    navtoGo(url = '', obj = {}) {
      this.$navto.push(url, obj)
    },
    helpCenter() {
      this.$navto.push('WebHtmlView', { src: 'http://www.greenbon.cn/', title: serverOptions.title })
    },
    clearCache() {
      uni.showToast({ title: '清除缓存' })
    },
    loginOut() {
      const that = this
      uni.showModal({
        content: '是否退出登录？',
        confirmText: '确定',
        cancelText: '取消',
        success: function(data) {
          if (data.confirm) {
            that.$ext.user.loginOut()
          } else if (data.cancel) {

          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  .main-body{
    height: 100%;
    .main{
      height: 100%;
      .scroll-view-main {
        height: 100%;
      }
    }
  }
  .m-main {
    overflow: hidden;
    background-color: #f7f7f7;
    .list{
      background-color: #fff;
      padding: 0 30upx;
      height: 88upx;
      .title{
        display: inline-block;
        float: left;
        height: 88upx;
        line-height: 88upx;
        font-size: 32upx;
        color: #333;
      }
      .text{
        display: inline-block;
        float: right;
        height: 88upx;
        line-height: 88upx;
        font-size: 30upx;
        color: #999999;
        margin-right: 20upx;
      }
      .icon{
        display: inline-block;
        float: right;
        height: 44upx;
        width: 44upx;
        margin-top: 22upx;
      }
    }
  }
  .width-height-atuo{
    width: 100%;
    height: 100%;
  }
  .m-t-20{
    margin-top: 20upx;
  }
  .p-t-20{
    padding-top: 20upx;
  }
</style>
