<template>
  <page>
    <view slot="content" class="page-content">
      <m-nav-bar title="海报详情" left-icon="left" @clickLeft="back" />

      <view class="content-main">
        <!-- 海报展示区域 -->
        <view class="poster-container">
          <!-- 海报背景图 -->
          <image 
            :src="posterUrl" 
            mode="widthFix" 
            class="poster-background"
            @load="onPosterLoad"
          />
          
          <!-- 二维码图片，根据不同海报有不同位置 -->
          <view class="qrcode-wrapper" :style="{
            top: qrCodeStyle.y + 'rpx',
            left: qrCodeStyle.x + 'rpx',
            width: qrCodeStyle.width + 'rpx',
            height: (qrCodeStyle.height + 40) + 'rpx'
          }">
            <image 
              v-if="posterData && qrCodeUrl"
              :src="qrCodeUrl" 
              class="poster-qrcode"
              :class="{ 'ellipse-qrcode': posterData.isEllipse }"
              :style="{
                width: qrCodeStyle.width + 'rpx',
                height: qrCodeStyle.height + 'rpx'
              }"
              @load="logQrCodeStyle"
              show-menu-by-longpress
            />
          </view>

          <!-- 用户头像和名字 -->
          <view class="avatar-name-wrapper" :style="{
            top: avatarNameStyle.y + 'rpx',
            left: avatarNameStyle.x + 'rpx',
          }">
            <image 
              v-if="infoObj.avatarUrl"
              :src="formatAvatarUrl(infoObj.avatarUrl)" 
              class="user-avatar"
              :style="{
                width: avatarNameStyle.avatarWidth + 'rpx',
                height: avatarNameStyle.avatarHeight + 'rpx'
              }"
            />
            <text 
              v-if="infoObj.name"
              class="user-name"
            >{{infoObj.name}}</text>
          </view>
          
          <!-- 用于生成合成图的canvas，默认隐藏 -->
          <canvas 
            canvas-id="posterCanvas" 
            :style="{width: '750rpx', height: posterHeight + 'rpx'}"
            class="poster-canvas"
          ></canvas>
        </view>
        
        <!-- 生成图片组件 -->
        <poster 
          v-if="composingPoster"
          ref="poster"
          :list="posterList"
          background-color="#FFF"
          :width="750"
          :height="posterHeight"
          @on-success="posterSuccess"
          @on-error="posterError"
        ></poster>
      </view>
      
      <!-- 底部固定保存按钮 -->
      <view class="fixed-bottom-btn">
        <view class="save-button" @tap="generatePoster">
          保存到相册
        </view>
      </view>
    </view>
  </page>
</template>

<script>
import Poster from '@/components/business/zhangyuhao-poster/Poster.vue'
import { mapState } from 'vuex'
import serverOptions from '@/config/env/options'
import common from '@/common/util/main'

export default {
  components: {
    Poster
  },
  data() {
    return {
      id: null,
      type: '',
      posterTitle: '海报详情',
      qrCodeUrl: '', // 二维码URL
      composingPoster: false, // 是否正在合成海报
      generatedPosterUrl: '', // 生成后的海报URL
      posterHeight: 1000, // 默认海报高度
      posterLoaded: false,
      posterList: [], // 用于合成海报的元素列表
      posterData: null, // 当前海报的详细数据
      canvasWidth: 750, // canvas宽度，单位px
      canvasHeight: 1334, // canvas高度，单位px
      // 头像和名字样式（默认样式，各海报会覆盖）
      avatarNameStyle: {
        x: 50, // 默认水平位置
        y: 50, // 默认垂直位置
        avatarWidth: 86, // 头像宽度
        avatarHeight: 86, // 头像高度
      },
      defaultAvatar: '', // 默认头像路径
      imageBaseUrl: 'https://file.greenboniot.cn/', // 图片基础URL
      dailyPosterConfigs: {
        8: { // 专业护理海报
          qrCodeStyle: { x: 425, y: 1195, width: 225, height: 225 },
          showText: true,
          isEllipse: true, // 设置为椭圆形
          avatarNameStyle: { x: 100, y: 65, avatarWidth: 86, avatarHeight: 86 }
        },
        // 1: { // 周一海报
        //   qrCodeStyle: { x: 425, y: 810, width: 160, height: 160 },
        //   showText: true,
        //   avatarNameStyle: { x: 48, y: 40, avatarWidth: 86, avatarHeight: 86 }
        // },
        // 2: { // 周二海报
        //   qrCodeStyle: { x: 140, y: 80, width: 130, height: 130 },
        //   showText: false,
        //   avatarNameStyle: { x: 48, y: 120, avatarWidth: 86, avatarHeight: 86 }
        // },
        // 3: { // 周三海报
        //   qrCodeStyle: { x: 375, y: 850, width: 160, height: 160 },
        //   showText: true,
        //   avatarNameStyle: { x: 48, y: 40, avatarWidth: 86, avatarHeight: 86 }
        // },
        // 4: { // 周四海报
        //   qrCodeStyle: { x: 375, y: 20, width: 160, height: 160 },
        //   showText: true,
        //   avatarNameStyle: { x: 48, y: 280, avatarWidth: 86, avatarHeight: 86 }
        // },
        // 5: { // 周五海报
        //   qrCodeStyle: { x: 750, y: 400, width: 150, height: 150 },
        //   showText: true,
        //   avatarNameStyle: { x: 48, y: 80, avatarWidth: 86, avatarHeight: 86 }
        // },
        // 6: { // 周六海报
        //   qrCodeStyle: { x: 300, y: 700, width: 180, height: 180 },
        //   showText: true,
        //   avatarNameStyle: { x: 48, y: 40, avatarWidth: 86, avatarHeight: 86 }
        // },
        9: { // 周日海报
          qrCodeStyle: { x: 425, y: 1195, width: 225, height: 225 },
          showText: true,
          isEllipse: true, // 设置为椭圆形
          avatarNameStyle: { x: 48, y: 1200, avatarWidth: 86, avatarHeight: 86 }
        },
      },
      festivalPosterConfigs: {
        // 节日海报配置类似上面，根据需要添加
        101: {
          qrCodeStyle: { x: 40, y: 720, width: 225, height: 225 },
          showText: false,
          isEllipse: true, // 设置为椭圆形
          avatarNameStyle: { x: 40, y: 980, avatarWidth: 86, avatarHeight: 86 }
        },
        // 其他节日海报配置...
      }
    }
  },
  computed: {
    ...mapState('user', {
      userInfo: state => state.userInfo,
      fansRecord: state => state.fansRecord,
      accountId: state => state.accountId
    }),
    
    // 添加infoObj计算属性，参考my/index.vue的实现
    infoObj() {
      return {
        name: this.fansRecord?.nickName,
        avatarUrl: this.fansRecord?.headPath ? this.fansRecord.headPath : this.defaultAvatar,
        lightLogoPath: this.fansRecord?.lightLogoPath
      }
    },
    
    // 根据ID获取海报URL
    posterUrl() {
      if (!this.id) return '';
      
      // 不再根据ID写死URL，直接从会话存储中获取
      const posterData = uni.getStorageSync('selectedPosterData');
      if (posterData && posterData.imageUrl) {
        console.log('从缓存获取海报图片URL:', posterData.imageUrl);
        return posterData.imageUrl;
      }
      
      // 如果没有找到，仍然使用旧的逻辑作为备选
      if (this.type === 'daily') {
        const dailyPosterMap = {
          1: 'monday',
          2: 'tuesday',
          3: 'wednesday',
          4: 'thursday',
          5: 'friday',
          6: 'saturday',
          7: 'sunday',
          8: 'professional-care'
        };
        const imageName = dailyPosterMap[this.id] || 'monday';
        const url = this.$static_ctx + `image/business/hulu-v2/distribution-poster-${imageName}.png`;
        console.log('使用ID查找日常海报URL:', this.id, url);
        return url;
      } else {
        // 节日海报暂时使用默认图片
        const url = this.$static_ctx + 'image/business/hulu-v2/english-poster.png';
        console.log('使用默认节日海报URL:', url);
        return url;
      }
    },
    
    // 根据海报类型获取二维码样式
    qrCodeStyle() {
      if (!this.posterData) return { x: 275, y: 650, width: 200, height: 200 };
      return this.posterData.qrCodeStyle;
    }
  },
  onLoad(options) {
    // 获取路由参数
    this.id = parseInt(options.id || 0);
    this.type = options.type || 'daily';
    this.posterTitle = options.title || '海报详情';
    
    console.log('海报详情页参数:', options);
    
    // 从缓存获取选中的海报数据
    const selectedPosterData = uni.getStorageSync('selectedPosterData');
    console.log('从缓存获取的海报数据:', selectedPosterData);
    
    // 确认海报ID与选中的一致
    if (selectedPosterData && selectedPosterData.id === this.id) {
      console.log('海报ID匹配，使用缓存的海报数据');
    } else {
      console.log('海报ID不匹配或未找到缓存数据, 当前ID:', this.id, '缓存ID:', selectedPosterData?.id);
    }
    
    // 加载海报配置
    this.loadPosterConfig();
    
    // 获取分销二维码
    this.getQrcode();
  },
  methods: {
    // 格式化头像URL，添加基础路径前缀
    formatAvatarUrl(url) {
      if (!url) return '';
      
      // 如果已经是完整的URL（http或https开头），则直接返回
      if (url.startsWith('http://') || url.startsWith('https://')) {
        return url;
      }
      
      // 否则添加基础URL前缀
      return this.imageBaseUrl + url;
    },
    
    back() {
      uni.navigateBack();
    },
    
    // 加载海报配置
    loadPosterConfig() {
      // 尝试从会话存储中获取海报数据
      const posterData = uni.getStorageSync('selectedPosterData');
      
      // 检查缓存的海报ID是否与当前请求的ID匹配
      if (posterData && posterData.id === this.id) {
        console.log('从缓存加载海报数据:', posterData);
        
        // 设置二维码位置和样式属性
        let config;
        
        // 根据海报ID确定二维码位置和配置
        if (this.type === 'daily') {
          // 使用预设的二维码位置配置
          config = this.dailyPosterConfigs[this.id];
          console.log('日常海报配置:', config);
        } else {
          // 节日海报配置
          config = this.festivalPosterConfigs[this.id];
          console.log('节日海报配置:', config);
        }
        
        // 如果没有找到配置，使用默认配置
        if (!config) {
          config = {
            qrCodeStyle: { x: 375, y: 650, width: 200, height: 200 },
            showText: true,
            avatarNameStyle: { x: 48, y: 40, avatarWidth: 86, avatarHeight: 86 }
          };
          console.log('未找到对应ID的配置，使用默认配置');
        }
        
        // 设置头像名称样式
        if (config.avatarNameStyle) {
          this.avatarNameStyle = config.avatarNameStyle;
          console.log('设置头像名称样式:', this.avatarNameStyle);
        }
        
        // 合并配置和缓存数据
        this.posterData = {
          ...posterData,
          qrCodeStyle: config.qrCodeStyle,
          showText: config.showText || true,
          isEllipse: config.isEllipse || false,
          avatarNameStyle: config.avatarNameStyle
        };
        
        console.log('最终使用的海报数据:', this.posterData);
        return;
      }
      
      console.log('缓存中无匹配海报数据，使用预设配置');
      
      // 如果没有缓存数据，使用配置
      let config;
      
      if (this.type === 'daily') {
        // 获取对应ID的配置
        config = this.dailyPosterConfigs[this.id];
        // 确保数据存在
        if (!config) {
          config = this.dailyPosterConfigs[1] || {
            qrCodeStyle: { x: 375, y: 650, width: 200, height: 200 },
            showText: true,
            avatarNameStyle: { x: 48, y: 40, avatarWidth: 86, avatarHeight: 86 }
          };
        }
      } else {
        config = this.festivalPosterConfigs[this.id];
        
        // 如果是节日海报但没有对应配置
        if (!config) {
          config = {
            qrCodeStyle: { x: 375, y: 650, width: 200, height: 200 },
            showText: false,
            avatarNameStyle: { x: 48, y: 40, avatarWidth: 86, avatarHeight: 86 }
          };
        }
      }
      
      // 确保配置存在
      if (!config) {
        config = {
          qrCodeStyle: { x: 375, y: 650, width: 200, height: 200 },
          showText: false,
          avatarNameStyle: { x: 48, y: 40, avatarWidth: 86, avatarHeight: 86 }
        };
      }
      
      // 设置头像名称样式
      if (config.avatarNameStyle) {
        this.avatarNameStyle = config.avatarNameStyle;
        console.log('设置头像名称样式:', this.avatarNameStyle);
      }
      
      // 直接使用完整配置
      this.posterData = config;
      
      // 确保qrCodeStyle存在
      if (!this.posterData.qrCodeStyle) {
        this.posterData.qrCodeStyle = { x: 375, y: 650, width: 200, height: 200 };
      }
    },
    
    // 获取分销二维码
    async getQrcode() {
      try {
        uni.showLoading({ title: '加载中' });
        
        const codeUserInfo = common.getKeyVal('user', 'codeUserInfo', true);
        
        if (!codeUserInfo || !codeUserInfo.id) {
          uni.showToast({
            title: '获取用户信息失败',
            icon: 'none'
          });
          uni.hideLoading();
          return;
        }
        
        // 获取分销员信息
        const { data: distributorInfo } = await this.$api.distribution.accompanydistributorQueryOneByUserId({
          userId: codeUserInfo.id,
          providerId: serverOptions.providerId
        });
        
        if (!distributorInfo || !distributorInfo.id) {
          uni.showToast({
            title: '您还不是分销员',
            icon: 'none'
          });
          uni.hideLoading();
          return;
        }
        
        // 如果没有则插入参数生成二维码
        let { data } = await this.$api.common.codeInsert({
          path: 'pages/accompany-home/index?id=' + distributorInfo.id,
          appid: serverOptions.getoptions().appId,
          name: '分销海报二维码',
          businessType: 6,
          businessId: distributorInfo.id,
          CustomParameters: `providerId=${serverOptions.providerId}&businessId=${distributorInfo.id}`
        });
        
        this.qrCodeUrl = data.qrcodePath;
        
        // 如果是https开头，转换为http（为了适应微信小程序白名单）
        if (this.qrCodeUrl && this.qrCodeUrl.startsWith('http://')) {
          this.qrCodeUrl = this.qrCodeUrl.replace('http://', 'https://');
          console.log('已将二维码图片URL从http转换为https:', this.qrCodeUrl);
        }
        
        uni.hideLoading();
      } catch (error) {
        console.error('获取二维码失败:', error);
        uni.hideLoading();
        uni.showToast({
          title: '获取二维码失败',
          icon: 'none'
        });
      }
    },
    
    // 海报背景图加载完成
    onPosterLoad(e) {
      this.posterLoaded = true;
      
      try {
        // 获取系统信息
        const sysInfo = uni.getSystemInfoSync();
        // 设备像素比
        const pixelRatio = sysInfo.pixelRatio || 2;
        // 屏幕宽度(px)
        const screenWidth = sysInfo.screenWidth;
        
        // 计算canvas大小(实际绘制大小)
        this.canvasWidth = screenWidth;
        
        // 根据图片比例计算高度
        if (e.detail && e.detail.height && e.detail.width) {
          const ratio = e.detail.height / e.detail.width;
          // 实际显示高度
          this.posterHeight = Math.round(750 * ratio);
          // Canvas绘制高度
          this.canvasHeight = Math.round(screenWidth * ratio);
          
          console.log('Canvas尺寸:', this.canvasWidth, this.canvasHeight);
          console.log('图片比例:', ratio);
        }
      } catch (error) {
        console.error('获取系统信息失败:', error);
      }
    },
    
    // 海报生成成功回调
    posterSuccess(e) {
      this.composingPoster = false;
      this.generatedPosterUrl = e.detail.path;
      
      // 确保生成的海报URL使用http协议
      let posterPath = this.generatedPosterUrl;
      if (posterPath && posterPath.startsWith('http://')) {
        posterPath = posterPath.replace('http://', 'https://');
        console.log('已将生成的海报URL从http转换为https:', posterPath);
      }
      
      // 保存到相册
      uni.saveImageToPhotosAlbum({
        filePath: posterPath,
        success: () => {
          uni.hideLoading();
          uni.showToast({
            title: '保存成功',
            icon: 'success'
          });
        },
        fail: (err) => {
          uni.hideLoading();
          // 如果是用户拒绝授权导致的失败
          if (err.errMsg.indexOf('auth deny') >= 0) {
            uni.showModal({
              title: '提示',
              content: '需要您授权保存图片到相册',
              confirmText: '去设置',
              cancelText: '取消',
              success: (res) => {
                if (res.confirm) {
                  uni.openSetting();
                }
              }
            });
          } else {
            uni.showToast({
              title: '保存失败',
              icon: 'none'
            });
          }
        }
      });
    },
    
    // 海报生成失败回调
    posterError(error) {
      console.error('生成海报失败:', error);
      this.composingPoster = false;
      uni.hideLoading();
      uni.showToast({
        title: '海报生成失败',
        icon: 'none'
      });
    },
    
    // 生成海报
    generatePoster() {
      if (!this.posterLoaded || !this.qrCodeUrl) {
        uni.showToast({
          title: '图片正在加载，请稍候',
          icon: 'none'
        });
        return;
      }
      
      uni.showLoading({
        title: '正在生成海报...'
      });
      
      try {
        // 处理图片URL，确保使用http协议
        let posterUrlToUse = this.posterUrl;
        let qrCodeUrlToUse = this.qrCodeUrl;
        // 使用处理过的头像URL
        let avatarUrlToUse = this.formatAvatarUrl(this.infoObj.avatarUrl);
        
        // 将https转换为http
        if (posterUrlToUse && posterUrlToUse.startsWith('http://')) {
          posterUrlToUse = posterUrlToUse.replace('http://', 'https://');
          console.log('已将海报背景图片URL从http转换为https:', posterUrlToUse);
        }
        
        if (qrCodeUrlToUse && qrCodeUrlToUse.startsWith('http://')) {
          qrCodeUrlToUse = qrCodeUrlToUse.replace('http://', 'https://');
          console.log('已将二维码图片URL从http转换为https:', qrCodeUrlToUse);
        }
        
        // 下载需要的图片
        const downloadPromises = [
          // 下载背景图
          new Promise((resolve, reject) => {
            uni.downloadFile({
              url: posterUrlToUse,
              success: res => {
                if (res.statusCode === 200) {
                  resolve(res.tempFilePath);
                } else {
                  reject(new Error('下载背景图失败'));
                }
              },
              fail: err => reject(err)
            });
          }),
          // 下载二维码图
          new Promise((resolve, reject) => {
            uni.downloadFile({
              url: qrCodeUrlToUse,
              success: res => {
                if (res.statusCode === 200) {
                  resolve(res.tempFilePath);
                } else {
                  reject(new Error('下载二维码图失败'));
                }
              },
              fail: err => reject(err)
            });
          })
        ];
        
        // 添加头像下载（如果有）
        if (avatarUrlToUse) {
          downloadPromises.push(
            new Promise((resolve, reject) => {
              uni.downloadFile({
                url: avatarUrlToUse,
                success: res => {
                  if (res.statusCode === 200) {
                    resolve(res.tempFilePath);
                  } else {
                    resolve(null); // 下载失败时不中断整个流程
                  }
                },
                fail: err => resolve(null) // 下载失败时不中断整个流程
              });
            })
          );
        } else {
          downloadPromises.push(Promise.resolve(null));
        }
        
        Promise.all(downloadPromises).then(([bgImagePath, qrCodePath, avatarPath]) => {
          // 获取到图片的本地路径后，开始绘制
          const ctx = uni.createCanvasContext('posterCanvas', this);
          const canvasW = this.canvasWidth;
          const canvasH = this.canvasHeight;
          
          // 清空画布
          ctx.clearRect(0, 0, canvasW, canvasH);
          
          // 绘制背景图
          ctx.drawImage(bgImagePath, 0, 0, canvasW, canvasH);
          
          // 获取系统信息
          const sysInfo = uni.getSystemInfoSync();
          // 屏幕宽度(px)
          const screenWidth = sysInfo.screenWidth;
          
          // 计算实际rpx到px的转换比例
          const rpxToPx = screenWidth / 750;
          
          // 计算二维码在canvas中的位置和大小
          const qrCodePos = {
            x: Math.round(this.qrCodeStyle.x * rpxToPx),
            y: Math.round(this.qrCodeStyle.y * rpxToPx),
            width: Math.round(this.qrCodeStyle.width * rpxToPx),
            height: Math.round(this.qrCodeStyle.height * rpxToPx)
          };
          // 针对节假日海报二维码位置做特殊偏移
          if (this.type === 'festival') {
            qrCodePos.x -= Math.round(20 * rpxToPx); // 左移10rpx
            qrCodePos.y -= Math.round(40 * rpxToPx); // 上移30rpx
          }
          // 修改绘制时的偏移量，添加水平方向的偏移
          // 绘制时的垂直位置偏移量，让二维码位置更靠下
          const yOffset = Math.round(100 * rpxToPx); // 增加100rpx的垂直偏移
          // 水平方向的偏移量，让二维码位置更靠右
          const xOffset = Math.round(32 * rpxToPx); // 增加50rpx的水平偏移
          
          // 应用修正后的位置
          qrCodePos.y += yOffset;
          qrCodePos.x += xOffset;
          
          // 增加二维码尺寸
          qrCodePos.width += Math.round(20 * rpxToPx);  // 宽度增加2px
          qrCodePos.height += Math.round(20 * rpxToPx); // 高度增加10px
          
          console.log('屏幕信息:', {screenWidth, rpxToPx});
          console.log('二维码样式:', this.qrCodeStyle);
          console.log('二维码绘制信息(修正后尺寸和位置):', qrCodePos);
          
          // 绘制二维码
          if (this.posterData && this.posterData.isEllipse) {
            try {
              // 椭圆形二维码处理
              ctx.save(); // 保存当前状态
              
              // 创建椭圆形裁剪区域
              const centerX = qrCodePos.x + qrCodePos.width / 2;
              const centerY = qrCodePos.y + qrCodePos.height / 2;
              const radiusX = qrCodePos.width / 2;
              const radiusY = qrCodePos.height / 2;
              
              // 使用贝塞尔曲线模拟椭圆 (更好的兼容性)
              ctx.beginPath();
              
              // 绘制椭圆的贝塞尔曲线近似值
              const kappa = 0.5522848;
              const ox = radiusX * kappa;
              const oy = radiusY * kappa;
              
              ctx.moveTo(centerX - radiusX, centerY);
              ctx.bezierCurveTo(centerX - radiusX, centerY - oy, centerX - ox, centerY - radiusY, centerX, centerY - radiusY);
              ctx.bezierCurveTo(centerX + ox, centerY - radiusY, centerX + radiusX, centerY - oy, centerX + radiusX, centerY);
              ctx.bezierCurveTo(centerX + radiusX, centerY + oy, centerX + ox, centerY + radiusY, centerX, centerY + radiusY);
              ctx.bezierCurveTo(centerX - ox, centerY + radiusY, centerX - radiusX, centerY + oy, centerX - radiusX, centerY);
              
              ctx.closePath();
              ctx.clip(); // 使用椭圆形作为裁剪区域
              
              // 在裁剪区域内绘制二维码
              ctx.drawImage(qrCodePath, qrCodePos.x, qrCodePos.y, qrCodePos.width, qrCodePos.height);
              
              ctx.restore(); // 恢复到保存状态
            } catch (error) {
              console.error('椭圆绘制失败，回退到矩形绘制:', error);
              // 如果椭圆绘制失败，回退到普通绘制
              ctx.drawImage(qrCodePath, qrCodePos.x, qrCodePos.y, qrCodePos.width, qrCodePos.height);
            }
          } else {
            // 正常矩形二维码
            ctx.drawImage(qrCodePath, qrCodePos.x, qrCodePos.y, qrCodePos.width, qrCodePos.height);
          }
          
          // 绘制头像和名字
          if (avatarPath && this.infoObj.name) {
            // 计算头像位置
            let yOffset = 0;
            let xOffset = 0;
            
            // 为周日和节假日海报增加额外的垂直偏移
            if (this.type === 'daily' && this.id === 9) {
              // 周日(id=9)海报需要更多的下移
              yOffset = 80; 
              console.log('应用周日海报垂直偏移:', yOffset);
            } else if (this.type === 'festival') {
              // 节假日海报需要特别调整
              yOffset = 100; // 节假日海报垂直偏移更大
              xOffset = 20; // 节假日海报可能需要水平调整
              console.log('应用节假日海报特殊偏移 - 垂直:', yOffset, '水平:', xOffset);
            }
            
            const avatarPos = {
              x: Math.round((this.avatarNameStyle.x + xOffset) * rpxToPx),
              y: Math.round((this.avatarNameStyle.y + yOffset) * rpxToPx), 
              width: Math.round(this.avatarNameStyle.avatarWidth * rpxToPx),
              height: Math.round(this.avatarNameStyle.avatarHeight * rpxToPx)
            };
            
            // 绘制圆形头像
            ctx.save();
            ctx.beginPath();
            const avatarCenterX = avatarPos.x + avatarPos.width / 2;
            const avatarCenterY = avatarPos.y + avatarPos.height / 2;
            const avatarRadius = avatarPos.width / 2;
            ctx.arc(avatarCenterX, avatarCenterY, avatarRadius, 0, Math.PI * 2, false);
            ctx.clip();
            ctx.drawImage(avatarPath, avatarPos.x, avatarPos.y, avatarPos.width, avatarPos.height);
            ctx.restore();
            
            // 绘制用户名
            if (this.infoObj.name) {
              ctx.save();
              
              // 统一使用深色文字
              ctx.setFillStyle('rgba(0, 0, 0, 0.85)');
              
              ctx.setFontSize(28 * rpxToPx);
              ctx.setTextBaseline('middle');
              ctx.font = 'bold ' + (28 * rpxToPx) + 'px sans-serif';
              
              // 计算文本位置（在头像右侧）
              const textX = avatarPos.x + avatarPos.width + 20 * rpxToPx;
              const textY = avatarPos.y + avatarPos.height / 2;
              
              ctx.fillText(this.infoObj.name, textX, textY);
              ctx.restore();
            }
            
            console.log('头像绘制信息:', {
              position: avatarPos,
              userName: this.infoObj.name
            });
          }
          
          // 绘制完成
          ctx.draw(false, () => {
            // 延迟更长时间确保绘制完成
            setTimeout(() => {
              this.saveCanvasToAlbum();
            }, 1000); // 增加到1000ms确保绘制充分完成
          });
        }).catch(err => {
          console.error('下载图片失败:', err);
          uni.hideLoading();
          uni.showToast({
            title: '下载图片失败',
            icon: 'none'
          });
        });
      } catch (error) {
        console.error('绘制海报失败:', error);
        uni.hideLoading();
        uni.showToast({
          title: '生成海报失败',
          icon: 'none'
        });
      }
    },
    
    // 将Canvas保存到相册的独立方法
    saveCanvasToAlbum() {
      let retryCount = 0;
      const maxRetry = 3;
      
      const saveAction = () => {
        // 将画布内容保存为图片
        uni.canvasToTempFilePath({
          canvasId: 'posterCanvas',
          success: (res) => {
            const tempFilePath = res.tempFilePath;
            console.log('海报生成成功，临时路径：', tempFilePath);
            
            // 检查文件是否有效
            uni.getFileInfo({
              filePath: tempFilePath,
              success: (fileInfo) => {
                console.log('文件信息:', fileInfo);
                if (fileInfo.size > 0) {
                  // 文件有效，保存到相册
                  this.saveImageFile(tempFilePath);
                } else {
                  // 文件大小为0，重试
                  this.handleSaveError(new Error('生成的图片文件无效'), retryCount, maxRetry, saveAction);
                }
              },
              fail: (err) => {
                // 获取文件信息失败，重试
                this.handleSaveError(err, retryCount, maxRetry, saveAction);
              }
            });
          },
          fail: (err) => {
            this.handleSaveError(err, retryCount, maxRetry, saveAction);
          }
        }, this);
      };
      
      saveAction();
    },
    
    // 保存图片到相册
    saveImageFile(filePath) {
      // 确保filePath是使用http协议，而不是https
      let filePathToUse = filePath;
      if (filePathToUse && filePathToUse.startsWith('http://')) {
        filePathToUse = filePathToUse.replace('http://', 'https://');
        console.log('已将保存的图片路径从http转换为https:', filePathToUse);
      }
      
      uni.saveImageToPhotosAlbum({
        filePath: filePathToUse,
        success: () => {
          uni.hideLoading();
          uni.showToast({
            title: '保存成功',
            icon: 'success'
          });
        },
        fail: (err) => {
          console.error('保存到相册失败:', err);
          uni.hideLoading();
          // 如果是用户拒绝授权导致的失败
          if (err.errMsg && err.errMsg.indexOf('auth deny') >= 0) {
            uni.showModal({
              title: '提示',
              content: '需要您授权保存图片到相册',
              confirmText: '去设置',
              cancelText: '取消',
              success: (res) => {
                if (res.confirm) {
                  uni.openSetting();
                }
              }
            });
          } else {
            uni.showToast({
              title: '保存失败',
              icon: 'none'
            });
          }
        }
      });
    },
    
    // 处理保存过程中的错误
    handleSaveError(err, currentRetry, maxRetry, retryFn) {
      console.error('生成或保存图片失败:', err);
      
      if (currentRetry < maxRetry) {
        // 重试
        setTimeout(() => {
          console.log(`第${currentRetry + 1}次重试...`);
          retryFn();
        }, 500);
      } else {
        // 重试次数用完
        uni.hideLoading();
        uni.showToast({
          title: '生成海报失败',
          icon: 'none'
        });
      }
    },
    
    // 原来用于直接保存二维码的方法，现在改为调用生成海报方法
    savePoster() {
      this.generatePoster();
    },
    
    // 添加日志，确认椭圆形类的赋值问题
    logQrCodeStyle(e) {
      console.log('二维码加载完成，样式:', this.qrCodeStyle);
      console.log('posterData:', this.posterData);
      console.log('是否椭圆形:', this.posterData?.isEllipse);
      console.log('应用的class:', this.posterData?.isEllipse ? 'ellipse-qrcode' : 'none');
      
      // 同时输出头像位置配置，帮助调试
      console.log('头像名字样式:', this.avatarNameStyle);
    }
  }
}
</script>

<style lang="scss" scoped>
.page-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  position: relative;
  padding-bottom: 120rpx; /* 为底部按钮留出空间 */
}

.content-main {
  position: relative;
  flex: 1;
  overflow: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx;
}

/* 海报容器 */
.poster-container {
  position: relative;
  width: 100%;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  border-radius: 16rpx;
}

/* 海报背景图 */
.poster-background {
  width: 100%;
  display: block;
  object-fit: contain; /* 确保图片完整显示 */
  max-height: 100vh;
}

/* 二维码包装区域 */
.qrcode-wrapper {
  position: absolute;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 头像和名字包装区域 */
.avatar-name-wrapper {
  position: absolute;
  z-index: 2;
  display: flex;
  flex-direction: row;
  align-items: center;
}

/* 用户头像样式 */
.user-avatar {
  border-radius: 50%;
  object-fit: cover;
}

/* 用户名样式 */
.user-name {
  margin-left: 20rpx;
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.85);
  font-weight: bold;
}

/* 二维码样式 */
.poster-qrcode {
  width: 100%;
  height: 100%;
}

/* 椭圆形二维码样式 */
.ellipse-qrcode {
  border-radius: 50%; /* 设置椭圆形 */
  overflow: hidden;
}

/* 二维码区域提示文字 */
.qrcode-tip {
  font-size: 22rpx;
  color: #333;
  background-color: rgba(255, 255, 255, 0.8);
  text-align: center;
  width: 100%;
  padding: 5rpx 0;
  margin-top: 8rpx;
  border-radius: 10rpx;
}

/* 海报画布 */
.poster-canvas {
  position: absolute;
  left: -9999px;
  top: 0;
  z-index: -1;
  visibility: hidden;
}

/* 固定在底部的按钮容器 */
.fixed-bottom-btn {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 20rpx 0;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 底部保存按钮 */
.save-button {
  width: 670rpx;
  height: 88rpx;
  line-height: 88rpx;
  background-color: #00B484;
  color: #fff;
  text-align: center;
  font-size: 30rpx;
  border-radius: 44rpx;
}
</style>
