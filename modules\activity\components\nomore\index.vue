<template>
  <view>
    <template v-if="type == 0">
      <view class="nomore">
          <image :src="ico" mode="" class="ico"></image>

          <view class="nomore-tip">
            没有相关病例
          </view>
      </view>
    </template>
    <template v-else-if='type == 2'>
      <view class="spantip">
        {{tip}}
      </view>
    </template>
  </view>

</template>

<script>
  // const static_ctx = 'http://localhost:3000'
  export default {
    name:"nomore",
    props:{
      type:{
        type:[Number,String],
        default:0,
      },
      tip:{
        type:String,
        default:'咱没有对应模板可填写'
      },
    },
    data(){
      return{
        ico:this.$static_ctx + 'image/business/case-collect/icon-im-nomore.png',
      }
    }
  }
</script>

<style lang="scss" scoped>
  .nomore{
    min-height: 300upx;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;

    .ico{
      width: 250upx;
      height: 250upx;
    }
    .nomore-tip{
      color: #bcbcbc;
      font-size: 24upx;
      margin-left: -20upx;
    }



  }
  .spantip{
        height: 80rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #7c7c7c;
  }
</style>
