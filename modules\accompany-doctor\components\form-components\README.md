# 表单组件使用说明

这是一个通用表单组件，用于创建各种类型的表单。组件支持多种表单项类型，包括文本输入、选择器、图片上传、文本区域、身份证上传和开关等。

## 导入组件

```js
import AppForm from '@/modules/accompany-doctor/form-components/AppForm.vue'
```

## 基本用法

```vue
<template>
  <view>
    <app-form
      v-model="formData"
      :form-fields="formFields"
      :valid-rules="validRules"
      :static-ctx="$static_ctx"
      :show-reset-button="true"
      reset-text="重置"
      submit-text="提交"
      @submit="handleSubmit"
      @reset="handleReset"
    >
      <!-- 可以使用具名插槽添加自定义内容 -->
      <template v-slot:custom-field>
        <!-- 自定义字段内容 -->
      </template>
    </app-form>
  </view>
</template>

<script>
import AppForm from '@/modules/accompany-doctor/form-components/AppForm.vue'

export default {
  components: {
    AppForm
  },
  data() {
    return {
      formData: {}, // 表单数据对象
      formFields: [
        {
          type: 'input',
          name: 'name',
          label: '姓名',
          required: true,
          placeholder: '请输入姓名',
          inputType: 'text'
        },
        {
          type: 'picker',
          name: 'gender',
          label: '性别',
          required: true,
          mode: 'selector',
          range: ['男', '女'],
          placeholder: '请选择性别'
        },
        {
          type: 'switch',
          name: 'isActive',
          label: '启用状态',
          color: '#07C160'
        }
      ],
      validRules: {
        name: {
          required: true,
          message: '请输入姓名'
        },
        gender: {
          required: true,
          message: '请选择性别'
        }
      }
    }
  },
  methods: {
    handleSubmit(formData) {
      console.log('表单提交:', formData)
      // 处理表单提交逻辑
    },
    handleReset() {
      console.log('表单已重置')
      // 处理表单重置后的逻辑
    }
  }
}
</script>
```

## 支持的表单字段类型

### 1. 文本输入 (input)

```js
{
  type: 'input',
  name: 'name', // 字段名称
  label: '姓名', // 显示标签
  required: true, // 是否必填
  placeholder: '请输入姓名', // 占位文本
  inputType: 'text', // 输入类型：text, number, password 等
  maxlength: 50 // 最大长度限制
}
```

### 2. 选择器 (picker)

```js
// 普通选择器
{
  type: 'picker',
  name: 'gender',
  label: '性别',
  required: true,
  mode: 'selector', // 选择器模式：selector
  range: ['男', '女'], // 选项数组
  placeholder: '请选择性别'
}

// 地区选择器
{
  type: 'picker',
  name: 'regionValue',
  label: '服务城市',
  placeholder: '请选择',
  required: true,
  mode: 'region',
  level: 'city', // 选择级别：province, city, district
  provinceField: 'province', // 省份值存储字段
  cityField: 'city' // 城市值存储字段
}
```

### 3. 图片上传 (image)

```js
{
  type: 'image',
  name: 'avatar',
  label: '头像',
  required: true
}
```

### 4. 文本区域 (textarea)

```js
{
  type: 'textarea',
  name: 'description',
  label: '描述',
  required: false,
  placeholder: '请输入描述',
  maxlength: 200,
  showCounter: true // 是否显示字数统计
}
```

### 5. 身份证上传 (idcard)

```js
{
  type: 'idcard',
  label: '身份证',
  required: true,
  frontField: 'idCardFront', // 正面图片字段名
  backField: 'idCardBack', // 背面图片字段名
  frontIcon: this.$static_ctx + 'image/business/hulu-v2/IDCardFace.png', // 正面示意图标
  backIcon: this.$static_ctx + 'image/business/hulu-v2/IDCardNationalEmblemFace.png', // 背面示意图标
  frontText: '人像面', // 正面提示文本
  backText: '国徽面' // 背面提示文本
}
```

### 6. 开关 (switch)

```js
{
  type: 'switch',
  name: 'isActive',
  label: '启用状态',
  required: false,
  color: '#07C160' // 开关颜色，默认为微信绿色
}
```

### 7. 自定义插槽 (slot)

```js
{
  type: 'slot',
  slotName: 'custom-field', // 插槽名称
  label: '自定义字段',
  name: 'customField',
  required: false
}
```

使用自定义插槽时，需要在组件中提供对应的具名插槽：

```vue
<app-form :form-fields="formFields" v-model="formData">
  <template v-slot:custom-field>
    <!-- 自定义插槽内容 -->
    <view class="custom-field">
      <!-- 自定义UI和逻辑 -->
    </view>
  </template>
</app-form>
```

## 表单验证规则

表单验证规则使用对象格式，键名对应表单字段的name属性：

```js
validRules: {
  // 基本必填验证
  name: {
    required: true,
    message: '请输入姓名'
  },
  
  // 自定义验证函数
  age: {
    required: true,
    validator: value => value && parseInt(value) > 0 && parseInt(value) < 120,
    message: '请输入有效年龄(1-120)'
  },
  
  // 正则表达式验证
  phone: {
    required: true,
    validator: value => value && /^1[3-9]\d{9}$/.test(value),
    message: '请输入正确的手机号码'
  },
  
  // 特殊类型验证
  avatar: {
    required: true,
    message: '请上传头像',
    type: 'image' // 图片类型特殊处理
  }
}
```

## 组件属性

| 属性名 | 类型 | 默认值 | 说明 |
|-------|------|-------|------|
| value/v-model | Object | {} | 表单数据对象（双向绑定） |
| formFields | Array | [] | 表单字段配置数组，必填 |
| validRules | Object | {} | 表单验证规则 |
| staticCtx | String | '' | 静态资源URL前缀 |
| agreementText | String | '' | 协议文本，如果设置则显示协议勾选项 |
| submitText | String | '提交' | 提交按钮文本 |
| showResetButton | Boolean | false | 是否显示重置按钮 |
| resetText | String | '重置' | 重置按钮文本 |
| showBottomArea | Boolean | true | 是否显示底部按钮区域 |

## 事件

| 事件名 | 参数 | 说明 |
|-------|------|------|
| input | formData | 表单数据变化时触发 |
| submit | formData | 提交表单时触发，参数为表单数据 |
| reset | - | 重置表单时触发 |
| agreement-click | - | 点击协议文本时触发 |
| change | event | 选择器值变化时触发 |

## 示例

### 完整的表单示例

```vue
<template>
  <view class="container">
    <app-form
      v-model="form"
      :form-fields="formItems"
      :valid-rules="validRules"
      :static-ctx="$static_ctx"
      submit-text="提交申请"
      :show-reset-button="true"
      reset-text="重置"
      @submit="handleSubmit"
      @reset="handleReset"
    >
      <!-- 自定义标签选择器 -->
      <template v-slot:custom-field>
        <view class="custom-field">
          <view class="custom-buttons">
            <view 
              v-for="(tag, idx) in tags" 
              :key="idx" 
              class="custom-tag"
              :class="{ active: selectedTags.includes(tag) }"
              @click="toggleTag(tag)"
            >
              {{tag}}
            </view>
          </view>
        </view>
      </template>
    </app-form>
  </view>
</template>

<script>
import AppForm from '@/modules/accompany-doctor/form-components/AppForm.vue'

export default {
  components: {
    AppForm
  },
  data() {
    return {
      form: {
        name: '',
        gender: '',
        phone: '',
        isAvailable: true,
        customTags: []
      },
      // 标签数据
      tags: ['专业', '耐心', '准时', '热情'],
      selectedTags: [],
      // 表单配置
      formItems: [
        {
          type: 'input',
          name: 'name',
          label: '姓名',
          placeholder: '请输入姓名',
          required: true
        },
        {
          type: 'picker',
          name: 'gender',
          label: '性别',
          placeholder: '请选择',
          required: true,
          mode: 'selector',
          range: ['男', '女']
        },
        {
          type: 'input',
          name: 'phone',
          label: '手机号',
          placeholder: '请输入手机号码',
          required: true,
          inputType: 'number'
        },
        {
          type: 'switch',
          name: 'isAvailable',
          label: '启用状态',
          color: '#07C160'
        },
        {
          type: 'slot',
          slotName: 'custom-field',
          label: '个人特点标签',
          name: 'customTags'
        }
      ],
      // 验证规则
      validRules: {
        name: {
          required: true,
          message: '请输入姓名'
        },
        gender: {
          required: true,
          message: '请选择性别'
        },
        phone: {
          required: true,
          validator: value => value && /^1[3-9]\d{9}$/.test(value),
          message: '请输入正确的手机号码'
        }
      }
    }
  },
  methods: {
    // 表单提交
    handleSubmit(formData) {
      // 添加标签数据
      formData.customTags = this.selectedTags;
      console.log('提交数据:', formData);
    },
    
    // 表单重置
    handleReset() {
      this.selectedTags = [];
    },
    
    // 切换标签
    toggleTag(tag) {
      if(this.selectedTags.includes(tag)) {
        this.selectedTags = this.selectedTags.filter(item => item !== tag);
      } else {
        this.selectedTags.push(tag);
      }
      // 更新表单数据
      this.form.customTags = this.selectedTags;
    }
  }
}
</script>

<style lang="scss">
/* 自定义标签样式 */
.custom-field {
  width: 100%;
}

.custom-buttons {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20rpx;
}

.custom-tag {
  height: 60rpx;
  padding: 0 20rpx;
  line-height: 60rpx;
  background: #F4F6FA;
  border-radius: 30rpx;
  font-size: 26rpx;
  color: #666;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
}

.custom-tag.active {
  background: #E6F7F2;
  color: #00B484;
  border: 1px solid #00B484;
}
</style> 