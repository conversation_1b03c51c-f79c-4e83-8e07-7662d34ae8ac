<!--
<subscribe-remind></subscribe-remind>
-->
<template>
  <view v-if="!isLogin">
    <view class="login-remind" :class="defaultConfig.class" :style="{'background': defaultConfig.backgroundColor}">
      <text class="icon-warning-red-bg"></text>
      <view class="hint">
        登录手机号，同步自己的个人记录哦！
      </view>
      <view class="btn" @tap="returnFn()">
        登录/注册
      </view>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex'
export default {
  components: {

  },
  computed: {
    ...mapState('user', {
      isLogin: state => state.isLogin
    })
  },
  props: {
    routerName: {
      type: String,
      default() {
        return 'Login'
      }
    },
    // 参数设置
    config: {
      type: Object,
      required: false,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      defaultConfig: {
        class: '',
        backgroundColor: '#fdf6ec'
      }
    }
  },
  watch: {
    config: {
      handler(val) {
        this.copyConfig()
      },
      deep: true
    }
  },
  mounted() {
    this.copyConfig()
  },
  methods: {
    /**
       * 初始化拷贝config对象
       */
    copyConfig() {
      const that = this
      const obj = that.config
      Object.keys(obj).forEach(function(key) {
        that.defaultConfig[key] = obj[key]
      })
    },
    returnFn() {
      this.navtoGo('Login', { 'redirect': this.routerName })
    },
    navtoGo(url, obj = {}) {
      this.$navto.push(url, obj)
    }
  }
}
</script>

<style lang="scss" scoped>
  .login-remind{
    display: flex;
    align-items: center;
    color: #e6a23c;
    @include rounded(10upx);
    padding: 14upx;
    margin: 10upx;
    text.icon-warning-red-bg{
      @include iconImg(36, 36, '/business/icon-warning-red-bg.png');
      display: inline-block;
      vertical-align: middle;
      margin-right: 14upx;
    }
    .hint{
      display: inline-block;
      vertical-align: middle;
      font-size: 24upx;
      line-height: 36upx;
      // width: calc(100% - 184upx);
      flex: 1;
    }
    .btn{
      padding: 0 12upx;
      margin: 0;
      display: inline-block;
      vertical-align: middle;
      font-size: 22upx;
      line-height: 52upx;
      height: 52upx;
      // width: 150upx;
      text-align: center;
      color: #ffffff;
      border: 2upx solid #fd9e14;
      @include rounded(6upx);
      // margin-left: 20upx;
      background: #fd9e14;
    }
  }
  .uni-popup{
    z-index: 999!important;
  }
  .show-popup{
    background-color: #fff;
    width: 560upx;
    @include rounded(6upx);
    overflow: hidden;
    .popup-t{
      text-align: center;
      font-size: 36upx;
      line-height: 48upx;
      padding: 30upx 0 20upx 0;
    }
    .popup-m-img{
      display: inline-block;
      margin-bottom: 20upx;
      .l{
        vertical-align: middle;
        display: inline-block;
        width: 90upx;
        height: 90upx;
        @include rounded(50%);
        margin-right: 20upx;
        overflow: hidden;
        image{
          width: 100%;
          height: 100%;
        }
      }
      .m{
        display: inline-block;
        color: #333;
        font-size: 28upx;
        line-height: 42upx;
        vertical-align: middle;
      }
    }
    .popup-m{
      display: block;
      margin-bottom: 20upx;
      font-size: 28upx;
      line-height: 42upx;
      color: #333;
      padding: 0 30upx;
    }
    .popup-b{
      border-top: 2upx solid $contentDdt;
      view {
        line-height: 88upx;
        height: 88upx;
        font-size: 36upx;
        text-align: center;
        display: inline-block;
      }
      .l{
        width: calc(50% - 2upx);
        border-right: 2upx solid $contentDdt;
      }
      .r{
        width: 50%;
        color: $topicC;
      }
    }
  }
</style>
