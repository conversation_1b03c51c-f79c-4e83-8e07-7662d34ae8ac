<template>
  <page>
    <view slot="content" class="main-body">
      <view class="content-main">
        <view class="header">
          <text class="l-l">头像</text>
          <yq-avatar
            class="avatar"
            :disabled="!isUpdate"
            :isFullScreen="true"
            selWidth="500upx"
            selHeight="500upx"
            @upload="myUpload"
            :avatarSrc="regForm.headPic?regForm.headPic:this.$static_ctx + 'image/system/avatar/icon-default-avatar.png'"
            avatarStyle="width: 80upx; height: 80upx; border-radius: 100%;">
          </yq-avatar>
        </view>
        <view>
          <title-input title="昵称" type="text" :maxlength="20" :isRequired="true" :disabled="!isUpdate" :initialValue="regForm.nickName" placeholder="请输入" @returnFn="nameFn"></title-input>
          <dictionary-selector :isRequired="true" :disabled="!isUpdate" :config="template.sex" :cData="regForm.sex" @updateForm="updateForm"></dictionary-selector>
          <!-- <title-jump-date title="生日" :isRequired="true" :disabled="!isUpdate" :date="regForm.birthday?regForm.birthday:'请选择'" @returnFn="birthdayFn"></title-jump-date>
          <title-input title="常用邮箱" :disabled="!isUpdate" type="text" :maxlength="20" :initialValue="regForm.email" placeholder="请输入" @returnFn="emailFn"></title-input> -->
        </view>
        <view class="btn-bg m-t-20 m-b-20" @tap="onSubmit()">{{ isUpdate ? '保存':'编辑'}}</view>
      </view>
    </view>
  </page>
</template>
<script>
import {mapState} from 'vuex'
// import TitleJump from '@/components/business/content-title/title-jump'
import TitleInput from '@/components/business/content-title/title-input'
// import TitleJumpDate from '@/components/business/module/title-jump-date'

import YqAvatar from '@/modules/system/components/yq-avatar/yq-avatar.vue'
import common from '@/common/util/main'

export default {
  components: {
    // TitleJump,
    TitleInput,
    // TitleJumpDate,
    YqAvatar
  },
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $accurateConversion: this.$accurateConversion,
      file_ctx: this.file_ctx,
      $static_ctx: this.$static_ctx,
      $timePlugin: this.$timePlugin,
      $validate: this.$validate,
      isUpdate: true,
      regForm: {
        birthday: this.$common.formatDate(new Date(), 'yyyy-MM-dd'),
        headPic: '',
        avatar: '',
        sex: ''
      },
      template: {
        sex: {
          label: '性别',
          name: 'sex',
          placeholder: '请选择性别',
          required: true,
          array: [
            { value: '男', key: '1' },
            { value: '女', key: '2' }
          ],
          dicKey: 'sex'
        }
      }
    }
  },
  onLoad() {
    this.getDetail()
  },
  computed: {
    ...mapState('user', {
      fansRecord: state => state.fansRecord,
      accountId: state => state.accountId
    })
  },
  methods: {
    nameFn(v) {
      this.regForm.nickName = v
    },
    emailFn(v) {
      this.regForm.email = v
    },

    myUpload(rsp) {
      const that = this
      // rsp.path // 更新头像方式一
      // rsp.avatar.imgSrc = rsp.path; //更新头像方式二
      const uploadTask = uni.uploadFile({
        url: this.$constant.noun.uploadInterface,
        filePath: rsp.path,
        name: 'file',
        header: common.getUploadFileHeader(),
        formData: { // HTTP 请求中其他额外的 form data
          groupId: "26000",
          relId: ''
        },
        success: (uploadFileRes) => {
          console.log('uploadFileRes-----------------', uploadFileRes, JSON.parse(uploadFileRes.data))
          const returnData = JSON.parse(uploadFileRes.data).data
          if (JSON.stringify(returnData) !== '[]') {
            this.regForm.headPath = returnData[0].dir
            this.regForm.headPic = this.file_ctx + returnData[0].dir
            this.regForm = Object.assign({}, this.regForm)
          }
        }
      })
      uploadTask.onProgressUpdate((res) => {
        // console.log('上传进度' + res.progress);
        // console.log('已经上传的数据长度' + res.totalBytesSent);
        // console.log('预期需要上传的数据总长度' + res.totalBytesExpectedToSend);
        // console.log(res)
      })
    },
    returnFn(e) {
      this.index = e.target.value
    },
    updateForm(obj) {
      obj.key = obj.key.trim()
      this.regForm[obj.key] = obj.value
    },
    birthdayFn(v) {
      this.regForm.birthday = v
    },
    onSubmit() {
      const that = this
      if (!that.isUpdate) {
        this.$uniPlugin.loading('开启编辑')
        this.isUpdate = true
        setTimeout(() => {
          that.$uniPlugin.hideLoading()
        }, 500)
        return
      }
      const tipArr = {
        nickName: '请输入姓名',
        // email: '请输入邮箱',
        // birthday: '请选择生日'
      }
      const params = {
        nickName: that.regForm.nickName, // 姓名
        // email: that.regForm.email, // 邮箱
        // birthday: that.regForm.birthday // 生日
      }
      // 表单验证
      if (!that.$common.validationForm(tipArr, params)) {
        return
      }
      // const regEmail = /^[A-Za-z0-9\u4e00-\u9fa5]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/
      // if (!regEmail.test(that.regForm.email)) {
      //   that.$uniPlugin.toast('邮箱格式不正确')
      //   return
      // }
      that.$uniPlugin.loading('保存中', true)
      // if (that.regForm.headPic.indexOf('icon-default-avatar.png') > -1) {
        //   that.regForm.headPic = ''
      // }
      that.$api.community.fansrecordInfoUpdate({...that.regForm,accountId: this.accountId }).then(res => {
        that.$uniPlugin.hideLoading()
        this.$uniPlugin.toast(res.msg)
        that.$ext.user.getCommunityFansRecord().then(() => {
          setTimeout(() => {
            that.getDetail()
            that.$navto.back(1)
          }, that.$constant.noun.delayedOperationTime)
        })
      }).catch(() => {
        that.$uniPlugin.hideLoading()
      })
    },
    getDetail() {
      const that = this
      // that.$uniPlugin.loading('加载中', true)
      // that.$api.common.sysuserBasicsinfo().then(res => {
      //   that.$uniPlugin.hideLoading()
      //   that.isUpdate = false
      //   that.regForm = res
      //   // that.regForm.headPic = ''
      //   // that.regForm.headPic = res.headPic ? res.headPic : that.$static_ctx + 'image/system/avatar/icon-default-avatar.png'
      // })
      const data = that.$validate.isNull(that.fansRecord) ? {} : JSON.parse(JSON.stringify(that.fansRecord))
      console.log(data)
      that.regForm = data
      that.regForm.headPic = data.headPath ? that.file_ctx + data.headPath : that.$static_ctx + 'image/system/avatar/icon-default-avatar.png'
    }
  }

}
</script>

<style lang="scss" scoped>
  .main-body{
    height: 100%;
  }
.content-main{
  .header{
    background: #fff;
    border-bottom: 1px solid #f5f5f5;
    height: 80upx;
    padding: 20upx 30upx;
    margin-top: 10upx;
    .l-l{
      float: left;
      line-height: 80upx;
      height: 80upx;
      font-size: 32upx;
    }
    .avatar{
      height: 80upx;
      width: 80upx;
      overflow: hidden;
      float: right;
      @include rounded(50%);
    }
  }
}
  .name{
    margin-left: 464upx;
    color: #999999;
    font-size: 30upx;
  }
  .m-t-20{
    margin-top: 40upx;
  }
</style>
