import MessageDefault from './MessageDefault'

export default class HandleOrderEvaluate extends MessageDefault {
    constructor (websocket) {
        super()
        this.websocket = websocket
    }

    match (dataJson) {
        const { ORDER_EVALUATE_CMD } = this.chatCmd
        return dataJson.cmd === ORDER_EVALUATE_CMD
    }

    processMessage (dataJson) {
        const data = dataJson.data
        if (!this.$validate.isNull(this.$common.getKeyVal('chat', 'chatItem')) && this.$common.getKeyVal('chat', 'chatItem').orderId === data.id) {
            this.$common.setKeyVal('chat', 'orderDetail', data, false)
        }
    }
}