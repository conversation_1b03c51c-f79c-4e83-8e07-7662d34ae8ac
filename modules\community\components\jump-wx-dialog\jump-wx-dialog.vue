<template>
  <!-- 结束咨询弹框 -->
  <showmodel
    type="center"
    dialogStyle='width:632rpx;height:350rpx;border-radius:10rpx'
    :show="centerVisible"
    @cancel="cancelCenterVisible"
    root-class="centerRoot"
    :clickbg="false"
  >
    <view class="dialogContent">
      <view class="dialog-title">
        操作提示
      </view>
      <view class="dialog-content">访问小程序解锁更多内容</view>
      <view class="dialog-btns">
        <view class="dialog-btn" @tap="cancelCenterVisible">
          再想想
        </view>
        <view class="dialog-space"></view>
        <view class="dialog-btn active">
          <slot name="jump-wx-btn" />
        </view>
      </view>
      <view class="dialog-floor">
        <checkbox-group @change="checkboxChange">
          <label class="flex-box">
            <checkbox :checked="tipChecked" style="transform:scale(0.7)" />
            不再提示
          </label>
        </checkbox-group>
      </view>
    </view>
  </showmodel>
</template>

<script>
import showmodel from '@/components/basics/showmodel/showmodel.vue'

export default {
  components: {
    showmodel
  },
  data() {
    return {
      centerVisible: false,
      tipChecked: false,
      isOpen: true // 是否可以打开弹窗 用于勾选了不再提示
    }
  },
  methods: {
    checkboxChange() {
      this.tipChecked = !this.tipChecked
    },
    open() {
      if (!this.isOpen) {
        this.centerVisible = false
      } else {
        this.centerVisible = true
      }
    },
    cancelCenterVisible() {
      this.close()
    },
    close() {
      this.centerVisible = false
      this.isOpen = !this.tipChecked
    }
  }
}
</script>

<style scoped lang="scss">
.dialog-title{
	font-size: 36upx;
	font-weight: 550;
	line-height: 2;
	display: flex;
	align-items: center;
	justify-content: center;
	padding-top: 33upx;
	// padding-bottom: 58upx;
}
.dialog-btns{
	display:flex;
	padding:0 20upx;
	.dialog-btn{
		flex:1;
		height: 80upx;
		background-color:#eeeeee;
		font-size:32upx;
		display:flex;
		align-items:center;
		justify-content:center;
		border-radius:50upx
	}
	.dialog-btn.active{
		background-color:#00d29d;
		color:#fff
	}
	.dialog-space{
		width: 20upx;
		height: 1upx
	}
}
.dialog-content {
  text-align: center;
  padding: 24upx 32upx;
  font-size: 28upx;
  color: #333;
  line-height: 32upx;
}
.flex-box {
  display: flex;
  align-items: center;
}
.dialog-floor {
  padding: 12upx 32upx;
  font-size: 26upx;
  color: #333;
  line-height: 32upx;
}
</style>