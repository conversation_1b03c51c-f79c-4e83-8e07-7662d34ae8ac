<template>
    <page>
        <view slot="content" class="body-main">
          <view class="m-main-body">
            <scroll-refresh class="scroll-refresh-main" :isShowEmptySwitch="true" :fixed="false" :up="upOption" :down="downOption" @returnFn="returnFn" @scrollInit="scrollInit">
              <view class="header">
                  <view class="header-l">
                      <image mode="aspectFit" :src="detailObj.logo"></image>
                  </view>
                  <view class="header-r">
                      <view class="title">{{ detailObj.hospitalName }}</view>
                      <view class="title-info"></view>
                  </view>
              </view>
              <view class="hospital-content">
                <view class="hospital-head">
                  <h3>基础信息</h3>
                  <view class="info">{{ detailObj.desc }}</view>
                </view>
                <view class="line"></view>
                <view class="outpatient">
                    <view class="info">门诊服务：<span v-for="(item,index) in serviceList" :key="index">{{ item }}</span></view>
                    <view class="line"></view>
                </view>
                <view class="outpatient-date">
                    <view class="info">门诊时间：{{ detailObj.serviceTime }}</view>
                    <view class="line"></view>
                </view>
                <!-- <view class="hospital-official">{{  }}</view> -->
                <view class="hospital-phone">
                    <view class="info"><uni-icons class="phone" :size="14" color="#666" type="phone" />{{ detailObj.tel }}</view>
                    <view class="line"></view>
                </view>
                <view class="hospital-address"><uni-icons class="location" :size="14" color="#666" type="location" />{{ detailObj.address }}</view>
              </view>
              <view class="accompany-bott">
                <view class="accompany-bott-item" @click="gotoOrder(item)" v-for="(item,index) in accompanyMap" :key="item.id">
                  <view class="img"><image :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-half-day'+ (index+1) +'.png'"></image></view>
                  <view class="title">{{item.serviceName}}</view>
                </view>
              </view>
              <view class="hospital-depa">
                <view class="depa-header">
                  <tabs-sticky class="mytab" v-model="curIndex" :fixed="false" :tabs="tabs" @change="changeTab">
                  </tabs-sticky>
                </view>
                <view class="depa-title">
                    <view class="title-l">
                        科室医生
                    </view>
                    <view class="title-r">
                        加星表示特色科室
                    </view>
                </view>
                <view class="depa-tag-list">
                    <view :class="moreFlag?'depa-tag-box box-active':'depa-tag-box'">
                        <view
                            :class="depaActive==index?'depa-tag-item active':'depa-tag-item'"
                            v-for="(item,index) in departmentsList"
                            :key="item.id"
                            @click="handleTagActive(item.id,index)"
                        >
                            {{ item.name }}
                        </view>
                    </view>
                </view>
                <view class="more" @click="moreFlag = !moreFlag">
                    {{ moreFlag?'收起':'展开更多' }}
                    <uni-icons class="move-active" :size="10" color="#666" :type="moreFlag?'top':icnosType" />
                </view>
              </view>
              <view class="depa-doctor-item"
                v-for="item in indexlist"
                :key="item.id"
                @click="handleJump(item.id)"
              >
                <view class="doctor-item-box">
                  <view class="doctor-item-l">
                    <image mode="aspectFit" :src="item.expertPic"></image>
                  </view>
                  <view class="doctor-item-r">
                    <view class="item-r-head">
                      <view class="name">{{ item.name }}<span>{{ item.post }}</span></view>
                    </view>
                    <view class="item-r-bott">
                      擅长领域：{{ item.introduction }}
                    </view>
                  </view>
                </view>
                <view class="line"></view>
              </view>
            </scroll-refresh>
          </view>
        </view>
    </page>
</template>

<script>
  import UniIcons from '@/components/uni/uni-icons/uni-icons'
  import TabsSticky from '@/components/basics/tabs-sticky'
  import { isDomainUrl } from '@/utils/index.js'
    import serverOptions from '@/config/env/options'
  export default {
    components: {
        UniIcons,
        TabsSticky,
    },
    data(){
      return{
        file_ctx: this.file_ctx,
        $constant: this.$constant,
        detailObj:{},
        departmentsList:[],
        doctorList:[],
        currentActive:0,
        depaList:[],
        depaActive:0,
        hospitalId:'',
        deptId:null,
        mescroll: null, // mescroll实例对象
        downOption: {
          auto: false // 不自动加载
        },
        upOption: {
          auto: false, // 不自动加载
          empty: {
            top: 0,
            zIndex: 999,
          },
          onScroll: true
        },
        indexlist:[],
        moreFlag:false,
        icnosType:'bottom',
        serviceList:[],
        curIndex:0,
        tabs: [{ name: '科室医生', consultStatus: '' },{ name: '点评', consultStatus: '' },],
        accompanyMap:[],
      }
    },
    onLoad(option){
        const that = this
        this.hospitalId = option.id
        // 医院详情接口
        this.$api.hospital.hospitalQueryOne({id:option.id}).then(res=>{
          if(res.code == 0){
            this.detailObj = {...res.data,logo:isDomainUrl(res.data.logo)}
            this.serviceList = this.detailObj.service?.split('、')
          }
        })

        // 医院下的科室接口
        this.$api.hospital.crawlershospitaldeptQuery({id:option.id}).then(res=>{
          if(res.code == 0){
            this.departmentsList = [{name:'全部科室',id:'0'},...res.data]
            console.log(this.departmentsList,'this.departmentsList')
          }
        })

        this.getAccompanyservicePage()

        this.$nextTick(() => {
          that.init()
        })
    },
        onShareAppMessage(){
          return {
                title: serverOptions.title,
                path: '/modules/activity/hospital-ranking/hospital-detail/index?id=' + this.hospitalId,
              }
        },
        onShareTimeline(){
          return {
            title: serverOptions.title,
            path: '/modules/activity/hospital-ranking/hospital-detail/index?id=' + this.hospitalId,
          }
        },
    methods:{
      gotoOrder(options){
        const city = this.$common.getKeyVal('user','cityName',true)
        this.$navto.push('ServiceDetail',{id:options.id, classifyId:options.classifyId, name:options.classifyName,city});
      },
      changeTab(tab){
        if(tab == '1'){
          this.$navto.push('diagnosisComment',
          {
            id:this.hospitalId,
            hospitalName:this.detailObj.hospitalName,
            type:1,
          })
        }
      },

      returnFn(obj) {
        const that = this
        setTimeout(function () {
          let params = {
            current: obj.pageNum,
            size: obj.pageSize,
            condition:{
              hospitalId:that.hospitalId,
              deptId:that.deptId,
            }
          }
          that.$api.hospital.crawlershospitaldoctorQuery(params).then(res => {
              let data = res.data.records.map(item=>({...item,expertPic:isDomainUrl(item.expertPic)})) || []
              if (obj.pageNum === 1) {
                  that.indexlist = []
              }
              that.indexlist = [...that.indexlist, ...data]
              obj.successCallback && obj.successCallback(data)
          })
        }, that.$constant.noun.scrollRefreshTime)
      },

      scrollInit(scroll) {
        scroll.optUp.page.num = 1
        scroll.optUp.page.size = 10
        this.mescroll = scroll
      },

      init() {
        this.$nextTick(() => {
          this.mescroll.triggerDownScroll()
        })
      },

      // 点击激活科室或者点评
      handleClickActive(index){
          this.currentActive = index
      },
      async getAccompanyservicePage(){
        let {data:{records}} = await this.$api.accompanyDoctor.getAccompanyservicePage({current:0,size:4,condition:{state:1}})
        this.accompanyMap = records
      },

      // 点击科室
      handleTagActive(id,index){
          this.depaActive = index
          this.mescroll.triggerDownScroll()
          if(index == 0){
              this.deptId = null
          } else {
              this.deptId = id
          }
      },

      handleJump(id){
          this.$navto.push('DoctorDetail', {id})
      },

      // 通过医院主键和科室主键查询其下医生 hospitalId医院id deptId科室id
      crawlershospitaldoctorQuery(obj){
          this.$api.hospital.crawlershospitaldoctorQuery(obj).then(res=>{
              // this.doctorObj =
              console.log(res,'res对象555')
              if(res.code == 0){
                  this.indexlist = res.data.records
              }
          })
      }
    },
 }
</script>

<style lang='scss' scoped>
.line{
    display: block;
    width: 100%;
    height: 1upx;
    background-color: #e0e0e0;
    transform: scaleY(.333);
}
.body-main{
    height: 100%;
    background-color: #f5f5f5;
    .m-main-body{
      height: 100%;
      .scroll-refresh-main{
        height: 100%;
        ::v-deep .mescroll-uni{
          .z-paging-content{
            // background-color: #fff !important;
            // background-color: pink !important;
          }
        }
        .depa-doctor-item{
          padding:20upx 40upx;
          margin:0 20upx 0;
          background-color: #fff;
          .doctor-item-box{
            display: flex;
            padding-bottom:20upx;
            .doctor-item-l{
              width: 140upx;
              height: 100upx;
              image{
                width: 100%;
                height: 100%;
              }
            }
              .doctor-item-r{
                display: flex;
                flex-direction: column;
                flex: 1;
                margin-left: 15upx;
                .item-r-head{
                  .name{
                    color:#000;
                  }
                  span{
                    margin-left: 10upx;
                  }
                }
                .item-r-bott{
                  margin-top: 20upx;
                  display: -webkit-box;
                  -webkit-box-orient: vertical;
                  -webkit-line-clamp: 2;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  color:#333;
                }
              }
          }
        }
        .header{
            display: flex;
            align-items: center;
            padding:30upx 20upx;
            margin:0 20upx;
            border-radius: 20upx;
            background-color: #fff;
            .header-l{
                width: 80upx;
                height: 80upx;
                image{
                    width: 100%;
                    height: 100%;
                }
            }
            .header-r{
                margin-left: 10upx;
                .title{
                    font-size: 32upx;
                    font-weight: bold;
                    color:#000;
                }
            }

        }
        .hospital-content{
            padding:20upx;
            margin:20upx 20upx 0;
            background-color: #fff;
            border-radius: 20upx;
            .hospital-head{
                padding:20upx 0;
                h3{
                    color:#000;
                    font-size: 32upx;
                    font-weight: bold;
                }
                .info{
                    padding-top: 15upx;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 3;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    color:#333;
                }
            }
            .outpatient{
                .info{
                    padding:25upx 0;
                    color:#333;
                    span{
                        display: inline-block;
                        color: rgb(253, 80, 62);
                        background-color: rgba(253, 80, 62, 0.1);
                        padding:0 10upx;
                        border-radius: 10upx;
                        margin-right: 10upx;
                    }
                }
            }
            .outpatient-date{
                .info{
                    padding:25upx 0;
                    color:#333;
                }
            }
            .hospital-phone{
                .info{
                    padding:25upx 0;
                    color:#333;
                }
            }
            .hospital-address{
                padding-top:25upx;
                color:#333;
            }
        }
        .accompany-bott{
          display: flex;
          padding:40rpx 0;
          background: #FFFFFF;
          border-radius: 16rpx;
          margin: 20rpx 20rpx 0;
          .accompany-bott-item{
            display: flex;
            flex: 1;
            flex-direction: column;
            align-items: center;
            text-align: center;
            // justify-content: center;
            .img{
              width: 72rpx;
              height: 72rpx;
              image{
                width: 100%;
                height: 100%;
              }
            }
            .title{
              font-size: 24rpx;
              color: #1D2029;
              margin-top: 12rpx;
              width: 120rpx;
              word-wrap: break-word;
              white-space: normal;
              word-break: break-all;
            }

          }
        }
        .hospital-depa{
            margin:20upx 20upx 0;
            border-radius: 20upx 20upx 0 0;
            padding:20upx;
            background-color: #fff;
            // height: 100%;
            .depa-header{
                display: flex;
                width: 100%;
                ::v-deep.mytab{
                  width: 100%;
                  .tabs-sticky{
                    width: 100%;
                    .tabs-sticky-body{
                      .tab{
                        display: flex;
                        flex: 1;
                        .text-width{
                          width: 100%;
                        }
                      }
                    }
                  }
                }
            }
            .depa-title{
                display: flex;
                justify-content: space-between;
                margin:30upx 0 30upx;
                .title-l{
                    color:#333;
                }
                .title-r{
                    color:#000;
                    font-size: 26upx;
                }
            }
            .depa-tag-list{
                .depa-tag-box{
                    display: flex;
                    flex-wrap: wrap;
                    max-height:182upx;
                    overflow: hidden;
                    .depa-tag-item{
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        padding:4upx 7upx;
                        border-radius: 15upx;
                        border:1px solid #8f939c;
                        margin-right: 15upx;
                        margin-bottom: 15upx;
                        overflow: hidden;
                        color:#8f939c;
                    }
                    .active{
                        border-color: #2979ff;
                        color:#2979ff;
                    }
                }
                &::-webkit-scrollbar{
                    height: 0;
                }
                .box-active{
                    max-height:360upx;
                    overflow: auto;
                }
            }
            .more{
              display: block;
              height: 40upx;
              line-height: 40upx;
              width: calc(100% - 40upx);
              font-size: 28upx;
              text-align: center;
              margin: 15upx 20upx 0;
              border-radius: 20upx;
              color: #333;
              .move-active{
                margin-left: 10upx;
              }
            }
        }
      }
    }
}
</style>
