<template>
  <view class="withdrawal-detail">
    <!-- 顶部导航栏 -->
    <uni-nav-bar
      color="#1d2029"
      :border="false"
      :inFontWeight="true"
      :showBtnsRight="false"
      :fixed="false"
      title="提现详情"
      statusBar
      @clickLeft="back"
      left-icon="left"
      left-width="48rpx"
      right-width="100px"
      backgroundColor="rgba(0,0,0,0)"
    />
    
    <!-- 提现成功卡片 -->
    <view class="success-card">
      <view class="card-title" :class="{
        'title-success': withdrawalDetail.state === 2,
        'title-failed': withdrawalDetail.state === 3,
        'title-auditing': withdrawalDetail.state === 1
      }">{{ getStatusText(withdrawalDetail.state) }}</view>
      
      <view class="detail-list">
        <view class="detail-item">
          <text class="item-label">提现流水号：</text>
          <text class="item-value">{{ withdrawalDetail.id || '暂无数据' }}</text>
        </view>
        
        <view class="detail-separator"></view>
        
        <view class="detail-item">
          <text class="item-label">提现金额：</text>
          <text class="item-value">{{ formatAmount(withdrawalDetail.amount) }}</text>
        </view>
        
        <view class="detail-separator"></view>
        
        <view class="detail-item">
          <text class="item-label">提现时间：</text>
          <text class="item-value">{{ formatDate(withdrawalDetail.createTime) }}</text>
        </view>
        
        <view class="detail-separator"></view>
        
        <view class="detail-item">
          <text class="item-label">提现完成时间：</text>
          <text class="item-value">{{ formatDate(withdrawalDetail.auditTime) }}</text>
        </view>
        
        <view class="detail-separator"></view>
        
        <view class="detail-item">
          <text class="item-label">手续费：</text>
          <text class="item-value">{{ formatAmount(0) }}</text>
        </view>
        
        <view class="detail-separator"></view>
        
        <view class="detail-item">
          <text class="item-label">银行卡号：</text>
          <text class="item-value">{{ '暂无数据' }}</text>
        </view>
        
        <view class="detail-separator"></view>
        
        <view class="detail-item">
          <text class="item-label">银行户名：</text>
          <text class="item-value">{{ '暂无数据' }}</text>
        </view>
        
        <view class="detail-separator"></view>
        
        <view class="detail-item">
          <text class="item-label">提现名称：</text>
          <text class="item-value">{{ '暂无数据' }}</text>
        </view>
        
        <view class="detail-separator"></view>
        
        <view class="detail-item">
          <text class="item-label">提现账号：</text>
          <text class="item-value">{{ '暂无数据' }}</text>
        </view>
        
        <view class="detail-separator" v-if="withdrawalDetail.failReason"></view>
        
        <view class="detail-item" v-if="withdrawalDetail.failReason">
          <text class="item-label">失败原因：</text>
          <text class="item-value error-text">{{ withdrawalDetail.failReason }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import uniNavBar from '@/components/uni/uni-nav-bar/uni-nav-bar.vue'

export default {
  components: {
    uniNavBar
  },
  data() {
    return {
      withdrawalDetail: {
        id: '',
        amount: 0,
        createTime: null,
        auditTime: null,
        state: 0,
        failReason: ''
      }
    }
  },
  onLoad(options) {
    // 使用页面传递的数据
    if (options.id) {
      // 对URL编码的参数进行解码
      const decodedStatus = options.status ? decodeURIComponent(options.status) : '';
      const decodedTime = options.time ? decodeURIComponent(options.time) : '';
  
      // 创建提现详情对象
      this.withdrawalDetail = {
        id: options.id,
        amount: this.parseAmount(options.amount),
        createTime: this.parseDate(decodedTime),
        auditTime: this.parseDate(decodedTime),
        state: this.parseStatus(decodedStatus),
        status: decodedStatus
      };
    } else {
      uni.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        this.back();
      }, 1500);
    }
  },
  methods: {
    // 返回上一页
    back() {
      uni.navigateBack();
    },
    
    // 解析状态
    parseStatus(status) {
      if (status === '已失败') return 3;
      if (status === '审核中') return 1;
      if (status === '处理中') return 0;
      return 2; // 已完成
    },
    
    // 获取状态文本
    getStatusText(state) {
      const status = this.withdrawalDetail.status;
      
      // 直接使用传入的状态文本(如果存在)，保持与列表页一致
      if (status) {
        return status === '已失败' ? '提现失败' : 
              status === '审核中' ? '审核中' : 
              status === '处理中' ? '处理中' : '提现成功';
      }
      
      // 如果没有传入状态文本，则根据状态码判断
      if (state === 3) return '提现失败';
      if (state === 1) return '审核中';
      if (state === 0) return '处理中';
      return '提现成功';
    },
    
    // 解析金额
    parseAmount(amount) {
      if (!amount) return 0;
      // 尝试将金额字符串转为数字（单位分）
      try {
        return parseFloat(amount) * 100;
      } catch (e) {
        return 0;
      }
    },
    
    // 解析日期
    parseDate(dateStr) {
      if (!dateStr) return Date.now();
      // 尝试解析日期字符串
      try {
        // 简单替换处理，如 "2024.05.12 14:30" 转为 "2024-05-12 14:30"
        const normalized = dateStr.replace(/\./g, '-');
        return new Date(normalized).getTime() || Date.now();
      } catch (e) {
        return Date.now();
      }
    },
    
    // 格式化金额
    formatAmount(amount) {
      if (!amount && amount !== 0) return '0.00';
      return (amount / 100).toFixed(2);
    },
    
    // 格式化日期
    formatDate(timestamp) {
      if (!timestamp) return '--';
      
      try {
        const date = new Date(Number(timestamp));
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        
        return `${year}-${month}-${day} ${hours}:${minutes}`;
      } catch (error) {
        console.error('日期格式化错误:', error);
        return '--';
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.withdrawal-detail {
  min-height: 100vh;
  padding-bottom: 40rpx;
  background-color: #f5f7fa;
}

.success-card {
  margin: 30rpx;
  padding: 30rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 36rpx;
  font-weight: 500;
  text-align: center;
  margin-bottom: 40rpx;
}

.title-success {
  color: #00B484;
}

.title-failed {
  color: #FF5500;
}

.title-auditing {
  color: #FF9500;
}

.detail-list {
  padding: 0;
}

.detail-item {
  display: flex;
  padding: 24rpx 20rpx;
  font-size: 28rpx;
  line-height: 40rpx;
}

.detail-separator {
  height: 1rpx;
  background-color: #f0f0f0;
  margin: 0 20rpx;
}

.item-label {
  color: #666666;
  width: 220rpx;
  flex-shrink: 0;
}

.item-value {
  color: #333333;
  flex: 1;
}

.error-text {
  color: #FF5500;
}
</style>
