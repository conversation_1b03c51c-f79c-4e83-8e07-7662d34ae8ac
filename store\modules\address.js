/**
 * 系统全局
 */
import validate from '@/common/util/validate'
const address = {
    namespaced: true,
    state: {
        addressData: [],
    },
    mutations: {
        /**
         * 案例模板
         * @param state
         * @param data
         * @constructor
         */
        UPDATEADDRESSDATA: (state, data) => {
          state.addressData = data
        },
    },
    actions:{
      UpdateAddressData(context, addressData){
         context.commit('UPDATEADDRESSDATA', addressData)
      }
    }
}

export default address
