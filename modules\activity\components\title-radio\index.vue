
<template>
  <view class="title-radio clear-float" :class="{'bdt':defaultConfig.bdt,'bdb':defaultConfig.bdb && !child,'horizontal':horizontal}">
    <view class="l-l"   :style="{'color': defaultConfig.titleColor}" :class="defaultConfig.titleClass">
      {{defaultConfig.label}}
      <text class="star" v-if="defaultConfig.required">*</text>
    </view>


    <template v-if="!horizontal">
      <view class="l-r">
        <view class="li" :style="{
          'min-width':cwidth
        }" v-for="(item, index) in array" :key="index" @tap="returnFn(index)">
          <em :class="[item.checked ? 'icon-yijianfankui-d-ok' : 'icon-yijianfankui-d']"></em>
          <text :class="{'color-topicC': item.checked}">{{ item.openStatus === 1 ? item.label : item.value}}</text>
          <input
            v-if="item.openStatus === 1 && item.checked && !item.noinput"
            :disabled="disabled"
            v-model="item.value"
            style="margin-bottom: 24upx;"
            @input="returnFn(index)"
          />
        </view>
      </view>
    </template>


    <template v-else>
      <view class="libox1">
        <view  class="libox">
          <view class="li"  v-for="(item, index) in array" :key="index" @tap="returnFn(index)">
            <em :class="[item.checked ? 'icon-yijianfankui-d-ok' : 'icon-yijianfankui-d']"></em>
            <text :class="{'color-topicC': item.checked}">{{ item.openStatus === 1 ? item.label : item.value}}</text>
            <input
              v-if="item.openStatus === 1 && item.checked && !item.noinput"
              :disabled="disabled"
              v-model="item.value"
              style="margin-bottom: 24upx;"
              @input="returnFn(index)"
            />
          </view>
        </view>
      </view>

    </template>


  </view>
</template>

<script>

export default {
  data() {
    return {
      form: {
        data: {
          select: '',
          extendValue: '',
          extendValueChecked: false
        }
      },
      array: [],
      index: 0,
      defaultConfig: {
        bdt: false,
        bdb: true,
        titleColor: '#333',
        textColor: '#333',
        label: '单选',
        name: 'select',
        required: false,
        array: [],
        dicKey: '',
        openStatus: 2
      },
      cwidth:"50%",
    }
  },
  watch: {
    cData: {
      handler(val) {
        this.watchDataMain(val)
      },
      deep: true
    },
    config: {
      handler(val) {
        this.copyConfig()
      },
      deep: true
    }
  },
  props: {
    horizontal:{
      type:Boolean,
      default:false,
    },
    child:{
      type:Boolean,
      default:false,
    },
    // 是否禁用disable
    disabled: {
      type: Boolean,
      default() {
        return false
      }
    },
    // 初始值传值，用于回显
    cData: {
      type: Array,
      required: false,
      default() {
        return []
      }
    },
    // 参数设置
    config: {
      type: Object,
      required: false,
      default: () => {
        return {}
      }
    },
    // updatecount:{
    //   type:
    // },
  },
  computed: {

  },
  mounted() {
    this.getDic(() => {
      this.copyConfig()
      // console.log('jjjk')
      console.log(this.cData)
      if (!this.$validate.isNull(this.cData)) {
        this.watchDataMain(this.cData)
      }else{
        for (let i = 0; i < this.array.length; i++) {
          if(this.array[i].openStatus == 1){
            if((this.array[i].label + '').length >= 8){
              this.cwidth = '100%';
            }
          }else{
            if((this.array[i].value + '').length >= 8){
              this.cwidth = '100%';
            }
          }
          // this.array[i].checked = false
          // if (isVal && val[0].id === this.array[i].id) {
          //   isVal = false
          //   this.array[i].checked = true
          //   this.array[i].value = val[0].value
          //   index = i
          // }
        }
      }
    })
  },
  methods: {
    /**
       * 初始化拷贝config对象
       */
    copyConfig() {
      const that = this
      const obj = that.config
      Object.keys(obj).forEach(function(key) {
        that.defaultConfig[key] = obj[key]
      })
    },
    /**
     * @param {Array<object>}
     * 监听Data主逻辑方法
     */
    watchDataMain(val) {
      console.log('斤斤计较')
      // val[0].id = val[0].value
      // debugger
      this.form.data.select = val[0].id
      let isVal = true
      let index = ''
      for (let i = 0; i < this.array.length; i++) {
        if(this.array[i].openStatus == 1){
          if((this.array[i].label + '').length >= 8){
            this.cwidth = '100%';
          }
        }else{
          if((this.array[i].value + '').length >= 8){
            this.cwidth = '100%';
          }
        }
        this.array[i].checked = false
        if (isVal && val[0].id === this.array[i].id) {
          isVal = false
          this.array[i].checked = true
          this.array[i].value = val[0].value
          index = i
        }
      }

      console.log('this.array[i]',this.array)
      console.log(index)
      if (index !== '') {
        this.$set(this.array, index, this.array[index])
      }
    },
    /**
       * 获取字典数据组
       */
    getDic(callBack) {
      const that = this
      const ar = that.config.array || []
      if (ar.length > 0) {
        that.array = ar
        for (const i in that.array) {
          that.array[i].checked = false
        }
        callBack()
        return
      }
      const params = {
        dictType: that.config.dicKey
      }
      that.$ext.dic.getDicInfo(params, (res) => {
        that.array = res
        for (const i in that.array) {
          that.array[i].checked = false
        }
        callBack()
      }, () => {
        this.array = []
        callBack()
      })
    },
    /**
       * picker触发选中事件
       * @param e
       */
    returnFn(index) {
      // debugger
      const that = this
      if (that.disabled) return
      for (const i in that.array) {
        that.array[i].checked = false
      }
      that.array[index].checked = true
      that.$set(that.array, index, that.array[index])
      const obj = that.array[index]
      that.form.data.select = obj.id

      let result = [{
        id:obj.id,
        value:obj.value,
        extendValue:that.form.data.extendValue
      }]
      that.$emit('updateForm', { key: '' + that.config.name, value: result})
    },
    extendValueFn () {
      this.form.data.extendValueChecked = !this.form.data.extendValueChecked
    }
  }
}
</script>

<style lang="scss" scoped>

  .color-topicC{
    color: $topicC !important;
  }
  .title-radio{
    //height: 100%;
    //overflow: hidden;
    background-color:#ffffff;
    .l-l{
      //float: left;
      //margin-left: 30upx;
      // line-height: 88upx;
      line-height: 1.5;
      color: #333333;
      font-weight: 600;
      font-size:30upx;
    }
    .l-l.font36{
      font-size: 36upx;
    }
    .l-r{
      //float: right;
      //margin-right: 30upx;
      display: flex;
      flex-wrap: wrap;
      padding-bottom: 20upx;


    }

    .li{
      min-width: 50%;
      flex: 1;
      //display: inline-block;
      //margin-right: 40upx;
      line-height: 88upx;
      em{
        vertical-align: middle;
        margin-right: 10upx;
            width: 24px !important;
            height: 24px !important;
      }
      .icon-yijianfankui-d{
        @include iconImg(36, 36, '/system/icon-yijianfankui-d.png');
      }
      .icon-yijianfankui-d-ok{
        @include iconImg(36, 36, '/system/icon-yijianfankui-d-ok.png');
      }
      text{
        font-size: 32upx;
        vertical-align: middle;
        // color: #999999;
        color: #000;
        margin-left: 10upx;

      }
      input {
        height: 80upx;
        line-height: 80upx;
        color: #333;
        font-size: 32upx;
        text-align: left;
        display: inline-block;
        vertical-align: middle;
        width: calc(100% - 64upx);
        border: 2upx solid #ede9e9;
        border-radius: 10upx;
      }
    }
    .li:last-child{
      margin-right: 0;
    }
  }
  .star{
    color: #F85E4C !important;
    padding-top: 6upx;
    font-size: 32upx;
    display: inline-block;
    margin-left: 10rpx;
  }

  .horizontal{
    display: flex;
    align-items: center;
    .libox1{
      flex:1;
      display: flex;
      justify-content: flex-end;
    }
    .libox{
      display: inline-flex;
      align-items: center;
      justify-content: flex-end;
      // flex:1
    }
    .l-l{
      font-weight: 500 !important;
    }
    .l-r{
      flex:1;
      display: flex;
      justify-content: flex-end;
    }
    .li{
     display: flex;
     align-items: center;
     min-width: auto!important;
    }

    text{
      font-size: 24upx!important;
      vertical-align: middle;
      // color: #999999;
      color: #000;
      margin-left: 10upx;
      margin-right: 20upx;
      white-space: nowrap


    }

    // em{
    //   width:14px;
    //   height:14px
    // }
  }


</style>
