<template>
  <view>
    <view class="guidanceBox">
      <view class="guidanceTitle">
        <image class="cencelTitleIcon" :src="heartAdd"></image>
        {{clockMap.length ? '服务中' : '等待服务'}}
      </view>

      <view class="insureSign"  v-if="insuredInfo.pay && !insuredInfo.refundInsure">
        <view class="">门诊无忧保障中，就诊更安心</view>
        <view class="surrender" @click="clearEnsure">取消保障</view>
      </view>
      <!-- 已取消保障文案 -->
      <view class="insureSign" v-if="insuredInfo.pay && insuredInfo.refundInsure">
          <view class="">已取消保障，已退款￥{{insuredInfo.price / 100}}</view>
      </view>
      <view class="guidanceCard">
        <view class="headerTab" @click="selectServer(item)">
          <!-- 服务名称 -->
          <view class="serverTitle">
            <image class="serverIcon" :src="file_ctx + accompanybookOne.serviceDetailImg" mode=""></image>
            <view class="serviceName">
              <view class="serverNameT">{{accompanybookOne.serviceName}}</view>
              <view class="serverTime"><text class="tabTitle">陪诊时间:</text>{{timestampToDateTime(accompanybookOne.startTime)}}~{{timestampToDateTime(accompanybookOne.endTime,true)}}</view>
              <view class="serverTime"><text class="tabTitle">就诊医院:</text>{{accompanybookOne.hospitalName}}</view>
              <view class="employee">
                <text class="tabTitle">陪诊师</text>:
                <image class="avatar" :src="file_ctx + avatar" mode=""></image>
                {{accompanybookOne.employeeName}}
              </view>
            </view>
          </view>

          <view class="orderMap">
            <view class="serverNameT">订单信息</view>
            <view class="orderItem">
            <text class="orderTitle">订单号</text>
              <view class="">
                {{accompanybookOne.id}}
                <text class="copy" @click="copyOrderId(accompanybookOne.id)">复制</text>
              </view>
            </view>
            <view class="orderItem">
              <text class="orderTitle">服务项目</text>
              {{accompanybookOne.serviceName}}
            </view>
            <view class="orderItem">
              <text class="orderTitle">创建时间</text>
              {{timestampToDateTime(accompanybookOne.createTime)}}
            </view>
          </view>

          <view class="orderMap">
            <view class="serverNameT">
              <view class="serverNameTitle">总价格</view>
              <text class="payPrice" v-if="insuredInfo.pay && !insuredInfo.refundInsure">￥{{accompanybookOne.payPrice / 100 + serverOptions.productCodeMap.filter(e=>e.productCode === insuredInfo.productCode)[0].insuranceNum}}</text>
              <text class="payPrice" v-else>￥{{accompanybookOne.payPrice / 100 }}</text>
            </view>
            <view class="orderItem">
            <text class="orderTitle">服务费</text>
            ￥{{accompanybookOne.payPrice / 100}}
            </view>
            <view class="orderItem">
              <text class="orderTitle">支付方式</text>
              {{getOrderType()}}
            </view>
            <view class="orderItem" v-if="insuredInfo.pay && !insuredInfo.refundInsure">
              <text class="orderTitle">门诊无忧服务</text>
              ￥{{serverOptions.productCodeMap.filter(e=>e.productCode === insuredInfo.productCode)[0].insuranceNum}}
            </view>
            <view class="orderItem">
              <text  class="orderTitle">支付时间</text>
              {{timestampToDateTime(accompanybookOne.payTime)}}
            </view>
          </view>

          <!-- <view class="service-record" v-if='clockMap.length'>
            <view class="infoTitle">服务记录</view>
              <view class="infoBox">
                <view class="clock-start" v-for="(item,index) in clockMap" :key="index">
                <view class="clock-start-h">
                  <view class="clock-start-h-l">
                    <view class="time">{{timestampToDateTime(item.createTime)}}</view>
                    <view class="title">签{{['到','出'][index]}}打卡，开始服务</view>
                  </view>
                </view>
                <view class="clock-start-b" v-if="item.imagePath">
                  <image :src="file_ctx + imageItem" class="clock-start-item" v-for="imageItem in item.imagePath.split(',')" :key="imageItem"></image>
                </view>
                </view>
              </view>
            </view> -->
          </view>
      </view>
    </view>
    <view class="buttonMap" v-if="insuredInfo.pay && !insuredInfo.refundInsure">
      <view class="buttonItem" @click="getEpolicyUrl">门诊无忧服务单</view>
      <view class="buttonItem" @click="$navto.push('insuranceList',{accompanyId:accompanybookOne.id}) ">门诊无忧服务开票</view>
    </view>
    <view class="clearBtn" v-if="!clockMap.length" @click="clearOrder">取消订单</view>
  </view>
</template>

<script>
function debounce(func, delay) {
  let timer;
  return (...args)=> {
    clearTimeout(timer);
    timer = setTimeout(() => {
      func.apply(this, args);
    }, delay);
  };
}
  import serverOptions from '@/config/env/options'
  export default{
    props:{
      accompanybookOne:{
        type:Object,
      },
      // 购买人信息
      insuredInfo:{
        type:Object,
        default:{}
      }
    },
    data(){
      return {
        serverOptions,
        file_ctx:this.file_ctx,
        currentServer:{},
        serverCurrent:{},
        avatar:'',
        clockMap:[],
        heartAdd: this.$static_ctx + "image/business/hulu-v2/heart-add.png",
        clearEnsure:debounce(()=>this.$emit('ConfirmPurchaseInsurance','surrender'),300),
      }
    },
    async mounted() {
        let {data:clockMap} = await this.$api.accompanyDoctor.signinlogGetLogList({
          businessType:4,
          businessId:this.accompanybookOne.id
        })
        this.clockMap = clockMap;
        console.log('clockMap',clockMap);
    },
    computed:{
      async getAccompanybookOne(){
        let {data:{records:[serverCurrent]}} = await this.$api.accompanyDoctor.getAccompanyservicePage({
          condition:{serviceId:this.accompanybookOne.serviceId},
        })
        this.serverCurrent = serverCurrent;
        if(this.accompanybookOne.employeeId){
          let {data:{avatar}} = await this.$api.accompanyDoctor.getAccompanyemployeeQueryOne({
          id:this.accompanybookOne.employeeId
          })
          this.avatar = avatar;
        }
        return this.accompanybookOne
      },
    },
    methods:{
      getOrderType(){
        let {comboPay,orderType,payType} = this.accompanybookOne;
        if(comboPay) return '套餐支付';
        if(orderType) return orderType === 1 ? '线下支付' : '联合订单支付';
        return ['','微信支付','支付宝支付'][payType];
      },
      async getEpolicyUrl(){
        let data = await this.$api.accompanyDoctor.accompanyinsureEpolicyUrl({accompanyId:this.accompanybookOne.id})
        wx.downloadFile({ //将文档下载到本地
                url: data.msg,//pdf链接
                success(res) {
                  wx.openDocument({ //打开文档
                    filePath: res.tempFilePath,//本地文档路径
                    fileType: "pdf",//文档类型
                    showMenu: true,
                    success: function (res) {
                      wx.showToast({
                        title: '打开文档成功',
                      })
                    },
                    fail: function (res) {
                      wx.showToast({
                        title: '打开文档失败',
                      })
                    },
                  })
                },
              })
      },
      copyOrderId(data){
        uni.setClipboardData({
        	data,
        	success: function () {
        		console.log('success');
        	}
        });
      },
      clearOrder(){
        if(this.accompanybookOne.pay){
          return this.$emit('clearOrder');
        }
        uni.showModal({
        title: '提示',
        content: '确定要取消订单吗？',
        success: async (res)=> {
          if (res.confirm) {
            let {data:{records,total}} = await this.$api.accompanyDoctor.accompanybookCancel({
              id:this.accompanybookOne.id,
              cancelReason: "用户主动取消",
              refundAmount: this.accompanybookOne.payPrice || 0 // 全额退款，单位分
            });
            uni.navigateBack()
            setTimeout(function() {
              uni.showToast({
                title:'取消成功',
                icon:'none'
              })
            }, 10);
          }
        }
        });
      },
      timestampToDateTime(timestamp,flag) {
        // 创建一个新的Date对象，传入的时间戳是以毫秒为单位的
        var date = new Date(timestamp);
        // 获取年、月、日、时、分、秒
        var year = date.getFullYear();
        var month = ("0" + (date.getMonth() + 1)).slice(-2); // 月份是从0开始的，所以+1，并补零
        var day = ("0" + date.getDate()).slice(-2); // 获取天数，并补零
        var hour = ("0" + date.getHours()).slice(-2); // 获取小时，并补零
        var minute = ("0" + date.getMinutes()).slice(-2); // 获取分钟，并补零
        var second = ("0" + date.getSeconds()).slice(-2); // 获取秒数，并补零
        if(flag) return `${minute}:${second}`
        // 返回格式化的字符串
        return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
      }

    }
  }
</script>

<style lang="scss">
  .buttonMap{
      display: flex;
      justify-content: space-between;
      margin: 22rpx 0 22rpx 0;
      padding: 0 32rpx;
      box-sizing: border-box;
      width: 100%;
    .buttonItem{
      width: 334rpx;
      height: 88rpx;
      background: #FFFFFF;
      border-radius: 44rpx 44rpx 44rpx 44rpx;
      border: 2rpx solid #D9DBE0;
      font-weight: 400;
      font-size: 32rpx;
      color: #1D2029;
      text-align: center;
      line-height: 88rpx;
    }
  }
  .cencelTitleIcon{
    width: 48rpx;
    height: 48rpx;
    margin-right: 12rpx;
  }
  .insureSign{
    width: 100%;
    height: 60rpx;
    background: #E0F4EF;
    border-radius: 8rpx 8rpx 8rpx 8rpx;
    border: 1rpx solid #AAD8CC;
    display: flex;
    justify-content: space-between;
    padding: 0 24rpx;
    align-items: center;
    font-weight: 500;
    font-size: 26rpx;
    color: #00926B;
    margin: 32rpx 0;
    box-sizing: border-box;
    .surrender{

    }
  }
  .service-record{
    padding-top: 20rpx;
    background: #FFFFFF;
    border-radius: 16rpx;
    overflow: hidden;
    .title{
      font-size: 28rpx;
      color: #1D2029;
      line-height: 40rpx;
    }
    .infoBox{

    }
    .clock-start{
      .clock-start-h{
        display: flex;
        justify-content: space-between;
        .clock-start-h-l{
          .time{
            font-size: 28rpx;
            color: #4E5569;
            line-height: 40rpx;
          }
          .title{
            margin-top: 16rpx;
            font-size: 28rpx;
            color: #1D2029;
            line-height: 40rpx;
          }
        }
        .clock-start-h-r{
          display: flex;
          align-items: center;
          justify-content: center;
          width: 184rpx;
          height: 64rpx;
          background: #FFFFFF;
          border-radius: 8rpx;
          border: 1rpx solid #D9DBE0;
          font-size: 28rpx;
          color: #1D2029;
        }
      }
      .clock-start-b{
        display: flex;
        flex-wrap: wrap;
        padding: 16rpx;
        background: #F4F6FA;
        border-radius: 16rpx;
        overflow: hidden;
        .clock-start-item{
          width: 170rpx;
          height: 170rpx;
          border-radius: 16rpx;
          margin-right: 16rpx;
          margin-bottom: 18rpx;
          &:nth-child(3n){
            margin-right: 0;
          }
        }
      }
    }
  }
  .orderItem{
    width: 100%;
    font-weight: 400;
    font-size: 26rpx;
    color: #4E5569;
    display: flex;
    justify-content: space-between;
    margin-bottom: 16rpx;
    .orderTitle{
      font-weight: 400;
      font-size: 26rpx;
      color: #1D2029;
    }
    .payPrice{
      font-weight: 500;
      font-size: 26rpx;
      color: #FF5500;
    }
    .copy{
      font-weight: 400;
      font-size: 26rpx;
      color: #316EAB;
      margin-left: 8rpx;
    }
  }
  .serverNameT{
    display: flex;
    justify-content:space-between;
    .serverNameTitle{
      font-weight: 600;
      font-size: 32rpx;
      color: #1D2029;
    }
    .payPrice{
      font-weight: 500;
      font-size: 36rpx;
      color: #FF5500;
    }
  }
  .orderMap{
    // border-bottom: 2rpx solid #EAEBF0;
    padding-bottom: 32rpx;
    .serverNameT{
      margin: 32rpx 0;
    }
  }
  .serverTitle{
    display: flex;
    padding-bottom: 32rpx;
    border-bottom: 2rpx solid #EAEBF0;
    .paySign{
      font-weight: 400;
      font-size: 22rpx;
      color: #FF5500;
    }
    .payNums{
      font-weight: 500;
      font-size: 36rpx;
      color: #FF5500;
    }
    .serverIcon{
      width: 144rpx;
      height: 144rpx;
      background: #D8D8D8;
      border-radius: 12rpx;
      border: 1rpx solid #D9DBE0;
      margin-right: 20rpx;
      flex-shrink: 0;
    }
    .serviceName{
      font-weight: 500;
      font-size: 32rpx;
      color: #1D2029;
      display: flex;
      flex-wrap: wrap;
      .serverTime{
        width: 100%;
        font-weight: 400;
        font-size: 24rpx;
        color: #1D2029;
      }
      .employee{
        font-weight: 400;
        font-size: 22rpx;
        color: #1D2029;
        display: flex;
        align-items: center;
        .avatar{
          width: 32rpx;
          height: 32rpx;
          margin-left: 20rpx;
          border-radius: 50%;
          margin-right: 8rpx;
        }
      }
      .signal{
        font-weight: 400;
        font-size: 22rpx;
        color: #FF5500;
      }
      .serverNum{
        font-weight: 500;
        font-size: 36rpx;
        color: #FF5500;
      }
      .tag{
        font-weight: 400;
        font-size: 20rpx;
        color: #868C9C;
      }
    }
  }
  .guidanceBox{
    width: 100vw;
    height: calc(100vh - 274rpx - 350rpx - 60rpx);
    padding: 0 32rpx;
    box-sizing: border-box;
    overflow: scroll;
  }
  .guidanceCard{
    width: 686rpx;
    background: #FFFFFF;
    border-radius: 16rpx;
    margin-top: 32rpx;
    .headerTab{
      width: 686rpx;
      background: #FFFFFF;
      border-radius: 16rpx;
      padding: 24rpx;
      box-sizing: border-box;
      .serverIcon{
        width: 144rpx;
        height: 144rpx;
        background: #D8D8D8;
        border-radius: 12rpx;
        border: 1rpx solid #D9DBE0;
        margin-right: 20rpx;
      }
      .serviceName{
        font-weight: 500;
        font-size: 32rpx;
        color: #1D2029;
        .signal{
          font-weight: 400;
          font-size: 22rpx;
          color: #FF5500;
        }
        .serverNum{
          font-weight: 500;
          font-size: 36rpx;
          color: #FF5500;
        }
        .tag{
          font-weight: 400;
          font-size: 20rpx;
          color: #868C9C;
        }
      }
      .changeServer{
        width: 148rpx;
        height: 52rpx;
        background: #FFFFFF;
        border-radius: 36rpx;
        border: 1rpx solid #D9DBE0;
        margin-left: auto;
      }
    }
  }
  .guidanceTitle{
    font-weight: 500;
    font-size: 36rpx;
    color: #1D2029;
    display: flex;
  }
  .guidanceTwoTitle{
    font-weight: 400;
    font-size: 26rpx;
    color: #868C9C;
  }
  .clearBtn{
    width: 684rpx;
    height: 88rpx;
    background: #FFFFFF;
    border-radius: 44rpx;
    border: 1rpx solid #D9DBE0;
    font-weight: 400;
    font-size: 32rpx;
    color: #4E5569;
    text-align: center;
    line-height: 88rpx;
    margin: 32rpx auto;
  }

</style>
