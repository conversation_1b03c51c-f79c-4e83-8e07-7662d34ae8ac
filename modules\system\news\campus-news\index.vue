<template>
  <page>
    <view slot="content" class="main-body">
      <scroll-refresh class="scroll-refresh-main" :fixed="false" :up="upOption" :down="downOption" @returnFn="returnFn" @scrollInit="scrollInit">
        <view class="mian">
          <view class="list" v-for="(item,index) in pdList" :key="index" @tap="navtoFn(item)">
            <view class="list-body">
              <view class="tag" :class="[typeReturnFn(item.businessType)]">{{typeReturnFn(item.businessType, 'name')}}</view>
              <view v-if="item.readStatus === 2" class="offiCialone"></view>
              <view class="t">
                {{item.createTimeStr}}
              </view>
              <view class="tag-bor-main">
                <view class="tag-bor">
                  {{item.receiveType === 1 ? '全体' : '定向'}}
                </view>
              </view>
              <view class="m">
                <view class="title">
                  {{item.title}}
                </view>
                <view class="content">
                  {{item.content}}
                </view>
              </view>
              <view class="b">
                发布人：{{item.author? item.author : '管理员'}}
              </view>
            </view>
          </view>
        </view>
      </scroll-refresh>
    </view>
  </page>
</template>

<script>
import { mapState } from 'vuex'
import TabsSticky from '@/components/basics/tabs-sticky'
export default {
  components: {
    TabsSticky
  },
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $accurateConversion: this.$accurateConversion,
      file_ctx: this.file_ctx,
      $timePlugin: this.$timePlugin,
      $static_ctx: this.$static_ctx,
      regForm: {},
      mescroll: null, // mescroll实例对象
      downOption: {
        auto: false // 不自动加载
      },
      upOption: {
        auto: false // 不自动加载
      },
      pdList: [] // 列表数据
    }
  },
  computed: {
    ...mapState('user', {
      codeUserInfo: state => state.codeUserInfo, // 当前登录用户信息
      curSelectUserInfo: state => state.curSelectUserInfo, // 当前选中的用户信息
      curSelectUserInfoId: state => state.curSelectUserInfoId, // 当前选中的档案的ID
    })
  },
  onLoad(paramsObj) {
    const query = this.$Route.query
    if (!this.$validate.isNull(query)) {}
  },
  onShow() {
    this.init()
  },
  methods: {
    init() {
      this.$nextTick(() => {
        this.pdList = []
        this.mescroll.triggerDownScroll()
      })
    },
    navtoFn(item) {
      if (item.businessType === 1) { // 通知
        this.$navto.push('NoticeAdviceDetail', { id: item.id, index: 2 })
      }
      let result = []
      for (let obj of this.pdList) {
          if (obj.id === item.id) {
              obj.readStatus = 1
          }
        result.push(obj)
      }
      this.pdList = result
    },
    typeReturnFn(type, mark) {
      let name = ''
      let tonal = ''
      if (type === 1) {
        name = '普通通知'
        tonal = 'icon-bg-orange'
      } else if (type === 2) {
        name = '待办通知'
        tonal = 'icon-bg-yellow'
      }
      // else if (type === this.$constant.noun.dynamicHomework) {
      //   name = '亲子任务'
      //   tonal = 'icon-bg-green'
      // } else if (type === this.$constant.noun.dynamicDietNotice) {
      //   name = '食谱预告'
      //   tonal = 'icon-bg-blue'
      // } else if (type === this.$constant.noun.dynamicCareCollect) {
      //   name = '每日叮嘱'
      //   tonal = 'icon-bg-deep-orange'
      // } else if (type === this.$constant.noun.dynamicWeekcareTeachingReport) {
      //   name = '周保教汇总'
      //   tonal = 'icon-bg-purple'
      // } else if (type === this.$constant.noun.dynamicNotifications) {
      //   name = '通知提醒'
      //   tonal = 'icon-bg-red'
      // }
      return mark === 'name' ? name : tonal
    },
    scrollInit(scroll) {
      scroll.optUp.page.num = 1
      scroll.optUp.page.size = 5
      this.mescroll = scroll
    },
    changeReadFn(id, fn) {
      this.$uniPlugin.loading('加载中...', true)
      this.$api.common.commondynamicChangeRead({ id: id }).then(res => {
        fn()
        this.$uniPlugin.hideLoading()
      })
    },
    returnFn(obj) {
      const that = this
      function queryPage(pageNum, pageSize, fn) {
        const param = {
          current: pageNum,
          size: pageSize,
          condition: {
            userType: that.$userType,
            tenantId: 1
          }
        }

        that.$api.common.noticelogQueryPage(param).then(res => {
          if (res && res.data.records) {
            let list = res.data.records
            fn(list)
          }
        })

      }
      setTimeout(function() {
        queryPage(obj.pageNum, obj.pageSize, (data) => {
          if (obj.pageNum === 1) {
            that.pdList = []
          }
          for(const i in data) {
            data[i].createTimeStr = that.$timePlugin.formatDate(new Date().valueOf(), data[i].createTime)
          }
          that.pdList = that.pdList.concat(data)
          obj.successCallback && obj.successCallback(data || [])
        })
      }, that.$constant.noun.scrollRefreshTime)
    }
  }
}
</script>

<style lang="scss" scoped>
  .icon-bg-orange{
    @include iconImg(120,44,'/business/news/icon-bg-orange.png');
    background-size: 100% 100%;
  }
  .icon-bg-yellow{
    @include iconImg(120,44,'/business/news/icon-bg-yellow.png');
    background-size: 100% 100%;
  }
  .icon-bg-green{
    @include iconImg(120,44,'/business/news/icon-bg-green.png');
    background-size: 100% 100%;
  }
  .icon-bg-blue{
    @include iconImg(120,44,'/business/news/icon-bg-blue.png');
    background-size: 100% 100%;
  }
  .icon-bg-red{
    @include iconImg(120,44,'/business/news/icon-bg-red.png');
    background-size: 100% 100%;
  }
  .icon-bg-deep-orange{
    @include iconImg(120,44,'/business/news/icon-bg-deep-orange.png');
    background-size: 100% 100%;
  }
  .icon-bg-purple{
    @include iconImg(150,44,'/business/news/icon-bg-purple.png');
    background-size: 100% 100%;
  }
  .main-body{
    height: 100%;
    .scroll-refresh-main{
      height: 100%;
    }
  }
  .mian{
    padding: 30upx;
    .list{
      background-color: #fff;
      @include rounded(20upx);
      margin-bottom: 30upx;
      .list-body{
        padding: 30upx;
        position: relative;
        overflow: hidden;
        .tag{
          position: absolute;
          left: 0;
          top: 0;
          text-align: center;
          overflow: hidden;
          font-size: 22upx;
          line-height: 44upx;
          color: #FFFFFF;
        }
        .t{
          font-size: 24upx;
          line-height: 36upx;
          color: #999;
          text-align: right;
          @include ellipsis(1);
          display: block;
        }
        .tag-bor-main{
          margin-bottom: 10upx;
          .tag-bor{
            display: inline-block;
            border: 2upx solid $topicC;
            color: $topicC;
            font-size: 24upx;
            line-height: 36upx;
            padding: 0upx 20upx;
            @include rounded(6upx);
          }
        }
        .m{
          margin-bottom: 20upx;
          .title{
            color: #333;
            font-size: 32upx;
            line-height: 48upx;
            font-weight:500;
            @include ellipsis(1);
            display: block;
          }
          .content{
            font-size: 28upx;
            line-height: 42upx;
            color: #666;
            @include ellipsis(2);
            display: block;
          }
        }
        .b{
          margin-top: 20upx;
          color: #999;
          font-size: 20upx;
          line-height: 30upx;
        }
        .offiCialone{
          position: absolute;
          top: 12upx;
          right: 12upx;
          width:18upx;
          height:18upx;
          background: #FF4A4A;
          @include rounded(50%);
          overflow: hidden;
        }
      }
    }
  }
</style>
