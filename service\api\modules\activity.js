import request from '@/common/util/request'
import env from '@/config/env'

/**
 * 社区请求工具类，
 * 注意：API接口只做请求数据和格式化数据的事情，其他事务一概不处理
 */
export default {

    // /dm/api/v1/hospitalserviceevaluate/query/one
    hospitalserviceevaluatequeryone (param) {
        const url = env.ctx + 'dm/api/v1/hospitalserviceevaluate/query/one'
        return request.get(url, param)
    },
    //
    hospitalserviceevaluatequerypage (param) {
        const url = env.ctx + 'dm/api/v1/hospitalserviceevaluatelog/query/page'
        return request.postJson(url, param)
    },
    /**
     * 根据模板ids获取对应模板结构
     * @param param
     * @returns {*}
     */
    queryBusinessTemplateDTOList(param) {
      const url = env.ctx + 'dm/api/v1/common/queryBusinessTemplateDTOList'
      return request.get(url, param)
    },

    // 二期保存模板节点数据
    submitformtemplatewrite(param) {
      const url = env.ctx + 'dm/api/v2/formtemplatewrite/formWrite'
      return request.postJson(url, param)
    },
    // /
    hospitalserviceevaluateloginsert(param){
      const url = env.ctx + 'dm/api/v1/hospitalserviceevaluatelog/insert'
      return request.postJson(url, param)
    },

    // 字典 /manage/api/hospital/listAllHospital
    hospitallistAllHospital(param){
      // const url = env.ctx + 'manage/api/hospital/queryList'
      const url = env.ctx + 'manage/api/installUnit/listAllByInstallType'

      return request.postJson(url, param)
    },

    // 获取

    hospitalserviceevaluatelogqueryone (param) {
        const url = env.ctx + 'dm/api/v1/hospitalserviceevaluatelog/query/one'
        return request.get(url, param)
    },
    // 获取默认安装单位 packetRecord/queryPage
    packetRecordqueryPage (param) {
        const url = env.ctx + 'manage/api/packetRecord/queryPage'
        return request.postJson(url, param)
    },
	// 获取默认安装单位 v2
	packetRecordGetNewPacketLog (param) {
	    const url = env.ctx + 'manage/api/packetRecord/getNewPacketLog'
	    return request.get(url, param)
	},
    // 添加小程序粉丝
    // fansrecordAddMinappSource (param) {
    //     const url = env.ctx + 'dm/api/v1/fansrecord/add/minapp/source'
    //     return request.postJson(url, param)
    // },
    /**
   *  二期病例征集 --- 根据主键单一查询
   * @param param
   * @returns {*}
   */
  researchqueryBySnapshotId(param) {
    const url = env.ctx + '/v1/research/queryBySnapshotId'
    return request.get(url, param)
  },
  /**
   * 学术调研 分页接口
   * @param resolve
   * @param reject
   */
   researchQueryPage(param) {
    const url = env.ctx + 'dm/api/v1/research/query/page'
    return request.postJson(url, param)
  },
  /**
   * 学术调研--根据主键单一查询
   * @param param
   * @returns {*}
   */
  researchQueryOne(param, headers) {
    const url = env.ctx + 'dm/api/v1/research/query/one'
    return request.get(url, param, headers)
  },
  // 二期保存填写病例模板节点数据
  casecollectsubmitlogformWrite(param, headers) {
    const url = env.ctx + 'dm/api/v1/casecollectsubmitlog/casecollect/formwrite'
    return request.postJson(url, param, headers)
  },
  // 二期获取病例列表 /v1/casecollectsubmitlog/query/submitLog/page
  casecollectsubmitlogquerylist(param, headers) {
    const url = env.ctx + 'dm/api/v1/casecollectsubmitlog/query/submitLog/page'
    return request.postJson(url, param, headers)
  },
  // 二期病例征集类型数量统计
  casecollectsubmitlogtypecount(param) {
	  // 小程序
    param.isApplet = 1;
    const url = env.ctx + 'dm/api/v1/casecollectsubmitlog/get/submit/type/count'
    return request.get(url,param)
  },
  // 二期病例征集移除操作 /dm/api/v1/casecollectsubmitlog/delete/batch/
  casecollectsubmitlogdeletebatch(param) {
    const url = env.ctx + 'dm/api/v1/casecollectsubmitlog/delete/batch/' + param.ids
    return request.delete(url)
  },
  // 二期病例表单提交成待审核
  casecollectsubmitlogcommitwait(param) {
    const url = env.ctx + 'dm/api/v1/casecollectsubmitlog/commit/wait/audit'
    return request.postForm(url, param)
  },
  // 根据问卷地推扫码返回的id获取问卷任务详情
  dmactivityexpandconfigQueryOneTaskId(param, headers) {
    const url = env.ctx + 'dm/api/v1/dmactivityexpandconfig/query/one/taskId'
    return request.get(url, param, headers)
  },
  /**
   *  二期病例征集 --- 根据主键单一查询
   * @param param
   * @returns {*}
   */
  businesstemplatequeryone(param) {
    const url = env.ctx + '/v1/businesstemplate/query/one'
    return request.get(url, param)
  },

  /**
   *  二期病例征集 --- 根据主键单一查询
   * @param param
   * @returns {*}
   */

  researchqueryBySnapshotId(param) {
    const url = env.ctx + '/v1/research/queryBySnapshotId'
    return request.get(url, param)
  },

  // 二期获取病例详情
  casecollectactivityQueryOne(param) {
      const url = env.ctx + 'dm/api/v1/casecollectactivity/query/one'
      return request.get(url, param)
  },

  // 二期获取对应病例步骤
  casecollectflowconfigsteplist(param, headers) {
      const url = env.ctx + 'dm/api/v2/casecollectflowconfig/get/list/by/mainid'
      return request.get(url, param, headers)
  },

  // 二期获取填写表单记录 /v1/casecollectsubmitlog/get/current/submit/collect
  casecollectsubmitlogsubmit(param, headers) {
      const url = env.ctx + 'dm/api/v1/casecollectsubmitlog/get/current/submit/collect'
      return request.get(url, param, headers)
  },
  // 二期根据节点获取模板详情
  casecollectsubmitlogqueryone(param) {
      const url = env.ctx + 'dm/api/v1/businesstemplate/query/one'
      return request.get(url, param)
  },
  // 二期保存填写病例模板节点数据
  casecollectsubmitlogformWrite(param, headers) {
    const url = env.ctx + 'dm/api/v1/casecollectsubmitlog/casecollect/formwrite'
    return request.postJson(url, param, headers)
  },

  // 二期获取病例列表 /v1/casecollectsubmitlog/query/submitLog/page
  casecollectsubmitlogquerylistV2(param, headers) {
    const url = env.ctx + 'dm/api/v2/casecollectsubmitlog/query/submitLog/page'
    return request.postJson(url, param, headers)
  },


  // 二期病例表单提交成待审核
  casecollectsubmitlogcommitwait(param) {
    const url = env.ctx + 'dm/api/v1/casecollectsubmitlog/commit/wait/audit'
    return request.postForm(url, param)
  },


  // 二期病例征集移除操作 /dm/api/v1/casecollectsubmitlog/delete/batch/
  casecollectsubmitlogdeletebatch(param) {
    const url = env.ctx + 'dm/api/v1/casecollectsubmitlog/delete/batch/' + param.ids
    return request.delete(url)
  },


  // 二期获取节点详情接口/v1/casecollectflowconfig/query/one?id=837675370022162434 2023-3-27
  casecollectflowconfigqueryone(param, headers) {
    const url = env.ctx + 'dm/api/v1/casecollectflowconfig/query/one'
    return request.get(url, param, headers)
  },

  // 二期病例征集类型数量统计
  casecollectsubmitlogtypecount(param) {

	  // 小程序
	  param.isApplet = 1;
    const url = env.ctx + 'dm/api/v1/casecollectsubmitlog/get/submit/type/count'
    return request.get(url,param)
  },


  // 二期病例征集患者类型对应key的名称
  codefindByParentId(param, headers) {
    const url = env.ctx + 'manage/api/code/findByParentId'
    return request.get(url, param, headers)
  },


  // 二期病例征集患者获取历史填写表单
//
  casecollectsubmititemlogbyflowid(param, headers) {
    const url = env.ctx + 'dm/api/v1/casecollectsubmititemlog/get/submititemlog/by/flowid'
    return request.get(url, param, headers)
  },


  // 二期回访周期列表 /v1/casecollectvisitconfig/get/list/by/mainid
  casecollectvisitconfiggetlistmainid(param) {
    const url = env.ctx + 'dm/api/v2/casecollectvisitconfig/get/list/by/mainid'
    return request.get(url,param)
  },


  // 二期待回访列表
  casecollectsubmitlogqueryvisit(param) {
    const url = env.ctx + 'dm/api/v1/casecollectsubmitlog/query/visit/page'
    return request.postJson(url,param)
  },

  // 二期待回访周期获取节点 casecollectvisitconfig
  casecollectvisitconfigqueryone(param) {
      const url = env.ctx + 'dm/api/v1/casecollectvisitconfig/query/one'
      return request.get(url, param)
  },


  // 二期获取回访条目统计
  casecollectsubmitlogvisitlogcount(param) {
      const url = env.ctx + 'dm/api/v1/casecollectsubmitlog/get/visit/log/count'
      return request.get(url, param)
  },

  // 二期获取病例提交份数
  casecollectsubmitlogmysubmitlogcount(param) {
      const url = env.ctx + 'dm/api/v1/casecollectsubmitlog/get/my/submitlog/count'
      return request.get(url, param)
  },


  // 根据医生id 获取绑定的患者 /dm/api/v1/physicianattacheinvitere/get/patientinfo/by/physicianid
  getpatientinfophysicianid(param){
    const url = env.ctx + 'dm/api/v1/physicianattacheinvitere/get/patientinfo/by/physicianid'
    return request.get(url, param)
  },

  // 保存患者档案
  savepatientinfophysicianid(param){
    const url = env.ctx + 'dm/api/v1/patientinfo/insert'
    return request.postJson(url, param)
  },


  // /v1/patientinfo/update

  // 更新患者病程
  updatepatientinfophysicianid(param){
    const url = env.ctx + 'dm/api/v1/patientinfo/update'
    return request.putJson(url, param)
  },

  // 获取单一患者档案
  getgetpatientinfophysicianidone(param) {
    const url = env.ctx + 'dm/api/v1/patientinfo/query/one'
    return request.get(url, param)
  },


  // 获取拜访列表
  getvisitingplanpage(param){
    const url = env.ctx + 'dm/api/v1/visitingplan/query/applet/page'
    return request.postJson(url, param)
  },

  // 获取拜访详情
  // /v1/visitingplan/get/visitingplan
  getvisitingplan(param){
    const url = env.ctx + 'dm/api/v1/visitingplan/get/visitingplan'
    return request.get(url, param)
  },

  // 完善拜访对象 /v1/visitingplanobjectlist/commit
  visitingplanobjectlistcommit(param){
    const url = env.ctx + 'dm/api/v1/visitingplanobjectlist/commit'
    return request.putJson(url, param)
  },
  // 拜访计划对象单一查询
  // /dm/api/v1/visitingplanobjectlist/query/one
  // /v1/
  getvisitingplanobjectlistqueryone(param){
    const url = env.ctx + 'dm/api/v1/visitingplanobjectlist/get/customer/detail'
    return request.get(url, param)
  },


  // 获取签到对象详情 /v1/
  getSigninlogBusinessid(param) {
    const url = env.ctx + 'dm/api/v1/signinlog/get/signinlog/by/businesstype/and/businessid'
    return request.get(url, param)
  },

  // 获取签到列表 /dm/api/v1/signinlog/query/list
  getSigninlogQueryList(param) {
    const url = env.ctx + 'dm/api/v1/signinlog/query/list'
    return request.get(url, param)
  },

  // 获取签到详情 /dm/api/v1/signinlog/query/one
  getSigninlogQueryOne(param) {
    const url = env.ctx + 'dm/api/v1/signinlog/query/one'
    return request.get(url, param)
  },


  // 保存签到 /dm/api/v1/signinlog/insert
  savesigninlogInsert(param){
    const url = env.ctx + 'dm/api/v1/signinlog/insert'
    return request.postJson(url, param)
  },

  // 更新签到信息 /dm/api/v1/visitingplanobjectlist/update/sign
  visitingplanobjectlistupdate(param){
    const url = env.ctx + 'dm/api/v1/visitingplanobjectlist/update/sign'
    return request.putForm(url, param)
  },
  // 更新签到
  updatesigninlogInsert(param){
    const url = env.ctx + 'dm/api/v1/signinlog/update'
    return request.putJson(url, param)
  },


  // 获取所有备案机构
  listAllByInstallType(data) {
    const url = env.ctx + 'manage/api/installUnit/listAllByInstallType'
    return request.postJson(url, data)
  },

  // 根据父节点id查询字典表
  findByParentId(data) {
    const url = env.ctx + 'manage/api/code/findByParentId'
    return request.get(url, data)
  },

  // 保存新增客户
  insertCustomer (data) {
    const url = env.ctx + 'dm/api/v1/customer/insert'
    return request.postJson(url, data)
  },
  // 更新客户
  updateCustomer(data) {
    const url = env.ctx + 'dm/api/v1/customer/update'
    return request.putJson(url, data)
  },
  // 获取客户列表
  getCustomerList(data){
    const url = env.ctx + 'dm/api/v1/customer/query/page'
    return request.postJson(url, data)
  },

  // 删除客户 // 根据主键id指定删除
  deleteCustomerOne (data) {
    const url = env.ctx + `dm/api/v1/customer/delete/one/${data.id}`
    return request.delete(url, data)
  },

  // 单一查询客户
  getCustomerOne(data){
    const url = env.ctx + `dm/api/v1/customer/query/one`
    return request.get(url, data)
  },

  // 评分规则设置-根据主键单一查询businessId
  researchScorerulesQueryOneBusinessId(data) {
    const url = env.ctx + `dm/api/v1/scorerules/query/one/businessId`
    return request.get(url, data)
  },
  
  // 分享健康自测帖子
  scoreresultlogShareLog(data) {
    const url = env.ctx + `dm/api/v1/scoreresultlog/share/log`
    return request.postJson(url, data)
  },
  
  // 健康自测订阅消息
  subscribeInsert(data) {
    const url = env.ctx + `dm/api/v1/subscribe/insert`
    return request.postJson(url, data)
  },

  // 健康自测进入详情调用 为了数据统计
  scoreresultlogBrowseLog(data) {
    const url = env.ctx + `dm/api/v1/scoreresultlog/browse/log`
    return request.postJson(url, data)
  },

  // 健康自测 查询是否有订阅, 如果有数据, 则有订阅
  getSubscribeGetOneByBusiness(data) {
    const url = env.ctx + `dm/api/v1/subscribe/get/one/by/business/account`
    return request.get(url, data)
  },

  // 健康自测 取消推送
  healthSubscribeCancel(data) {
    const url = env.ctx + `dm/api/v1/subscribe/cancel`
    return request.postForm(url, data)
  },

}
