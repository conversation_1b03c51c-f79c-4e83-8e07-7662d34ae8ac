<template>
  <view class="category-module" :class="'index-' + skinColor" v-if="showClassifySection">
    <view class="accompany-head">
      <view class="accompany-head-l">
        <view class="teacher"></view>
        <view class="info">
          <view class="info-t"></view>
          <view class="info-b">1000+陪诊师</view>
        </view>
      </view>
      <view class="accompany-head-r" @tap="handleOrderByCategory">
        <button>立即预约</button>
      </view>
    </view>
    <view class="accompany-bott" v-if="categoryList && categoryList.length">
      <!-- 使用swiper组件实现滑动效果 -->
      <swiper
        class="category-swiper"
        circular
        :current="current"
        @change="(e)=>current = e.detail.current"
        :autoplay="false"
        :interval="5000"
        :duration="800"
        :disable-touch="false"
        @touchstart="handleTouchStart"
        @touchend="handleTouchEnd"
      >
        <swiper-item v-for="(page, pageIndex) in categoryPages" :key="pageIndex">
          <view class="swiper-category-content">
            <view
              class="accompany-bott-item"
              v-for="item in getCategoryPageItems(pageIndex)"
              :key="item.classifyId"
              @tap="handleServiceWithCategory(item)"
            >
              <view class="icon-container">
                <image class="category-icon" :src="file_ctx + item.icon"></image>
              </view>
              <view class="category-title">{{item.name}}</view>
            </view>
          </view>
        </swiper-item>
      </swiper>
      <!-- 自定义指示器点 -->
      <view class="swiper-dot" v-if="categoryPages > 1">
        <view
          class="swiper-dot-item"
          :class="{active: current === pageIndex}"
          @click="current = pageIndex"
          v-for="pageIndex in categoryPages"
          :key="pageIndex"
        ></view>
      </view>
    </view>
    <view class="accompany-bott" v-else>
      <view class="no-data">暂无分类数据</view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'CategoryModule',
  props: {
    showClassifySection: {
      type: Boolean,
      default: false
    },
    categoryList: {
      type: Array,
      default: () => []
    },
    file_ctx: {
      type: String,
      required: true
    },
    skinColor: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      categoryPages: 1,
      current: 0,
      touchStartX: 0,
      touchEndX: 0,
      isSwiping: false
    }
  },
  watch: {
    categoryList: {
      handler(newVal) {
        if (newVal && newVal.length) {
          // 计算分类页数
          this.categoryPages = Math.ceil(newVal.length / 4);
        }
      },
      immediate: true
    }
  },
  methods: {
    // 获取指定页的分类项
    getCategoryPageItems(pageIndex) {
      const start = pageIndex * 4;
      const end = start + 4;
      return this.categoryList.slice(start, end);
    },

    // 处理滑动开始事件
    handleTouchStart(e) {
      this.touchStartX = e.touches[0].clientX;
      this.isSwiping = false;
    },

    // 处理滑动结束事件
    handleTouchEnd(e) {
      this.touchEndX = e.changedTouches[0].clientX;
      // 计算滑动距离
      const distance = this.touchEndX - this.touchStartX;

      // 如果滑动距离过小
      if (Math.abs(distance) < 10) {
        this.isSwiping = false;
        return;
      }

      this.isSwiping = true;

      // 如果已经是第一页且向右滑动，或者是最后一页且向左滑动，增加阻尼效果
      if ((this.current === 0 && distance > 0) ||
          (this.current === this.categoryPages - 1 && distance < 0)) {
        return;
      }
    },

    // 点击分类"立即预约"按钮
    handleOrderByCategory() {
      const firstCategory = this.categoryList && this.categoryList.length ? this.categoryList[0] : null;
      this.$emit('order-by-category', firstCategory);
    },

    // 点击分类项
    handleServiceWithCategory(item) {
      // 如果是滑动中，不触发点击事件
      if (this.isSwiping) return;

      if (!item) {
        uni.showToast({
          title: '暂无数据',
          icon: 'none'
        });
        return;
      }
      this.$emit('service-with-category', item);
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../../style/blueSkin.scss';

.category-module {
  position: relative;
  width: 686rpx;
  box-sizing: border-box;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-top: 80rpx; /* 增加顶部边距，避免被搜索框遮挡 */
  z-index: 5; /* 确保不被搜索框遮挡 */
  touch-action: manipulation; /* 优化触摸行为 */
}

.swiper-dot {
  position: absolute;
  bottom: 12rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;

  .swiper-dot-item {
    width: 12rpx;
    height: 12rpx;
    background: rgba(51,51,51,0.1);
    border-radius: 50%;
    margin-right: 12rpx;
  }

  .swiper-dot-item.active {
    background: #489DF0;
  }
}

.accompany-head {
  display: flex;
  height: 150rpx;
  align-items: center;
  justify-content: space-between;
  border-bottom: 2rpx solid #e5e5e5;
  margin: 0 20rpx 24rpx;
  width: calc(100% - 40rpx);

  .accompany-head-l, .accompany-head-r {
    display: flex;
    align-items: center;

    .teacher {
      position: absolute;
      top: -10rpx;
      width: 114rpx;
      height: 160rpx;
      background-image: url(https://file.greenboniot.cn/static/image/business/accompany-doctor/icon-accompany-doctor-specialty-teacher.png);
      background-size: 100%;
      background-repeat: no-repeat;
    }

    .info {
      display: flex;
      flex-direction: column;
      margin-left: 128rpx;

      .info-t {
        width: 144rpx;
        height: 36rpx;
        margin-bottom: 10rpx;
      }

      .info-b {
        font-size: 28rpx;
        color: #1D2029;
        line-height: 40rpx;
      }
    }
  }

  .accompany-head-r {
    button {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 160rpx;
      height: 72rpx;
      background: #00B484; /* 默认绿色主题 */
      box-shadow: 0rpx 0rpx 8rpx 0rpx #FFFFFF;
      border-radius: 36rpx;
      font-weight: 600;
      font-size: 24rpx;
      color: #FFFFFF;
      line-height: 34rpx;

      &::after {
        border: none !important;
      }
    }
  }
}

.accompany-bott {
  display: flex;
  flex-wrap: wrap;
  position: relative;

  .no-data {
    width: 100%;
    text-align: center;
    font-size: 28rpx;
    color: #A5AAB8;
    padding: 30rpx 0;
  }
}

.category-swiper {
  width: 100%;
  height: 240rpx;
  background-image: linear-gradient(to bottom, #BEE9DD, #FFFFFF); /* 默认绿色主题渐变 */
  border-radius: 16rpx;
  overflow: hidden;
  touch-action: pan-x; /* 优化触摸体验，限制为水平滑动 */
  user-select: none; /* 防止文本选择干扰滑动 */
}

.swiper-category-content {
  display: flex;
  flex-wrap: wrap;
  height: 100%;
  justify-content: flex-start;
  -webkit-overflow-scrolling: touch; /* 平滑滚动 */
  pointer-events: auto; /* 确保事件能够被正确捕获 */
}

.accompany-bott-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 25%;
  height: auto;
  margin-right: 0;
  margin-top: 20rpx;
  padding: 10rpx;
  box-sizing: border-box;
  transition: transform 0.2s ease; /* 添加过渡效果 */

  &:active {
    transform: scale(0.95); /* 点击时轻微缩小，提供触摸反馈 */
  }
}

.icon-container {
  width: 100rpx;
  height: 100rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 10rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.category-icon {
  width: 90rpx;
  height: 90rpx;
}

.category-title {
  font-size: 24rpx;
  color: #1D2029;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}

/* 蓝色皮肤样式 */
.index-blueSkin {
  background-image: linear-gradient(to bottom, #FFFFFF, #659DF3);
  height: 400rpx;
  .accompany-head {
    width: 100%;
    height: 112rpx;
    border-radius: 24rpx 24rpx 0rpx 0rpx;
    margin-bottom: 0rpx !important;
    border-bottom: none !important;
    box-sizing: border-box;
    width: calc(100% - 40rpx);
    background: transparent;

    .accompany-head-l {
      .teacher {
        width: 170rpx;
        height: 218rpx;
        left: 62rpx;
        top: -66rpx;
        background-image: url(https://file.greenboniot.cn/static/image/business/accompany-doctor/icon-accompany-doctor-specialty-teacher-blue.png);
        background-size: 100%;
        background-repeat: no-repeat;
      }

      .info {
        margin-left: 220rpx;
        margin-top: -30rpx;
        .info-t {
          width: 168rpx;
          height: 40rpx;
          background-image: url(https://file.greenboniot.cn/static/image/business/accompany-doctor/icon-accompany-doctor-specialty-text-blue.png);
          background-size: 100%;
          background-repeat: no-repeat;
        }
      }
    }

    .accompany-head-r button {
      width: 168rpx;
      height: 62rpx;
      background: linear-gradient(91deg, #489DF0 0%, #117CE5 100%);
      box-shadow: inset 0rpx 0rpx 8rpx 0rpx #FFFFFF;
      border-radius: 36rpx;
      margin-right: 12rpx;
      margin-top: -30rpx;
      font-weight: bold;
      font-size: 24rpx;
      color: #FFFFFF;
    }
  }

  .accompany-bott {
    width: 100%;
    border-radius: 16rpx;
    position: relative;
    padding: 0 24rpx;
    box-sizing: border-box;
  }

  .category-swiper {
    background-image: none;
    background: transparent;
    width: 100%;
    height: 208rpx;
  }

  .swiper-category-content {
    background: #FFFFFF;
    width: 100%;
    margin: 0 auto;
    border-radius: 16rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  }

  .swiper-dot {
    .swiper-dot-item.active {
      background: #489DF0;
    }
  }
  .accompany-bott-item {
    padding: 0 !important;
    margin: 0 !important;
    .icon-container {
      width: 96rpx;
      height: 96rpx;
      background-color: #FFFFFF;
      border-radius: 48rpx;
      box-shadow: 0 4rpx 12rpx #E5EFFD;
      margin-top: 32rpx;
    }
    .category-icon {
      width: 72rpx;
      height: 72rpx;
      margin-top: 2rpx
    }

    .category-title {
      font-size: 28rpx;
      color: #333333;
    }
  }
}
/* 绿色皮肤样式 */
.index-greenSkin {
  .accompany-head {
    width: 100%;
    height: 112rpx;
    background: #EDF8FF;
    border-radius: 24rpx 24rpx 0rpx 0rpx;
    border: 2rpx solid #00B484;
    margin-bottom: 0rpx !important;
    border-bottom: none !important;
    box-sizing: border-box;
    width: calc(100% - 40rpx);

    .accompany-head-l {
      .teacher {
        width: 170rpx;
        height: 218rpx;
        left: 62rpx;
        top: -80rpx;
        background-image: url(https://file.greenboniot.cn/static/image/business/accompany-doctor/icon-accompany-doctor-specialty-teacher-blue.png);
        background-size: 100%;
        background-repeat: no-repeat;
      }

      .info {
        margin-left: 220rpx;

        .info-t {
          width: 168rpx;
          height: 40rpx;
          background-image: url(https://file.greenboniot.cn/static/image/business/accompany-doctor/icon-accompany-doctor-specialty-text.png);
          background-size: 100%;
          background-repeat: no-repeat;
        }
      }
    }

    .accompany-head-r button {
      width: 168rpx;
      height: 62rpx;
      background: linear-gradient(91deg, #00B484 0%, #00B484 100%);
      box-shadow: inset 0rpx 0rpx 8rpx 0rpx #FFFFFF;
      border-radius: 36rpx;
      margin-right: 44rpx;
      font-weight: bold;
      font-size: 24rpx;
      color: #FFFFFF;
    }
  }

  .accompany-bott {
    width: 100%;
    border-radius: 16rpx;
    position: relative;
  }

  .swiper-category-content {
    background-image: linear-gradient(to bottom, #BEE9DD, #FFFFFF);
  }
  .accompany-bott-item {
    padding: 0 !important;
    margin: 0 !important;
    .icon-container {
      width: 90rpx;
      height: 90rpx;
      background-color: #FFFFFF;
      margin-top: 36rpx;
    }
    .category-icon {
      width: 72rpx;
      height: 72rpx;
      margin-top: 2rpx
    }

    .category-title {
      font-size: 28rpx;
      color: #333333;
    }
  }

  .swiper-dot {
    .swiper-dot-item.active {
      background: #00B484;
    }
  }
}
</style>
