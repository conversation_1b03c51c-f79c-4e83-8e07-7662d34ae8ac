<!--
*
*config  [Object] 参数设置
name: 'select'         字段名
-------------------------------------------

*returnFn    [function]     回调函数
*cData        [String]       (默认选中)key传入方式
*getData     [String]      做数据处理首次默认值watch执行监听
*disabled: false         是否禁用
*
*字典选择器(已经放到全局使用) <select-address  :config="xxx" :cData="xxx"  @returnFn = "xxx"></select-address>
 -->
<template>
  <view class="select-address-main" v-if="isShow" :style="{'display': isShow ? 'block' : 'none'}">
    <view class="select-address-mask" @tap.stop="close"></view>
    <view class="select-address-tag" :class="[isShow ? 'select-address-tag-toggle' : '']">
      <view class="select-address-header">
        <view class="select-address-action select-address-cancel" @tap.stop="close">取消</view>
        <view class="select-address-action select-address-confirm" @tap.stop="returnFn">确定</view>
      </view>
      <view class="select-address-header height-atuo text-align-left p-30" v-if="!$validate.isNull(dataValueList)">
        <view class="select-address-header-li" v-for="(item, index) in  dataValueList" :key="index">
          {{item.text}}
        </view>
        <view class="icon-close-black-circle" @tap="del"></view>
      </view>
      <view class="select-address-content">
        <scroll-view scroll-x="true" class="scroll-view-x-main">
          <view class="scroll-view-x-ul">
            <view class="scroll-view-x-li" v-for="(item, index) in  array" :key="index">
              <scroll-view  scroll-y="true" class="scroll-view-y-main">
                <view class="scroll-view-y-ul">
                  <view class="scroll-view-y-li" :class="[dataValue[index] === yItem.value ? 'color-topicC' : '']" @tap="choose(index, yIndex, yItem)" v-for="(yItem, yIndex) in item" :key="yIndex">
                    {{yItem.text}}
                  </view>
                </view>
              </scroll-view>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>
  </view>
</template>

<script>
  import cityData from './city-data.js'
  export default {
    components: {

    },
    data() {
      return {
        // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
        $constant: this.$constant,
        $common: this.$common,
        $accurateConversion: this.$accurateConversion,
        file_ctx: this.file_ctx,
        $static_ctx: this.$static_ctx,
        $timePlugin: this.$timePlugin,
        $validate: this.$validate,
        dataValue: [], // 当前页面双向数据
        dataValueList: [], // 当前选中的数据
        array: [],
        cityDataList: cityData, // 本地地址省、市、区
        isShow: false, // 是否开启标签选择
        defaultConfig: {
          name: 'select',
          length: 3, // 选择地区的长度，1：省、2：省市、3：省市区、4省市区街道
          isRequired: true // 是否必选全都选择才能触发确认
        }
      }
    },
    computed: {

    },
    props: {
      // 是否禁用disable
      disabled: {
        type: Boolean,
        default() {
          return false
        }
      },
      // 初始值传值，用于回显
      cData: {
        type: Array,
        required: false,
        default() {
          return []
        }
      },
      // 参数设置
      config: {
        type: Object,
        required: false,
        default: () => {
          return {}
        }
      },
      // 数组下标
      arrayIndex: {
        type: [String, Number],
        default() {
          return ''
        }
      }
    },
    watch: {
      cData: {
        handler(val) {
          this.watchDataMain(val)
        },
        immediate: true,
        deep: true
      },
      config: {
        handler(val) {
          this.copyConfig()
        },
        immediate: true,
        deep: true
      }
    },
    mounted() {
      this.init()
    },
    methods: {
      // 初始化数据
      init() {
        this.dataValue = JSON.parse(JSON.stringify(this.cData))
        this.arrayInit()
        this.copyConfig()
      },
      // 开启选择界面
      show() {
        if (this.disabled) return
        this.isShow = true
        this.init()
      },
      // 关闭选择界面
      close() {
        this.isShow = false
      },
      /**
       * 初始化拷贝config对象
       */
      copyConfig() {
        const that = this
        that.dataValue = JSON.parse(JSON.stringify(that.cData))
        const obj = JSON.parse(JSON.stringify(that.config))
        Object.keys(obj).forEach(function(key) {
          that.defaultConfig[key] = obj[key]
        })
        that.defaultConfig = Object.assign({}, that.defaultConfig)
        if (!that.$validate.isNull(that.dataValue)) {
          that.watchDataMain(that.dataValue)
        }
      },
      /**
       * 监听cData主逻辑方法
       */
      watchDataMain(val) {
        const that = this
        that.dataValue = JSON.parse(JSON.stringify(val))
        this.arrayInit()
        that.defaultConfig = Object.assign({}, that.defaultConfig)
      },

      // 显示数组格式化
      arrayInit() {
        const that = this
        that.array = that.arrayFn()
        if (that.defaultConfig.length < that.array.length) { // 限制选择地区的长度
          that.array.splice(that.defaultConfig.length, that.array.length - that.defaultConfig.length)
        }
        const dataValueList = []
        for (let i = 0; i < that.array.length; i++) {
          for (let j = 0; j < that.array[i].length; j++) {
            if (that.array[i][j].value === that.dataValue[i]) {
              dataValueList.push({
                value: that.array[i][j].value,
                text: that.array[i][j].text
              })
            }
          }
        }
        that.dataValueList = dataValueList
        that.$set(that.array, 0, that.array[0])
      },
      // 组装页面显示数组
      arrayFn() {
        const that = this
        if (that.$validate.isNull(that.dataValue)) {
          return [this.cityDataList]
        } else {
          let temporaryArr = this.cityDataList // 临时环节数据
          let arr = []
          for (let i = 0; i < that.dataValue.length; i++) {
            for (let j = 0; j < temporaryArr.length; j++) {
              if (temporaryArr[j].value === that.dataValue[i]) {
                let list = []
                list = list.concat([temporaryArr])
                if (!that.$validate.isNull(temporaryArr[j].children)) {
                  if (i === that.dataValue.length - 1) {
                    list = list.concat([temporaryArr[j].children])
                  }
                  temporaryArr = temporaryArr[j].children
                }
                arr = arr.concat(list)
                break
              }
            }
          }
          return arr
        }
      },
      // 清除选择的地区
      del() {
        const that = this
        that.dataValue = []
        that.arrayInit()
      },
      // 选择当前选项
      choose(index, yIndex, yItem) {
        const that = this
        if (that.disabled) return
        // 参入数据相同时不做任何改变停止下面方法
        if (that.dataValue[index] === yItem.value) return

        // dataValue的数据结构：['省编号', '市编号', '区编号', '街道编号']
        if (that.$validate.isNull(that.dataValue)) { // 当dataValue为空是给多维数组默认第一维
          that.dataValue = [yItem.value]
        } else {
          if (!that.$validate.isNull(that.dataValue[index])) { // 当前维度存在时
            if (index !== that.dataValue.length - 1) { // 改变当前维度的数据，并且删除后面维度的数据
              that.dataValue.splice(index + 1, that.dataValue.length - (index + 1))
            }
            that.dataValue[index] = yItem.value // 修改当前维度的数据
          } else { // 当前维度不存在
            that.dataValue[index] = yItem.value // 增加当前维度并且赋值
          }
        }
        if (that.defaultConfig.length < that.dataValue.length) { // 限制选择地区的长度
          that.dataValue.splice(that.defaultConfig.length, that.dataValue.length - that.defaultConfig.length)
        }
        that.arrayInit()
        that.$set(that.dataValue, 0, that.dataValue[0])
      },
      // 返回数据
      returnDataFn() {
        const that = this
        const data = that.dataValueList
        return data
      },
      /**
       * picker触发选中事件
       * @param e
       */
      returnFn() {
        const that = this
        if (that.disabled) return
        if (that.defaultConfig.isRequired) {
          if (that.defaultConfig.length !== that.dataValue.length) {
            that.$uniPlugin.toast('请选择全部地址')
            return
          }
        }
        const returnVal = {
          key: that.config.name,
          value: that.dataValue,
          data: that.dataValueList
        }
        if (String(that.arrayIndex)) {
          returnVal.index = that.arrayIndex
        }
        that.defaultConfig = Object.assign({}, that.defaultConfig)
        that.close()
        that.$emit('updateForm', returnVal)
      }
    }
  }
</script>

<style lang="scss" scoped>
  .color-999{
    color: #999 !important;
  }
  .color-topicC {
    color: $topicC !important;
  }
  .height-atuo {
    height: auto !important;
  }
  .text-align-left {
    text-align: left !important;
  }
  .select-address-main{
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 999;
    box-sizing: border-box;
    font-size: 32upx;
    .select-address-mask {
      position: fixed;
      z-index: 999;
      top: 0;
      right: 0;
      left: 0;
      bottom: 0;
      background: rgba(0,0,0,.5);
    }
    .select-address-tag {
      position: fixed;
      left: 0;
      bottom: 0;
      -webkit-transform: translateY(100%);
      transform: translateY(100%);
      -webkit-backface-visibility: hidden;
      backface-visibility: hidden;
      z-index: 999;
      width: 100%;
      background: #fff;
      visibility: hidden;
      -webkit-transition: visibility .3s,-webkit-transform .3s;
      transition: visibility .3s,-webkit-transform .3s;
      transition: transform .3s,visibility .3s;
      transition: transform .3s,visibility .3s,-webkit-transform .3s;
      .select-address-header {
        display: block;
        position: relative;
        text-align: center;
        width: 100%;
        height: 90upx;
        background: #fff;
        &:after {
          content: "";
          position: absolute;
          left: 0;
          bottom: 0;
          right: 0;
          height: 2upx;
          clear: both;
          border-bottom: 2upx solid $contentDdt;
          color: $contentDdt;
          -webkit-transform-origin: 0 100%;
          transform-origin: 0 100%;
          -webkit-transform: scaleY(.5);
          transform: scaleY(.5);
        }
        .select-address-header-li {
          display: inline-block;
          vertical-align: middle;
          margin-right: 16upx;
          color: #666;
          line-height: 42upx;
        }
        .icon-close-black-circle{
          display: inline-block;
          margin-left: 16upx;
          vertical-align: middle;
          @include iconImg(40,40,'/business/icon-close-black-circle.png');
        }
        .select-address-action {
          display: block;
          max-width: 50%;
          top: 0;
          height: 100%;
          box-sizing: border-box;
          padding: 0 30upx;
          font-size: 32upx;
          line-height: 90upx;
          overflow: hidden;
          cursor: pointer;
        }
        .select-address-cancel {
          float: left;
          color: #999;
        }
        .select-address-confirm {
          float: right;
          color: $topicC;
        }
      }
      .select-address-content {
        position: relative;
        display: block;
        width: 100%;
        height: 640upx;
        box-sizing: border-box;
        background: #fff;
        padding: 30upx 0;
        .scroll-view-x-main {
          width: 100%;
          height: 100%;
          .scroll-view-x-ul {
            height: 100%;
            width: -webkit-max-content;
            width: -moz-max-content;
            width: max-content;
            .scroll-view-x-li {
              height: 100%;
              width: 240upx;
              display: inline-block;
              vertical-align: top;
              .scroll-view-y-main {
                height: 100%;
                .scroll-view-y-ul {
                  height: 100%;
                  width: 100%;
                  .scroll-view-y-li {
                    padding: 0 16upx;
                    line-height: 42upx;
                    margin-bottom: 16upx;
                    box-sizing: border-box;
                    display: block;
                  }
                }
              }
            }
          }
        }
      }
    }
    .select-address-tag-toggle {
      visibility: visible;
      -webkit-transform: translate(0);
      transform: translate(0);
    }
  }
</style>
