<template>
  <!-- :templateData="templateData" -->
  <!-- <view> -->
  <!-- value ="活动类型1病例征集2学术调研"， -->
    <form-template :submitText='submitText' :isAccuratePromotion='isAccuratePromotion' :activitytype='activitytype' :cDisabled='cDisabled' :height="height" :ptype='1' :noloading='noloading' :cmainid='mainId' :emainId='emainId' :updatecount="updatecount" :updatepage='updatepage' :businessid="editid"  @returnFn="returnFn">

    </form-template>
  <!-- </view> -->
</template>

<script>
import FormTemplate from '@/modules/activity/components/form-template/index.vue';
import { mapState } from "vuex"
// import common from '@/common/util/main'
export default {
  name: 'fillincase',
  components: {
    FormTemplate
  },
  props: {
    // 提交按钮文案
    submitText:{
      type:String,
      default:null,
    },
    // 入口是不是精准地推
    isAccuratePromotion:{
      type:Boolean,
      default:false,
    },
    height: {
      type: String,
      default: '100vh'
    },
    updatecount:{
      type:Number,
      default:0,
    },
    steps: {
      type: Array,
      default: function() {
        return [];
      }
    },
    ids:{
      type:[Number,String],
    }

  },
  data() {
    return {
      $common: this.$common,
      editid:'',
      updatepage:0,
      mainId:null,
      noloading:false,
      emainId:null,
      back:false,
      activitytype:2,
      cDisabled:false,
      // step: 0,
      // updatecount: 0,
      // regForm: {
      //   // 834610603728510977
      //   id: '835483840192643077'
      // },
      // templateData: null
      // height
    };
  },
  watch:{
    // updatecount(n){
    //   this.editid = this.id;
    // },
    ids(n){
      console.log('进来了',n)
      this.editid = n;
    }
  },
  onLoad(options){
    // console.log('options',options)
    const query = this.$Route.query
    console.log('query',query)
    if (!this.$validate.isNull(query)) {
      this.editid = query.id;
      this.emainId = query.mainId
      this.back = query.back;
      this.cDisabled = query.disabled

      this.$nextTick(() => {
        this.updatepage += 1
      })
    //   this.id = query.id
    //   this.index = query.index
    }
  },
  mounted() {
    if(this.ids){
      this.editid = this.ids;
    }
    // 834610603728510977

    // this.getDetail('835483840192643077');
  },
  computed: {
    ...mapState('user', {
      recordUserInfo: state => state.recordUserInfo // 当前登录用户信息
    })
  },
  methods: {

    // 835483840192643077
    // getDetail(id) {
    //   const that = this;
    //   that.$api.activity.researchQueryOne({ id: id }).then(res => {
    //     res.data.pushTimeText = this.$common.formatDate(new Date(res.data.createTime), 'yyyy-MM-dd');
    //     that.regForm = res.data;
    //     that.$api.activity.queryBusinessTemplateDTOList({ templateIds: that.regForm.copyTemplateId }).then(res => {
    //       this.templateData = res.data;
    //       that.openTemplate = true;
    //       // this.$forceUpdate()

    //       this.$nextTick(() => {
    //         console.log('进来');
    //         console.log(this.templateData);
    //         this.updatecount += 1;
    //       });
    //     });
    //   });
    // },
    returnFn(obj) {
      let param = obj.param
      const { name, userId } = this.recordUserInfo;

      if(param.patientInfo instanceof Object){
        const curSelectStore = this.$common.getKeyVal('user', 'curSelectStore', true)
        let recordId = curSelectStore && curSelectStore.userTenantRecordList[0] ? curSelectStore.userTenantRecordList[0].recordId : null;

        if(!recordId){
          return this.$uniPlugin.toast('档案为空,不能保存');
        }






        param.patientInfo.physicianUserId = recordId
      }
      param = {
        ...param,
        userName: name,
        accountId: this.$common.getKeyVal('user', 'accountId', true),
        userId: userId,
        // attacheUserId:userId,
        // physicianUserId:userId,
        activityType:this.activitytype,
        formTemplateWriteType:5,// 3 病例征集流程提交记录 4 病例征集回访 5 学术调研流程提交记录 6 学术调研回访
      };
      const that = this;
      that.$api.activity.casecollectsubmitlogformWrite(param).then(res => {
        that.$uniPlugin.toast('提交成功');
        this.mainId = res.data.mainId


        this.noloading = true;

        this.$nextTick(() => {
          setTimeout(() => {
            this.noloading = false;
          },500)
        })

        if(obj.next){
          this.$emit('next')

          if(this.emainId && this.back){
             this.$navto.back(1);
          }
        }





        // this.getDetail(this.id);
      }).catch(e => {
        // console.log('jkkk错误')
        this.noloading = true;

        this.$nextTick(() => {
          setTimeout(() => {
            this.noloading = false;
          },500)
        })
      });
    }
  }
};
</script>

<style lang="scss" scoped></style>
