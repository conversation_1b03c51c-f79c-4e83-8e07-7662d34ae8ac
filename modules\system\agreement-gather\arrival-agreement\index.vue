<template>
  <page>
    <view class="main-body" slot="content">
      <scroll-view scroll-y="true" class="main">
        <view class="m-main">
          <h3>{{platformTitle}}（以下简称平台）依据《陪诊师入驻协议》（以下简称本协议）的规定提供服务，本协议具有合同效力。</h3>
          <p>本协议甲方：{{registerTitle}}</p>
          <p>本协议乙方：陪诊师或您</p>
          <p>请认真阅读本协议，审阅并接受或者不接受本协议。若您确认勾选同意本协议时，即表示您已充分阅读、理解并同意您与平台达成本协议约定，且自愿受本协议的条款约束。本平台有权随时根据实际情况变更本协议并在本平台上予以公告，不另作个别通知。经修订的条款一经在本平台公布后，立即自动生效。如您不同意相关变更，必须停止使用本平台，若您继续使用本平台，则表示您已接受并自愿遵守经修改后的条款。本协议内容包括协议正文及所有本平台在服务详情页面展示的各服务须知。</p>
          
          <h3>一、陪诊师定义及工作内容</h3>
          <p>1、陪诊师定义</p>
          <p>陪诊师是近年来为了应对社会快速发展所造成的家庭陪伴缺失问题而出现的一种新兴职业，该职业能解决或缓解特定群体的就医难题，包括但不仅限于空巢老人、独居青年、残障人士、孕妇、行动不便患者、异地就医者等，为他们提供专业、高效、舒适的陪诊服务。</p>
          <p>2、工作内容</p>
          <p>1）陪诊服务：即陪诊师陪同患者或其家属完成指定医院内看病就诊的服务工作，包括但不仅限于在医院陪同就医、协助办理院内的各种就医手续、代取预约号、排队、陪同检查、代取报告、取药、打印发票清单等等，不包括陪护工作；</p>
          <p>2）跑腿服务：即患者或其家属由于各种原因无法亲自到医院办理各种预约、手续或者其他就医流程，陪诊师协助患者和家属来完成这些可以代办的事项，包括但不限于跨地区代问诊、送取报告、提前预约检查、取诊断病历、跨地区病理化验、代开药、代邮寄等。</p>
          
          <h3>二、入驻要求及申请资料</h3>
          <p>1、乙方申请入驻成为甲方平台陪诊师应当满足以下条件：</p>
          <p>1）熟悉所在城市各医院的基本情况及就诊流程，具备基本医学常识或急救技能者更佳，有医学护理专业、医护工作经验、医院义工服务经验或陪诊经验者更佳；</p>
          <p>2）无犯罪记录，无赌博、酗酒、吸毒、涉黄等不良嗜好，无传染性疾病，有健康证者优先；</p>
          <p>3）热爱陪诊师行业，具备较强的服务意识，善于使用各类电子设备，善于沟通交流，善于规划安排各类事务；</p>
          <p>4）诚实正直，积极乐观，富有爱心、责任心、耐心、同理心；</p>
          <p>5）年龄在20岁~50岁之间，男女不限，时间灵活，有充足的可提供服务时间。</p>
          <p>2、乙方应当在签订本协议之日向甲方提供如下资料：</p>
          <p>1）乙方本人真实有效的身份证正反面照片；</p>
          <p>2）乙方本人近三个月的生活照片，不得借用他人照片，不得提供虚假照片，要求着装整齐、照片规范清晰，面部无墨镜等遮挡物；</p>
          <p>3）乙方本人真实完整的自我介绍，包括最高学历、所学专业、相关工作经验、相关证书以及性格特点等，如是医护人员，请上传有效证件有效页面；</p>
          <p>4）乙方对其提供的文件资料的真实性、合法性、准确性、有效性承担全部法律责任。若乙方提供虚假、过期文件或未及时更新而导致纠纷或被相关国家机关处罚的，由乙方独立承担全部法律责任，如因此造成甲方平台损失的，乙方应予以赔偿，若甲方代为赔偿的，甲方有权向乙方追究相应赔偿。</p>
          
          <h3>三、乙方行为规范要求</h3>
          <p>1、不得在服务过程中刷手机短视频、长时间接听电话等，与客户沟通时请务必放下手机，表示尊重；</p>
          <p>2、不得随意拍摄和传播客户照片、视频，不得暴露客户个人信息，包括但不限于姓名、电话、住址、家庭情况、经济情况、病史等，否则涉及侵犯他人隐私将承担相应的法律后果；</p>
          <p>3、不得利用客户手机等移动设备、使用支付宝、微信、电子社保卡等电子支付手段进行未经客户授权的支付或转账，否则可能构成盗窃罪并需承担相应的刑事法律责任；</p>
          <p>4、不得向客户提供平台指定陪诊服务之外的其他类型的服务、推荐偏方以及兜售商品(尤其是药品、保健品、保险等金融产品)，一经发现将永久取消平台入驻及接单资格；</p>
          <p>5、不得与客户私下达成协议，以低于平台价格为客户提供服务，否则涉及的风险将由个人自行承担，一经发现将永久取消平台入驻及接单资格；</p>
          <p>6、不得在服务过程中以各种理由向客户索要、加收费用，以及收取客户的红包；</p>
          <p>7、不得要求客户给予与实际服务质量不符的评价；</p>
          <p>8、不得现场直播陪诊过程。</p>
          
          <h3>四、双方权责</h3>
          <p>1、乙方可利用自身资源、渠道、经验在全国范围内进行推广获客，扩大平台订单量；</p>
          <p>2、乙方提供服务过程中产生的一切费用均自行承担；</p>
          <p>3、乙方不得以任何形式擅自转让或授权他人使用自己在本平台注册的账号；</p>
          <p>4、乙方有义务确保在本平台上发布的信息真实，无误导性；</p>
          <p>5、乙方在使用本平台时不得违反国家法律法规、行政规章制度、不得侵犯他人知识产权或其他合法权益、不得违背社会公共利益或公共道德、不得违反本平台的相关规定，如有违反而导致任何法律后果的发生，乙方将自行承担相应的法律责任；</p>
          <p>6、乙方同意接收来自本平台的信息；</p>
          <p>7、甲方为乙方提供平台系统功能(包括但不限于客户下单、陪诊师接单、陪诊师查看工单、陪诊师提现等)及技术支持；</p>
          <p>8、甲方有权使用乙方在本平台上发布的相关信息，但仅用于陪诊服务的在本平台产品上或者互联网的其他平台上展示推广、促进客户下单、以及陪诊师的服务管理。甲方不得随意将陪诊师隐私资料用于非法用途；</p>
          <p>9、甲方有权对乙方的注册信息、使用评价等信息进行查阅并处理。根据国家法律法规、本协议的内容和本平台所掌握的事实依据，可以认定乙方存在违法或违反本协议行为以及在本平台交易平台上的不当行为，甲方有权无需征得乙方同意在本交易平台及所在网站上公布乙方的违法或不当的行为，并有权随时作出删除相关信息、终止服务等处理。</p>
          
          <h3>五、费用说明</h3>
          <p>1、平台服务费：甲方有权向乙方收取平台服务费。甲方有权随时根据实际情况变更此条款费用金额并在本平台上予以公告，不另作个别通知，乙方可根据自身情况选择是否接受；</p>
          <p>2、陪诊收入：乙方根据自身时间安排接单，按订单服务，甲方按照每笔服务订单交易金额的85%，扣除单笔手续费后，向乙方支付陪诊收入，乙方应按时按质完成订单，一个订单的服务周期按照客户购买的服务时间周期决定，具体由乙方与客户沟通协商是否适当缩短或延长，若超时，乙方应让客户在平台上追加超时费，服务结束时间自动顺延；</p>
          <p>3、甲方按订单向乙方结算，收入会自动转账到乙方在本平台的个人钱包内，乙方可以自行申请提现，乙方发起提现申请后，经甲方核实后进行转账，一般情况下1~2个工作日到账；</p>
          <p>4、如果乙方在服务过程中出现迟到、严重工作失误、服务态度差、推销其他服务或产品等情况，导致客户投诉或评价在三星及以下的，经甲方核实情况，并协调乙方与客户沟通无法达成谅解的，将视情节严重程度在乙方该笔订单收入中扣除10~50%, 用于补偿客户损失。</p>
          
          <h3>六、合作关系</h3>
          <p>1、本平台仅为乙方提供一个与就医患者(客户)信息交流平台，不参与乙方与客户的交易，对交易达成后履行过程中的权利义务均不加以监管或控制，乙方与其他用户、客户产生纠纷的，可以请求甲方予以协调处理，但甲方不承担责任；</p>
          <p>2、甲方与乙方之间签署《入驻协议》,双方属于平等合作关系，不存在任何的劳动关系，亦不属于雇佣关系；</p>
          <p>3、甲方强制要求乙方须购买相应意外保险，乙方在接单前应评估自身身体情况是否适合为客户提供陪诊服务，如因自身原因导致服务期间突发疾病或意外的，所有医疗费用均由个人承担，甲方不承担由此发生的一切责任。</p>
          <p>本平台所有陪诊/代办服务订单均须在本小程序内按照标准流程完成，警惕不法分子冒充本平台及工作人员以任何理由进行私下派单、结算，以免造成不必要的经济损失。</p>
        </view>
      </scroll-view>
    </view>
  </page>
</template>

<script>
  import serverOptions from '@/config/env/options'
  import {
    mapState
  } from 'vuex'
  export default {
    components: {

    },
    data() {
      return {
        // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
        $constant: this.$constant,
        $common: this.$common,
        $accurateConversion: this.$accurateConversion,
        file_ctx: this.file_ctx,
        $static_ctx: this.$static_ctx,
        $timePlugin: this.$timePlugin,
        $validate: this.$validate,
        id: undefined,
        idUpdate: true,
        regForm: {}
      }
    },
    computed: {
      ...mapState('user', {

      }),
      ...mapState('system', {
        temporaryStorage: state => state.temporaryStorage
      }),
      
      // 平台标题
      platformTitle() {
        return serverOptions.title || '小葫芦陪诊'
      },
      
      // 注册公司名称
      registerTitle() {
        return serverOptions.registerTitle || '广州绿葆网络发展有限公司'
      }
    },
    // 离开当前页面后执行
    onUnload() {
      // this.$common.setKeyVal('system', 'temporaryStorage', {})
    },
    onLoad(paramsObj) {
      const query = this.$Route.query
      if (!this.$validate.isNull(query)) {
        this.id = query.id
      }
      this.init()
    },
    onShow() {},
    methods: {
      init() {
        this.$nextTick(() => {

        })
      },
      navtoGo(url, obj = {}) {
        this.$navto.push(url, obj)
      },
      getDetail(id) {
        const that = this

      }
    }

  }
</script>

<style lang="scss" scoped>
  h3{
    color: #000;
    font-weight: 600;
  }
  .m-l-16 {
    margin-left: 16upx;
  }

  .star {
    color: #F85E4C;
    font-size: 32upx;
    display: inline-block;
  }

  .main-body {
    height: 100%;

    .main {
      height: 100%;
      background: #fff;

      .m-main {
        background: #fff;
        padding: 30upx;
      }
    }
  }

  .title {
    font-size: 36upx;
    line-height: 54upx;
    text-align: center;
  }

  .text-title {
    font-size: 28upx;
    line-height: 42upx;
    margin-top: 16upx;
    color: #333;
  }

  .text {
    font-size: 24upx;
    line-height: 36upx;
    margin-top: 16upx;
    color: #666;
  }

  .text-indent-40 {
    text-indent: 40upx;
  }

  .p-b-8 {
    padding-bottom: 8upx;
  }
</style>
