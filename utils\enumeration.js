//处理并组成需要的key value 的枚举数组
function getResult(key = 'label', value = 'value', list) {
  const result = []
  for (const i in list) {
    const obj = {}
    obj[key] = list[i].label
    obj[value] = list[i].value
    result.push(obj)
  }
  return result
}

// 性别
export function getGenderList(key, value) {
  const arr = [
    { label: '男', value: 1 },
    { label: '女', value: 2 },
  ]
  return getResult(key, value, arr)
}

// 婚姻情况
export function getMaritalStatus(key, value) {
  const arr = [
    { label: '已婚', value: 1 },
    { label: '未婚', value: 2 },
  ]
  return getResult(key, value, arr)
}

// 活动子项 - 执行状态
export function getTasksItemsCheckInStatusList(key, value) {
  const arr = [
    { label: '待开始', value: 1 },
    { label: '进行中', value: 2 },
    { label: '已完成', value: 3 },
    { label: '已失效', value: 4 },
  ]
  return getResult(key, value, arr)
}

// 活动主体 - 执行状态
export function getTasksExcuseStatusList(key, value) {
  const arr = [
    { label: '进行中', value: 1 },
    { label: '待执行', value: 2 },
    { label: '已结束', value: 3 },
  ]
  return getResult(key, value, arr)
}

// 用户余额-提现渠道
export function getWithdrawPayChannel(key, value) {
  const arr = [
    { label: '分销提现', value: 1 },
    { label: '返现提现', value: 2 },
  ]
  return getResult(key, value, arr)
}

// 分销员类型
export function getDistributionType(key, value) {
  const arr = [
    { label: '系统', value: 0 }, // 一级
    { label: '门店-店长', value: 1 }, // 二级
    { label: '店员', value: 2 }, // 三级
    { label: '个人', value: 3 }, // 二级 在页面展示都是个人 主动申请的
    { label: '个人', value: 4 }, // 三级 被人邀请
  ]
  return getResult(key, value, arr)
}

// 用户收入水平
export function getPersonalIncomeLevel(key, value) {
  const arr = [
    { label: '3000~5000', value: '3000~5000' },
    { label: '5000~10000', value: '5000~10000' },
    { label: '10000~15000', value: '10000~15000' },
    { label: '15000~20000', value: '15000~20000' },
    { label: '20000~25000', value: '20000~25000' },
    { label: '25000~30000', value: '25000~30000' },
    { label: '30000+', value: '30000+' },
  ]
  return getResult(key, value, arr)
}
