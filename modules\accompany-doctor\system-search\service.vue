<template>
  <view class="m-main-body">
    <scroll-refresh class="scroll-refresh-main" :isShowEmptySwitch="true" :fixed="false" :up="upOption" :down="downOption" @returnFn="returnFn" @scrollInit="scrollInit">
      <view class="wrapper" :style="{height:contentList.length==0 && '0'}">
        <view class="wrapper-item" v-for="(item,index) in contentList" :key="index" @click="handleClickJump(item.id)">
        <!-- <view class="accompany-service-item" v-for="item in 3" :key="item"> -->
          <view class="project-item-l"><image :src="file_ctx + item.listImg"></image></view>
          <view class="project-item-r">
            <view class="project-item-r-title">{{ item.serviceName }}</view>
            <view class="project-item-r-info">{{ item.description }}</view>
            <view class="project-item-r-appointment">
              <view class="project-item-r-money">¥<span>{{ item.price / 100 }}</span></view>
              <view class="project-item-r-btn">预约</view>
            </view>
          </view>
        </view>
      </view>
    </scroll-refresh>
  </view>
</template>

<script>
  import { mapState } from 'vuex'
  import { isDomainUrl } from '@/utils/index.js'
  export default {
    props: {
      search:{
        type:String,
        default:''
      }
    },
    data(){
      return{
        file_ctx: this.file_ctx,
        $constant: this.$constant,
        mescroll: null, // mescroll实例对象
        downOption: {
          auto: false // 不自动加载
        },
        upOption: {
          auto: false, // 不自动加载
          empty: {
            top: 0,
            zIndex: 999,
          },
          onScroll: true
        },
        contentList:[],
      }
    },
    computed: {
      ...mapState('user', {
        accountId: state => state.accountId,
      }),
    },
    onLoad(){
      this.$nextTick(() => {
        this.init()
      })
    },
    mounted(){},
    methods:{
      handleClickJump(id){
        this.$navto.push('ServiceDetail',{id})
      },
      returnFn(obj) {
        const that = this
        setTimeout(function () {
          let params = {
            current: obj.pageNum,
            size: obj.pageSize,
            condition: {
              serviceName:that.search,
              state:1,
            }
          }
          that.$api.accompanyDoctor.getAccompanyservicePage(params).then(res => {
            // let data = res.data.records.map(item=>({...item,banner:item.banner.split(',').map(item=>(isDomainUrl(item))),listCover:isDomainUrl(item.listCover)}))
            let data = res.data.records
            if (obj.pageNum === 1) {
              that.contentList = []
            }
            that.contentList = [...that.contentList, ...data]
            obj.successCallback && obj.successCallback(data)
          })
        }, that.$constant.noun.scrollRefreshTime)
      },
      scrollInit(scroll) {
        scroll.optUp.page.num = 1
        scroll.optUp.page.size = 10
        this.mescroll = scroll
      },
      init() {
        this.$nextTick(() => {
          this.mescroll.triggerDownScroll()
        })
      },
    },
 }
</script>

<style lang='scss' scoped>
  .m-main-body{
    height: 100%;
    .scroll-refresh-main{
      height: 100%;
      // ::v-deep .z-paging-content{
      //   background-color: transparent !important;
      //   padding: 0 32rpx;
      //   width: calc(100% - 64rpx);
      // }
      .wrapper{
        height: 100%;
        // padding:24rpx 32rpx;
        .wrapper-item{
          display: flex;
          padding: 24rpx 32rpx;
          border-bottom: 1rpx solid #EAEBF0;
          background-color: #fff;
          .project-item-l{
            width: 144rpx;
            height: 144rpx;
            border-radius: 12rpx;
            border: 1rpx solid #D9DBE0;
            margin-right: 20rpx;
            image{
              width: 100%;
              height: 100%;
            }
          }
          .project-item-r{
            display: flex;
            flex: 1;
            flex-direction: column;
            .project-item-r-title{
              font-size: 30rpx;
              color: #1D2029;
              line-height: 42rpx;
            }
            .project-item-r-info{
              display: -webkit-box;
              width: 332rpx;
              font-size: 22rpx;
              color: #868C9C;
              line-height: 32rpx;
              margin: 4rpx 0 10rpx;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            .project-item-r-appointment{
              display: flex;
              justify-content: space-between;
              .project-item-r-money{
                color: #FF5500;
                span{
                  font-size: 36rpx;
                  line-height: 50rpx;
                }
              }
              .project-item-r-btn{
                display: flex;
                justify-content: center;
                align-items: center;
                width: 110rpx;
                height: 56rpx;
                background: #fff;
                border-radius: 28rpx;
                border: 1rpx solid #00B484;
                font-size: 26rpx;
                color: #00B484;
                line-height: 36rpx;
              }
            }
          }
          &:last-child{
            border-bottom: none;
          }
        }
      }
    }
  }
</style>