<template>
  <view class="">
    <view class="selectInsure" v-if="!isLocking">
      <view class="selectItem" :class="{currentSelict:currentType === 0}" @click="selectItem(0)">
        <view class="selectItemTitle">基础保障</view>
        <view class="selectItemContent">￥5/人</view>
        <image v-if="currentType === 0" class="subscript" :src="subscript" mode=""></image>
      </view>
      <view class="selectItem" :class="{currentSelict:currentType === 1}" @click="selectItem(1)">
        <view class="selectItemTitle">标准保障</view>
        <view class="selectItemContent">￥10/人</view>
        <image v-if="currentType === 1" class="subscript" :src="subscript" mode=""></image>
      </view>
    </view>
    <view class="insureContent">
      <view v-for="(item,k) in currentInsure" :key="k">
        <view v-if="k === 'middle'" class="middleTitle">保障内容:</view>
        <view :class="'content' + k">
          <view class="contentBox" v-for="(text,index) in item" :key="index">
            <text class="contentTitle">{{text.title}}：</text>
            <text class="contentContent">{{text.value}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  import serverOptions from '@/config/env/options'
  export default{
    props:{
      isLocking:{
        type:Boolean,
        default:false
      },
      initialInsuranceNum: {
        type: Number,
        default: 5
      }
    },
    watch: {
      initialInsuranceNum: {
        handler(newVal) {
          console.log('initialInsuranceNum changed:', newVal, typeof newVal);
          // 延迟一帧再设置保险类型，确保DOM已更新
          this.$nextTick(() => {
            this.setInsuranceType();
          });
        },
        immediate: true
      }
    },
    methods:{
      setInsuranceType() {
        // 强制转换为数字进行比较
        const insuranceNum = Number(this.initialInsuranceNum);
        
        if (insuranceNum === 10) {
          this.currentType = 1;
          console.log('设置为标准保障(10元)');
        } else {
          this.currentType = 0;
          console.log('设置为基础保障(5元)');
        }
        
        // 强制更新视图
        this.$forceUpdate();
      },
      selectItem(type){

        this.currentType = type;
        
        // 获取选择的保险信息
        const productCode = serverOptions.productCodeMap[this.currentType].productCode;
        const insuranceNum = Number(serverOptions.productCodeMap[this.currentType].insuranceNum);
        this.$emit('selectItem', { productCode, insuranceNum });
      },
      getProductCode(){
        return {productCode:serverOptions.productCodeMap[this.currentType].productCode,insuranceNum:serverOptions.productCodeMap[this.currentType].insuranceNum}
      }
    },
    computed:{
      currentInsure(){
        return this.currentType === 0 ? this.insure5 : this.insure10
      }
    },
    data(){
      return {
        currentType:0,
        subscript: this.$static_ctx + "image/business/hulu-v2/subscript.png",
        insure5:{
          top:[
              {"title": "产品名称","value": "门诊无忧意外保障服务"},
              {"title": "产品内容","value": "就医导诊服务、就医提醒服务、附赠门诊医疗意外保险"},
              {"title": "产品价格","value": "5元"}
            ],
          middle:[
            {"title": "门诊过程中因遭受意外伤害导致身故或伤残","value": "10万元"},
            {"title": "医疗意外导致身故或伤残","value": "1万元"},
            {"title": "意外伤害导致骨折的医疗费用","value": "0.5万元"},
            {"title": "因输液或药物导致过敏性休克或持续昏迷时间超过 48 小时","value": "0.5万"},
            {"title": "因输液或输血导致感染乙肝病毒","value": " 0.5万"},
            {"title": "因输液或输血导致感染丙肝病毒","value": "0.5万"},
            {"title": "因输液或输血导致感染梅毒(苍白密螺旋体)","value": "0.5万"},
            {"title": "因输液或输血导致感染 HIV (类免疫缺陷病毒)","value": "0.5万"},
          ],
          bottom:[
            {"title": "门诊医疗意外保险内容及保额","value": "门诊过程中因遭受意外伤害导致身故或伤残 10万元，医疗意外导致身故或伤残1万元，意外伤害导致骨折的医疗费用0.5万元，因输液、输血发生保单约定的意外2.5万元（共5项，每项0.5万）。"},
            {"title": "就医导诊服务","value": "在服务有效期内，服务方提供专业的就医导诊服务，为患者提供便捷、高效的挂号和就诊指引。通过深入了解患者的病情、症状和需求，以专业的导引服务帮助患者快速定位到正确的就诊科室"},
            {"title": "就医提醒服务","value": "在服务有效期内，我们将对于您本次的就诊时间、就诊医院以及就诊注意事项，通过短信或其他专人的方式进行提醒告知"},
            { "title": "有效时间","value": "本产品服务仅对当次预约挂号有效。其中门诊医疗意外保险保障期间为当次预约挂号订单自医院门诊就诊时间开始至就诊结束，保险期限为1天"},
            {"title": "退订规则","value": "本服务在就诊前可申请退订，本次挂号医院就诊日当天及之后不支持退订"},
            {"title": "客服电话","value": "详情咨询或有异议可拨打4000785968 "},
            {"title": "保险理赔入口","value": "保险事故发生后48小时内尽快拨打国任财产保险股份有限公司全国统一客户服务热线956030报案"},
            {"title": "保险权益","value": "门诊医疗意外保险为服务公司赠送用户的保险权益，最终投保成功以保险公司核实为准。"},
            {"title": "其它未尽事宜",value:'详见《服务细则》'}
          ]
        },
        insure10:{
          top:[
              {"title": "产品名称","value": "门诊无忧意外保障服务"},
              {"title": "产品内容","value": "就医导诊服务、就医提醒服务、医疗意外法律咨询服务、附赠门诊医疗意外保险"},
              {"title": "产品价格","value": "10元"}
            ],
          middle:[
            {"title": "门诊过程中因遭受意外伤害导致身故或伤残","value": "20万元"},
            {"title": "医疗意外导致身故或伤残","value": "2.5万元"},
            {"title": "意外伤害导致骨折的医疗费用","value": "1万元"},
            {"title": "因输液或药物导致过敏性休克或持续昏迷时间超过 48 小时","value": "1万元"},
            {"title": "因输液或输血导致感染乙肝病毒","value": "1万元"},
            {"title": "因输液或输血导致感染丙肝病毒","value": "1万元"},
            {"title": "因输液或输血导致感染梅毒(苍白密螺旋体)","value": "1万元"},
            {"title": "因输液或输血导致感染 HIV (类免疫缺陷病毒)","value": "1万元"},
          ],
          bottom:[
            {"title": "门诊医疗意外保险内容及保额","value": "门诊过程中因遭受意外伤害导致身故或伤残 20万元，医疗意外导致身故或伤残2.5万元，意外伤害导致骨折的医疗费用1万元，因输液、输血发生保单约定的意外5万元（共5项，每项1万）"},
            {"title": "就医导诊服务","value": "在服务有效期内，服务方提供专业的就医导诊服务，为患者提供便捷、高效的挂号和就诊指引。通过深入了解患者的病情、症状和需求，以专业的导引服务帮助患者快速定位到正确的就诊科室"},
            {"title": "就医提醒服务","value": "在服务有效期内，我们将对于您本次的就诊时间、就诊医院以及就诊注意事项，通过短信或其他专人的方式进行提醒告知"},
            {"title": "医疗意外法律咨询服务","value": "在服务有效期内，如您有对于医疗纠纷、医疗事故责任认定等方面的疑问，可在工作日9：30 -- 18：00时间段通过电话4000785968 进行法律咨询服务解答本次疑问和获得相关的法律帮助"},
            { "title": "有效时间","value": "本产品服务仅对当次预约挂号有效。其中门诊医疗意外保险保障期间为当次预约挂号订单自医院门诊就诊时间开始至就诊结束，保险期限为1天"},
            {"title": "退订规则","value": "本服务在就诊前可申请退订，本次挂号医院就诊日当天及之后不支持退订"},
            {"title": "客服电话","value": "详情咨询或有异议可拨打4000785968 "},
            {"title": "保险理赔入口","value": "保险事故发生后48小时内尽快拨打国任财产保险股份有限公司全国统一客户服务热线956030报案"},
            {"title": "保险权益","value": "门诊医疗意外保险为服务公司赠送用户的保险权益，最终投保成功以保险公司核实为准。"},
            {"title": "其它未尽事宜",value:'详见《服务细则》'}
          ]
        },
      }
    }
  }
</script>

<style lang="scss" scoped>
  .selectInsure{
    display: flex;
    justify-content: space-between;
    .currentSelict{
      background: rgba(0,180,132,0.15) !important;
      .selectItemTitle{
        color: #00B484 !important;
      }
    }
    .selectItem{
      width: 268rpx;
      height: 96rpx;
      background: rgba(119,119,119,0.15);
      border-radius: 8rpx 8rpx 8rpx 8rpx;
      padding: 10rpx 0;
      box-sizing: border-box;
      position: relative;
      .subscript{
        width: 48rpx;
        height: 35rpx;
        position: absolute;
        bottom: 0;
        right: 0;
      }
      .selectItemTitle{
        font-weight: 500;
        font-size: 28rpx;
        color: #333333;
        text-align: center;
        margin-bottom: 4rpx;
      }
      .selectItemContent{
        font-weight: 500;
        font-size: 24rpx;
        color: #777777;
        text-align: center;
      }
    }
  }
  .insureContent{
    margin-top: 32rpx;
  }
  .middleTitle{
    font-weight: 500;
    font-size: 24rpx;
    color: #333333;
  }
  .contenttop,.contentbottom{
    .contentBox{
    }
    .contentTitle{
      font-weight: 500;
      font-size: 24rpx;
      color: #333333;
    }
    .contentContent{
      font-weight: 500;
      font-size: 22rpx;
      color: #777777;
    }
  }
  .contentmiddle{
    background: #F4F6FA;
    border-radius: 16rpx 16rpx 16rpx 16rpx;
    padding: 20rpx;
    .contentBox{
     display: flex;
     justify-content: space-between;
     align-items: center;
    }
    .contentTitle{
      font-weight: 400;
      font-size: 22rpx;
      color: #777777;
      max-width: 296rpx;
    }
    .contentContent{
      font-weight: 500;
      font-size: 22rpx;
      color: #333333;
    }
  }
</style>
