<template>
  <view class="withdrawal-page">
    <!-- 顶部导航栏 -->
    <m-nav-bar title="提现" left-icon="left" @clickLeft="back" />

    <!-- 提现内容 -->
    <view class="withdrawal-content">
      <!-- 提现金额 -->
      <view class="amount-section">
        <view class="section-title">提现金额</view>
        <view class="amount-input-wrapper">
          <text class="currency-symbol">¥</text>
          <input
            type="digit"
            v-model="withdrawalAmount"
            placeholder="请输入金额"
            class="amount-input"
            @input="checkAmount"
          />
          <text class="all-amount" @click="setMaxAmount">全部提现</text>
        </view>
        <view class="amount-limit">可提现余额￥{{ availableBalance }}</view>
      </view>

      <!-- 提现说明 -->
      <view class="withdrawal-info">
        <view class="info-title">提现须知</view>
        <view class="info-content">
          <view class="info-item">1. 客户订单服务完成后，会进行分账，完成分账后会进入【余额】</view>
          <view class="info-item">2. 提现到账周期T+1</view>
        </view>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-btn-wrapper">
      <button
        class="submit-btn"
        :class="{ 'disabled': !isAmountValid }"
        :disabled="!isAmountValid"
        @click="submitWithdrawal"
      >
        提交申请
      </button>
    </view>
  </view>
</template>

<script>
import uniNavBar from '@/components/uni/uni-nav-bar/uni-nav-bar.vue'
import serverOptions from '@/config/env/options'
import common from '@/common/util/main'

export default {
  components: {
    uniNavBar
  },
  data() {
    return {
      providerId: '',
      providerName: '',
      withdrawalAmount: '',  // 提现金额
      availableBalance: '0', // 可提现余额
      isAmountValid: false,  // 金额是否有效
      minAmount: 1,          // 最小提现金额
      distributorId: '',     // 分销员ID
      receiverNo: '',        // 收款账号
      configData: {          // 配置数据
        appid: '',
        orgCode: ''
      }
    }
  },
  onLoad(options) {
    // 接收传递过来的参数
    if (options.providerId) {
      this.providerId = options.providerId;
    } else {
      // 如果没有传入providerId，从配置中获取
      this.providerId = serverOptions.providerId;
    }

    if (options.providerName) {
      this.providerName = options.providerName;
    }

    // 加载配置数据
    this.loadConfigData().then(() => {
      // 获取分销员信息，然后获取余额
      this.getDistributorInfo();
    });
  },
  methods: {
    // 返回上一页
    back() {
      uni.navigateBack();
    },

    // 获取分销员信息
    async getDistributorInfo() {
      try {
        // 从本地存储获取当前用户信息
        const codeUserInfo = common.getKeyVal('user', 'codeUserInfo', true);
        if (!codeUserInfo || !codeUserInfo.id) {
          console.error('未获取到用户ID');
          return;
        }

        // 调用接口获取分销员信息
        const res = await this.$api.distribution.accompanydistributorQueryOneByUserId({
          userId: codeUserInfo.id,
          providerId: serverOptions.providerId
        });

        // 如果接口返回成功且有分销员信息
        if (res.code === 0 && res.data) {
          console.log('获取到分销员信息:', res.data);

          // 保存分销员ID用于其他接口调用
          this.distributorId = res.data.id;

          // 保存收款账号
          this.receiverNo = res.data.receiverNo || '';

          // 获取可提现余额
          this.getAvailableBalance();
        } else {
          console.warn('用户不是分销员或获取分销员信息失败');
          uni.showToast({
            title: '获取分销员信息失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('获取分销员信息异常:', error);
        uni.showToast({
          title: '获取分销员信息失败',
          icon: 'none'
        });
      }
    },

    // 加载配置数据方法
    async loadConfigData() {
      try {
        console.log('开始加载配置信息');
        const response = await this.$api.distribution.queryConfig();
        if (response && response.code === 0 && response.data) {
          this.configData.appid = response.data.defaultAppid || "";
          this.configData.orgCode = response.data.defaultOrgCode || "";
          console.log('配置信息加载成功:', this.configData);
        } else {
          console.error('配置信息加载失败:', response);
          // 使用默认值
          this.configData.appid = "OP10000305";
          this.configData.orgCode = "980271";
        }
      } catch (error) {
        console.error('获取配置信息出错:', error);
        // 出错时使用默认值
        this.configData.appid = "OP10000305";
        this.configData.orgCode = "980271";
      }
    },

    // 获取可提现余额
    async getAvailableBalance() {
      try {
        // 确保有收款账号
        if (!this.receiverNo) {
          console.error('未获取到收款账号');
          return;
        }

        uni.showLoading({
          title: '加载中...'
        });

        // 调用查询账户余额API
        const res = await this.$api.accompanyDoctor.lklaccompanyBalanceQuery({
          orgNo: this.configData.orgCode || '980271',
          payType: '04',
          appid: this.configData.appid || 'OP10000305',
          receiverNo: this.receiverNo,
          providerId: this.providerId,
        });

        console.log('获取余额',res);

        if (res.data && res.data.respData) {
          // 从接口返回的respData中获取余额数据
          const balanceData = res.data.respData;

          // 使用curBalance作为当前余额（当前余额）
          if (balanceData.curBalance) {
            this.availableBalance = balanceData.curBalance;
          } else {
            this.availableBalance = '0.00';
          }
        } else {
          this.availableBalance = '0.00';
          console.error('获取余额返回数据格式异常:', res);
        }

        uni.hideLoading();
      } catch (error) {
        uni.hideLoading();
        console.error('获取余额失败:', error);
        uni.showToast({
          title: '获取余额失败',
          icon: 'none'
        });
        this.availableBalance = '0.00';
      }
    },

    // 检查金额是否有效
    checkAmount() {
      // 去除非数字和非小数点字符
      this.withdrawalAmount = this.withdrawalAmount.replace(/[^\d.]/g, '');

      // 确保只有一个小数点
      const parts = this.withdrawalAmount.split('.');
      if (parts.length > 2) {
        this.withdrawalAmount = parts[0] + '.' + parts.slice(1).join('');
      }

      // 限制小数点后两位
      if (parts.length > 1 && parts[1].length > 2) {
        this.withdrawalAmount = parts[0] + '.' + parts[1].substring(0, 2);
      }

      // 检查金额是否有效
      const amount = parseFloat(this.withdrawalAmount || 0);
      const available = parseFloat(this.availableBalance || 0);

      this.isAmountValid = amount >= this.minAmount && amount <= available && amount > 0;
    },

    // 设置最大提现金额
    setMaxAmount() {
      this.withdrawalAmount = this.availableBalance;
      this.checkAmount();
    },

    // 提交提现申请
    async submitWithdrawal() {
      if (!this.isAmountValid) {
        return;
      }

      try {
        uni.showLoading({
          title: '提交中'
        });

        // 如果输入金额和余额已是元为单位，转换为分（整数）再提交
        const amountInCents = Math.round(parseFloat(this.withdrawalAmount) * 100);
        const codeUserInfo = common.getKeyVal('user', 'codeUserInfo', true);
        const res = await this.$api.accompanyDoctor.accompanypayoutInsert({
          providerId: this.providerId,
          providerName: this.providerName,
          amount: amountInCents,
          userId: codeUserInfo.id,
          userType: 2
        });

        uni.hideLoading();

        if (res.code === 0 || res.code === 200) {
          uni.showToast({
            title: '提现申请已提交',
            icon: 'success'
          });

          // 延迟返回上一页
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        } else {
          uni.showToast({
            title: res.msg || '提交失败',
            icon: 'none'
          });
        }
      } catch (error) {
        uni.hideLoading();
        console.error('提现申请失败:', error);
        uni.showToast({
          title: '提交失败，请重试',
          icon: 'none'
        });
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.withdrawal-page {
  min-height: 100vh;
  background-color: #F4F6FA;
  display: flex;
  flex-direction: column;
  position: relative;
  padding-bottom: 120rpx;
}

.withdrawal-content {
  padding: 30rpx;
}

/* 金额部分样式 */
.amount-section {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 30rpx;
}

.amount-input-wrapper {
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid #EEEEEE;
  padding-bottom: 20rpx;
  margin-bottom: 20rpx;
}

.currency-symbol {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  margin-right: 20rpx;
}

.amount-input {
  flex: 1;
  font-size: 36rpx;
  color: #333333;
}

.all-amount {
  font-size: 28rpx;
  color: #00B484;
  padding: 10rpx 20rpx;
}

.amount-limit {
  font-size: 24rpx;
  color: #999999;
}

/* 提现说明样式 */
.withdrawal-info {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 30rpx;
}

.info-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
}

.info-content {
  color: #666666;
}

.info-item {
  font-size: 26rpx;
  line-height: 1.6;
  margin-bottom: 16rpx;
}

/* 提交按钮样式 */
.submit-btn-wrapper {
  padding: 30rpx;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #FFFFFF;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.submit-btn {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  background-color: #00B484;
  color: #FFFFFF;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 45rpx;
  text-align: center;

  &.disabled {
    background-color: #CCCCCC;
  }
}
</style>
