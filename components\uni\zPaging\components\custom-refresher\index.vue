<!-- z-paging自定义的下拉刷新view -->
<template>
	<view class="refresher-container mescroll-upwarp">
		<!-- 这里的图片请换成自己项目的图片 -->
    	<!-- <view class="upwarp-progress mescroll-rotate"></view>
    	<view class="refresher-text">{{statusText}}</view> -->
		<image class="refresher-image" mode="aspectFit" :src="iconurl"></image>
		<text class="refresher-text">{{statusText}}</text>
	</view>
</template>

<script>
	export default {
		data() {
			return {
			   iconurl:this.$static_ctx + 'image/business/hulu-v2/icon-hulu-loading.gif',
			};
		},
		computed: {
			statusText() {
				// 这里可以做i18n国际化相关操作，可以通过uni.getLocale()获取当前语言(具体操作见i18n-demo.vue);
				// 获取到当前语言之后，就可以自定义不同语言下的展示内容了
				const statusTextArr = ['下拉刷新', '释放更新', '正在努力刷新中...', '刷新成功啦~'];
				return statusTextArr[this.status];
			}
		},
		props: {
			status: {
				type: Number,
				default: function() {
					return 0;
				},
			},
		}
	}
</script>

<style scoped>
  /* 下拉刷新,上拉加载--旋转进度条 */
  .mescroll-downwarp .downwarp-progress,
  .mescroll-upwarp .upwarp-progress {
  	display: inline-block;
  	width: 32upx;
  	height: 32upx;
  	border-radius: 50%;
  	border: 2upx solid gray;
  	border-bottom-color: transparent;
  	vertical-align: middle;
  }

  /* 旋转动画 */
  .mescroll-rotate {
  	-webkit-animation: mescrollRotate 0.6s linear infinite;
  	animation: mescrollRotate 0.6s linear infinite;
  }

  @-webkit-keyframes mescrollRotate {
  	0% {
  		-webkit-transform: rotate(0deg);
  	}

  	100% {
  		-webkit-transform: rotate(360deg);
  	}
  }

  @keyframes mescrollRotate {
  	0% {
  		transform: rotate(0deg);
  	}

  	100% {
  		transform: rotate(360deg);
  	}
  }
	.refresher-container {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		height: 150rpx;
		flex-direction: column;
		align-items: center;
		justify-content: center;
    /* background-color:#fff; */
    /* width: 100vw; */
	}

	.refresher-image {
		margin-top: 10rpx;
		height: 45px;
		width: 45px;
	}

	.refresher-text {
		/* margin-top: 10rpx; */
		font-size: 24rpx;
		color: #666666;
    margin-left: 10upx;
	}
</style>
