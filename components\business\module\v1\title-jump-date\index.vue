<!-- 下拉日期组件<title-jump-date title="日期" :date="regForm.date" @returnFn="dateReturnFn"></title-jump-date>
></title-jump-date> -->
<template>
  <view class="title-jump" :class="{ bdt: bdt, bdb: bdb }">
    <picker
      :disabled="disabled"
      mode="date"
      :value="date"
      :fields="fields"
      :start="startDate"
      :end="endDate"
      @change="returnFn"
    >
      <text class="l-t"><em class="xingxing" v-if="isRequired">*</em>{{ title }}</text>
      <text class="l-r" :class="{ 'color-999': disabled, oh: oh }">
        {{ date }}
      </text>
      <em class="icon-gengduo jump"></em>
    </picker>
  </view>
</template>

<script>

export default {

  data() {
    return {
      defaultConfig: {},
      date: ''
    }
  },
  props: {
    value: String,
    // 超出隐藏
    oh: {
      type: Boolean,
      default() {
        return false
      }
    },
    // 数组下标
    dIndex: {
      type: [String, Number],
      default() {
        return ''
      }
    },
    // 是否禁用disable
    disabled: {
      type: <PERSON>olean,
      default() {
        return false
      }
    },
    // fields有效值 year、month、day，表示选择器的粒度，默认为 day，App 端未配置此项时使用系统 UI
    fields: {
      type: String,
      required: false,
      default() {
        return 'day'
      }
    },
    // 开始时间
    startDate: {
      type: String,
      required: false,
      default() {
        return ''
      }
    },
    // 结束时间
    endDate: {
      type: String,
      required: false,
      default() {
        return ''
      }
    },
    // 标题名称
    title: {
      type: String,
      required: false,
      default() {
        return ''
      }
    },
    // border-top
    bdt: {
      type: Boolean,
      required: false,
      default() {
        return false
      }
    },
    // border-bottom
    bdb: {
      type: Boolean,
      required: false,
      default() {
        return true
      }
    },
    isRequired: {
      type: Boolean,
      default() {
        return false
      }
    },
    obj: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  watch: {
    obj: {
      handler(val) {
        this.copyConfig()
      },
      deep: true
    },
    value: {
      handler () {
        this.date = this.value
      },
      immediate: true,
      deep: true
    },
    date: {
      handler () {
        this.$emit('input', this.date)
        this.$emit('change', this.date)
      },
      deep: true
    }
  },
  mounted() {
    this.copyConfig()
    this.watchDataMain(this.date)
  },
  methods: {
    watchDataMain(val) {
      this.dateText = val.toString()
    },
    /**
     * 初始化拷贝config对象
     */
    copyConfig() {
      const that = this
      const obj = that.obj
      Object.keys(obj).forEach(function (key) {
        that.defaultConfig[key] = obj[key]
      })
      that.defaultConfig = Object.assign({}, that.defaultConfig)
    },
    returnFn(e) {

      if (this.$validate.isNull(this.defaultConfig)) {
        this.date = e.target.value
        if (!this.$validate.isNull(this.dIndex)) {
          const obj = {
            value: this.date,
            index: this.dIndex
          }
          this.$emit('returnFn', obj)
        } else {
          this.$emit('returnFn', e.target.value)
        }
      } else {
        this.defaultConfig.date = e.target.value
        this.defaultConfig.dIndex = this.dIndex
        this.$emit('returnFn', this.defaultConfig)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.color-999 {
  color: #999999 !important;
}
.title-jump {
  height: 88upx;
  overflow: hidden;
  background-color: #ffffff;
  .l-t {
    display: inline-block;
    vertical-align: middle;
    line-height: 88upx;
    color: #333333;
    // font-weight: 600;
    font-size:30upx;
    @include ellipsis();
    width: 240upx;
  }
  .l-r {
    display: inline-block;
    vertical-align: middle;
    font-size: 32upx;
    color: #333;
    line-height: 88upx;
    width: calc(100% - 274upx);
    text-align: right;
  }
  .jump {
    width: 34upx;
    height: 34upx;
    // margin: 0upx 20upx 0 0;
    display: inline-block;
    vertical-align: middle;
  }
  .oh {
    @include ellipsis(1);
  }
  .xingxing {
    color: #f85e4c;
    padding-top: 6upx;
    font-size: 32upx;
    display: inline-block;
  }
}
</style>
