import MessageDefault from "./MessageDefault"
import common from '@/common/util/main'

export default class HandleLoginOut extends MessageDefault {
    constructor (websocket) {
        super()
        this.websocket = websocket
    }

    match (dataJson) {
        const { LOGIN_OUT_CMD } = this.chatCmd
        return dataJson.cmd === LOGIN_OUT_CMD
    }

    processMessage (dataJson) {
      this.webSocketOut.call(this.websocket)
    }

    webSocketOut() {
        const webSocketWarningText = "异地登录请重新登录";
        common.setKeyVal('chat', 'webSocketWarningText', webSocketWarningText, false)
        // 修改重连状态
        this.webSocketIsReconnect = false;
        this.webSocketPingTimer = null
        if (this.webSocket) {
          console.log("关闭websocket");
          // 关闭 websocket
          this.webSocket.close();
        }
      }
}