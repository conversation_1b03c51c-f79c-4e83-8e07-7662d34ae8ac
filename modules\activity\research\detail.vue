<template>
  <page>
    <view slot="content" class="advice">
      <view class="li">
        <view class="main-content">
          <view class="title">
            <text class="name">{{regForm.title}}</text>
          </view>
          <view class="secenter">
            <view class="l">
              来源：{{ regForm.author || '运营' }}
            </view>
            <view class="l">
              {{ regForm.pushTimeText }}
            </view>
          </view>
          <view class="cover-path">
            <image class="width-height-atuo" mode="aspectFit" :src="file_ctx + regForm.coverPath"/>
          </view>
          <view class="video-content">
            <view class="introduction">
              <view class="head">
                <view class="l">
                </view>
                <view class="r">
                  简介
                </view>
              </view>
            </view>
          </view>

          <view>
            <rich-text v-if="regForm.desc" :nodes="regForm.desc"></rich-text>
            <view class="article" v-else>
              <text>{{regForm.desc}}</text>
            </view>
          </view>
          <view class="head">
            <view class="l">
            </view>
            <view class="r">
              问卷填写
            </view>
          </view>

            <form-template v-if="openTemplate" :businessId="regForm.id" :templateData="templateData" @returnFn="returnFn"></form-template>

        </view>
      </view>
    </view>
  </page>
</template>

<script>
import FormTemplate from '@/modules/business/template/form-template'
import { mapState } from "vuex"
export default {
  name: 'NoticeAdviceDetail',
  components: {
    FormTemplate
  },
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $accurateConversion: this.$accurateConversion,
      file_ctx: this.file_ctx,
      $timePlugin: this.$timePlugin,
      id: undefined,
      index: undefined,
      regForm:{},
      templateData:{},
      openTemplate:false
    }
  },
  computed: {
    ...mapState('user', {
      recordUserInfo: state => state.recordUserInfo // 当前登录用户信息
    })
  },
  onLoad(paramsObj) {
    const query = this.$Route.query
    if (!this.$validate.isNull(query)) {
      this.id = query.id
      this.index = query.index
    }
    this.id && this.getDetail(this.id)
  },
  methods: {
    returnFn(param){
      const { name,userId } = this.recordUserInfo
      param = {
        ...param,
        userName: name,
        accountId: this.$common.getKeyVal('user', 'accountId', true),
        userId: userId
      }
      const that = this
      that.$api.activity.formtemplatewriteFormWrite(param).then(res => {
        that.$uniPlugin.toast('提交成功')
        this.getDetail(this.id)
      })
    },
    getDetail(id) {
      const that = this
      that.$api.activity.researchQueryOne({ id: id }).then(res => {
        res.data.pushTimeText = this.$common.formatDate(new Date(res.data.createTime), 'yyyy-MM-dd')
        that.regForm = res.data
        that.$api.activity.queryBusinessTemplateDTOList({ templateIds: that.regForm.copyTemplateId }).then(res => {
          this.templateData = res.data
          that.openTemplate = true
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.head {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  display: -webkit-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
  -webkit-box-align: center;
  -ms-flex-align: center;
  -webkit-align-items: center;
  align-items: center;
  margin-top: 30upx;
  .l{
    width: 10upx;
    height: 32upx;
    background: #00D29D;
    border-radius: 1px;
  }
  .r{
    margin-left: 10upx;
  }
}
.advice{
  width: 100%;
  //height: 100%;
  overflow-y: auto;
  background: #f7f7f7;
  .li{
    background: #fff;
    width: 100%;
    height: 100%;
    //overflow: hidden;
    .main-content{
      width: 100%;
      padding: 30upx;
      box-sizing: border-box;
      //overflow: hidden;
      .title{
        width: 100%;
        .name{
          font-size: 40upx;
          display: block;
          margin:0 14upx 10upx 0;
          font-weight: 600;
          color: #121822;
        }

      }
      .secenter{
        padding: 30upx 0 30upx 0;
        position: relative;
        display: -webkit-box;
        display: -moz-box;
        display: -ms-flexbox;
        display: flex;
        display: -webkit-flex;
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        -webkit-justify-content: flex-start;
        justify-content: flex-start;
        -webkit-box-align: center;
        -ms-flex-align: center;
        -webkit-align-items: center;
        align-items: center;
        .l {
          font-size: 25upx;
          font-weight: 400;
          color: #A5AEBB;
          line-height: 20upx;
          margin-right: 40upx;
        }
      }
      .cover-path{
        image{
          width: 100%;
          //height: 100%;
        }
      }
      .video-content{
        width: 100%;
        .introduction{
          padding-top: 10upx;
          position: relative;
          .content{
            color: #726e6e;
          }
        }
      }
      .article{
        line-height: 1.5;
        font-size: 28upx;
        color: #666666;
        padding-bottom: 20upx;
      }
    }
  }
}
</style>
