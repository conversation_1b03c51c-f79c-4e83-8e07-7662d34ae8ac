<template>
  <view class="title-rate" :style="{background: defaultConfig.bg}">
    <view class="title-rate-title" v-if="defaultConfig.isTitle && defaultConfig.titleType === 'top'" :style="{background: defaultConfig.bgTitle}">
      {{defaultConfig.title}}
    </view>
    <view class="title-rate-t">
      <view class="title-rate-li-title" v-if="defaultConfig.isTitle && defaultConfig.titleType === 'left'">{{defaultConfig.title}}</view>
      <view class="title-rate-list">
        <view class="title-rate-li-main">
          <view class="title-rate-li" @tap="_click(index)" :class="[item.state ? 'icon-score-star-on' : 'icon-score-star']" v-for="(item,index) in list" :key="index"></view>
        </view>
      </view>
    </view>
    <view class="title-rate-b" v-if="defaultConfig.isRemark">
      {{list[listIndex].text}}
    </view>
  </view>
</template>

<script>
  export default {
    name: "UniRate",
    components: {

    },
    props: {
      // 数组下标
      dIndex: {
        type: [String, Number],
        default() {
          return ''
        }
      },
      // 初始值
      cData: {
        type: [Number, String],
        default() {
          return 5
        }
      },
      // 参数设置
      config: {
        type: Object,
        required: false,
        default: () => {
          return {}
        }
      },
      disabled: {
        // 是否可点击
        type: [Boolean, String],
        default: false
      }
    },
    data() {
      return {
        defaultConfig: {
          titleType: 'top', // 标题类型；top、left
          isTitle: false, // 是否开启标题
          title: '评分',
          bgTitle: '#f7f7f7', // 标题背景色
          bg: '#fff', // 背景色
          isRemark: true // 是否开启评语
        },
        valueSync: 0, // 当前选中的值
        listIndex: 0, // 当前选中的下标
        list: [ // 评分数组
          {
            value: 1,
            text: '非常差',
            state: false
          },
          {
            value: 2,
            text: '差',
            state: false
          },
          {
            value: 3,
            text: '一般',
            state: false
          },
          {
            value: 4,
            text: '满意',
            state: false
          },
          {
            value: 5,
            text: '推荐',
            state: false
          }
        ]
      };
    },
    computed: {

    },
    created() {

    },
    mounted() {
      this.copyConfig()
      this.watchDataMain(this.cData)
    },
    watch: {
      cData: {
        handler(val) {
          this.watchDataMain(val)
        },
        deep: true
      },
      config: {
        handler(val) {
          this.copyConfig()
        },
        deep: true
      }
    },
    methods: {
      /**
       * 初始化拷贝config对象
       */
      copyConfig() {
        const that = this
        const obj = that.config
        Object.keys(obj).forEach(function(key) {
          that.defaultConfig[key] = obj[key]
        })
      },
      watchDataMain(val) {
        const valueSync = Number(val);
        this.stateListInit(valueSync)
      },
      // 评分数组初始化
      stateListInit(valueSync) {
        for (let i = 0; i < this.list.length; i++) {
          this.list[i].state = false
          if (valueSync === this.list[i].value) {
            this.listIndex = i
            this.valueSync = this.list[i].value
          }
        }
        this.$set(this.list, this.listIndex, this.list[this.listIndex])
        this.stateListFn()
      },
      // 改变评分状态
      stateListFn() {
        for (let i = 0; i < this.list.length; i++) {
          if (this.listIndex >= i) {
            this.list[i].state = true
          }
        }
        this.$set(this.list, this.listIndex, this.list[this.listIndex])
        this.returnFn()
      },
      // 点击事件
      _click(index) {
        if (this.disabled) {
          return
        }
        this.listIndex = index
        this.valueSync = this.list[index].value
        this.stateListFn()
      },
      returnFn() {
        if (this.disabled) {
          return
        }
        this.valueSync = this.list[this.listIndex].value

        if (!this.$validate.isNull(this.dIndex)) {
          const obj = {
            value: this.valueSync,
            index: this.dIndex
          }
          this.$emit('returnFn', obj)
        } else {
          this.$emit("returnFn", {
            value: this.valueSync
          })
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .title-rate{
    .title-rate-title {
      padding: 30upx 30upx 10upx 30upx;
      font-size: 32upx;
      line-height: 48upx;
    }
    .title-rate-t{
      position: relative;
      height: 104upx;
      padding: 30upx;
      box-sizing: border-box;
      .title-rate-li-title {
        line-height: 48upx;
        position: absolute;
        top: 30upx;
        left: 30upx;
        max-width: 180upx;
        @include ellipsis(1);
      }
      .title-rate-list{
        position: absolute;
        top: 30upx;
        left: 50%;
        -webkit-transform: translateX(-50%);
        transform: translateX(-50%);
        line-height: 0;
        .title-rate-li-main {
          vertical-align: middle;
          display: inline-block;
          .title-rate-li{
            vertical-align: middle;
            display: inline-block;
            margin-right: 16upx;
          }
          .title-rate-li:last-of-type{
            margin-right: 0;
          }
        }
        .icon-score-star-on{
          @include iconImg(48, 44, '/business/icon-score-star-on.png');
        }
        .icon-score-star{
          @include iconImg(48, 44, '/business/icon-score-star.png');
        }
      }
    }
    .title-rate-b{
      text-align: center;
      font-size: 32upx;
      line-height: 48upx;
      padding-bottom: 30upx;
    }
  }
</style>
