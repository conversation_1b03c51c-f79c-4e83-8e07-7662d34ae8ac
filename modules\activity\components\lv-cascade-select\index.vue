<template>
  <view
		class="list-view">
	<!-- <view
		class="list-view"
		:class="{
			isAbsolute:isAbsolute
		}"
		:style="{
			height:height,
			top:top,
		}"
	>
		<view class="list-view-item" :class="level === 2 ? 'w50' : ''">
			<scroll-view
				class="list-scroll-view"
				:scroll-x="true"
				:scroll-y="true"
				:style="{
					height:height
				}"
			>
				<template v-for="item in provinceOptions">
					<view
						:key="item[labelValue]"
						class="list-scroll-item"
						:class="{ selected: item[labelValue] === selectedArray[0]}"
						@click="handleClick(item,0)"
					>
						{{ item[labelText] }}
					</view>
				</template>
			</scroll-view>
		</view>
		<view class="list-view-item" :class="level === 2 ? 'w50' : ''">
			<scroll-view
				class="list-scroll-view"
				:scroll-x="true"
				:scroll-y="true"
				:style="{
					height:height
				}"
			>
				<template v-for="item in cityOptions">
					<view
						:key="item[labelValue]"

						class="list-scroll-item"
						:class="{ selected: item[labelValue] === selectedArray[1]}"
						@click="handleClick(item,1)"
					>
						{{ item[labelText] }}
					</view>
				</template>
			</scroll-view>
		</view>
		<view class="list-view-item" :class="level === 2 ? 'w50' : ''" v-if="level === 3">
			<scroll-view
				class="list-scroll-view"
				:scroll-x="true"
				:scroll-y="true"
				:style="{
					height:height
				}"
			>
				<template v-for="item in townOptions">
					<view
						:key="item[labelValue]"
						class="list-scroll-item selectbox"
						:class="{ selected: item[labelValue] === selectedArray[2]}"
						@click="handleClick(item,2)"
					>
						{{ item[labelText] }}

						<icon
							v-if="item[labelValue] === selectedArray[2] && selected === selectedArray[2]"
							class="list-scroll-ico"
							size="14"
							type="success"
						/>
					</view>
				</template>
			</scroll-view>
		</view> -->
	</view>
</template>

<script>
export default {
	name: 'LvCascadeSelect',
	props: {
		// isAbsolute: {
		// 	type: Boolean,
		// 	default: false,
		// },
		// top: {
		// 	type: String,
		// 	default: '100%'
		// },
		// list: {
		// 	type: Array,
		// 	default: function (){
		// 		return []
		// 	}
		// },
		// updatecount: {
		// 	type: Number,
		// 	default: 0
		// },
		// value: {
		// 	type: Array,
		// 	default: function () {
		// 		return ['all', 'all', 'all']
		// 	}
		// },
		// selectedKey: {
		// 	type: [Number, String],
		// 	default: 'all',
		// },
		// height: {
		// 	type: String,
		// 	default: '400px'
		// },
		// labelText: {
		// 	type: String,
		// 	default: 'name'
		// },
		// labelValue: {
		// 	type: String,
		// 	default: 'code'
		// },
    // level:{
    //   type:Number,
    //   default:3
    // }

	},
	data(){
		return {
			// selected: 'all',

			// // 省
			// provinceOptions: [],
			// cityOptions: [],
			// townOptions: [],
			// defaultOptions: [
			// 	{
			// 		label: '全国',
			// 		value: 'all',
			// 		children: [
			// 			{
			// 				label: '全国',
			// 				value: 'all',
			// 				children: [
			// 					{
			// 						label: '全国',
			// 						value: 'all'
			// 					}
			// 				]
			// 			}
			// 		]
			// 	}
			// ],

			// defaultProvinceResult: [],
			// // 选中值
			// selectedArray: ['all', 'all', 'all'],
			// selectedLabelArray: ['全国', '全国', '全国']
		}
	},
	watch: {
		// selectedKey(n) {
		// 	this.selected = n
		// },
		// updatecount(n) {
		// 	console.log('updatecount', this.provinceOptions)
		// 	if(this.provinceOptions.length === 0){
		// 		this.initData()
		// 	}else{

		// 		console.log('updateOptions')
		// 		this.updateOptions()
		// 	}
		// },
		// list: {
		// 	handler(val) {
		// 		this.initData()
		// 	},
		// 	deep: true
		// },
		// value: {
		// 	handler(val) {
		// 		this.selectedArray = []
		// 		if(val){
		// 			this.selectedArray[0] = val[0]
		// 			this.selectedArray[1] = val[1]
		// 			this.selectedArray[2] = val[2]
		// 		}else{
		// 			this.selectedArray[0] = null
		// 			this.selectedArray[1] = null
		// 			this.selectedArray[2] = null
		// 		}
		// 		// this.watchDataMain(val)
		// 	},
		// 	deep: true
		// }
	},
	mounted(){
		// console.log('this.this.provinceOptions', this.provinceOptions)
		// console.log(this.value)
		// if(this.provinceOptions.length === 0 && (this.value[0] === 'all' || this.value.length === 0)){
		// 	this.initData()
		// }else{
		// 	this.selected = this.selectedKey
		// 	console.log('updateOptions', this.selectedKey)
		// 	this.initDefaultProvince()
		// 	const list = [
		// 		...this.defaultProvinceResult,
		// 		...this.list
		// 	]

		// 	const provinceOptions = this.getProvinceOptions(list)

		// 	this.provinceOptions = provinceOptions
		// 	this.updateOptions()
		// }
		// this.initData()
	},
	methods: {
// 		updateOptions(){
// 			const value = this.selectedArray
// 			// const list = this.list
// 			const list = [
// 				...this.defaultProvinceResult,
// 				...this.list
// 			]
// 			const provinceIdx = this.provinceOptions.findIndex(item => item[this.labelValue] === value[0])
// 			if(provinceIdx === -1){
// 				this.cityOptions = []
// 				return
// 			}
// 			const cityOptions = this.getProvinceOptions(list[provinceIdx].children)
// 			this.cityOptions = cityOptions

// 			let cityIdx = this.cityOptions.findIndex(item => item[this.labelValue] === value[1])
// 			if(cityIdx === -1){
// 				// this.townOptions = []
// 				cityIdx = 0
// 				// return
// 			}

// 			const townOptions = this.getProvinceOptions(cityOptions[cityIdx].children)
// 			this.townOptions = townOptions

// 		},
// 		handleClick(item, idx){
// 			this.selectedArray[idx] = item[this.labelValue]
// 			this.selectedLabelArray[idx] = item[this.labelText]
// 			// console.log('item', this.selectedArray)
// 			this.updateOptions()
// 			let close = false
// 			if(idx === 0){
// 				this.selectedArray[1] = this.cityOptions[0][this.labelValue]
// 				this.selectedArray[2] = this.townOptions[0][this.labelValue]
// 				this.selectedLabelArray[2] = this.townOptions[0][this.labelText]
// 				this.selectedLabelArray[1] = this.cityOptions[0][this.labelText]
// 				// this.selected = false

// 			}else if(idx === 1){
// 				this.selectedArray[2] = this.townOptions[0][this.labelValue]
// 				this.selectedLabelArray[2] = this.townOptions[0][this.labelText]

// 				// this.selectedArray[0] = this.cityOptions[0][this.labelValue]
//         // this.selectedArray[1] = this.cityOptions[0][this.labelValue]
// 				// this.selectedLabelArray[0] = this.cityOptions[0][this.labelText]
// 				// this.selectedLabelArray[1] = this.cityOptions[0][this.labelText]
//         console.log(this.cityOptions,'this.cityOptions')
//         console.log(this.townOptions,'this.townOptions')
//         // if(this.level === 2){
//         //   close = true
//         // }
// 				// this.selected = false
// 			}else if(idx === 2){
// 				close = true
// 				// this.selected = true
// 			}

// 			this.$forceUpdate()
// 			this.$emit('change', {
// 				select: [
// 					...this.selectedArray
// 				],
// 				selectLabel: [
// 					...this.selectedLabelArray
// 				],
// 				close: close
// 			})

// 		},
// 		initDefaultProvince(){
// 			if(this.defaultProvinceResult.length === 0){

//       	const defaultProvinceResult = []
//       	for(let i=0;i<this.defaultOptions.length;i++){
//       		const obj = {
//       			children: []
//       		}
//       		if(this.defaultOptions[i].children){
//       			for(let j = 0;j<this.defaultOptions[i].children.length;j++){
//       				let tempobj = {
//       					children: []
//       				}
//       				if(this.defaultOptions[i].children[j].children){
//       					for(let ii = 0;ii<this.defaultOptions[i].children[j].children.length;ii++){
//       						let tempobj2 = {
//       							children: []
//       						}
//       						tempobj2[this.labelText] = this.defaultOptions[i].children[j].children[ii].label
//       						tempobj2[this.labelValue] = this.defaultOptions[i].children[j].children[ii].value

//       						// this.defaultOptions[i].children[j].children
//       						tempobj.children.push(tempobj2)
//       					}
//       				}

//       				tempobj[this.labelText] = this.defaultOptions[i].label
//       				tempobj[this.labelValue] = this.defaultOptions[i].value

//       				obj.children.push(tempobj)

//       			}
//       		}
//       		obj[this.labelText] = this.defaultOptions[i].label
//       		obj[this.labelValue] = this.defaultOptions[i].value
//       		defaultProvinceResult.push(obj)

//       	}

//       	this.defaultProvinceResult = defaultProvinceResult
// 			}
// 		},
// 		initData(){
// 			// this.updateOptions()

// 			const value = this.value

// 			this.initDefaultProvince()

// 			const list = [
// 				...this.defaultProvinceResult,
// 				...this.list
// 			]
// 			const provinceOptions = this.getProvinceOptions(list)

// 			console.log('provinceOptions', provinceOptions)
// 			this.provinceOptions = provinceOptions

// 			let provinceIdx = this.provinceOptions.findIndex(item => item[this.labelValue] === value[0])
// 			console.log('provinceIdx', provinceIdx)
// 			if(provinceIdx === -1){
// 				// this.cityOptions = []
// 				provinceIdx = 0
// 				// return
// 			}

// 			const cityOptions = this.getProvinceOptions(list[provinceIdx].children)
// 			console.log('cityOptions', cityOptions)
// 			this.cityOptions = cityOptions

// 			let cityIdx = this.cityOptions.findIndex(item => item[this.labelValue] === value[1])
// 			if(cityIdx === -1){
// 				cityIdx = 0
// 				// this.townOptions = []
// 				// return
// 			}

// 			const townOptions = this.getProvinceOptions(cityOptions[cityIdx].children)
// 			this.townOptions = townOptions

// 		},
// 		getProvinceOptions(list = [], isChild){
// 			const provinceOptions = []
// 			for(let i=0;i<list.length;i++){
// 				if(isChild){
// 					let obj = {

// 					}
// 					obj[this.labelText] = list[i][this.labelText]
// 					obj[this.labelValue] = list[i][this.labelValue]

// 					provinceOptions.push(obj)
// 				}else{
// 					let obj = {
// 						children: this.getProvinceOptions(list[i].children, true)
// 					}
// 					obj[this.labelText] = list[i][this.labelText]
// 					obj[this.labelValue] = list[i][this.labelValue]

// 					provinceOptions.push(obj)
// 				}

// 			}
// 			return provinceOptions
// 		}
	}
}
</script>

<style scoped lang="scss">
  // .w50{
  //   min-width: 50% !important;
  // }
  // .isAbsolute{
  //   position: absolute;
  //   left: 0;
  //   right: 0;
  // }
  // .list-scroll-view{
  //   height: 100%;
  //   &:first-child,
  //   &:last-child {
  //     border: 0;
  //   }
  // }
  // .list-scroll{

  //    &-item {
  //     height: 80rpx;
  //     padding: 0 13rpx 0 40rpx;
  //     line-height: 80rpx;
  //     white-space: nowrap;
  //     overflow: hidden;
  //     text-overflow: ellipsis;
  //     text-align: center;
  //    }
  //    &-item.selected{
  //      background-color: #fff;
  //    }
  // }
  // .list-view{
  //   display: flex;
  //   font-size: 30rpx;
  //   height: 100%;
  //   min-height: 400upx;
  //   background-color: #f1f1f1;
  //   // min-height: 100%;
  //   .list-view-item{
  //     flex:1;
  //     max-width: 33.33%;
  //   }
  //   .list-view-item.one{
  //     background-color: yellowgreen;
  //   }
  //   .list-view-item.two{
  //     background-color: beige;
  //   }
  //   .list-view-item.three{
  //     background-color: skyblue;
  //   }

  //   .selectbox{
  //     position: relative;
  //     padding-right:28upx;
  //     .list-scroll-ico{
  //       position: absolute;
  //       top: 50%;
  //       right: 5upx;
  //       height: 60upx;
  //       transform: translateY(-50%);

  //     }
  //   }

  // }
</style>
