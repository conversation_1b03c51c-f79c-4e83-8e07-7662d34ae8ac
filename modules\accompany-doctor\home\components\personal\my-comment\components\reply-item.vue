<template>
    <view class="content">
        <view class="reply">
            <template v-if="info.imagePath">
                <image @click="previewImage(info.imagePath.split(','), eIndex)" v-for="(e, eIndex) in info.imagePath.split(',')" :key="eIndex"
                    :src="file_ctx + e" mode="widthFix" class="content-img"
                ></image>
            </template>
            <template v-if="info.content">
                <view class="reply-content" v-html="info.content"></view>
            </template>
            <view class="reply-main">
                <text class="reply-user">
                    @{{ info.mutualNickname }}
                </text>
                <view class="reply-user-content" v-html="info.mutualContent">
                </view>
            </view>
            <view class="reply-bottom">
                <text class="reply-time">{{ info.createTime }}</text>
                <view class="flex-box" v-if="info.subscribeStatus !== 2">
                    <button class="flex-box btn" type="warn" @tap="$emit('del', info)">
                        <uni-icons type="trash" color="#fff" :size="16" />
                    </button>
                    <view class="flex-box" style="padding-left: 16rpx;">
                        <uni-icons type="heart" style="padding-right: 6rpx;" :size="16" />
                        {{ info.likeNumber }}
                    </view>
                </view>
                <text class="del-text" v-else>该评论已删除</text>
            </view>
        </view>
    </view>
</template>

<script>
import defaultImg from '@/components/basics/default-avatar/index'
import mpHtml from '@/components/basics/mp-html/mp-html.vue'
import UniIcons from '@/components/uni/uni-icons/uni-icons.vue';
export default {
    components: {
        defaultImg,
        mpHtml,
        UniIcons
    },
    props: {
        info: {
            type: Object,
            default: function () {
                return {}
            }
        }
    },
    data () {
        return {
            file_ctx: this.file_ctx,
            defaultAvatar: this.$static_ctx + 'image/system/avatar/icon-default-avatar.png'
        }
    },
    methods: {
        // 预览图片
		previewImage (list, index) {
			uni.previewImage({
				current: index,
				urls: list.map(item => this.file_ctx + item)
			})
		},
    }
}
</script>

<style scoped lang="scss">
.content {
    display: flex;
    box-sizing: border-box;
}
.reply {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding-left: 21upx;
    box-sizing: border-box;
    &-top {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 62upx;
    }
    &-box {
        width: 120upx;
        height: 50upx;
        background: #F6F6F6;
        @include rounded(25upx);
        box-sizing: border-box;
        text-align: center;
    }
    &-text {
        font-size: 31upx;
        font-weight: 500;
        color: #00D29D;
        line-height: 50upx;
    }
    .user-info {
        display: flex;
        align-items: center;
        .name {
            font-size: 31upx;
            font-weight: 500;
            color: #2D2D2D;
            line-height: 42upx;
        }
    }
    &-content {
        padding: 13upx 0 16upx;
        font-size: 26upx;
        font-weight: 400;
        color: #282828;
        line-height: 42upx;
    }
    &-main {
        padding: 24upx;
        background: #F6F6F6;
        @include rounded(15upx);
        .reply-user {
            display: block;
            padding-bottom: 12upx;
            font-size: 26upx;
            color: #282828;
            line-height: 42upx;
        }
        .reply-user-content {
            font-size: 26upx;
            color: #7F7F7F;
            line-height: 42upx;
        }
    }
    &-bottom {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 15rpx;
    }
    &-time {
        font-size: 24upx;
        color: #7F7F7F;
        line-height: 42upx;
        // padding-top: 12upx;
    }
    .flex-box {
        display: flex;
        align-items: center;
        flex-direction: row;
    }
    .btn {
        padding: 0 24upx;
        margin: 0;
        height: 36upx;
        line-height: 36upx;
    }
    .del-text {
        color: #ACACAC;
        font-size: 24upx;
    }
}
.user-avatar {
    width: 62upx;
    height: 62upx;
    @include rounded(50%);
    .img {
      @include rounded(50%);
      vertical-align:middle;
      display: inline-block;
      overflow: hidden;
      box-sizing: border-box;
      height: 100%;
      width: 100%;
    }
}
.content-img {
	width: 200upx;
	height: 100%;
}
</style>
