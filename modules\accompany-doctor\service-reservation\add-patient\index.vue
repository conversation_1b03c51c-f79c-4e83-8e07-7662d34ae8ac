<template>
  <view class="add-patient-container">
    <uni-nav-bar
      color="#1d2029"
      :border="false"
      :inFontWeight="true"
      :showBtnsRight="false"
      :fixed="false"
      title="就诊人档案"
      statusBar
      @clickLeft="back"
      left-icon="left"
      left-width="48rpx"
      right-width="100px"
      backgroundColor="rgba(0,0,0,0)"
    />
    <!-- 患者列表 -->
    <view class="patient-list">
      <!-- 如果有患者数据就显示列表，否则显示空状态 -->
      <template v-if="patientList.length > 0">
        <view class="patient-item" v-for="(item, index) in patientList" :key="index">
          <view class="patient-info">
            <view class="patient-name">{{item.name}}</view>
            <!-- <view class="patient-relation">{{item.symptom}}</view> -->
          </view>
          <view class="patient-actions">
            <view class="patient-edit" @click.stop="editPatient(item)">
              <image :src="patientEditIcon" mode="aspectFit" class="action-icon"></image>
            </view>
            <view class="patient-delete" @click.stop="deletePatient(item.id)">
              <image :src="patientDeleteIcon" mode="aspectFit" class="action-icon"></image>
            </view>
          </view>
        </view>
      </template>
      
      <!-- 空状态显示 -->
      <view v-if="patientList.length === 0" class="empty-state">
        <view class="empty-text">暂无就诊人档案</view>
      </view>
    </view>
    
    <!-- 底部新增按钮 -->
    <view class="add-btn-wrapper">
        <view class="add-patient-btn" @click="goToAddNewPatient">
        <text class="add-patient-btn-text">新增就诊人</text>
        <image :src="patientAddIcon" mode="aspectFit" class="add-icon-image"></image>
        </view>
    </view>
  </view>
</template>

<script>
import uniNavBar from '@/components/uni/uni-nav-bar/uni-nav-bar.vue'
import serverOptions from '@/config/env/options'

export default {
  components: {
    uniNavBar
  },
  data() {
    return {
      patientList: [],
      patientAddIcon: this.$static_ctx + "image/business/hulu-v2/patient-add.png",
      patientEditIcon: this.$static_ctx + "image/business/hulu-v2/patient-edit.png",
      patientDeleteIcon: this.$static_ctx + "image/business/hulu-v2/patient-delete.png",
      providerId: null
    }
  },
  onLoad(options) {
    // 获取患者档案列表
    this.getProviderId();
    
    // 如果有action参数并且是add，则直接跳转到新增页面
    if (options.action === 'add') {
      this.goToAddNewPatient();
    }
    
    // 监听刷新列表事件
    uni.$on('refreshPatientList', this.loadPatientList);
  },
  onShow() {
    // 页面显示时刷新列表
    if (this.providerId) {
      this.loadPatientList();
    } else {
      this.getProviderId();
    }
  },
  onUnload() {
    // 移除事件监听
    uni.$off('refreshPatientList', this.loadPatientList);
  },
  methods: {
    // 返回上一页
    navigateBack() {
      uni.navigateBack();
    },
    
    // 获取服务商ID
    async getProviderId() {
      try {
        // 直接从全局配置中获取服务商ID
        this.providerId = serverOptions.providerId;
        // 获取患者列表
        this.loadPatientList();
      } catch(error) {
        console.error('获取服务商ID失败', error);
      }
    },
    
    // 加载患者档案列表
    async loadPatientList() {
      try {
        if (!this.providerId) {
          console.error('服务商ID不存在');
          return;
        }
        const {data} = await this.$api.accompanyDoctor.accompanypatientQueryAll({providerId: this.providerId});
        this.patientList = data || [];
      } catch(error) {
        uni.showToast({
          title: '获取档案失败',
          icon: 'none'
        });
      }
    },
    // 编辑患者
    editPatient(patient) {
      uni.navigateTo({
        url: './edit?id=' + patient.id
      });
    },
    
    // 删除患者
    async deletePatient(id) {
      uni.showModal({
        title: '提示',
        content: '确定要删除该就诊人吗？',
        success: async (res) => {
          if (res.confirm) {
            try {
              await this.$api.accompanyDoctor.accompanypatientDeleteOne({id});
              uni.showToast({
                title: '删除成功',
                icon: 'success'
              });
              // 重新加载列表
              this.loadPatientList();
            } catch(error) {
              console.error('删除患者档案失败', error);
              uni.showToast({
                title: '删除失败',
                icon: 'none'
              });
            }
          }
        }
      });
    },
    
    // 前往新增就诊人页面
    goToAddNewPatient() {
      this.$navto.push('addPatient',{ action: 'add' });
    },
    back() {
      uni.navigateBack();
    },
  }
}
</script>

<style lang="scss" scoped>
.add-patient-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

/* 导航栏样式 */
.nav-header {
  display: flex;
  align-items: center;
  height: 90rpx;
  background-color: #fff;
  padding: 0 30rpx;
  position: relative;
}

.nav-back {
  width: 60rpx;
  display: flex;
  align-items: center;
}

.nav-back-icon {
  font-size: 36rpx;
  color: #333;
}

.nav-title {
  flex: 1;
  text-align: center;
  font-size: 34rpx;
  font-weight: 500;
  color: #333;
}

.nav-actions {
  display: flex;
  align-items: center;
}

.nav-action-item {
  margin-left: 40rpx;
  font-size: 36rpx;
  color: #333;
}

/* 患者列表样式 */
.patient-list {
  height: calc(100vh - 136rpx - 177rpx);
  box-sizing: border-box;
  padding: 20rpx 30rpx;
  overflow-y: auto;
  background: #F4F6FA;
}

.patient-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 120rpx;
  background-color: #fff;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
  padding: 0 30rpx;
}

.patient-info {
  display: flex;
  align-items: center;
}

.patient-name {
  font-size: 32rpx;
  color: #333;
  margin-right: 20rpx;
}

.patient-relation {
  font-size: 28rpx;
  color: #999;
}

.patient-actions {
  display: flex;
  align-items: center;
}

.patient-edit, .patient-delete {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
  padding: 8rpx;
}

.action-icon {
  width: 36rpx;
  height: 36rpx;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 底部新增按钮 */
.add-btn-wrapper {
    height: 136rpx;
    box-sizing: border-box;
    width: 100%;
    padding: 20rpx 32rpx;
    position: fixed;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    background-color: #fff;
    border-top: 1rpx solid #EAEAEA;
}

.add-patient-btn {
    height: 96rpx;
    line-height: 96rpx;
    text-align: center;
    background-color: #00B484;
    color: #fff;
    font-size: 32rpx;
    font-weight: 500;
    border-radius: 48rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}
.add-patient-btn-text {
    margin-right: 80rpx;
}
.add-icon-image {
    width: 36rpx;
    height: 36rpx;
    position: absolute;
    right: 250rpx;
}
</style>
