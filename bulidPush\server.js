import express from 'express';
import cors from 'cors';
import { exec } from 'child_process';
import optionsMap from "../config/env/optionsMap.js";
import {
  ParsingOptions,
  createInstructionSet,
  runTimeMain,
  override,
  commandCode
} from "./utils.js";

const app = express();
const PORT = 8888;

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 存储当前推送状态
let pushStatus = {
  isRunning: false,
  currentTask: null,
  progress: 0,
  total: 0,
  logs: []
};

// 添加日志
function addLog(message, type = 'info') {
  const log = {
    timestamp: new Date().toISOString(),
    message,
    type
  };
  pushStatus.logs.push(log);
  console.log(`[${type.toUpperCase()}] ${message}`);
  
  // 保持最新100条日志
  if (pushStatus.logs.length > 100) {
    pushStatus.logs = pushStatus.logs.slice(-100);
  }
}

// 获取小程序列表
app.get('/api/miniprogram/list', (req, res) => {
  try {
    const list = optionsMap.map(item => ({
      id: item.id,
      title: item.title,
      registerTitle: item.registerTitle,
      appId: item.appId,
      providerId: item.providerId,
      source: item.source,
      icon: item.icon,
      miniProgram: item.miniProgram
    }));
    console.log('触发获取小程序列表',list);
    
    res.json({
      code: 200,
      message: '获取成功',
      data: list
    });
  } catch (error) {
    res.status(500).json({
      code: 500,
      message: '获取小程序列表失败',
      error: error.message
    });
  }
});

// 执行推送
app.post('/api/miniprogram/publish', async (req, res) => {
  try {
    const { description, idList, version } = req.body;
    
    // 验证参数
    if (!description) {
      return res.status(400).json({
        code: 400,
        message: '描述信息不能为空'
      });
    }
    
    if (!version) {
      return res.status(400).json({
        code: 400,
        message: '版本号不能为空'
      });
    }
    
    if (!idList || !Array.isArray(idList) || idList.length === 0) {
      return res.status(400).json({
        code: 400,
        message: 'idList不能为空且必须是数组'
      });
    }
    
    // 检查是否有推送任务正在运行
    if (pushStatus.isRunning) {
      return res.status(409).json({
        code: 409,
        message: '有推送任务正在运行中，请稍后再试'
      });
    }
    
    // 验证idList中的id是否存在
    const validIds = optionsMap.map(item => item.id);
    const invalidIds = idList.filter(id => !validIds.includes(id));
    if (invalidIds.length > 0) {
      return res.status(400).json({
        code: 400,
        message: `无效的小程序ID: ${invalidIds.join(', ')}`
      });
    }
    
    // 同步执行推送任务并等待完成
    const result = await executePushTask({ description, idList, version });
    
    // 返回推送结果
    res.json({
      code: 200,
      message: result.success ? '推送任务完成' : '推送任务完成，但有部分失败',
      data: {
        taskId: Date.now(),
        description,
        idList,
        version,
        result: {
          success: result.success,
          total: result.total,
          completed: result.completed,
          failed: result.failed,
          logs: result.logs
        }
      }
    });
    
  } catch (error) {
    res.status(500).json({
      code: 500,
      message: '启动推送任务失败',
      error: error.message
    });
  }
});

// 异步执行推送任务
async function executePushTask(parameters) {
  let completed = 0;
  let failed = 0;
  const results = [];
  
  try {
    // 设置推送状态
    pushStatus.isRunning = true;
    pushStatus.currentTask = parameters;
    pushStatus.progress = 0;
    pushStatus.total = parameters.idList.length;
    pushStatus.logs = [];
    
    addLog(`开始推送任务: ${parameters.description}`);
    addLog(`版本号: ${parameters.version}`);
    addLog(`目标小程序: ${parameters.idList.join(', ')}`);
    
    // 生成编译指令集合
    const instructionSet = createInstructionSet(optionsMap, parameters);
    addLog(`生成了 ${instructionSet.length} 个编译指令`);
    
    // 执行推送
    for (let index = 0; index < instructionSet.length; index++) {
      const element = instructionSet[index];
      const targetApp = optionsMap.find(app => app.id === element.id);
      
      addLog(`开始处理: ${targetApp?.title || element.id} (${index + 1}/${instructionSet.length})`);
      
      try {
        // 覆写配置文件
        await override(element.id);
        addLog(`配置文件覆写完成: ${element.id}`);
        
        // 执行编译命令
        addLog(`开始编译: ${element.code}`);
        await commandCode(element.code);
        addLog(`编译完成: ${targetApp?.title || element.id}`);
        
        completed++;
        results.push({
          id: element.id,
          title: targetApp?.title || element.id,
          status: 'success',
          message: '推送成功'
        });
        
        pushStatus.progress = index + 1;
        
        // 如果不是最后一个，等待缓冲时间
        if (index < instructionSet.length - 1) {
          addLog('等待缓冲时间...');
          await delay(10000);
        }
        
      } catch (error) {
        failed++;
        const errorMsg = `处理失败: ${targetApp?.title || element.id} - ${error.message}`;
        addLog(errorMsg, 'error');
        results.push({
          id: element.id,
          title: targetApp?.title || element.id,
          status: 'failed',
          message: error.message
        });
      }
    }
    
    addLog('所有推送任务完成');
    
    return {
      success: failed === 0,
      total: instructionSet.length,
      completed,
      failed,
      results,
      logs: [...pushStatus.logs]
    };
    
  } catch (error) {
    const errorMsg = `推送任务执行失败: ${error.message}`;
    addLog(errorMsg, 'error');
    
    return {
      success: false,
      total: parameters.idList.length,
      completed,
      failed: parameters.idList.length - completed,
      results,
      logs: [...pushStatus.logs],
      error: error.message
    };
  } finally {
    // 重置推送状态
    pushStatus.isRunning = false;
    pushStatus.currentTask = null;
  }
}

// 延迟函数
const delay = (ms) => {
  return new Promise(resolve => {
    setTimeout(resolve, ms);
  });
};

// 获取推送状态
app.get('/api/miniprogram/status', (req, res) => {
  res.json({
    code: 200,
    message: '获取状态成功',
    data: pushStatus
  });
});

// 获取推送日志
app.get('/api/miniprogram/logs', (req, res) => {
  const { limit = 50 } = req.query;
  const logs = pushStatus.logs.slice(-parseInt(limit));
  
  res.json({
    code: 200,
    message: '获取日志成功',
    data: {
      logs,
      total: pushStatus.logs.length
    }
  });
});

// 清空日志
app.delete('/api/miniprogram/logs', (req, res) => {
  pushStatus.logs = [];
  res.json({
    code: 200,
    message: '日志已清空'
  });
});

// 停止推送任务（如果支持的话）
app.post('/api/miniprogram/stop', (req, res) => {
  if (!pushStatus.isRunning) {
    return res.status(400).json({
      code: 400,
      message: '当前没有运行中的推送任务'
    });
  }
  
  // 注意：这里只是标记停止，实际的停止逻辑需要在executePushTask中实现
  addLog('收到停止推送任务的请求', 'warning');
  
  res.json({
    code: 200,
    message: '停止请求已发送（注意：正在执行的编译任务可能无法立即停止）'
  });
});

// 健康检查
app.get('/api/health', (req, res) => {
  res.json({
    code: 200,
    message: '服务运行正常',
    data: {
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      version: process.version
    }
  });
});

// 错误处理中间件
app.use((error, req, res, next) => {
  console.error('服务器错误:', error);
  res.status(500).json({
    code: 500,
    message: '服务器内部错误',
    error: process.env.NODE_ENV === 'development' ? error.message : '内部错误'
  });
});

// 404处理
app.use((req, res) => {
  res.status(404).json({
    code: 404,
    message: '接口不存在'
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`\n🚀 小程序推送服务已启动`);
  console.log(`📡 服务地址: http://localhost:${PORT}`);
  console.log(`📋 API文档:`);
  console.log(`   GET  /api/miniprogram/list     - 获取小程序列表`);
  console.log(`   POST /api/miniprogram/publish  - 执行推送`);
  console.log(`   GET  /api/miniprogram/status   - 获取推送状态`);
  console.log(`   GET  /api/miniprogram/logs     - 获取推送日志`);
  console.log(`   DELETE /api/miniprogram/logs   - 清空日志`);
  console.log(`   POST /api/miniprogram/stop     - 停止推送任务`);
  console.log(`   GET  /api/health               - 健康检查`);
  console.log(`\n✨ 服务已就绪，等待推送请求...\n`);
});

export default app;