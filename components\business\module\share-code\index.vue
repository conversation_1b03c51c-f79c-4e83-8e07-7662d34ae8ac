<!--
*
*config  [Object] 参数设置
backgroundColor: #f7f7f7     背景色
titleColor: #333,            文本字体颜色
label: 提示,                 文本
buttonText: 登录,            按钮文本
buttonBgColor: #FFB924,     按钮背景色
required: false,            是否重点提醒
buttonTextColor: #fff,      按钮字体颜色
-------------------------------------------
*disabled: false            是否禁用
*returnFn    [function]     回调函数
*
*局部引入使用 <explain-button  :config="xxx" @returnFn="xxx"></explain-button>
 -->
<template>
    <!-- #ifdef MP-WEIXIN -->
    <view>
        <view class="explain-button" :style="{'background': defaultConfig.backgroundColor}">
            <view class="explain-button-r clear-float">
                <view @tap="returnFn()" class="e-btn f-r" :style="{'color': defaultConfig.buttonTextColor, 'background': defaultConfig.buttonBgColor}">
                  <view class="e-btn-l"></view>
                  <text class="e-btn-r">{{defaultConfig.buttonText}}</text>
                </view>
            </view>
        </view>
    </view>
    <!-- #endif -->
</template>

<script>
import { mapState } from 'vuex'
export default {
  data() {
    return {
      defaultConfig: {
        titleColor: '#333',
        backgroundColor: '#fff',
        buttonText: '分享',
        buttonBgColor: '#FFB924',
        buttonTextColor: '#fff',
        label: '',
        required: false,
        path: '',
        title: ''
      }
    }
  },
  computed: {
    ...mapState('user', {
      isLogin: state => state.isLogin
    })
  },
  watch: {
    config: {
      handler(val) {
        this.copyConfig()
      },
      deep: true
    }
  },
  props: {
    // 是否禁用disable
    disabled: {
      type: Boolean,
      default() {
        return false
      }
    },
    // 参数设置
    config: {
      type: Object,
      required: false,
      default: () => {
        return {}
      }
    },
    routerName: {
      type: String,
      default() {
        return 'Login'
      }
    }
  },
  mounted() {
    this.copyConfig()
  },
  methods: {
    navtoGo(url, obj = {}) {
      this.$navto.push(url, obj)
    },
    /**
             * 初始化拷贝config对象
             */
    copyConfig() {
      const that = this
      const obj = that.config
      Object.keys(obj).forEach(function(key) {
        that.defaultConfig[key] = obj[key]
      })
      that.defaultConfig = Object.assign({}, that.defaultConfig)
    },
    /**
             * picker触发选中事件
             * @param v
             */
    returnFn(v) {
      const that = this
      if (that.disabled) return
      let query = {}
      if (!that.$validate.isNull(that.defaultConfig.query)) query = that.defaultConfig.query
      that.navtoGo('CommnonShareCode', query)
      // this.$emit('returnFn')
    }
  }
}
</script>

<style lang="scss" scoped>
    .explain-button{
        .explain-button-r{
            display: inline-block;
            vertical-align: middle;
            width: 180upx;
            .e-btn{
              @include rounded(28upx);
              max-width: 140upx;
              padding: 0 20upx;
              @include ellipsis(1);
              .e-btn-l{
                @include iconImg(28,28,'/business/icon-but-share-QR-code.png');
                display: inline-block;
                vertical-align: middle;
                margin-right: 16upx;
              }
              .e-btn-r{
                display: inline-block;
                vertical-align: middle;
                height: 56upx;
                line-height: 56upx;
                font-size: 28upx;
                text-align: center;
              }
            }
        }
    }
</style>
