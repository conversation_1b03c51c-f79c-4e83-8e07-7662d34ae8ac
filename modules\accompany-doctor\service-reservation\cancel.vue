<template>
  <view class="guidanceBox">
    <!-- 头部标题 -->
    <view class="headerTap">
      <view class="cencelTitle">
        <image class="cencelTitleIcon" :src="cencelTitle" mode=""></image>
        已取消
      </view>
      <view class="refund-info" v-if="getServiceRefund || getInsuredRefund">
        <view class="getRefund" v-if="getServiceRefund">服务费已退款￥{{getServiceRefund}}</view>
        <view class="getRefund" v-if="getInsuredRefund">保险费已退款￥{{getInsuredRefund}}</view>
      </view>
    </view>
    <view class="insureSign" v-if="insuredInfo.pay">
        <view class="" v-if="insuredInfo.refundInsure === 1">服务费、门诊无忧服务费将原路退回</view>
        <view class="" v-else>门诊无忧服务已过退保时间，无法退款</view>
    </view>
    <!-- 内容区域 -->
    <view class="guidanceCard">
      <view class="headerTab">
        <view class="serverInfo">
          <image class="serverIcon" :src="file_ctx + accompanybookOne.serviceDetailImg" mode=""></image>
          <!-- 服务名称 -->
          <view class="serviceName">
            <view class="serverTitle">{{accompanybookOne.serviceName}}</view>
            <view class="">
              <text class="signal">¥</text>
              <text class="serverNum">{{accompanybookOne.payPrice / 100}}</text>
            </view>
          </view>
          <view class="tabBox">
            <view class="tabItem">
              <text class="tabTitle">就诊时间:</text>
                {{timestampToDateTime(accompanybookOne.startTime)}}~{{timestampToDateTime(accompanybookOne.endTime,true)}}
              </view>
            <view class="tabItem">
              <text class="tabTitle">就诊医院:</text>
              {{accompanybookOne.hospitalName}}
            </view>
            <view class="tabItem">
              <text class="tabTitle">取消原因:</text>
              {{accompanybookOne.cancelReason}}
            </view>
          </view>
        </view>
        <view class="orderInfo">
          <view class="orderTitle">订单信息</view>
          <view class="orderValue">
            <text class="orderIdTitle">订单号</text>
            <view class="">
              {{accompanybookOne.id}}
              <text class="copy" @click="handleCopyOrder">复制</text>
            </view>
          </view>
          <view class="orderValue">
            <text class="timeTitle">服务费</text>
            <text class="payPrice">￥{{accompanybookOne.payPrice / 100}}</text>
          </view>
          <view class="orderValue" v-if="insuredInfo.pay">
            <text class="timeTitle">门诊无忧服务费用</text>
            <text class="payPrice">￥{{insuredInfo.price / 100}}</text>
          </view>
          <view class="orderValue" v-if="accompanybookOne.pay">
            <text class="timeTitle">支付时间</text>
            {{timestampToDateTime(accompanybookOne.payTime)}}
          </view>
          <view class="orderValue" v-if="accompanybookOne.pay">
            <text class="timeTitle">支付方式</text>
            {{['','微信支付','支付宝支付'][accompanybookOne.payType] || ''}}
          </view>
          <view class="orderValue" v-if="accompanybookOne.pay">
            <text class="timeTitle">支付金额</text>
            <text class="payPrice" v-if="insuredInfo && insuredInfo.pay">￥{{(accompanybookOne.payPrice / 100) + (insuredInfo.price / 100)}}</text>
            <text class="payPrice" v-else>￥{{accompanybookOne.payPrice / 100}}</text>
          </view>
          <view class="orderValue">
            <text class="timeTitle">创建时间</text>
            {{timestampToDateTime(accompanybookOne.createTime)}}
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  export default{
    props:{
      accompanybookOne:{
        type:Object,
      },
      insuredInfo:{
        type:Object,
        default:{}
      }
    },
    data(){
      return {
        file_ctx:this.file_ctx,
        cencelTitle: this.$static_ctx + "image/business/hulu-v2/cencelTitle.png",
        currentServer:{},
        serverCurrent:{},
      }
    },
    async mounted() {

    },
    computed:{
      async getAccompanybookOne(){
        let {data:{records:[serverCurrent]}} = await this.$api.accompanyDoctor.getAccompanyservicePage({
          condition:{serviceId:this.accompanybookOne.serviceId},
        })
        this.serverCurrent = serverCurrent;
        return this.accompanybookOne
      },
      getRefund(){
        let refundAmount = (this.accompanybookOne.refundAmount || 0) / 100; // 退款金额（分转元）
        let insuredPay = 0;
        if(this.insuredInfo && this.insuredInfo?.pay){
          insuredPay = this.insuredInfo.price / 100;
          if(this.insuredInfo.refundInsure !== 1) {
            insuredPay = 0;
          }
        }
        let totalRefund = refundAmount + insuredPay;
        return totalRefund;
      },
      // 获取服务费退款金额
      getServiceRefund(){
        return (this.accompanybookOne.refundAmount || 0) / 100;
      },
      // 获取保险费退款金额
      getInsuredRefund(){
        if(this.insuredInfo && this.insuredInfo?.pay && this.insuredInfo.refundInsure === 1){
          return this.insuredInfo.price / 100;
        }
        return 0;
      }
    },
    methods:{
      handleCopyOrder(){
        uni.setClipboardData({
          data: this.accompanybookOne.id,
          success: function () {
            uni.showToast({title: '复制成功',icon: 'success'});
          }
        });
      },
      timestampToDateTime(timestamp,flag) {
        if(!timestamp) return ''
        // 创建一个新的Date对象，传入的时间戳是以毫秒为单位的
        var date = new Date(timestamp);
        // 获取年、月、日、时、分、秒
        var year = date.getFullYear();
        var month = ("0" + (date.getMonth() + 1)).slice(-2); // 月份是从0开始的，所以+1，并补零
        var day = ("0" + date.getDate()).slice(-2); // 获取天数，并补零
        var hour = ("0" + date.getHours()).slice(-2); // 获取小时，并补零
        var minute = ("0" + date.getMinutes()).slice(-2); // 获取分钟，并补零
        if(flag) return `${month}.${day} ${hour}:${minute}`
        // 返回格式化的字符串
        return `${year}.${month}.${day} ${hour}:${minute}`;
      }

    }
  }
</script>

<style lang="scss">
  .insureSign{
    width: 100%;
    height: 60rpx;
    background: #E0F4EF;
    border-radius: 8rpx 8rpx 8rpx 8rpx;
    border: 1rpx solid #AAD8CC;
    display: flex;
    justify-content: space-between;
    padding: 0 24rpx;
    align-items: center;
    font-weight: 500;
    font-size: 26rpx;
    color: #00926B;
    margin: 32rpx 0;
    box-sizing: border-box;
    .surrender{

    }
  }
  .guidanceBox{
    width: 100vw;
    padding: 0 32rpx;
    box-sizing: border-box;
  }
  .headerTap{
    display: flex;
    justify-content: space-between;
    align-items: center;
    .cencelTitle{
      font-weight: 600;
      font-size: 36rpx;
      color: #1D2029;
      display: flex;
      align-items: center;
      .cencelTitleIcon{
        width: 40rpx;
        height: 40rpx;
        margin-right: 12rpx;
      }
    }
    .getRefund{
      font-weight: 400;
      font-size: 32rpx;
      color: #1D2029;
    }
  }
  .tabBox{
    width: 100%;
  }
  .tabItem{
    font-weight: 400;
    font-size: 24rpx;
    color: #1D2029;
    margin-top: 12rpx;
  }
  .tabTitle{
    font-weight: 400;
    font-size: 24rpx;
    color: #4E5569;
    margin-right: 10rpx;
  }
  .guidanceCard{
    width: 686rpx;
    height: 426rpx;
    background: #FFFFFF;
    border-radius: 16rpx;
    margin-top: 32rpx;
    .headerTab{
      width: 686rpx;
      background: #FFFFFF;
      border-radius: 16rpx;
      padding: 24rpx;
      box-sizing: border-box;
      .serverInfo{
        display: flex;
        padding: 8rpx;
        flex-wrap: wrap;
        padding-bottom: 32rpx;
        border-bottom: 2rpx solid #EAEBF0;
      }
      .orderInfo{
        .orderTitle{
          margin: 32rpx 0;
          font-weight: 600;
          font-size: 28rpx;
          color: #2D2F38;
        }
        .timeTitle{
          font-weight: 400;
          font-size: 26rpx;
          color: #1D2029;
        }
        .payPrice{
          font-weight: 500;
          font-size: 26rpx;
          color: #FF5500;
        }
        .orderIdTitle{
          font-weight: 400;
          font-size: 26rpx;
          color: #1D2029;
        }
        .orderValue{
          margin-bottom: 16rpx;
          display: flex;
          justify-content: space-between;
          font-weight: 400;
          font-size: 26rpx;
          color: #6F7281;
          .copy{
            margin-left: 8rpx;
            font-weight: 400;
            font-size: 26rpx;
            color: #316EAB;
          }
        }
      }
      .serverIcon{
        flex-shrink: 0;
        width: 96rpx;
        height: 96rpx;
        background: #D8D8D8;
        border-radius: 12rpx;
        border: 1rpx solid #D9DBE0;
        margin-right: 20rpx;
      }
      .serviceName{
        font-weight: 500;
        font-size: 32rpx;
        color: #1D2029;
        .signal{
          font-weight: 400;
          font-size: 22rpx;
          color: #FF5500;
        }
        .serverNum{
          font-weight: 500;
          font-size: 36rpx;
          color: #FF5500;
        }

        .serverTitle{
          font-weight: 600;
          font-size: 32rpx;
          color: #1D2029;
        }
        .signal{
          font-weight: 400;
          font-size: 22rpx;
          color: #FF5500;
        }
        .serverNum{
          font-weight: 500;
          font-size: 36rpx;
          color: #FF5500;
        }
        .tag{
          font-weight: 400;
          font-size: 20rpx;
          color: #868C9C;
        }
      }
      .changeServer{
        width: 148rpx;
        height: 52rpx;
        background: #FFFFFF;
        border-radius: 36rpx;
        border: 1rpx solid #D9DBE0;
        margin-left: auto;
      }
    }
  }
  .guidanceTitle{
    font-weight: 500;
    font-size: 36rpx;
    color: #1D2029;
  }
  .guidanceTwoTitle{
    font-weight: 400;
    font-size: 26rpx;
    color: #868C9C;
  }

</style>
