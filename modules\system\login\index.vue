<template>
  <page>
    <view slot="content" class="main-body">
      <view class="main">
        <!-- #ifdef MP-WEIXIN || H5 -->
        <status-bar-height></status-bar-height>
        <view class="main-top">
          <view class="l" @tap="noLogin()">
            <image
              class="width-height-atuo"
              :src="$static_ctx + 'image/business/icon-close-gray.png'"
            />
          </view>
        </view>
        <!-- #endif -->

        <view class="main-logo" :class="{
          mgt521:iswx,
        }">
          <image :style="{'width': serverOptions.title == '绿康家医' ? '250rpx' : '314rpx','height': serverOptions.title == '绿康家医' ? '160rpx' : '128rpx'}" class="logo-icon" :src="static_ctx2 + 'image/business/hulu-v2/' + serverOptions.icon" />
        </view>
        <view class="m-main">

          <!--#ifndef MP-WEIXIN-->
            <view class="inp">
              <view class="inp-text">
                <view class="l">
                  <image
                    class="width-height-atuo"
                    :src="$static_ctx + 'image/system/logo/icon-phone.png'"
                  />
                </view>
                <text class="r">账号</text>
              </view>
              <view class="inp-input">
                <mobile
                  :config="mobileConfig"
                  :value="regForm.name"
                  @update="updatePwd"
                />
              </view>
            </view>
            <view class="inp">
              <view class="inp-text">
                <view class="l">
                  <image
                    class="width-height-atuo"
                    :src="$static_ctx + 'image/system/logo/icon-pwd.png'"
                  />
                </view>
                <text class="r">密码</text>
              </view>
              <view class="inp-input">
                <password
                  :pwd-config="pwdConfig"
                  :value="regForm.pwd"
                  @updatePwd="updatePwd"
                />
              </view>
            </view>
            <view class="text-btn">
              <text class="l" @tap="navTo('Password')">忘记密码</text>
              <text
                class="r"
                @tap="navTo('VerificationCode', { phone: regForm.name })"
              >验证码登录</text
              >
            </view>

            <view class="wechat-login">
              <button
                :disabled="regForm.name === '' || regForm.pwd === ''"
                type=""
                class="b-btn"
                :class="{
                  'b-btn-color': btnStatus,
                  'opacity-5': regForm.name === '' || regForm.pwd === '',
                }"
                @tap.stop="onSubmit"
              >
                <image
                  class="width-height-atuo"
                  :src="
                    $static_ctx + 'image/system/logo/icon-login-password-btn.png'
                  "
                />
                {{ isLogining ? "正在登录" : "立即登录" }}
              </button>
            </view>
          <!--#endif-->
          <!--#ifdef MP-WEIXIN-->
          <view class="wechat-login wechat-login-wrapper">
            <button
              v-if="fansBindRecord && fansBindRecord.phone"
              class="b-btn btn-bg2"
              type="default"
              @tap="loginByFansBindRecord"
              :style="{
                'background-image':'url('+ static_ctx2 +'image/business/hulu-v2/icon-login-btn-bg.png)'
              }"
            >
              一键登录
            </button>
            <button
              v-else
              class="b-btn btn-bg2"
              type="default"
              open-type="getPhoneNumber"
              @getphonenumber="getphonenumber"
              :style="{
                'background-image':'url('+ static_ctx2 +'image/business/hulu-v2/icon-login-btn-bg.png)'
              }"
            >
              一键登录
            </button>
          </view>
          <!--#endif-->
        </view>
        <view class="noLogin" @click="noLogin">暂不登录</view>
        <agree-with-protocol ref="checkboxRef" @returnFn = "returnFn"></agree-with-protocol>
      </view>
    </view>
  </page>
</template>

<script>
import AgreeWithProtocol from '@/modules/system/agreement-gather/components/agree-with-protocol'
import password from '@/modules/system/components/basics/form/password'
import mobile from '@/modules/system/components/basics/form/mobile'
import { mapState } from 'vuex'
import serverOptions from '@/config/env/options'
export default {
  components: {
    password,
    mobile,
    AgreeWithProtocol
  },
  data() {
    return {
      serverOptions,
      iswx:false,
      static_ctx2:this.$static_ctx,
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $static_ctx: this.$static_ctx,
      isLogining: false,
      mobileConfig: {
        name: 'name',
        placeholder: '请输入手机号码'
      },
      pwdConfig: {
        name: 'pwd',
        isShowPWD: true,
        placeholder: '请输入密码 (6-20位)'
      },
      regForm: {
        name: '',
        pwd: '',
        isCheckbox: false
      },
      btnStatus: true,
      redirect: '',
      redirectParams: {},
      formPage: '',
      formPageParams: {},
      userInfoData: {},
      getphonenumberData: {},
      fansBindRecord: {} // 粉丝绑定的中央用户档案，拿其中的手机号码，无需重复授权
    }
  },
  computed: {
    ...mapState('user', {
      isLogin: state => state.isLogin
    }),
    isConfirm() {
      let flag = true
      for (const k in this.regForm) {
        if (k && this.regForm.hasOwnProperty(k)) {
          if (!this.regForm[k]) {
            flag = false
            break
          }
        }
      }
      return flag
    }
  },
  watch: {
    /** 密码去首尾空监听 */
    'regForm.pwd'() {
      this.regForm.pwd = this.$validate.trim(this.regForm.pwd)
    }
  },
  methods: {
    noLogin(){
      // 使用navigateBack返回上一页而不是跳转到首页
      const pages = getCurrentPages();
      console.log('pages跳转',pages);

      if(pages.length > 2) {
        uni.navigateBack();
      } else {
        // 如果没有上一页，才跳转到首页
        uni.switchTab({url:'pages/accompany-home/index'})
      }
    },
    returnFn(isCheckbox){
      this.regForm.isCheckbox = isCheckbox
    },
    getUserInfo() {
      let params = {}
      const getphonenumberData = this.getphonenumberData
      const that = this
      uni.getUserProfile({
        desc: '用于完善用户资料',
        success: (res) => {
          console.log(res)
          params = Object.assign(params, res)
          params.rawData = encodeURI(params.rawData)
          delete (params.userInfo)
          delete (params.errMsg)
          delete (params.cloudID)
          that.userInfoData = params
          that.getPhone()
        },
        fail: (error) => { }
      })
    },
    async loginByFansBindRecord() {
      await this.$refs.checkboxRef.isCheckboxToastFn()
      const { phone } = this.fansBindRecord
      this.quickLogin(phone)
    },
    async getphonenumber(e) {
      console.log(e);
      await this.$refs.checkboxRef.isCheckboxToastFn()
      const that = this
      if (e.detail.errMsg === 'getPhoneNumber:ok') {
        const params = {
          code: e.detail.code,
          cacheNumber: that.$common.getTokenUuid(), // 随机编码
        }
        this.getPhone(params)
      } else {
        that.setLoginStatus(false)
        that.$uniPlugin.toast('快捷登录已被取消')
      }
    },
    getPhone(params) {
      const that = this
      that.setLoginStatus(true)
      that.$ext.wechat.getPhone(params).then(res => {
        console.log(res)
        that.quickLogin(res.data.phoneNumber)
      }).catch(err => {
        that.setLoginStatus(false)
        that.$uniPlugin.toast(err.msg || '快捷登录出错了，请重试')
      })
    },
    quickLogin(params) {
      const that = this
      if (params) {
        that.setLoginStatus(true)
        that.$ext.wechat.quickLogin({ phone: params,cacheNumber: that.$common.getTokenUuid()}).then(res => {
          that.setLoginStatus(false)
          that.$uniPlugin.toast('登录成功')
          that.$ext.user.getInfoGroupPage(that, () => {
            that.setLoginStatus(false)
            // #ifdef MP-WEIXIN
              that.$ext.user.bindWeixinAccount({})
              that.$ext.user.usertenantrecordBindFans()
            // #endif
            that.$ext.webSocket.webSocketInit()

            // 登录成功后返回到之前的页面
            if (that.formPage === 'serviceReservation' && that.$Route && that.$Route.query && that.$Route.query.id) {
              // 对于预约页面需要特殊处理，保证服务ID传递正确
              that.$navto.replaceAll(that.formPage, {id: that.$Route.query.id});
              return;
            }
            that.back()
          })
        }).catch(err => {
          that.setLoginStatus(false)
          that.$uniPlugin.toast(err.msg || '快捷登录出错了，请重试')
        })
      } else {
        that.setLoginStatus(false)
        that.$uniPlugin.toast('快捷登录出错了，请重试')
      }


    },
    /**
     * 子组件返回 密码更新
     */
    updatePwd(obj) {
      if (obj) {
        for (const i in obj) {
          this.regForm[i] = obj[i]
        }
      }
    },
    /**
     * 立即登录
     */
    async onSubmit() {
      await this.$refs.checkboxRef.isCheckboxToastFn()
      if (this.isLogining) return
      const that = this
      const tipArr = {
        name: '请输入账号',
        pwd: '请输入密码(6-20位)'
      }
      const params = {
        name: this.regForm.name, // 登陆账户名或手机号
        pwd: this.regForm.pwd // 密码
      }
      // 表单验证
      if (!this.$common.validationForm(tipArr, params)) {
        return
      }
      const obj = this.$validate.checkPWD(this.regForm.pwd)
      if (!obj.statu) return
      that.setLoginStatus(true) // 设置登录状态
      that.$uniPlugin.loading('正在登录中')
      that.$ext.user.login(params.name, params.pwd, this.$constant.noun.logType).then(result => {
        that.$uniPlugin.hideLoading()
        that.$uniPlugin.toast('登录成功')
        that.$ext.user.getInfoGroupPage(that, () => {
          that.setLoginStatus(false)
          // #ifdef MP-WEIXIN
            that.$ext.user.bindWeixinAccount({})
            that.$ext.user.usertenantrecordBindFans()
          // #endif
          that.$ext.webSocket.webSocketInit()

          // 登录成功后返回到之前的页面
          if (that.formPage === 'serviceReservation' && that.$Route && that.$Route.query && that.$Route.query.id) {
            // 对于预约页面需要特殊处理，保证服务ID传递正确
            that.$navto.replaceAll(that.formPage, {id: that.$Route.query.id});
            return;
          }
          that.back()
        })
      }).catch(e => {
        this.$uniPlugin.hideLoading()
        // this.$uniPlugin.toast(e.message)
        that.setLoginStatus(false)
      })
    },
    /**
     * 设置登录状态
     */
    setLoginStatus(flag) {
      this.isLogining = flag
    },
    navTo(name, paramObj) {
      this.$navto.push(name, paramObj)
    },
    back() {
      if(this.formPage == 'PharmacyCyclopedia'){
        this.$navto.push(this.formPage,JSON.parse(decodeURIComponent(decodeURIComponent(this.formPageParams))))
        return
      }

      // 如果是从服务详情页进来的，需要传递id参数
      if(this.formPage == 'ServiceDetail' && this.$Route && this.$Route.query && this.$Route.query.id) {
        this.$navto.replaceAll(this.formPage, {id: this.$Route.query.id, city: this.$Route.query.city})
        return
      }

      let path = this.formPage || 'AccompanyHome'
      if (path === 'Login') path = 'AccompanyHome' // 防ios刁民
      let queryParams = {}
      if (!this.$validate.isNull(this.formPageParams)) {
        queryParams = JSON.parse(decodeURIComponent(this.formPageParams))
      }
      console.log('返回页面',path,queryParams);

      this.$navto.replaceAll(path, queryParams)
    }
  },
  onReady() {
    console.log(this.$Route,'this.$Route')
    const redirect = this.$Route.query.redirect
    const redirectParams = this.$Route.query.redirectParams
    const formPage = this.$Route.query.formPage
    const formPageParams = this.$Route.query.formPageParams

    if (!this.$validate.isNull(redirect)) {
      this.redirect = redirect
    }
    if (!this.$validate.isNull(redirectParams)) {
      this.redirectParams = redirectParams
    }
    if (!this.$validate.isNull(formPage)) {
      this.formPage = formPage
    }
    if (!this.$validate.isNull(formPageParams)) {
      this.formPageParams = formPageParams
    }
  },
  async onLoad() {
    // #ifndef H5
    this.iswx = true
    // #endif

    const fansBindRecord = await this.$ext.user.getFansBindRecord()
    this.fansBindRecord = fansBindRecord
  },

}
</script>

<style lang="scss" scoped>

uni-input {
  height: 80upx !important;
  font-size: 32upx !important;
}
uni-button:after {
  border: none !important;
}
.opacity-5 {
  @include opacity(0.5);
}
.bg-65CB77 {
  background: linear-gradient(90deg, #91e09f, #39c752) !important;
}
.btn-bg2.b-btn {
  background: none;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  border-radius: 0;
}
.btn-bg2::after {
  border: none;
}
.main-body {
  height: 100%;
  .main {
    background: #fff;
    height: 100%;
    .main-top {
      height: 88upx;
      padding: 0 30upx;
      box-sizing: border-box;
      line-height: 88upx;
      .l {
        display: inline-block;
        vertical-align: middle;
        width: 48upx;
        height: 48upx;
      }
    }
    .main-logo {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 118upx;
      //padding: 30upx;
      // padding-top: 100upx;
      // height: 400rpx;
      // image {
      //   width: 340upx;
      //   height: 150upx;
      //   margin: 0 auto;
      //   display: block;
      // }
    }
    .main-logo.mgt521 {
      margin-top: 512upx;
    }
    .logo-icon {
      width: 250rpx;
      height: 160rpx;
      transform: scale(1.5);
      margin-top: -100rpx;
    }
    .m-main {
      width: 100%;
      .inp {
        width: 520upx;
        margin: 30upx auto 0 auto;
        .inp-text {
          height: 48upx;
          margin-bottom: 14upx;
          .l {
            width: 48upx;
            height: 48upx;
            margin-right: 16upx;
            display: inline-block;
            vertical-align: middle;
          }
          .r {
            color: #333;
            font-size: 32upx;
            height: 48upx;
            line-height: 48upx;
            display: inline-block;
            vertical-align: middle;
          }
        }
      }
      .text-btn {
        width: 520upx;
        margin: 20upx auto 0 auto;
        position: relative;
        height: 30upx;
        .l {
          position: absolute;
          left: 0;
          top: 0;
          color: $topicC;
          font-size: 28upx;
        }
        .r {
          position: absolute;
          right: 0;
          top: 0;
          color: $topicC;
          font-size: 28upx;
        }
      }
    }
    .noLogin{
      margin:20rpx auto;
      text-align: center;
    }
  }
}
.m-t-20 {
  margin-top: 20upx;
}
.margin-0-auto {
  margin: 0 auto;
}
.width-height-atuo {
  width: 100%;
  height: 100%;
}
.container {
  background-color: #fff;
  height: calc(100vh - 1upx);
}

.content {
  background: #fff;
  height: calc(100vh - 1upx);
}
.b-btn {
  width: 497upx;
  //background: linear-gradient(
  //  90deg,
  //  rgba(255, 185, 36, 1),
  //  rgba(254, 219, 55, 1)
  //);
  // background: $topicC;
  color: #fff;
  // @include rounded(44upx);
}

.b-btn-color {
  //background: linear-gradient(
  //  90deg,
  //  rgba(255, 185, 36, 1),
  //  rgba(254, 219, 55, 1)
  //);
  background: $topicC;
  @include opacity(0.5);
  color: #fff;
  @include rounded(44upx);
}
.label-input-pwd,
.label-input {
  width: 100%;
  height: 48upx;

  text {
    padding-left: 54upx;
    display: inline-block;
    background-size: 48upx;
  }
}
.label-input-pwd {
  text {
    background-size: 48upx;
  }
}
.wechat-login {
  margin-top: 48upx;
  vertical-align: middle;
  display: flex;
  align-items: center;
  text-align: center;
  padding: 0 20% 0 20%;
  button {
    image {
      width: 24px;
      height: 24px;
      margin-right: 8px;
      display: inline-block;
      vertical-align: middle;
    }
  }
}
.registered {
  width: 120upx;
  margin: 32upx auto 0;
  .view-but {
    font-size: 30upx;
    line-height: 54upx;
    color: #b1b0af;
    text-align: center;
  }
}
.wechat-login-wrapper {
  // margin-top: 250upx;
}
.wechat-login + .wechat-login-wrapper {
  margin-top: 48upx;
}
</style>
