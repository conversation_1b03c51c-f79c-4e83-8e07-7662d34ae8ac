<!-- 内容模块标题配件组件 <title-jump :title="xx" :text="xx" @returnFn = "returnFn"></title-jump> -->
<!--<title-file :disabled="false" :config="config.isNotTo5"-->
<!--           :cData="{}" @returnFn="imgReturnFn"></title-file>-->
<!--
        isNotTo5: {
          label: '1、为什么天空是蓝色，请回答！！',
          name: 'isNotTo',
          required: true,
          count: 6, multiSelectCount: 6, theCluesText: '最多只能上传6张图片'
        },
-->
<template>
  <view class="upload-image">
    <view class="upload-image-main" :style="{padding: defaultConfig.padding, background: defaultConfig.background}">
      <view class="upload-image-main-t clear-float">
        <text class="icon-image" @tap="uploadImage" v-if="!disabled"></text>
        <view class="icon-list" :style="{margin: defaultConfig.margin}" v-for="(image,index) in imageList" :key="index" @tap="previewImage(index)">
          <view class="image" :style="{backgroundImage:'url(' + image.url + (image.url.indexOf('?') === -1 ? $constant.system.thumbnail.smaller : '') + ')'}">
            <view v-if="!disabled" class="icon-close-black-circle" @click.stop="del(index)"></view>
          </view>
        </view>
      </view>
      <view class="upload-image-main-b" v-if="defaultConfig.theCluesText">
        {{defaultConfig.theCluesText}}
      </view>
    </view>

    <!-- 素材管理图片弹窗 -->
    <view :class="{'is-photo-album-state': isPhotoAlbumState}" class="photo-album-list-main">
      <view class="obscuration" @tap="hideDrawerRigth"></view>
      <view class="photo-album-list-main-drawer">
        <view class="drawer-top">
          <view class="main">
            <em @tap="hideDrawerRigth"></em>
            <text>请选择素材管理图片</text>
          </view>
        </view>
        <view class="drawer-content">
          <scroll-refresh class="scroll-refresh-main" :fixed="false" :up="up" :down="down" @scrollInit="scrollInit" @returnFn="scrollReturnFn">
            <view class="ul">
              <view class="list" v-for="(item,index) in photoAlbumList" :key="index">
                <view class="name">{{item.name}}</view>
                <view class="desc">{{item.desc}}</view>
                <view class="img" v-if="item.attachmentListLength > 0">
                  <view class="img-ul clear-float" v-for="(aItem, aIndex) in item.coverPicArr" :key="aIndex">
                    <view class="img-li f-l" v-for="(liItem, liIndex) in aItem" :key="liIndex" @tap.stop="photoAlbumPreviewImage(index, aIndex, liIndex)">
                      <image mode="aspectFill" :src="liItem.url + $constant.system.thumbnail.small"/>
                      <view class="state-main" @tap.stop="photoAlbumChooseImage(index, aIndex, liIndex)">
                        <em class="state-main-icon" :class="liItem.isState ? 'icon-yijianfankui-d-ok' : 'icon-xuanze'"></em>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </scroll-refresh>
        </view>
        <view class="drawer-bottom">
          <view class="btn-bg" @tap.stop="photoAlbumSubmitFn">确认</view>
        </view>
      </view>
    </view>

    <!-- 上传图片方式弹窗 -->
    <uni-popup ref="imgChoosePopup" type="bottom">
      <view class="down-popup">
        <view class="body radius-on-the-angle">
          <view>
            <view class="text" @tap="chooseImage()">
              本地图片
            </view>
            <!--<view class="text" @tap="materialImage()">-->
            <!--  素材管理图片-->
            <!--</view>-->
          </view>
        </view>
        <view class="bottom" @tap="imgChoosePopupFn">
          取消
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import common from '@/common/util/main'
import uniPopup from '@/components/uni/uni-popup'
export default {
  components: {
    uniPopup
  },
  props: {
    child:{
      type:Boolean,
      default:false,
    },
    // 是否禁用disable
    disabled: {
      type: Boolean,
      default() {
        return false
      }
    },
    // 数组下标
    dIndex: {
      type: [String, Number],
      default() {
        return ''
      }
    },
    // 初始值
    cData: {
      type: Array,
      default() {
        return []
      }
    },
    // 参数设置
    config: {
      type: Object,
      required: false,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $static_ctx: this.$static_ctx,
      $common: this.$common,
      $accurateConversion: this.$accurateConversion,
      file_ctx: this.file_ctx,
      $timePlugin: this.$timePlugin,
      imageList: [], // 图片数组
      count: '', // 当前图片还剩余的最大数
      photoAlbumList: [], // 素材管理相册数组
      isPhotoAlbumState: false, // 是否素材管理相册数组
      down: {
        auto: false
      },
      up: {
        auto: false
      },
      mescroll: {},
      defaultConfig: {
        theCluesText: '',
        padding: '15px',
        margin: '0 10px 10px 0',
        background: '#fff',
        isLocalState: true, // 是否开始本地图片上传
        isMaterialState: true, // 是否开始素材管理图片上传
        sourceType: ['camera', 'album'], // album 从相册选图，camera 使用相机
        multiSelectCount: 9, // 最大上传数
        count: 9, // 图片选择最大数
        url: this.$constant.noun.uploadInterface, // 上传接口路径
        name: 'file', // 文件对应的 key , 开发者在服务器端通过这个 key 可以获取到文件二进制内容
        header: { // HTTP 请求 Header, header 中不能设置 Referer。

        },
        formData: { // HTTP 请求中其他额外的 form data
        groupId: 27000,
        relId: ''
        }
      }
    }
  },
  watch: {
    cData: {
      handler(val) {
        this.watchDataMain(val)
      },
      deep: true
    },
    config: {
      handler(val) {
        this.copyConfig()
      },
      deep: true
    }
  },
  mounted() {
    this.copyConfig()
    console.log('this.cData',this.cData)
    this.watchDataMain(this.cData)
  },
  methods: {
    /**
     * 初始化拷贝config对象
     */
    copyConfig() {
      const that = this
      const obj = that.config
      Object.keys(obj).forEach(function(key) {
        that.defaultConfig[key] = obj[key]
      })
    },
    watchDataMain(val) {
      this.imageList = val
    },
    // 上传图片按钮
    uploadImage() {
      const that = this
      if (!that.config.header) {
        that.defaultConfig.header = common.getUploadFileHeader()
      }
      that.count = that.defaultConfig.count
      if (that.defaultConfig.multiSelectCount === that.imageList.length) return that.$uniPlugin.toast('最多只能上传' + that.defaultConfig.multiSelectCount + '张图片')
      if ((that.defaultConfig.multiSelectCount - that.imageList.length) < that.defaultConfig.count) {
        that.count = that.defaultConfig.multiSelectCount - that.imageList.length
      }
      if (that.defaultConfig.isLocalState && that.defaultConfig.isMaterialState) { // 本地图片与素材管理图片都开启
        uni.showActionSheet({
          itemList: ['本地图片'],
          success (res) {
            console.log(res.tapIndex)
            if(res.tapIndex == 0){
              that.chooseImage()
            }
          },
          fail (res) {
            console.log(res.errMsg)
          }
        })
        // that.$refs.imgChoosePopup.open()
      } else if (that.defaultConfig.isLocalState && !that.defaultConfig.isMaterialState) { // 本地图片开启，素材管理图片不开启
        that.chooseImage()
      } else if (!that.defaultConfig.isLocalState && that.defaultConfig.isMaterialState) { // 本地图片不开启，素材管理图片开启
        that.materialImage()
      }
    },
    // 取消选择图片方法弹窗
    imgChoosePopupFn() {
      this.$refs.imgChoosePopup.close()
    },
    // 选择素材管理图片方法
    materialImage() {
      this.photoAlbumList = []
      this.isPhotoAlbumState = true
      this.$nextTick(() => {
        this.mescroll.triggerDownScroll()
      })
    },
    // 关闭素材管理相册模块
    hideDrawerRigth() {
      this.photoAlbumList = []
      this.isPhotoAlbumState = false
    },
    // 素材管理相册下拉组件初始化函数
    scrollInit(scroll) {
      scroll.optUp.page.num = 1
      scroll.optUp.page.size = 5
      this.mescroll = scroll
    },
    // 素材管理相册下拉组件数据函数
    scrollReturnFn(obj) {
      const that = this
      function queryPage(pageNum, pageSize, fn) {
        const param = {
          number: pageNum,
          size: pageSize,
          direction: 'asc',
          sort: 't.order_by'
        }
        that.$api.materialManagement.materialcategoryQueryPage(param).then(res => {
          if (res && res.data.records) {
            fn(res.data.records)
          }
        })
      }
      setTimeout(function() {
        queryPage(obj.pageNum, obj.pageSize, (data) => {
          if (obj.pageNum === 1) {
            that.photoAlbumList = []
          }
          for (const i in data) {
            data[i].coverPicArr = []
            data[i].attachmentListLength = 0
            if (data[i].attachmentList.length > 0) {
              data[i].attachmentListLength = data[i].attachmentList.length
              const arrOne = data[i].attachmentList
              const arr = []
              for (let j = 0; j < arrOne.length; j++) {
                arr.push({
                  extName: arrOne[j].extName,
                  fileName: arrOne[j].fileName,
                  filePath: arrOne[j].filePath,
                  url: that.file_ctx + arrOne[j].filePath,
                  id: arrOne[j].id,
                  isState: false
                })
              }
              for (let k = 0; k < arr.length; k += 4) {
                data[i].coverPicArr.push(arr.slice(k, k + 4))
              }
            }
            data[i].coverPicArrLength = data[i].coverPicArr.length
          }
          that.photoAlbumList = that.photoAlbumList.concat(data)
          obj.successCallback && obj.successCallback(data || [])
        })
      }, that.$constant.noun.scrollRefreshTime)
    },
    // 素材管理图片放大方法
    photoAlbumPreviewImage(index, aIndex, liIndex) {
      const imageList = []
      for (const i in this.photoAlbumList[index].coverPicArr) {
        for (const j in this.photoAlbumList[index].coverPicArr[i]) {
          imageList.push(this.photoAlbumList[index].coverPicArr[i][j].url)
        }
      }
      uni.previewImage({
        current: aIndex * 4 + liIndex,
        urls: imageList
      })
    },
    // 选择素材管理图片方法
    photoAlbumChooseImage(index, aIndex, liIndex) {
      const that = this
      let num = 0
      for (const k in that.photoAlbumList) {
        for (const i in that.photoAlbumList[k].coverPicArr) {
          for (const j in that.photoAlbumList[k].coverPicArr[i]) {
            if (that.photoAlbumList[k].coverPicArr[i][j].isState) {
              num++
            }
          }
        }
      }
      if (that.defaultConfig.multiSelectCount < that.imageList.length + num + 1) {
        that.$uniPlugin.toast('最多只能上传' + that.defaultConfig.multiSelectCount + '张图片')
      } else {
        that.photoAlbumList[index].coverPicArr[aIndex][liIndex].isState = !that.photoAlbumList[index].coverPicArr[aIndex][liIndex].isState
        that.$set(that.photoAlbumList, index, that.photoAlbumList[index])
      }
    },
    // 确认选择素材管理图片方法
    photoAlbumSubmitFn() {
      const that = this
      for (const k in that.photoAlbumList) {
        for (const i in that.photoAlbumList[k].coverPicArr) {
          for (const j in that.photoAlbumList[k].coverPicArr[i]) {
            if (that.photoAlbumList[k].coverPicArr[i][j].isState) {
              that.imageList.push({
                url: that.photoAlbumList[k].coverPicArr[i][j].url,
                extName: that.photoAlbumList[k].coverPicArr[i][j].extName,
                filePath: that.photoAlbumList[k].coverPicArr[i][j].filePath,
                fileName: that.photoAlbumList[k].coverPicArr[i][j].fileName,
                dir: that.photoAlbumList[k].coverPicArr[i][j].filePath,
                suffix: that.photoAlbumList[k].coverPicArr[i][j].extName,
                name: that.photoAlbumList[k].coverPicArr[i][j].fileName
              })
            }
          }
        }
      }
      that.returnFn(that.imageList)
    },
    // 选择本地图片方法
    chooseImage() {
      const that = this
      uni.chooseImage({
        sizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有
        sourceType: that.defaultConfig.sourceType, // 从相册选择
        count: that.count,
        success: (res) => {
          if (res) {
            if (res.tempFilePaths.length > 0) {
              if (that.defaultConfig.multiSelectCount < that.imageList.length + res.tempFilePaths.length) return that.$uniPlugin.toast('最多只能上传' + that.defaultConfig.multiSelectCount + '张图片')
              if (that.imageList.length < that.defaultConfig.multiSelectCount) {
                that.asyncUploadFile(res.tempFilePaths)
              } else {
                that.$uniPlugin.toast('已超出最大上传数')
              }
            } else {
              that.$uniPlugin.toast('上传图片失败，请继续上传')
            }
          } else {
            that.$uniPlugin.toast('上传图片失败，请继续上传')
          }
        },
        fail: (err) => {
          that.$uniPlugin.toast('上传图片失败，请继续上传')
        }
      })
    },
    // 本地图片数据组装逻辑
    async asyncUploadFile(list) {
      const that = this
      const arr = JSON.parse(JSON.stringify(list))
      for (let i = 0; i < arr.length; i++) {
        await that.awaitUploadFile(arr[i]).then((uploadFileRes) => {
          const returnData = JSON.parse(uploadFileRes.data).data
          if (!that.$validate.isNull(JSON.stringify(returnData))) {
            that.imageList = that.imageList.concat(returnData)
            for (const i in that.imageList) {
              that.imageList[i].url = that.file_ctx + that.imageList[i].dir
              that.imageList[i].extName = that.imageList[i].suffix
              that.imageList[i].filePath = that.imageList[i].dir
              that.imageList[i].fileName = that.imageList[i].name
            }
            that.$uniPlugin.hideLoading()
          } else {
            that.$uniPlugin.hideLoading()
          }
          that.returnFn(that.imageList)
        }).catch(() => {
          that.$uniPlugin.toast('上传图片失败，请继续上传')
        })
      }
    },
    // 本地图片上传方法
    awaitUploadFile(url) {
      const that = this
      return new Promise(function(resolve, reject) {
        that.$uniPlugin.loading('正在上传', true)
        const uploadTask = uni.uploadFile({
          url: that.defaultConfig.url,
          filePath: url,
          name: that.defaultConfig.name,
          header: that.defaultConfig.header,
          formData: that.defaultConfig.formData,
          success(uploadFileRes) {
            resolve(uploadFileRes)
          },
          fail() {
            that.$uniPlugin.hideLoading()
            reject()
          }
        })
        uploadTask.onProgressUpdate((res) => {
          // console.log('上传进度' + res.progress);
          // console.log('已经上传的数据长度' + res.totalBytesSent);
          // console.log('预期需要上传的数据总长度' + res.totalBytesExpectedToSend);
          // console.log(res)
        })
      })
    },
    // 点击图片放大方法
    previewImage(index) {
      const that = this
      const imageList = []
      for (const i in this.imageList) {
        imageList.push(that.imageList[i].url)
      }
      uni.previewImage({
        current: index,
        urls: imageList
      })
    },
    // 删除图片方法
    del(index) {
      this.imageList.splice(index, 1)
      this.returnFn(this.imageList)
    },
    // 组件返回事件
    returnFn(arr) {
      const that = this
      if (that.defaultConfig.isMaterialState) {
        that.imgChoosePopupFn()
        that.hideDrawerRigth()
      }
      if (!that.$validate.isNull(that.dIndex)) {
        const obj = {
          value: arr,
          index: that.dIndex
        }
        that.$emit('returnFn', obj)
      } else {
        that.$emit('returnFn', arr)
      }
      // that.$emit('returnFn', arr)
    }
  }
}
</script>

<style lang="scss" scoped>
.upload-image{
  .upload-image-main{
    width: 100%;
    padding: 30upx;
    box-sizing: border-box;
    overflow: hidden;
    .upload-image-main-t{
      .icon-image{
        width: 116upx;
        height: 116upx;
        float: left;
        margin-right: 20upx;
      }
      .icon-list{
        width: 116upx;
        height: 116upx;
        float: left;
        .image{
          width: 116upx;
          height: 116upx;
          background-size: cover;
          background-position: center center;
          display: inline-block;
          position: relative;
          .icon-close-black-circle{
            position: absolute;
            top: 0upx;
            right: 0upx;
            @include iconImg(40,40,'/business/icon-close-black-circle.png');
            margin: 0;
            z-index: 2;
          }
        }
      }
    }
    .upload-image-main-b{
      font-size: 24upx;
      line-height: 36upx;
      background: #f7f7f7;
      margin-top: 16upx;
      padding: 16upx;
      color: #FF4A4A;
    }
  }
}
/* 素材管理弹窗 */
.is-photo-album-state{
  visibility: visible !important;
  display: block !important;
}
.photo-album-list-main{
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  visibility: hidden;
  z-index: 999;
  height: 100%;
  .obscuration{
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
    z-index: 1000;
    height: 100%;
    background: rgba(0, 0, 0, .4);
  }
  .photo-album-list-main-drawer{
    position: absolute;
    top: 15%;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    z-index: 1001;
    .drawer-top{
      width: 100%;
      position: absolute;
      top: 0;
      left: 0;
      background: #F0F4F7;
      .main{
        width: 100%;
        height: 88upx;
        line-height: 88upx;
        text-align: center;
        position: relative;
        em{
          position: absolute;
          top: 30upx;
          left: 30upx;
          @include iconImg(28, 28, '/system/icon-nav-close.png');
        }
        text{
          font-size: 36upx;
          color: #333333;
          display: inline-block;
        }
      }
    }

    .drawer-content{
      position: absolute;
      left: 0;
      right: 0;
      bottom: 148upx;
      top: 88upx;
      background: #f7f7f7;
      .scroll-refresh-main{
        height: 100%;
      }
      .ul{
        .list{
          background: #fff;
          padding: 30upx;
          margin-bottom: 20upx;
          box-sizing: border-box;
          .name{
            font-size: 32upx;
            line-height: 48upx;
            margin-bottom: 16upx;
          }
          .desc{
            font-size: 28upx;
            line-height: 42upx;
            color: #666;
            margin-bottom: 16upx;
          }

          .img{
            margin-bottom: 20upx;
            .img-ul{
              margin-bottom: 20upx;
              .img-li{
                margin-right: 20upx;
                width: 150upx;
                height: 150upx;
                overflow: hidden;
                @include rounded(8upx);
                position: relative;
                .state-main{
                  position: absolute;
                  top: 8upx;
                  right: 8upx;
                  .state-main-icon{
                    height: 36upx;
                    width: 36upx;
                    @include rounded(50%);
                  }
                }
                image{
                  width: 100%;
                  height: 100%;
                }
                .img-li-num{
                  position: absolute;
                  left: 0;
                  right: 0;
                  top: 0;
                  bottom: 0;
                  background: rgba(0, 0, 0, 0.5);
                  text{
                    text-align: center;
                    width: 100%;
                    color: #fff;
                    font-size: 72upx;
                    position: absolute;
                    left: 0;
                    top: 50%;
                    -webkit-transform: translateY(-50%);
                    transform: translateY(-50%);
                    font-weight: 600;
                  }
                }
              }
              .img-li:last-child{
                margin-right: 0;
              }
            }
            .img-ul:last-child{
              margin-bottom: 0;
            }
          }
          &:last-of-type{
            margin-bottom: 0;
          }
        }
      }
    }
    .drawer-bottom{
      width: 100%;
      position: absolute;
      bottom: 0;
      left: 0;
      background: #fff;
      height: 148upx;
      box-sizing: border-box;
      overflow: hidden;
      padding-top: 20upx;
      @include downBoxShadow(-4upx, 0, 6upx, 1, 222, 222, 222);
    }
  }
}
/* 上传图片方式 */
.down-popup{
  .title{
    position: relative;
    padding: 0 30upx;
    background-color: #E5E5E5;
    .icon-nav-close{
      display: inline-block;
      vertical-align: middle;
      @include iconImg(26,26,'/system/icon-nav-close.png');
      margin-right: 14upx;
    }
    text{
      width: calc(100% - 180upx);
      display: inline-block;
      vertical-align: middle;
      height: 88upx;
      line-height: 88upx;
      text-align: center;
      font-size: 36upx;
    }
    .title-confirm{
      display: inline-block;
      vertical-align: middle;
      font-size: 28upx;
      line-height: 42upx;
      color: #fff;
      width: 140upx;
      background-color: $topicC;
      @include rounded(22upx);
      text-align: center;
    }
  }
  .body{
    background-color: #fff;
    margin-bottom: 10upx;
    .text{
      font-size: 36upx;
      line-height: 88upx;
      height: 88upx;
      text-align: center;
      border-bottom: 2upx solid $contentDdt;
    }
    .text:last-child{
      border-bottom: none;
    }
  }
  .bottom{
    background-color: #fff;
    height: 88upx;
    line-height: 88upx;
    text-align: center;
    font-size: 36upx;
    padding: 0 30upx;
  }
}
</style>
