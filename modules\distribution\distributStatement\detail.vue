<template>
  <view class="transaction-detail">
    <!-- 顶部导航栏 -->
    <uni-nav-bar
      color="#1d2029"
      :border="false"
      :inFontWeight="true"
      :showBtnsRight="false"
      :fixed="false"
      title="分账详情"
      statusBar
      @clickLeft="back"
      left-icon="left"
      left-width="48rpx"
      right-width="100px"
      backgroundColor="rgba(0,0,0,0)"
    />
    
    <!-- 分账详情卡片 -->
    <view class="detail-card">
      <view class="card-title" :class="{
        'title-success': detailData.commission === 1,
        'title-pending': detailData.commission === 0 || !detailData.commission
      }">{{ detailData.commission === 1 ? '已分账' : '待分账' }}</view>
      
      <view class="detail-list">
        <view class="detail-item">
          <text class="item-label">订单号：</text>
          <text class="item-value">{{ detailData.accompanyId || '暂无数据' }}</text>
        </view>
        
        <view class="detail-separator"></view>
        
        <!-- <view class="detail-item">
          <text class="item-label">分销员姓名：</text>
          <text class="item-value">{{ detailData.distributorName || '暂无数据' }}</text>
        </view> -->
        
        <view class="detail-separator"></view>
        
        <!-- <view class="detail-item">
          <text class="item-label">分销员电话：</text>
          <text class="item-value">{{ detailData.distributorPhone || '暂无数据' }}</text>
        </view> -->
        
        <view class="detail-separator"></view>
        
        <view class="detail-item">
          <text class="item-label">分账金额：</text>
          <text class="item-value">{{ formatAmount(detailData.separateAmount) }}</text>
        </view>
        
        <view class="detail-separator"></view>
        
        <view class="detail-item">
          <text class="item-label">分账比例：</text>
          <text class="item-value">{{ formatRate(detailData.separateRate) }}</text>
        </view>
        
        <view class="detail-separator"></view>
        
        <view class="detail-item">
          <text class="item-label">分账状态：</text>
          <text class="item-value">{{ detailData.commission === 1 ? '已分账' : '待分账' }}</text>
        </view>
        
        <view class="detail-separator"></view>
        
        <!-- <view class="detail-item">
          <text class="item-label">服务商名称：</text>
          <text class="item-value">{{ detailData.providerName || '暂无数据' }}</text>
        </view> -->
        
        <view class="detail-separator"></view>
        
        <view class="detail-item">
          <text class="item-label">支付时间：</text>
          <text class="item-value">{{ formatDateTime(detailData.payTime) }}</text>
        </view>
        
        <view class="detail-separator"></view>
        
        <view class="detail-item" v-if="detailData.separateTime">
          <text class="item-label">分账时间：</text>
          <text class="item-value">{{ formatDateTime(detailData.separateTime) }}</text>
        </view>
        
        <view class="detail-separator" v-if="detailData.separateTime"></view>
        
        <!-- <view class="detail-item">
          <text class="item-label">场景类型：</text>
          <text class="item-value">{{ getSceneTypeText(detailData.sceneType) }}</text>
        </view> -->
      </view>
    </view>
  </view>
</template>

<script>
import uniNavBar from '@/components/uni/uni-nav-bar/uni-nav-bar.vue'
import serverOptions from '@/config/env/options'

export default {
  components: {
    uniNavBar
  },
  data() {
    return {
      id: '',
      detailData: {},
      isLoading: true
    }
  },
  onLoad(options) {
    if (options.id) {
      this.id = options.id;
      this.getTransactionDetail();
    } else {
      uni.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    }
  },
  methods: {
    // 获取交易详情
    async getTransactionDetail() {
      try {
        uni.showLoading({
          title: '加载中'
        });
        
        // 使用分销流水查询接口获取详情
        const res = await this.$api.distribution.accompanydistriburecordQueryPage({
          current: 1,
          size: 1,
          condition: {
            id: this.id,
            // providerId: serverOptions.providerId
          }
        });
        
        if (res.code === 0 && res.data && res.data.records && res.data.records.length > 0) {
          // 获取原始数据
          this.detailData = res.data.records[0];
          
          // 确保separateStatus字段存在
          if (this.detailData.separateStatus === undefined && this.detailData.commission !== undefined) {
            this.detailData.separateStatus = this.detailData.commission;
          }
          
          // 确保businessId字段存在
          if (!this.detailData.businessId && this.detailData.accompanyId) {
            this.detailData.businessId = this.detailData.accompanyId;
          }
        } else {
          uni.showToast({
            title: '未找到交易记录',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('获取交易详情失败:', error);
        uni.showToast({
          title: '获取交易详情失败',
          icon: 'none'
        });
      } finally {
        this.isLoading = false;
        uni.hideLoading();
      }
    },
    
    // 返回上一页
    back() {
      uni.navigateBack();
    },
    
    // 格式化金额
    formatAmount(amount) {
      if (!amount && amount !== 0) return '¥0.00';
      return '¥' + (amount / 100).toFixed(2);
    },
    
    // 格式化分账比例
    formatRate(rate) {
      if (!rate) return '0.00%';
      
      // 尝试将rate转为数字并乘以100
      try {
        const rateValue = parseFloat(rate);
        return (rateValue * 100).toFixed(2) + '%';
      } catch (e) {
        return rate + '%';
      }
    },
    
    // 格式化日期时间
    formatDateTime(timestamp) {
      if (!timestamp) return '-';
      
      // 检查是否为时间戳格式
      if (typeof timestamp === 'number') {
        const date = new Date(timestamp);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      }
      
      // 处理字符串格式 yyyyMMddHHmmss
      if (typeof timestamp === 'string' && timestamp.length === 14) {
        const year = timestamp.substring(0, 4);
        const month = timestamp.substring(4, 6);
        const day = timestamp.substring(6, 8);
        const hours = timestamp.substring(8, 10);
        const minutes = timestamp.substring(10, 12);
        const seconds = timestamp.substring(12, 14);
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      }
      
      return timestamp;
    },
    
    // 获取场景类型文本
    getSceneTypeText(type) {
      const sceneTypeMap = {
        '1': '陪诊订单',
        '2': '套餐订单',
        '3': '其他订单'
      };
      return sceneTypeMap[type] || '未知类型';
    }
  }
}
</script>

<style lang="scss" scoped>
.transaction-detail {
  min-height: 100vh;
  padding-bottom: 40rpx;
  background-color: #f5f7fa;
}

.detail-card {
  margin: 30rpx;
  padding: 30rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 36rpx;
  font-weight: 500;
  text-align: center;
  margin-bottom: 40rpx;
}

.title-success {
  color: #00B484;
}

.title-pending {
  color: #FF9500;
}

.detail-list {
  padding: 0;
}

.detail-item {
  display: flex;
  padding: 24rpx 20rpx;
  font-size: 28rpx;
  line-height: 40rpx;
}

.detail-separator {
  height: 1rpx;
  background-color: #f0f0f0;
  margin: 0 20rpx;
}

.item-label {
  color: #666666;
  width: 180rpx;
  flex-shrink: 0;
}

.item-value {
  color: #333333;
  flex: 1;
}
</style> 