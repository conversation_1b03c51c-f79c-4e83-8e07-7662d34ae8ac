<template>
  <scroll-view :scroll-x="true" scroll-with-animation class="stepbox" >
    <!-- //方法一： -->
    <!--    <view class="con" wx:for="{{list}}" wx:key="list" wx:for-item="itemP" wx:for-index="indexP">
      <view class="head">
        <block wx:for="{{itemP.stepInfo}}" wx:key='list' wx:for-item="item" wx:for-index="index">
          <view class="head_info">
            <image class="head_icon" src="{{item.status==1?itemP.sel_icon:'../img/circle03.png'}}"></image>
            <view class="head_title">{{item.pro_name}}</view>
            <view wx:if="{{index!=0}}" class='head_line' style="width:{{600/(itemP.stepInfo.length-1)}}rpx;background:{{item.status==1?itemP.pro_color:''}};"></view>
          </view>
        </block>
      </view>
    </view> -->

    <view class="getwidth"></view>
    <!-- //方法二： -->
    <view class="head" :style="{ 'margin-left': list.length <= 3 ? pL + 'px' : 0 }">
      <progress
        :percent="percent"
        :style="{
          width: progresswidth
        }"
        stroke-width="3"
      />
      <template v-for="(item, index) in list">
        <view class="stepitem" @click="toggle(index)" :class="[step > index ? 'active' : '', currentindex == index ? 'current' : '']">
          <view class="head_info">
            <div class="head_icon">{{ item.step || index + 1 }}</div>
            <!-- <image class="head_icon" src="{{item.status==1?itemP.sel_icon:'../img/circle03.png'}}"></image> -->
            <view class="head_title">{{ item[namekey] }}</view>
          </view>
        </view>
      </template>
    </view>
  </scroll-view>
</template>

<script>
export default {
  name: 'titleSteps',
  props: {
    step: {
      type: Number,
      default: 0
    },
    list:{
      type:Array,
      default:function (){
        return []
      }
    },
    updatecount:{
      type:Number,
      default:0,
    },
    namekey:{
      type:String,
      default:"title",
    },
    update:{
      type:Number,
      default:0,
    }
  },

  data() {
    return {
      pL: 0,
      progresswidth: 0,
      percent: '50',
      // step:5,
      currentindex: 1,
      totallength: 0,
      // list: [
      //   {
      //     status: 1,
      //     pro_name: '提交申请'
      //   },
      //   {
      //     status: 1,
      //     pro_name: '审核不通过呢'
      //   },
      //   {
      //     status: 1,
      //     pro_name: '提交申请'
      //   },
      //   {
      //     status: 1,
      //     pro_name: '审核不通过呢'
      //   },
      //   {
      //     status: 1,
      //     pro_name: '提交申请'
      //   },
      //   {
      //     status: 1,
      //     pro_name: '审核不通过呢'
      //   }
      // ]
    };
  },
  watch: {
    update(n){
      this.step = n
      this.currentindex = n - 1
    },
    step(n) {
      console.log('kk;');
      this.getStepWidth();
    },
    updatecount(){
      // this.initProgress();
      this.initProgress();
      this.getStepWidth();
      this.getsliderW();
      this.initCurrent();
    },
  },
  mounted() {
    this.initProgress();
    this.getStepWidth();
    this.getsliderW();
    this.initCurrent();
  },
  methods: {
    toggle(index) {
      this.currentindex = index;

      this.$emit('change', index);
    },
    initCurrent() {
      if (this.step - 1 < 0) {
        this.currentindex = 0;
      } else {
        this.currentindex = this.step - 1;
      }
      //
    },
    getsliderW() {
      // console.log(this.$refs.sliderRef.clientWidth)
      var obj = uni.createSelectorQuery().in(this);
      let that = this;
      console.log(obj);
      obj.select('.getwidth').boundingClientRect(function(rect) {
        console.log('rect');
        console.log(rect);
        console.log(rect.height);
        console.log(rect.width);
        that.pL = (that.spaceNumber - rect.width) / 2;

        console.log('that.pL', that.pL);
        // that.init(rect.width);
      });
      obj.exec();
    },
    initProgress() {
      //progresswidth
      let screenwidth = uni.getSystemInfoSync().windowWidth;
      this.spaceNumber = ((screenwidth / 100) * 33 + 0).toFixed(2) - 0;
      this.progresswidth = (this.list.length - 1) * this.spaceNumber + 'px';
      this.totallength = (this.list.length - 1) * this.spaceNumber;
      console.log('screenwidth', screenwidth);
      console.log('spaceNumber', this.spaceNumber);
    },
    // 获取当前所占位置
    getStepWidth() {
      if (this.step > this.list.length) {
        this.step = this.list.length;
      }
      console.log(this.spaceNumber);
      console.log((this.step - 1) * this.spaceNumber);
      let width = (((this.step - 1) * this.spaceNumber) / this.totallength) * 100;
      console.log('percent', width);
      this.percent = width;
    }
  }
};
</script>

<style scoped lang="scss">
.getwidth {
  width: 100rpx;
  // display: none;
  height: 1rpx;
}
.stepbox {
  width: 100vw;
  padding: 0 20upx;
  box-sizing: border-box;
  text-align: center;
  // display: flex;
  // justify-content: center;
}
.stepitem {
  width: 33vw;
  // flex: 1;
}
.con {
  padding: 24rpx 20rpx;
  margin: 20rpx;
  background: #ffffff;
  border-radius: 8rpx;
}

.head {
  display: inline-flex;
  // display: flex;
  // justify-content: space-between;
  position: relative;
  margin: 0 auto;
}

.head_info {
  position: relative;
  display: flex;
  align-items: center;
  flex-direction: column;
  width: 100rpx;
  text-align: center;
  background-color: #fff;
}

.head_icon {
  width: 60rpx;
  height: 60rpx;
  border: 1upx solid #dbdbdb;
  border-radius: 50%;
  // background-color: ;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #dbdbdb;
}
.active {
  .head_icon {
    border-color: $topicC;
    color: $topicC;
  }
}
.current {
  .head_icon {
    background-color: $topicC;
    color: #fff;
  }
}

.head_title {
  margin-top: 10rpx;
  font-size: 20rpx;
  line-height: 28rpx;
  color: #333333;
  overflow: hidden; //超出隐藏
  text-overflow: ellipsis; //溢出用省略号显示
  display: -webkit-box; // 将对象作为弹性伸缩盒子模型显示。
  // 控制行数
  -webkit-line-clamp: 3; //超出两行隐藏
  -webkit-box-orient: vertical; // 从上到下垂直排列子元素
  //（设置伸缩盒子的子元素排列方式）
}

.head_line {
  position: absolute;
  top: 16rpx;
  right: 35%;
  height: 2rpx;
  background: #ccc;
}

progress {
  // width:600rpx;
  width: 100%;
  position: absolute;
  height: 6upx;
  top: 23%;
  left: 0;
  // left: 5%;
}
</style>
