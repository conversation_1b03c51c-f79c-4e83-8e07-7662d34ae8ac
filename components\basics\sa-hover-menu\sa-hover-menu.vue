<!--
	name: 悬浮菜单
	version: v0.0.1
	update_time: 2020-6-25
 -->
<template>
	<view class="page-wrap">
		<!-- 遮罩 -->
		<!-- <view class="mask" v-if="show" @tap="changeShow(false)" @touchmove.stop.prevent></view> -->
		<!-- 按钮 :class="{'click-btn-ball': touchStatus}" :class="{
			show: show,
			'major-box-left': boxPosition === 'left',
		}"-->
		<view class="major-box" :style="{top: top + 'px', left: left + 'px', transition: touchStatus ? '' : 'all 0.3s' }">
			<view class="click-btn"
				@tap="changeShow(!show)" @touchstart="touchstart" @touchend="touchend" @touchmove.stop.prevent="touchmove"
			>
       <image :src="static_ctx2 + 'image/business/hulu-v2/icon-post-menu-btn.png'" class="menuIcon" mode=""></image>
				<!-- <view class="text">快捷导航</view> -->
				<!-- <view class="text">导航</view> -->
			</view>

     <!-- <view class="nav-box" :class="{
        'top': (windowHeight - top) < 200
      }" v-if="!touchStatus">
      	<view class="nav-btn" v-for="(btn, index) in btnList" :key="index" @tap="clickBtn(btn)">
      		<view class="nav-icon" :style="{backgroundColor: btn.bgcolor}">{{btn.icon}}</view>
      		<view class="nav-text">{{btn.text}}</view>
      	</view>
      </view> -->
		</view>
    <!-- 结束咨询弹框 dialogStyle="width:632rpx;height:289rpx;border-radius:10rpx"-->
    <showmodel type='bottom' :clickbg='false' :show='bottomVisible' @cancel='cancelCenterVisible' rootClass='saHoverMenuRootClass'>
    	<view class="dialogContent">
    		<view class="dialog-title">
    			快捷导航
          <image @click='cancelCenterVisible' :src="static_ctx2 + 'image/business/hulu-v2/icon-post-menu-close.png'" class="dialog-close-icon" mode=""></image>
    		</view>
        <view class="dialog-btns">
          <view class="dialog-btn mr140 mgb64" @click="navtoIndex(0)">
            <image class="dialog-icon" :src="static_ctx2 + 'image/business/hulu-v2/icon-post-menu-home.png'" mode=""></image>
            <view class="dialog-text">
              占位
              <text class="t-text">首页</text>
            </view>
          </view>
          <view class="dialog-btn mr140 mgb64" @click="navtoIndex(1)">
            <image class="dialog-icon" :src="static_ctx2 + 'image/business/hulu-v2/icon-post-menu-communication.png'" mode=""></image>
            <view class="dialog-text">
              占位
              <text class="t-text">医患交流</text>
            </view>
          </view>
          <view class="dialog-btn  mgb64" @click="navtoIndex(2)">
            <image class="dialog-icon" :src="static_ctx2 + 'image/business/hulu-v2/icon-post-menu-release.png'" mode=""></image>
            <view class="dialog-text">
              占位
              <text class="t-text">发布帖子</text>
            </view>
          </view>
          <view class="dialog-btn mr140" @click="navtoIndex(3)">
            <image class="dialog-icon" :src="static_ctx2 + 'image/business/hulu-v2/icon-post-menu-my-release.png'" mode=""></image>
            <view class="dialog-text">
              占位
              <text class="t-text">我的发布</text>
            </view>
          </view>
          <view class="dialog-btn mr140" @click="navtoIndex(4)">
            <image class="dialog-icon" :src="static_ctx2 + 'image/business/hulu-v2/icon-post-menu-userinfo.png'" mode=""></image>
            <view class="dialog-text">
              占位
              <text class="t-text">个人资料</text>
            </view>
          </view>
        </view>
    	</view>

    </showmodel>
	</view>
</template>

<script>
// #ifdef H5
import wx from 'weixin-js-sdk'
// #endif
import showmodel from '@/components/basics/showmodel/showmodel.vue'
export default {
  components:{
    showmodel
  },
  props: {
    // 是否可以移动
    isMove: {
      type: Boolean,
      default: true
    },
    defaultTop: {
      type: Number,
      default: 300
    }
  },
	data() {
		return {
      bottomVisible:false,
      // $static_ctx: this.$static_ctx,
      static_ctx2:this.$static_ctx,
			touchStatus: false, // 当前是否正在被拖动
			show: false ,// 是否显示
			top: this.defaultTop,	// 顶端距离
			left: uni.getSystemInfoSync().windowWidth-50, // 左侧距离
			deviationTop: 0,	// 偏移量
			deviationLeft: 0,	// 偏移量
			windowHeight: uni.getSystemInfoSync().windowHeight,	// 视图高度
			windowWidth: uni.getSystemInfoSync().windowWidth,	// 视图高度
			btnList: [		// 所有按钮
				{
					icon: '首',
					text: '首页',
					bgcolor: '#FE7A7D',
					name: 'Index',
					url: '/pages/index/index'
				},
				{
					icon: '坛',
					text: '医患交流',
					bgcolor: '#CAB493',
					name: 'CircleHome',
					url: '/pages/circle-home/index'
				},
				{
					icon: '帖',
					text: '发布帖子',
					bgcolor: '#91B3B2',
					name: 'PostMessage',
					url: '/pages/post-message/index'
				},
				{
					icon: '发',
					text: '我的发布',
					bgcolor: '#959AB3',
					name: 'PersonalMyPosts',
					url: '/modules/community/personal/my-posts/index'
				},
				{
					icon: '个',
					text: '个人资料',
					bgcolor: '#73B3C7',
					name: 'Personal',
					url: '/pages/personal/index'
				}
			],
			boxPosition: 'right',
			// #ifdef H5
      ua: navigator.userAgent.toLowerCase(),
      // #endif
		};
	},
	methods: {
    cancelCenterVisible() {
      this.bottomVisible = false;
    },
		changeShow(e) {
			this.show = e
      this.bottomVisible = true;
		},
    navtoIndex(index) {
      let item = this.btnList[index] || {};
      this.navtoGo(item);
    },
		navtoGo(item, obj = {}) {
			const {name, url} = item
			// #ifdef H5
			if (this.ua.indexOf('miniprogram') !== -1 || this.ua.indexOf('wechatdevtools') !== -1) {
				wx.miniProgram.reLaunch({
					url: url
				})
			} else {
				if (['Index','CircleHome','PostMessage','Personal'].includes(name)) {
					this.$navto.pushTab(name, obj)
				} else {
					this.$navto.push(name, obj)
				}
			}
      // #endif

			// #ifndef H5
      if (['Index','CircleHome','PostMessage','Personal'].includes(name)) {
        this.$navto.pushTab(name, obj)
      } else {
        this.$navto.push(name, obj)
      }
      // #endif
    },
		// 点击按钮
		clickBtn: function(e) {
			this.navtoGo(e)
			this.show = false
		},
		// 拖动开始，记录一下偏移量
		touchstart: function(e) {
			this.touchStatus = true
			let touch = e.touches[0] || e.changedTouches[0];
			this.deviationTop = touch.clientY - this.top;
			this.deviationLeft = touch.clientX - this.left;
			console.log(this.deviationTop);
		},
		// 上下拖动时
		touchmove: function(e) {
      if(!this.isMove) return
			let touch = e.touches[0] || e.changedTouches[0];
			let top = touch.clientY;
			let left = touch.clientX;
			top = top - this.deviationTop;
			left = left - this.deviationLeft;
			if (this.boxPosition === 'right') {
				if (top < 0) {
					top = 0;
				}
				if (top > this.windowHeight - 50) {
					top = this.windowHeight - 50;
				}
				if (left < 0) {
					left = 0;
				}
				if (left > this.windowWidth - 50) {
					left = this.windowWidth - 50;
				}
			} else {
				if (top < 0) {
					top = 0;
				}
				if (top > this.windowHeight - 50) {
					top = this.windowHeight - 50;
				}
				if (left > 0) {
					left = 0;
				}
				if (left < (-this.windowWidth + 50)) {
					left = 0;
				}
			}
			this.top = top;
			this.left = left;
			return false;
		},
		// 停止触摸
		touchend() {
			this.touchStatus = false
			this.$nextTick(() => {
				if (this.boxPosition === 'right') {
					// if (this.left >= (this.windowWidth/2 - 25)) {
						this.boxPosition = 'right'
						this.left = this.windowWidth-50
					// } else {
					// 	this.boxPosition = 'left'
					// 	this.left = -this.windowWidth+50
					// }
          
				} else {
					if (this.left <= (-this.windowWidth/2 + 25)) {
						this.boxPosition = 'left'
						this.left = -this.windowWidth + 50
					} else {
						this.boxPosition = 'right'
						this.left = this.windowWidth - 50
					}
				}
			})
		}
	}
};
</script>

<style scoped lang="scss">
  .dialog-btns {
    padding-left: 102upx;
    padding-right: 104upx;
    display: flex;
    flex-wrap: wrap;
    .dialog-btn {
      width: 88upx;

    }
    .dialog-icon {
      width: 88upx;
      height: 88upx;
      margin-bottom: 12upx;
      vertical-align: top;
    }
    .dialog-text {
      height: 34upx;
      // width: 96upx;
      // font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 24rpx;
      color: #fff;
      line-height: 34rpx;
      text-align: center;
      position: relative;
      // font-style: normal;
    }
    .t-text {
      height: 34upx;
      position: absolute;
      top: 0;
      left: -6upx;
      font-weight: 400;
      font-size: 24rpx;
      color: #1D2029;
      line-height: 34rpx;
      text-align: center;
      white-space: nowrap;
      width: 96upx;
    }
    .mr140 {
      margin-right: 140upx;
    }
    .mgb64 {
      margin-bottom: 64upx;
    }
  }
  .dialog-title {
    padding: 32upx;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 44rpx;
    font-weight: 500;
    font-size: 32rpx;
    color: #1D2029;
    line-height: 44rpx;
  }
  .dialog-close-icon{
    width: 32upx;
    height: 32upx;
    position: absolute;
    right: 32upx;
    top: 50%;
    transform:translateY(-50%)
  }
	/* 遮罩 */
	// .mask {
	// 	position: fixed;
	// 	width: 100%;
	// 	height: 100%;
	// 	top: 0;
	// 	left: 0;
	// 	z-index: 99;
	// 	background: rgba(0, 0, 0, 0.4);
	// }
	/* 总盒子 */
	.major-box {
		border: 1px 0 solid;
		z-index: 100;
		width: 100%;
    position: fixed;
		/* height: 0px; */
		left: 650rpx;
		// overflow: hidden;
		transition: none;
		/* 取消鼠标事件 */
		// pointer-events: none;
    z-index: 101;
	}
	.major-box-left {
		left: 100rpx;
	}
	/* 展开时 */
	.major-box.show{
		left: 115rpx !important;
	}
	.major-box.major-box-left.show{
		left: -115rpx !important;
	}
	.click-btn,
	.nav-box {
		float: left;
		text-align: center;
	}
	.major-box-left {
		.nav-box {
			float: right;
		}
		.click-btn {
			float: right;
			border-radius: 0 100rpx 100rpx 0;
		}
	}
	/* 按钮样式 */
	.nav-box{
		background-color: #FFF;
	}
  .nav-box.top {
    transform: translateY(calc(-100% + 50px));
  }
	.click-btn {
    width: 104upx;
    height: 88upx;

		// display: flex;
		// flex-direction: column;
		// align-items: center;
		// justify-content: center;

		// width: 50px;
		// height: 50px;

		// background-color: #22daad;
		// color: #fff;
		// font-size: 24rpx;
		text-align: center;
		// border-radius: 100rpx 0 0 100rpx;
		// line-height: 32rpx;
		// padding: 10rpx 0;
		// box-sizing: border-box;
		overflow: hidden;
	}
  .menuIcon{
    width: 104upx;
    height: 88upx;
  }
	.major-box-left .click-btn-ball,
	.click-btn-ball {
		border-radius: 50%;
	}
	.major-box-left .click-btn view {
		// padding-right: 15rpx;
	}
	.click-btn view {
		// padding-left: 15rpx;
	}

	/* 按钮盒子 */
	.nav-box {
		width: 500rpx;
		padding: 30rpx 20rpx 20rpx 20rpx;
		display: flex;
		flex-wrap: wrap;
		text-align: center;
		/* justify-content: center; */
	}
	.nav-btn {
		/* flex: 1; */
		width: 33.3%;
		border: 0px #000 solid;
		min-width: 150rpx;
		padding: 12rpx 0;
		padding-bottom: 20rpx;
	}
	.nav-icon {
		width: 80rpx;
		height: 80rpx;
		line-height: 80rpx;
		display: inline-block;
		font-size: 24rpx;
		border-radius: 50%;
		background-color: #666;
		color: #fff;
	}
	.nav-text {
		font-size: 22rpx;
		font-weight: bold;
		margin-top: 8rpx;
	}
	// .text {
	// 	font-size: 24rpx;
	// 	line-height: 32rpx;
	// }
</style>
