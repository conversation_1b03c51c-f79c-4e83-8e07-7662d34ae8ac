<template>
  <view class="service-detail">
    <!-- <view class="my-data" :style="{'background-image':'url('+ file_ctx +'static/image/business/accompany-doctor/icon-accompany-service-detail-bg.png)','background-size': '100%'}"> -->
    <view class="my-data" :style="{'background-image':'url('+ file_ctx + detailObj.detailImg +')','background-size': '100%'}">
      <view :style="'height:' + statusBarHeight + 'px;'"></view>
      <view class="top-nav">
        <view class="top-nav-l" v-if="!showHome" @tap.stop="handleBack"><image mode="aspectFit" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-service-left-arrows.png'" class="header-search-img"/></view>
        <view class="indexPath" v-if="showHome" @tap.stop="handleBack"><image mode="aspectFit" :src="indexPath" class="header-search-img"/></view>
        <view class="top-nav-c"></view>
      </view>
    </view>
    <view class="service-detail-content">
      <view class="service-detail-head">
        <view class="service-detail-head-t">
          <view class="money">
            <view class="money-l">
              ￥<span class="price"><span v-if="detailObj.cityPrice">{{ isNaNFn(detailObj.cityPrice / 100) }}</span><span v-else>{{ isNaNFn(detailObj.price / 100) }}</span></span>
              <!-- <span class="num">次/起</span> -->
            </view>
            <view class="money-r" v-if="detailObj.tag && showDetailObj">
              <span class="special-project" v-for="(item,index) in detailObj.tag.split(',')" :key="index">{{ item }}</span>
            </view>
            <span v-else-if="detailObj.tag && detailObj.tag.split(',').length == 1" class="special-project">{{ detailObj.tag }}</span>
          </view>
          <view class="special-project-accompany">{{ detailObj.comboName }}</view>
        </view>
        <view class="service-detail-head-b">{{ detailObj.comboDesc }}</view>
      </view>
      <view class="service-detail-nav">
        <view class="nav-item" v-for="(item,index) in navList" :key="index" @tap="handletapNav(index)">
          <view :class="currentIndex == index ? 'text active' : 'text'">{{item.name}}</view>
          <view class="item-img" v-if="currentIndex == index"><image :src="file_ctx + 'static/image/business/hulu-v2/border-bottom.png'"></image></view>
        </view>
      </view>
      <view class="service-detail-item" v-if="currentIndex == 0">
        <rich-text class="text-rich" :nodes="detailObj.content" preview></rich-text>
      </view>
      <view class="service-detail-item" v-else>
        <rich-text class="text-rich" :nodes="detailObj.notice" preview></rich-text>
      </view>
    </view>
    <view class="service-detail-bottom">
      <button @tap="handleClickBuyNow">立即购买</button>
    </view>

    <!-- 购买弹窗 -->
    <uni-popup ref="guardDetailPopup" type="bottom">
      <view class="guard-detail-content">
        <view class="guard-detail-head">
          <view class="guard-detail-head-l"><image class="img" :src="file_ctx + detailObj.listImg"></image></view>
          <view class="guard-detail-head-r">
            <template v-if="detailObj.serviceDTOList.length>1">
              <view class="money">￥<span v-if="detailObj.cityPrice">{{ detailObj.cityPrice / 100 }}</span><span v-else>{{ detailObj.price / 100 }}</span></view>
            </template>
            <template v-else>
              <view class="money" v-if="comboTotalPrice">￥<span>{{ comboTotalPrice / 100 }}</span></view>
            </template>
          </view>
        </view>
        <view class="error" @tap="$refs.guardDetailPopup.close()"><image :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-service-error.png'"></image></view>
        <view class="classify" v-if="detailObj.serviceDTOList.length == 1">
          <view class="title">权益分类</view>
          <view class="classify-list">
            <view :class="classifyIndex == index ? 'classify-item active' : 'classify-item'" v-for="(item,index) in standList" :key="item.standId" @tap="handltapClassify(item,index)">{{ item.serviceNum }}次服务</view>
          </view>
        </view>
        <view class="explain">
          <view class="title">使用说明</view>
          <view class="info">本权益无等待期</view>
        </view>
        <view class="guard-detail-bottom" @tap="handletapBuy"><button>立即购买</button></view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  import serverOptions from '@/config/env/options'
  import uniPopup from '@/components/uni/uni-popup'
  import { mapState } from 'vuex'
import uniPlugin from '@/common/util/uni-plugin'

  export default {
    components:{
      uniPopup
    },
    data(){
      return{
        file_ctx:this.file_ctx,
        statusBarHeight: 0,
        navList:[{name:'服务内容'},{name:'预约须知'}],
        currentIndex:0,
        classifyIndex:0,
        detailObj:{},
        itemHeight:null,
        comboTotalPrice:null,
        standIdList:[],
        city:null,
        standList:[],
        standMoreList:[],
        query:{},
        indexPath: this.$static_ctx + "image/business/homeIcon.png",
      }
    },
    computed: {
      ...mapState('user', {
        curSelectUserInfo: state => state.curSelectUserInfo
      }),
      showHome(){
        // 检测当前页面栈是否还有上一页如果没有则返回首页
        const pages = getCurrentPages()
        return pages.length <= 1
      },
      showDetailObj(){
        return this.detailObj.tag.split(',').length > 1
      }
    },
    async onLoad(option){
      this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight // 获取设备的状态栏高度
      let query = this.$validate.isNull(this.$Route.query) ? option : this.$Route.query
      if(query?.city)this.city = query.city
      // 判断是否是渠道链传参
      if(option.scene){
        let parseOptions = this.changeStrObj(option.scene)
        if(parseOptions.gs){
          let {data} = await this.$api.sys.minichannellinkQueryOne({code:parseOptions.gs})
          if(data?.customParameters){
            let parseCustomParameters = this.changeStrObj(data.customParameters)
            query = {...parseCustomParameters,...parseOptions}
          }
        }
      }
      console.log('query',query);
      if(query?.id){
        this.query = query
        this.accompanycomboQueryOneByCity({id:query.id,city:query?.city})
      }else{
        uni.showToast({icon:'none',title:'订单不存在'})
      }
    },
    mounted(){
    },
    onShareAppMessage(){
      let path = `/modules/accompany-doctor/combo-detail/index?id=${this.query.id}&city=${this.query.city}`
      path += `&providerId=${serverOptions.providerId}`
      return {
        title: serverOptions.title,
        path
      }
    },
    onShareTimeline(){
      let path = `/modules/accompany-doctor/combo-detail/index?id=${this.query.id}&city=${this.query.city}`
      path += `&providerId=${serverOptions.providerId}`
      return {
        title: serverOptions.title,
        path
      }
    },
    methods:{
      changeStrObj(str){
        let parseStr = decodeURIComponent(decodeURIComponent(decodeURIComponent(str)))
        return parseStr.split('&').reduce((acc, cur) => {
          let [key, value] = cur.split('=')
          acc[key] = value
          return acc
        }, {})
      },
      isNaNFn(num){
        return isNaN(num) ? 999 : num ? num : 999
      },
      handleClickBuyNow(){
        this.$refs.guardDetailPopup.open()
      },
      // 立即购买
      async handletapBuy(){
        if(wx.getEnterOptionsSync().scene === 1154){
          return uni.showToast({
            icon:'none',
            title:'请点击下方按钮前往小程序使用完整功能'
          })
        }
        const accompanyComboUserRes = await this.accompanycombouserInsert()
        if(!accompanyComboUserRes) return
        const { id } = accompanyComboUserRes.data
        this.$ext.user.accompanyPay({
          bizOrderNo:id,
          successCb:()=>{},
          failCb:()=>{},
          type:2,
        })
      },

      async allinpaydetailGetOrderDetail(){
        const res = await this.$api.accompanyDoctor.allinpaydetailGetOrderDetail({bizOrderNo:this.detailObj.id})
        return Promise.resolve(res)
      },

      async accompanycombouserInsert(){
        const { centerUserId = '', phone = '' } = this.curSelectUserInfo || {}
        if(!phone){
          return this.$ext.user.loginOut('',false)
        }
        let params = {
          userId:centerUserId,
          phone:phone,
          comboId:this.detailObj.id,
          standIdList:(this.detailObj.serviceDTOList.length>1 ? this.standMoreList : this.standIdList) || [],
          city:this.city,
          source:2
        }
        const res = await this.$api.accompanyDoctor.accompanycombouserInsert(params)
        this.$refs.guardDetailPopup.close()
        return Promise.resolve(res)
      },
      handltapClassify(item,index){
        this.classifyIndex = index
        // this.comboTotalPrice = this.detailObj.serviceDTOList[index].standList[index2].comboPrice
        this.standIdList = [item.standId]
        this.comboTotalPrice = item.comboPrice
      },
      handletapNav(index){
        this.currentIndex = index
      },
      handleBack(){
        // 检测当前页面栈是否还有上一页如果没有则返回首页
        const pages = getCurrentPages()
        if (pages.length <= 1) {
          uni.switchTab({
            url: 'pages/accompany-home/index'
          })
        } else {
          this.$navto.back(1)
        }
      },

      // 根据id查询服务
      accompanycomboQueryOneByCity(params){
        this.$api.accompanyDoctor.accompanycomboQueryOneByCity(params).then(res=>{
          this.detailObj = res.data
          res.data.serviceDTOList.forEach(item=>{
            item.standList.forEach((item2,index2)=>{
              this.comboTotalPrice = item.standList[0]?.comboPrice || ''
              this.standIdList = [item.standList[0]?.standId]
              this.standMoreList.push(item2.standId)
              this.standList.push(item2)
            })
          })
        })
      },
    },
 }
</script>

<style lang='scss' scoped>
.service-detail{
  background: #F4F6FA;
  position: relative;
  height: 100vh;
  overflow-y: auto;
}
.img{
  width: 100%;
  height: 100%;
}
.my-data{
  height: 520rpx;
  width: 100%;
  .top-nav{
    // position: fixed;
    width: calc(100% - 56rpx);
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 40px;
    line-height: 40px;
    // z-index: 999;
    padding: 0 32rpx 0 24rpx;
    .indexPath{
      display: flex;
      width: 56rpx;
      height: 56rpx;
      image{
        width: 100%;
        height: 100%;
      }
    }
    .top-nav-l{
      display: flex;
      width: 48rpx;
      height: 48rpx;
      image{
        width: 100%;
        height: 100%;
      }
    }
    .top-nav-c{
      flex: 1;
      text-align: center;
      height: 44rpx;
      font-weight: 500;
      font-size: 32rpx;
      color: #2D2F38;
      line-height: 44rpx;
      margin-right: 48rpx;
    }
  }
}
.service-detail-content{
  position: absolute;
  top: 490rpx;
  left: 0;
  width: calc(100% - 64rpx);
  // height: calc(100% - 700rpx);
  background: #F4F6FA;
  border-radius: 32rpx 32rpx 0rpx 0rpx;
  padding: 32rpx 32rpx 276rpx;
  overflow: hidden;
  .service-detail-head{
    background-color: #fff;
    border-radius: 16rpx;
    padding: 0 32rpx 32rpx;
    .service-detail-head-t{
      padding: 20rpx 0 24rpx;
      border-bottom: 1rpx solid #EAEBF0;
      .money{
        display: flex;
        align-items: center;
        font-size: 30rpx;
        color: #FF5500;
        .money-l{
          .price{
            font-size: 56rpx;
            line-height: 80rpx;
          }
          .num{
            font-size: 28rpx;
            color: #868C9C;
            line-height: 40rpx;
            margin:0 16rpx 0 4rpx;
          }
        }
        .money-r{
          display: flex;
          .special-project{
            padding: 6rpx 16rpx;
            background: #DEF2ED;
            border-radius: 8rpx;
            font-size: 20rpx;
            color: #00664B;
            margin-right: 8rpx;
            &:last-child{
              margin-right: 0;
            }
          }
        }
        .special-project{
          padding: 6rpx 16rpx;
          background: #DEF2ED;
          border-radius: 8rpx;
          font-size: 20rpx;
          color: #00664B;
          margin-left: 16rpx;
        }
      }
      .special-project-accompany{
        font-size: 40rpx;
        color: #1D2029;
        line-height: 56rpx;
        margin-top: 24rpx;
      }
    }
    .service-detail-head-b{
      margin-top: 24rpx;
    }
  }
  .service-detail-nav{
    display: flex;
    justify-content: space-around;
    padding:24rpx 0;
    .nav-item{
      display: flex;
      flex-direction: column;
      align-items: center;
      .text{
        font-size: 32rpx;
        color: #1D2029;
        line-height: 44rpx;
      }
      .active{
        color: #00B484;
      }
      .item-img{
        display: flex;
        width: 38rpx;
        height: 10rpx;
        image{
          width: 100%;
          height: 100%;
        }
      }
    }
  }
  .service-detail-item{
    width: calc(100% - 64rpx);
    padding:32rpx;
    background: #FFFFFF;
    border-radius: 16rpx;
    .item-info{
      margin-bottom: 32rpx;
      .title{
        font-size: 28rpx;
        color: #1D2029;
        line-height: 40rpx;
      }
      .text{
        margin-top: 12rpx;
        font-size: 28rpx;
        color: #4E5569;
        line-height: 40rpx;
      }
      &:last-child{
        margin-bottom: 0;
      }
    }
  }
}
.service-detail-bottom{
  display: flex;
  justify-content: center;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 180rpx;
  background: #FFFFFF;
  box-sizing: border-box;
  padding: 24rpx 32rpx 0;
  box-shadow: 0rpx -1rpx 0rpx 0rpx #EAEBF0;
  button{
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 88rpx;
    background: #00B484;
    border-radius: 44rpx;
    font-size: 32rpx;
    color: #FFFFFF;
  }
}
.guard-detail-content{
  position: relative;
  background: #fff;
  padding: 48rpx 0rpx 68rpx;
  border-radius: 24rpx 24rpx 0rpx 0rpx;
  .guard-detail-head{
    display: flex;
    padding:0 32rpx;
    .guard-detail-head-l{
      width: 160rpx;
      height: 160rpx;
      // background-color: skyblue;
    }
    .guard-detail-head-r{
      margin-left: 24rpx;
      .money{
        color:#FF4100;
        span{
          font-size: 56rpx;
          color: #FF5500;
          line-height: 80rpx;
        }
      }
      .text{
        margin-top: 16rpx;
        font-size: 28rpx;
        color: #1D2029;
        line-height: 40rpx;
      }
    }
  }
  .error{
    position: absolute;
    right: 32rpx;
    top: 38rpx;
    width: 32rpx;
    height: 32rpx;
    image{
      width: 100%;
      height: 100%;
    }
  }
  .classify,.explain{
    margin: 48rpx 0;
    padding:0 32rpx;
    .title{
      font-size: 28rpx;
      color: #1D2029;
      line-height: 40rpx;
      margin-bottom: 24rpx;
    }
    .classify-list{
      display: flex;
      // .item-box{
      //   display: flex;
        .classify-item{
          font-size: 26rpx;
          color: #1D2029;
          line-height: 36rpx;
          padding:18rpx 32rpx;
          background: #F4F6FA;
          border-radius: 12rpx;
          margin-right: 20rpx;
          box-sizing: border-box;
        }
        .active{
          color: #00B484;
          border: 1rpx solid #00B484;
          background: #D7FAF1;
        }
      // }
    }
  }
  .explain{
    .info{
      font-size: 24rpx;
      color: #1D2029;
      line-height: 34rpx;
    }
  }
  .guard-detail-bottom{
    padding-top: 24rpx;
    border-top: 1rpx solid #EAEBF0;
    button{
      display: flex;
      align-items: center;
      justify-content: center;
      height: 88rpx;
      background: #00B484;
      border-radius: 44rpx;
      font-size: 32rpx;
      color: #FFFFFF;
      margin:0 32rpx;
      &::after{
        border: none !important;
      }
    }
  }
}
</style>
