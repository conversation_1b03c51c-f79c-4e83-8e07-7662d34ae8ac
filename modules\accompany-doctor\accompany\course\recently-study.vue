<template>
  <view class='second-classify'>
    <scroll-refresh class="scroll-refresh-main" :isShowEmptySwitch="true" :fixed="false" :up="upOption" :down="downOption" @returnFn="returnFn" @scrollInit="scrollInit">
    <view class="classify-content">
      <view class="content-item" v-for="item in contentList" :key="item.id" @click="handleClickJump(item.id)">
        <view class="classify-content-l"><image :src="file_ctx + item.listImg"></image></view>
        <view class="classify-content-r">
          <view class="tilte">{{ item.courseName }}</view>
          <view class="tags">已学 {{ item.finishChapter }} 课时/共 {{ item.chapter }} 课时</view>
        </view>
      </view>
    </view>
    </scroll-refresh>
  </view>
</template>

<script>
  import common from '@/common/util/main'
  import serverOptions from '@/config/env/options'

  export default {
    data(){
      return{
        file_ctx:this.file_ctx,
        mescroll: null, // mescroll实例对象
        downOption: {
          auto: false // 不自动加载
        },
        upOption: {
          auto: false, // 不自动加载
          empty: {
            top: 0,
            zIndex: 999,
          }
        },
        contentList:[],
        userInfo:null,
      }
    },
    async onLoad(){
      const userId = serverOptions.getUserId(this);
      if(!userId){
        uni.showToast({title:'请先登录',icon:'none'})
        return;
      } 
      let {data} = await this.$api.accompanyDoctor.getAccompanyemployeeOneByUserId({userId})
      this.userInfo = data;
    },
    mounted(){
      this.$nextTick(() => {
        this.init()
      })
    },
    methods:{
      handleClickJump(id){
        this.$navto.push('CourseDetail',{id})
      },
      returnFn(obj) {
        const that = this
        setTimeout(function () {
          let params = {
            current: obj.pageNum,
            size: obj.pageSize,
            condition: {
              employeeId:that.userInfo.id
            }
          }
          that.$api.accompanyDoctor.accompanycoursestudyQueryRecentStudyPage(params).then(res => {
            let data = res.data.records
            // let data = res.data.records.map(item=>(
            //   {
            //     ...item,
            //   }))
            if (obj.pageNum === 1) {
              that.contentList = []
            }
            that.contentList = [...that.contentList, ...data]
            obj.successCallback && obj.successCallback(data)
          })
        }, that.$constant.noun.scrollRefreshTime)
      },

      scrollInit(scroll) {
        scroll.optUp.page.num = 1
        scroll.optUp.page.size = 10
        this.mescroll = scroll
      },
      init() {
        this.$nextTick(() => {
          this.mescroll.triggerDownScroll()
        })
      },
    },
 }
</script>

<style lang='scss' scoped>
  .second-classify{
    height: 100vh;
    // padding: 0 32rpx;
    background: #F4F6FA;
  }
  .scroll-refresh-main{
    height: 100%;
  }
  .classify-content{
    margin-top: 32rpx;
    .content-item{
      display: flex;
      padding: 32rpx 24rpx;
      background-color: #fff;
      border-radius: 16rpx;
      margin-bottom: 20rpx;
      .classify-content-l{
        display: flex;
        width: 224rpx;
        height: 128rpx;
        // background: skyblue;
        margin-right: 16rpx;
        border-radius: 12rpx;
        overflow: hidden;
        image{
          width: 100%;
          height: 100%;
        }
      }
      .classify-content-r{
        .tilte{
          font-size: 28rpx;
          color: #1D2029;
          line-height: 40rpx;
        }
        .tags{
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 22rpx;
          color: #4E5569;
          padding: 4rpx 12rpx;
          background: #F4F6FA;
          border-radius: 8rpx;
          margin-top: 12rpx;
          width:fit-content;
        }
      }
      &:last-child{
        margin-bottom: 0;
      }
    }
  }
</style>