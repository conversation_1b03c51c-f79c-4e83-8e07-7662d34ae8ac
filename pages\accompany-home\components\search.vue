<!-- 搜索栏组件 <search :placeholder="***"  :value="***" @input="onKeyInput" ></search> -->
<template>
  <view class="search" :class="{ fixed: fixed }" :style="{ top: top + 'upx' }">
    <view
      class="input"
      :class="{ 'width-auto': !rightText && !isIcon }"
    >
      <view class="city-box" @click="handleCityFn" v-if="isShow">
        <view class="name"><slot name="cityName"></slot></view>
      </view>
      <view class="icon-view-l-line" v-if="isShow"></view>
      <view @tap="searcFn()" class="flex">
        <em class="icon-view-l icon-search" :style="{left:isShow?'154rpx':'30rpx'}"></em>
        <view class="input-view" v-if="placeholder">{{ placeholder }}</view>
        <!-- <swiper class="input-view" circular vertical :autoplay="autoplay" :interval="interval"
          :duration="duration" @tap="searcFn()" :style="{padding:isShow?'0 0 0 198rpx':'0 114rpx 0 78rpx'}">
          <swiper-item class="item-view" v-for="(item,index) in hotSearchList" :key="index" @touchmove.stop="">
            <view class="swiper-item uni-bg-red">{{ item.word }}</view>
          </swiper-item>
        </swiper> -->

        <view class="line"></view>
        <view class="search-text" @tap="returnFn">
          <view class="text">搜索</view>
          <view v-if="isIcon" class="icon icon-screen"></view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'Search',
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      file_ctx: this.file_ctx,
      $validate: this.$validate,
      inputValue: '',
      timer: undefined,
      width: 0,
      autoplay: true,
      interval: 3000,
      duration: 1000
    }
  },
  props: {
    hotSearchList:{ // 热搜列表
      type: Array,
      default() {
        return []
      }
    },
    // 提示文本style
    placeholderStyle: {
      type: String,
      default() {
        return ''
      }
    },
    isIcon: {
      type: Boolean,
      default() {
        return false
      }
    },
    rightText: {
      type: String,
      default() {
        return ''
      }
    },
    value: {
      type: String,
      required: false,
      default: ''
    },
    placeholder: {
      type: String,
      default() {
        return '搜索'
      }
    },
    k: { // 字段名称
      type: String,
      default() {
        return 'name'
      }
    },
    top: {
      type: String,
      default() {
        return '88'
      }
    },
    fixed: {
      type: [String, Boolean],
      default() {
        return true
      }
    },
    isShow: {
      type: Boolean,
      default() {
        return true
      }
    }
  },
  watch: {
    value: {
      handler(val) {
        this.inputValue = val
      },
      deep: true
    },
    /** 监听手机输入 */
    inputValue() {
      if (this.timer) {
        clearTimeout(this.timer)
      }
      this.timer = setTimeout(() => {
        this.inputValue = this.$validate.trim(this.inputValue)
        const obj = {}
        obj[this.k] = this.inputValue
        this.$emit('changeSearch', obj)
      }, 500)
    }
  },
  mounted() {
    // this.getRightWidth()

  },
  methods: {
    handleCityFn(){
      this.$emit('handleCityFn')
    },
    searcFn() {
      this.$navto.push('accompanyDoctorSystemSearch')
    },
    // 清除输入框
    remove() {
      this.inputValue = ''
    },
    onKeyInput(e) {
      this.inputValue = e.target.value
    },
    returnFn() {
      this.$emit('returnFn', this.inputValue)
    },
    getRightWidth() {
      this.$nextTick(() => {
        const that = this
        let width = 0
        // #ifdef H5
        width = this.$el.querySelector('.right-text').clientWidth;
        that.width = width
        // #endif
        // #ifdef MP
        var query = this.createSelectorQuery();
        query.select('.right-text').boundingClientRect(
          function (rect) {
            // console.log(rect)
            if (rect.width) {
              that.width = rect.width;
            }
          }
        ).exec();
        // #endif
        // this.width = width

      })


    }
  }
}
</script>

<style lang="scss" scoped>
.fixed {
  //position: fixed;
}
.search {
  /*position: fixed;*/
  /*top:200upx;*/
  left: 0;
  right: 0;
  height: 102upx;
  // padding: 16upx 32rpx;
  padding: 16upx 0;
  //background-color: $pageBg;
  z-index: 9999;
  box-sizing: border-box;
  .input {
    background-color: #fff;
    border-radius: 50rpx;
    overflow: hidden;
    width: calc(100% - 140rpx);
    vertical-align: middle;
    display: flex;
    flex-wrap: wrap;
    box-sizing: border-box;
    border: 2rpx solid #D9DBE0;
    /* height: 100%; */
    align-items: center;
    .city-box{
      padding: 0 32rpx;
      height: 100%;
      display: flex;
      align-items: center;
      .name{
        position: relative;
        font-size: 28rpx;
        display: inline-block;
        color: #1D2029;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        &::after{
          content: '';
          position: absolute;
          right: -10px; /* 调整这个值以改变倒三角形与文字的距离 */
          top: 50%;
          transform: translateY(-50%) rotate(90deg); /* 垂直居中并旋转180度形成倒三角形 */
          width: 0;
          height: 0;
          border-left: 12rpx solid #000; /* 倒三角形的宽度和颜色 */
          border-top: 8rpx solid transparent; /* 上边框透明 */
          border-bottom: 8rpx solid transparent; /* 下边框透明 */
        }
      }
    }
    .icon-view-l-line{
      width: 2rpx;
      height: 40rpx;
      background: #DBDDE0;
    }
    .flex{
      display: flex;
      align-items: center;
      justify-content: space-evenly;
      flex: 1;
    }
    .icon-view-l {
      left: 154upx;
      width: 34upx;
      height: 34upx;
    }
    .input-view {
      display: inline-block;
      align-items: center;
      height: 72upx;
      line-height: 72upx;
      // padding: 0 114rpx 0 78rpx;
      box-sizing: border-box;
      font-size: 28rpx;
      color: #A5AAB8;
      .item-view{
        display: flex;
        align-items: center;
        .swiper-item{
          font-size: 28rpx;
          color: #A5AAB8;
        }
      }
    }
    .icon-view-r {
      @include iconImg(34, 34, "/business/icon-close-black-circle.png");
      position: absolute;
      top: 50%;
      -webkit-transform: translateY(-50%);
      transform: translateY(-50%);
      right: 18upx;
    }
  }
  .placeholder-f-s-28 {
    font-size: 28upx;
  }
  .width-auto {
    width: 100%;
  }
  .line{
    right: 112rpx;
    width: 2rpx;
    height: 40rpx;
    background: #DBDDE0;
  }
  .search-text{
    display: flex;
    justify-content: center;
    width: 112rpx;
    height: 40rpx;
    .text{
      font-size: 28rpx;
      color: #1D2029;
      margin-top: -2rpx;
    }
  }
  .right-text {
    vertical-align: middle;
    display: inline-block;
    // width: 140upx;
    .text {
      text-align: right;
      font-size: 32upx;
      line-height: 48upx;
      color: #666;
      display: inline-block;
      vertical-align: middle;
      width: calc(100% - 40upx);
    }
    .icon {
      display: inline-block;
      vertical-align: middle;
      margin-left: 8upx;
    }
    .icon-screen {
      @include iconImg(32, 32, "/business/icon-screen.png");
    }
  }
}
</style>
