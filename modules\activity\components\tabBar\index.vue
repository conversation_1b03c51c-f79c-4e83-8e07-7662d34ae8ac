<template>
    <cover-view class="tab-bar">
      <cover-view class="tab-bar-border"></cover-view>
      <cover-view v-for="item,index in list" :key="index" class="tab-bar-item" :data-path="item.pagePath" :data-index="index" @click="switchTab">
        <template v-if="showico">
          <cover-image :src="selected === index ? item.selectedIconPath : item.iconPath"></cover-image>
        </template>
        <cover-view :style="{color: selected === index ? selectedColor : color}">{{ item.text }}</cover-view>
      </cover-view>
    </cover-view>
</template>

<script>
export default {
  name: 'tabBar',
  props:{
    activeindex:{
      type:Number,
      default:0,
    },
    showico:{
      type:Boolean,
      default:true,
    },
    list:{
      type:Array,
      default:function (){
        return []
        // {
        //   pagePath: '/index/index',
        //   iconPath: '/image/icon_component.png',
        //   selectedIconPath: '/image/icon_component_HL.png',
        //   text: '组件'
        // },
        // {
        //   pagePath: '/index/index2',
        //   iconPath: '/image/icon_API.png',
        //   selectedIconPath: '/image/icon_API_HL.png',
        //   text: '接口'
        // }
      }
    }
  },
  watch:{
    activeindex(n){
      this.selected = n;
    }
  },
  mounted(){
    this.selected = this.activeindex
  },
  data() {
    return {
      selected: 0,
      color: '#7A7E83',
      selectedColor: '#3cc51f',
      // list: [

      // ]
    }
  },
  methods: {
    switchTab(e) {
      const data = e.currentTarget.dataset;
      const url = data.path;
      this.selected = data.index;
      console.log('kkk')
      this.$emit('change',{
        current:data.index,
        url:data.path,
      })
      // wx.switchTab({ url });
      // this.setData({
      //   selected: data.index
      // });
    }
  }
}
</script>

<style scoped lang="scss">
.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 48px;
  background: white;
  display: flex;
  padding-bottom: env(safe-area-inset-bottom);
}

.tab-bar-border {
  background-color: rgba(0, 0, 0, 0.33);
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 1px;
  transform: scaleY(0.5);
}

.tab-bar-item {
  flex: 1;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.tab-bar-item cover-image {
  width: 27px;
  height: 27px;
}

.tab-bar-item cover-view {
  font-size: 10px;
}
</style>
