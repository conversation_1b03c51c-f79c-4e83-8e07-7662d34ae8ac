import MessageDefault from "./MessageDefault"

export default class HandleOrderDetail extends MessageDefault {
    constructor (websocket) {
        super()
        this.websocket = websocket
    }

    match (dataJson) {
        const { GET_ORDER_DETAIL_CMD } = this.chatCmd
        return dataJson.cmd === GET_ORDER_DETAIL_CMD
    }

    processMessage (dataJson) {
        const chatItem = this.$common.getKeyVal('chat', 'chatItem', false)
        // 推送咨询订单引导语
        const { gfDepartmentId, gfIssue, gfPatientId } = dataJson.data
        let sceneType = null
        if (!gfDepartmentId && !gfIssue && !gfPatientId) {
            sceneType = 2 // 首次推送欢迎语
        } else if (!gfDepartmentId) {
            sceneType = 3 // 选科室引导语
        } else if (!gfIssue) {
            sceneType = 4 // 咨询问题引导语
        } else if (!gfPatientId) {
            sceneType = 5 // 选择咨询人档案引导语
        }

        // 推送引导语
        if (sceneType) {
            const { ORDER_GUIDE } = this.chatCmd
            const data =  {
                cmd: ORDER_GUIDE,
                data: {
                    orderId: chatItem.orderId,
                    userId: chatItem.userId,
                    chatUserId: chatItem.chatUserId,
                    type: 4,
                    materialType: 1,
                    businessType: 1,
                    sceneType
                }
            }
            this.websocket.webSocketSend(ORDER_GUIDE, data)
        }

        this.$common.setKeyVal('chat', 'orderDetail', dataJson.data)
    }
}