<template>
  <view class="page">
    <m-nav-bar title="编辑就诊人" left-icon="left" @clickLeft="back" />

    <!-- 表单内容 -->
    <view class="form-content">
      <view class="form-card">
        <view class="form-item">
          <view class="form-label"><text class="required">*</text>姓名</view>
          <input
            type="text"
            class="form-input"
            v-model="patientForm.name"
            placeholder="请输入"
          />
        </view>

        <view class="form-item">
          <view class="form-label">年龄</view>
          <input
            type="text"
            class="form-input"
            v-model="patientForm.age"
            placeholder="请输入"
          />
        </view>

        <view class="form-item">
          <view class="form-label"><text class="required">*</text>性别</view>
          <picker
            @change="bindGenderChange"
            :value="genderIndex"
            :range="genderOptions"
            class="picker-wrapper-width"
          >
            <view class="picker-wrapper">
              <text class="picker-text">{{ genderText || '请选择' }}</text>
              <image class="picker-arrow" :src="iconRightArrow"></image>
            </view>
          </picker>
        </view>

        <view class="form-item">
          <view class="form-label">行动能力</view>
          <picker
            @change="bindMobilityChange"
            :value="mobilityIndex"
            :range="mobilityOptions"
            class="picker-wrapper-width"
          >
            <view class="picker-wrapper">
              <text class="picker-text">{{ patientForm.symptom || '请选择' }}</text>
              <image class="picker-arrow" :src="iconRightArrow"></image>
            </view>
          </picker>
        </view>

        <view class="form-item">
          <view class="form-label"><text class="required">*</text>手机号码</view>
          <input
            type="text"
            class="form-input"
            v-model="patientForm.phone"
            placeholder="请输入"
            maxlength="11"
          />
        </view>
        <view class="form-item">
          <view class="form-label"><text v-if="provinceValue.insureButton === 1" class="required">*</text>身份证号码</view>
          <input
            type="text"
            class="form-input"
            v-model="patientForm.idcard"
            placeholder="请输入"
            maxlength="18"
            @input="onIdCardInput"
          />
        </view>
      </view>
    </view>

    <!-- 底部保存按钮 -->
    <view class="add-btn-wrapper">
      <view class="add-patient-btn" @click="updatePatient">
        <text>保存</text>
      </view>
    </view>
  </view>
</template>

<script>
import uniNavBar from '@/components/uni/uni-nav-bar/uni-nav-bar.vue'
import serverOptions from '@/config/env/options'
export default {
  components: {
    uniNavBar
  },
  data() {
    return {
      patientId: null,
      patientForm: {
        id: null,         // 患者记录ID
        name: '',         // 姓名
        sex: null,        // 性别：1-男 0-女
        symptom: '',      // 症状/行动能力
        age: '',          // 年龄
        userId: '',
        accountId: '',
        phone: '',
        patientIdcard: '',
        idcard:''
      },
      genderOptions: ['女', '男'],  // 注意顺序：0-女 1-男
      genderIndex: 0,
      genderText: '',
      mobilityOptions: ['行动自如', '基本自理','需要轮椅', '需要搀扶', '卧床不起', '其他'],
      mobilityIndex: 0,
      iconRightArrow: this.$static_ctx + "image/business/hulu-v2/icon-right-arrow.png",
      providerId: null,
      provinceValue:{}
    }
  },
  async onLoad(options) {
    let provinceValue = (await this.$api.accompanyDoctor.accompanyproviderQueryOne({id:serverOptions.providerId})).data;
    this.provinceValue = provinceValue;
    console.log('provinceValue',provinceValue)
    if (options.id) {
      this.patientId = options.id;
      this.providerId = serverOptions.providerId;
      this.loadPatientData(options.id);
    } else {
      uni.showToast({
        title: '患者ID不存在',
        icon: 'none'
      });
      setTimeout(() => {
        this.$navto.replaceAll('Patient');
      }, 1500);
    }
  },
  methods: {
    // 身份证号码输入事件
    onIdCardInput() {
      // 当身份证号码长度为18位时，自动计算年龄
      if (this.patientForm.idcard && this.patientForm.idcard.length === 18) {
        this.calculateAgeFromIdCard();
      }
    },

    // 根据身份证号码计算年龄
    calculateAgeFromIdCard() {
      try {
        // 提取身份证中的出生日期（第7-14位）
        const birthYear = this.patientForm.idcard.substring(6, 10);
        const birthMonth = this.patientForm.idcard.substring(10, 12);
        const birthDay = this.patientForm.idcard.substring(12, 14);

        // 创建出生日期对象
        const birthDate = new Date(`${birthYear}-${birthMonth}-${birthDay}`);

        // 获取当前日期
        const today = new Date();

        // 计算年龄
        let age = today.getFullYear() - birthDate.getFullYear();

        // 检查是否已过生日
        const monthDiff = today.getMonth() - birthDate.getMonth();
        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
          age--;
        }

        // 设置年龄值
        this.patientForm.age = age.toString();
      } catch (error) {
        console.error('计算年龄失败', error);
      }
    },

    // 加载患者数据
    async loadPatientData(id) {
      try {
        uni.showLoading({
          title: '加载中...'
        });

        const {data} = await this.$api.accompanyDoctor.accompanypatientQueryOne({id});
        console.log('患者数据', data);

        if (data) {
          // 转换接口数据到表单数据
          this.patientForm = {
            id: data.id,
            name: data.name || '',
            sex: data.sex,
            symptom: data.symptom || '',
            age: data.age || '',
            userId: data.userId,
            accountId: data.accountId,
            phone: data.phone || '',
            idcard: data.idcard || '',
            patientIdcard: data.patientIdcard || ''
          };

          // 设置性别显示文本
          if (data.sex !== null && data.sex !== undefined) {
            this.genderIndex = data.sex;
            this.genderText = this.genderOptions[data.sex];
          }

          // 设置行动能力选择器的索引
          if (data.symptom) {
            const mobilityIndex = this.mobilityOptions.findIndex(item => item === data.symptom);
            if (mobilityIndex !== -1) {
              this.mobilityIndex = mobilityIndex;
            }
          }
        }

        uni.hideLoading();
      } catch (error) {
        uni.hideLoading();
        console.error('获取患者数据失败', error);
        uni.showToast({
          title: '获取患者数据失败',
          icon: 'none'
        });
      }
    },

    // 性别选择器变化
    bindGenderChange(e) {
      this.genderIndex = e.detail.value;
      this.patientForm.sex = parseInt(e.detail.value); // 0-女，1-男
      this.genderText = this.genderOptions[this.genderIndex];
    },

    // 行动能力选择器变化
    bindMobilityChange(e) {
      this.mobilityIndex = e.detail.value;
      this.patientForm.symptom = this.mobilityOptions[this.mobilityIndex];
    },

    // 验证表单
    validateForm() {
      if (!this.patientForm.name) {
        uni.showToast({
          title: '请输入就诊人姓名',
          icon: 'none'
        });
        return false;
      }

      if (this.patientForm.sex === null) {
        uni.showToast({
          title: '请选择性别',
          icon: 'none'
        });
        return false;
      }

      if (!this.patientForm.phone) {
        uni.showToast({
          title: '请输入手机号码',
          icon: 'none'
        });
        return false;
      }

      if (!/^1\d{10}$/.test(this.patientForm.phone)) {
        uni.showToast({
          title: '请输入正确的手机号码',
          icon: 'none'
        });
        return false;
      }

      // 判断是否需要验证身份证号码
      if(this.provinceValue.insureButton === 1){
        // 身份证号码验证
        if (!this.patientForm.idcard) {
          uni.showToast({
            title: '请输入身份证号码',
            icon: 'none'
          });
          return false;
        }
        // 身份证号码长度验证
        if (this.patientForm.idcard.length !== 18) {
          uni.showToast({
            title: '身份证号码长度不正确',
            icon: 'none'
          });
          return false;
        }
        // 身份证号码格式验证
        if (!/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(this.patientForm.idcard)) {
          uni.showToast({
            title: '身份证号码格式不正确',
            icon: 'none'
          });
          return false;
        }
      }
      return true;
    },

    // 更新患者信息
    async updatePatient() {
      if (!this.validateForm()) return;

      // 提交表单
      try {
        uni.showLoading({
          title: '保存中...'
        });

        // 确保有ID
        if (!this.patientId) {
          uni.showToast({
            title: '患者ID不存在',
            icon: 'none'
          });
          return;
        }

        // 获取服务商ID
        if (!this.providerId) {
          // 从全局配置中获取服务商ID
          this.providerId = serverOptions.providerId;
        }

        // 设置ID和服务商ID
        this.patientForm.id = this.patientId;
        this.patientForm.providerId = this.providerId;
        // 调用API更新患者信息
        await this.$api.accompanyDoctor.accompanypatientUpdate(this.patientForm);

        uni.hideLoading();
        uni.showToast({
          title: '更新成功',
          icon: 'success'
        });

        // 返回上一页并刷新列表
        setTimeout(() => {
          // 通知列表页刷新
          uni.$emit('refreshPatientList');
          uni.navigateBack();
        }, 1500);

      } catch (error) {
        uni.hideLoading();
        console.error('更新患者信息失败', error);
        uni.showToast({
          title: '更新失败，请重试',
          icon: 'none'
        });
      }
    },

    back() {
      uni.navigateBack();
    }
  }
}
</script>

<style lang="scss" scoped>
.page{
  width: 100vw;
  height: 100vh;
  top: 0;
  left: 0;
  background: #F4F6FA;
  position: fixed;
  overflow: scroll;
  padding-bottom: 160rpx;
}

/* 表单内容 */
.form-content {
  flex: 1;
  padding: 30rpx;
  overflow-y: auto;
}

.form-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 30rpx;
}

.form-item {
  display: flex;
  margin-bottom: 32rpx;
  border-bottom: 1rpx solid #eee;
  &:last-child {
    margin-bottom: 0;
  }
}

.form-label {
  font-size: 30rpx;
  width: 150rpx;
  color: #333;
  display: flex;
  align-items: center;
}

.required {
  color: #FF5500;
  margin-right: 8rpx;
}

.form-input {
  height: 80rpx;
  font-size: 30rpx;
  padding: 20rpx 0;
  margin-left: 32rpx;
}
.picker-wrapper-width {
  width: 70%;
}
.picker-wrapper {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 30rpx;
  margin-left: 32rpx;
}

.picker-text {
  font-size: 30rpx;
  color: #999;
}

.picker-arrow {
  width: 32rpx;
  height: 32rpx;
}

/* 底部保存按钮 */
.add-btn-wrapper {
  padding: 20rpx 32rpx;
  position: fixed;
  bottom: 0;
  width: 90%;
  left: 50%;
  transform: translateX(-50%);
  background-color: #fff;
  border-top: 1rpx solid #EAEAEA;
}

.add-patient-btn {
  height: 96rpx;
  line-height: 96rpx;
  text-align: center;
  background-color: #00B484;
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
