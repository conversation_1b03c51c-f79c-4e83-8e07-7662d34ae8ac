<template>
  <page>
    <view class="main-body" slot="content">
      <scroll-view scroll-y="true" class="main">
        <view class="m-main">
          <p>1. 用户在下单购买相应服务后，应提前告知陪诊师就诊人已患疾病具体情况、是否有传染性疾病(如:艾滋病、梅毒、乙肝、呼吸道传染病等)，同时应按就诊医院要求提供相应真实有效的证件及证明文件。如因用户未如实告知、未及时告知或告知不全而发生任何突发情况，或提供虚假证件及证明文件等资料，由此引发的一切意外与损失均由用户或实际就诊人自行承担，如因此造成本平台损失，本平台有权向用户或实际就诊人索要相应赔偿。</p>
          <p>2. 用户预约本平台服务须至少提前12小时下单并支付确认购买，未派单之前取消订单，不收取服务费用，如需退款或有任何疑问可咨询平台客服，平台会根据实际情况进行相应处理。订单在服务开始前12小时内取消订单，需扣除10%服务费；订单在服务开始前4小时内取消订单，需扣除50%服务费；如陪诊师已按照订单服务时间开始提供服务，用户或实际就诊人因任何原因取消或提前终止订单，平台将不退还任何费用。</p>
          <p>3. 本平台提供的服务需符合国家法律法规，用户不可要求陪诊人员从事违法犯罪活动，如若出现教唆、煽动陪诊人员从事违法犯罪活动的行为，一经核实，通报公安机关处理。</p>
          <p>4. 用户在陪诊服务过程中应以尊重、文明的态度与陪诊人员交流，不可出现恶意中伤、语言攻击和人身攻击等不当的行为，如若出现以上行为陪诊人员有权终止服务，且用户无权要求退款。</p>
          <p>5. 服务过程中用户不可要求陪诊人员提供违反社会秩序的服务，例如：插队、加号、辱骂他人、中伤医护人员等行为，如若用户提出违反社会秩序的服务，陪诊人员有权拒绝。拒绝无效者，陪诊人员有权终止服务，且用户无权要求退款。</p>
          <p>6. 陪诊师在服务过程中不会直接收取任何费用，服务过程中产生的医疗费用由用户自行承担。</p>
          <p>7. 80岁老人及8岁以下儿童需一名家人陪同，如无法符合前述条件，平台或陪诊师可拒绝提供服务。服务前家属或本人应告知可能发生的突发情况，如若不告知，发生任何情况，本平台及陪诊人员不承担任何责任。</p>
          <p>8. 本公司提供的服务为诊前协助约号、就医咨询、排队报到、排队缴费、排队取药、打印资料等跑腿类业务，不具备应对服务过程中出现的身体健康方面的突发状况，如若服务过程中出现身体健康方面的突发状况，本平台及陪诊人员不承担任何责任。</p>
          <p>9. 在服务时间内，如用户或实际就诊人因非平台或陪诊师原因发生意外事故，包括但不限于突发疾病身故，因自身原因引起摔伤、烫伤、就诊医院诊疗不当造成其他伤害或无法继续就诊而导致陪诊服务无法继续进行，责任由用户或实际就诊人自行负责，平台及陪诊师概不负责；</p>
          <p>10. 服务过程中请您保管好贵重物品，禁止将贵重物品以及服务无关的物品交付于陪诊人员手中，服务过程中如若出现物品丢失或遗失等情况，本平台及陪诊人员不承担任何责任。</p>
          <p>11. 本平台有权限根据用户或实际就诊人在平台上的数据信息进行平台内用户服务的消费者研究，以优化平台功能以及提供更优质的服务；同时，平台将遵循合法、正当、必要的原则，按照法律法规规定或协议约定使用留存用户或实际就诊人的个人信息，并妥善保管，非因法定事由，不得对外泄露。</p>
          <p>如果您通过本平台下单此项服务，本平台将自动认为您已同意以上协议！</p>
        </view>
      </scroll-view>
    </view>
  </page>
</template>

<script>
  import serverOptions from '@/config/env/options'
  import {
    mapState
  } from 'vuex'
  export default {
    components: {

    },
    data() {
      return {
        // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
        $constant: this.$constant,
        $common: this.$common,
        $accurateConversion: this.$accurateConversion,
        file_ctx: this.file_ctx,
        $static_ctx: this.$static_ctx,
        $timePlugin: this.$timePlugin,
        $validate: this.$validate,
        id: undefined,
        idUpdate: true,
        regForm: {}
      }
    },
    computed: {
      ...mapState('user', {

      }),
      ...mapState('system', {
        temporaryStorage: state => state.temporaryStorage
      })
    },
    // 离开当前页面后执行
    onUnload() {
      // this.$common.setKeyVal('system', 'temporaryStorage', {})
    },
    onLoad(paramsObj) {
      const query = this.$Route.query
      if (!this.$validate.isNull(query)) {
        this.id = query.id
      }
      this.init()
    },
    onShow() {},
    methods: {
      init() {
        this.$nextTick(() => {

        })
      },
      navtoGo(url, obj = {}) {
        this.$navto.push(url, obj)
      },
      getDetail(id) {
        const that = this

      }
    }

  }
</script>

<style lang="scss" scoped>
  h3{
    color: #000;
    font-weight: 600;
  }
  .m-l-16 {
    margin-left: 16upx;
  }

  .star {
    color: #F85E4C;
    font-size: 32upx;
    display: inline-block;
  }

  .main-body {
    height: 100%;

    .main {
      height: 100%;
      background: #fff;

      .m-main {
        background: #fff;
        padding: 30upx;
      }
    }
  }

  .title {
    font-size: 36upx;
    line-height: 54upx;
    text-align: center;
  }

  .text-title {
    font-size: 28upx;
    line-height: 42upx;
    margin-top: 16upx;
    color: #333;
  }

  .text {
    font-size: 24upx;
    line-height: 36upx;
    margin-top: 16upx;
    color: #666;
  }

  .text-indent-40 {
    text-indent: 40upx;
  }

  .p-b-8 {
    padding-bottom: 8upx;
  }
</style>
