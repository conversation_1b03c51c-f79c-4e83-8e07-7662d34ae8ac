import MessageDefault from './MessageDefault'
import navto from '@/router/config/nav-to'

// 转接
export default class HandleTransfer extends MessageDefault {
    constructor (websocket) {
        super()
        this.websocket = websocket
    }

    match (dataJson) {
        const { CHAT_TRANSFER_CMD } = this.chatCmd
        return dataJson.cmd === CHAT_TRANSFER_CMD
    }

    processMessage (dataJson) {
        const data = dataJson.data
        // 聊天列表中是否存在
        const chatList = this.$common.getKeyVal('chat', 'chatlist')
        chatList.forEach((item,index) => {
            if (item.id + '' === data.id + '') {
                chatList.splice(index, 1)
            }
        })
        chatList.unshift(data)
        
        // 是否是当前聊天对象 是否在聊天界面
        const chatItem = this.$common.getKeyVal('chat', 'chatItem')
        if (!this.$validate.isNull(chatItem) && chatItem.orderId + '' === data.transferOrderId + '') {
            navto.replace('Chat', data)
        }
    }
}