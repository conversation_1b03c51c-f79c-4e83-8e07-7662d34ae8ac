import request from '@/common/util/request'
import env from '@/config/env'

/**
 * 社区请求工具类，
 * 注意：API接口只做请求数据和格式化数据的事情，其他事务一概不处理
 */
export default {
    // 圈子分类列表
    circleclassifyQueryList (param) {
        const url = env.ctx + 'dm/api/v1/circleclassify/query/list'
        return request.get(url, param)
    },
    // 获取用户已关注圈子列表
    circleclassifyQuerySubscribeList (param) {
        const url = env.ctx + 'dm/api/v1/circleclassify/query/subscribe/list'
        return request.get(url, param)
    },
    // 获取用户未关注圈子列表
    circleclassifyQueryUnsubscribeList (param) {
        const url = env.ctx + 'dm/api/v1/circleclassify/query/unsubscribe/list'
        return request.get(url, param)
    },
    // 获取圈子详情
    circleclassifyQueryOne (param) {
        const url = env.ctx + 'dm/api/v1/circleclassify/query/one'
        return request.get(url, param)
    },
    // 用户关注圈子
    circleclassifySubscribe (param) {
        const url = env.ctx + 'dm/api/v1/circleclassify/subscribe'
        return request.postJson(url, param)
    },
    // 用户取消关注圈子
    circleclassifyUnsubscribe (param) {
        const url = env.ctx + 'dm/api/v1/circleclassify/unsubscribe'
        return request.putJson(url, param)
    },
    // 圈子访问记录
    circleclassifyVisit (param) {
      const url = env.ctx + 'dm/api/v1/circleclassify/visit'
      return request.postForm(url, param)
    }
}
