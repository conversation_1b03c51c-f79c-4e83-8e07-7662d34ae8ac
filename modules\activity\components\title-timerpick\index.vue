<template>
  <showbottommodel :scrolly="false" :show="visible" zindex='999' top='calc(100vh - 600rpx - 60px)' :autotitle="true" @cancel="cancel" :clickmask="false" root-class="showbottommodel">
    <view class="checkbox-t" slot="title"></view>
    <view class="checkbox-t">
      <view class="start" :class="activeIndex == 0 ? 'active' : ''" @click="toggleActive(0)">
        <view class="start-t">开始</view>
        <view>{{ begintimerinfo }}</view>
      </view>
      <view class="start" :class="activeIndex == 1 ? 'active' : ''" @click="toggleActive(1)">
        <view class="start-t">结束</view>
        <view>{{ endtimerinfo }}</view>
      </view>
      <view class="flex1"></view>
      <view class="btn" @click="query">确定</view>
    </view>

    <!-- {{years.length}} -->
    <picker-view indicator-style="height: 50px;" style="width: 100%; height: 500rpx;" :value="currentvalue" @change="bindChange">
      <picker-view-column class="flex1">
        <template v-for="item in years" >
          <view class="ceil" :key="item.label">{{ item }}</view>
        </template>
      </picker-view-column>
      <picker-view-column class="flex1">
        <template v-for="item in months" >
          <view class="ceil" :key="item.label">{{ item.label }}</view>
        </template>
      </picker-view-column>
      <picker-view-column class="flex1">
        <template v-for="item in days" >
          <view class="ceil" :key="item.label">{{ item.label }}</view>
        </template>
      </picker-view-column>
    </picker-view>
  </showbottommodel>
</template>

<script>
import showbottommodel from '../title-showmodel/index.vue';
const date = new Date();
export default {
  name: 'titleTimerPick',
  components: {
    showbottommodel
  },
  mounted(){
    var years = this.getYearsList();
    // console.log(years);
    let temp = '';
    for (var i = 0; i < years.length; i++) {
      for (var j = 1; j < 13; j++) {
        // console.log()
        temp = years[i] + '-' + j;
        this.tempDayObject[temp] = this.getMonthDay(years[i], j);
      }
    }

    console.log('this.tempDayObject',this.tempDayObject)

    // this.setData({
    //   years
    // });
    this.years = years;
    // 初始化
    this.initTimer();
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // 前后100年
    max: {
      type: Number,
      default: 100
    },
    // 选中值 [0,0,0]
    // value:{
    //   type:Object,
    //   value:[0,0,0],
    //   observer:function(news){
    //     // this.currentvalue = news;
    //     this.setData({
    //       currentvalue:news
    //     })
    //   }
    // },
    // 开始时间
    starttimer: {
      type: Number,
      default: new Date().getTime()
    },
    // 结束时间
    endtimer: {
      type: Number,
      default: new Date().getTime() + 24 * 60 * 60 * 1000
    }
  },
  data() {
    return {
      activeIndex: 0,
      // 年
      years: [],
      // 月
      months: [
        {
          label: '01月',
          value: 1
        },
        {
          label: '02月',
          value: 2
        },
        {
          label: '03月',
          value: 3
        },
        {
          label: '04月',
          value: 4
        },
        {
          label: '05月',
          value: 5
        },
        {
          label: '06月',
          value: 6
        },
        {
          label: '07月',
          value: 7
        },
        {
          label: '08月',
          value: 8
        },
        {
          label: '09月',
          value: 9
        },
        {
          label: '10月',
          value: 10
        },
        {
          label: '11月',
          value: 11
        },
        {
          label: '12月',
          value: 12
        }
      ],
      // 天
      days: [],

      //
      tempDayObject: {},
      // 选中值
      currentvalue: [],

      begintimerinfo: '',
      endtimerinfo: '',

      // 开始结果
      startTimerResult: '',
      startTimerInfoResult: '',

      // 结束结果
      endTimerResult: '',
      endTimerInfoResult: ''
    };
  },
  /**
   * 组件的方法列表
   */
  methods: {
    cancel(){
      this.$emit('cannel')
    },

    toggleActive(index) {
      // console.log(e)
      // var index = e.currentTarget.dataset.index;
      // console.log(index);

      if (index == 0) {
        var temp = new Date(this.startTimerResult);
        var year = temp.getFullYear();

        var yidx = this.years.findIndex(item => item == year);
        var monthidx = temp.getMonth();
        var dayidx = temp.getDate() - 1;
        var keystr = year + '-' + (monthidx + 1);
        var days = this.tempDayObject[keystr];
        var that = this;
        this.currentvalue = [yidx, monthidx, dayidx];

        // console.log()

        console.log(days)
        this.days = days;

        // this.setData(
        //   {
        //     days
        //     // begintimerinfo:begin,
        //     // endtimerinfo:endinfo
        //   },
        //   function() {
        //     that.setData({
        //       currentvalue: this.currentvalue
        //       // days
        //     });
        //   }
        // );
      } else if (index == 1) {
        var temp = new Date(this.endTimerResult);
        var year = temp.getFullYear();

        var yidx = this.years.findIndex(item => item == year);
        var monthidx = temp.getMonth();
        var dayidx = temp.getDate() - 1;
        var keystr = year + '-' + (monthidx + 1);
        var days = this.tempDayObject[keystr];
        var that = this;
        this.currentvalue = [yidx, monthidx, dayidx];


        this.days = days;

        // this.setData(
        //   {
        //     days
        //     // begintimerinfo:begin,
        //     // endtimerinfo:endinfo
        //   },
        //   function() {
        //     that.setData({
        //       currentvalue: this.currentvalue
        //       // days
        //     });
        //   }
        // );
      }

      this.activeIndex = index;
      // this.setData({
      //   activeIndex: index
      // });
    },
    resetTimer(year, month, day, type) {
      if (month < 10) {
        month = '0' + month;
      }
      if (day < 10) {
        day = '0' + day;
      }
      var result = year + '年' + month + '月' + day + '日';

      if (type == 1) {
        // this.setData({
        //   begintimerinfo: result
        // });
        this.begintimerinfo = result;
      } else if (type == 2) {
        this.endtimerinfo = result;
        // this.setData({
        //   endtimerinfo: result
        // });
      }

      return result;
    },
    initTimer() {
      var temp = new Date(this.starttimer);
      var end = new Date(this.endtimer);
      var year = temp.getFullYear();
      // var month = temp.getMonth() + 1;
      var monthidx = temp.getMonth();
      var dayidx = temp.getDate() - 1;

      var yidx = this.years.findIndex(item => item == year);

      console.log(this.currentvalue)

      this.startTimerResult = this.starttimer;
      this.endTimerResult = this.endtimer;
      this.startTimerInfoResult = year + '-' + (monthidx + 1) + '-' + (dayidx + 1);

      var keystr = year + '-' + (monthidx + 1);
      // console.log('keystr',keystr)
      var days = this.tempDayObject[keystr];
      var that = this;

      var begin = this.resetTimer(year, monthidx + 1, dayidx + 1);

      year = end.getFullYear();
      monthidx = end.getMonth();
      dayidx = end.getDate() - 1;

      this.endTimerInfoResult = year + '-' + (monthidx + 1) + '-' + (dayidx + 1);

      var endinfo = this.resetTimer(year, monthidx + 1, dayidx + 1);


      this.days = days;
      this.begintimerinfo = begin;
      this.endtimerinfo = endinfo;


      this.$nextTick(() => {
        this.currentvalue = [yidx, monthidx, dayidx];
      })

      // this.$forceUpdate()
      // console.log('this.days',this.days)

      // this.$nextTick(() => {

      // })
      // this.setData(
      //   {
      //     days,
      //     begintimerinfo: begin,
      //     endtimerinfo: endinfo
      //   },
      //   function() {

      //     that.setData({
      //       currentvalue: this.currentvalue
      //       // days
      //     });
      //   }
      // );
    },
    getYearsList(num) {
      num = num || this.max;
      console.log(num);
      const cur = date.getFullYear();

      let cur1, cur2;

      const before = [];

      const after = [];

      for (let i = 0; i < num; i++) {
        cur1 = cur - 1 - i;

        cur2 = cur + 1 - i;
        // console

        before.unshift(cur1); // 在开头添加

        after.push(cur2); // 在结尾添加
      }

      return [...before, cur, ...after];
    },
    getMonthDay(year, month) {
      var day = new Date(year, month, 0).getDate();
      var result = [];
      let temp = '';
      for (let i = 0; i < day; i++) {
        if (i + 1 < 10) {
          temp = '0' + (i + 1);
        } else {
          temp = i + 1;
        }
        result.push({
          label: temp + '日',
          value: i + 1
        });
      }
      return result;
    },
    cannel() {
      // console.log('之心了');
      this.$emit('cannel',{});
      // this.triggerEvent('cannel', {});
    },
    query() {
      this.$emit('query',{
        start: {
          label: this.startTimerInfoResult,
          value: this.startTimerResult
        },
        end: {
          label: this.endTimerInfoResult,
          value: this.endTimerResult
        }
      })

    },
    bindChange(e) {
      // console.log(e);

      this.currentvalue = e.detail.value

      if (this.activeIndex == 0) {
        if (this.startOutTimer) {
          clearTimeout(this.startOutTimer);
        }
        let yidx = this.currentvalue[0];
        let midx = this.currentvalue[1];
        let didx = this.currentvalue[2];
        this.startOutTimer = setTimeout(() => {
          let yv = this.years[yidx];
          let mv = this.months[midx].value;

          let strkey = yv +'-' +  mv;
          let dayArr = this.tempDayObject[strkey]
          this.days = dayArr;
          if(didx >= dayArr.length){
            didx = dayArr.length - 0
          }
          // console.log(dayArr)



          let dv = this.days[didx].value;

          let val = yv + '-' + mv + '-' + dv;

          let timer = new Date(val).getTime();

          this.startTimerResult = timer;
          this.startTimerInfoResult = val;
          // console.log(val);


          this.begintimerinfo = yv + '年' + (mv < 10 ? '0' + mv : mv) + '月' + (dv < 10 ? '0' + dv : dv) + '日'
          // console.log(timer)



        }, 600);
      } else {
        if (this.endOutTimer) {
          clearTimeout(this.endOutTimer);
        }
        let yidx = this.currentvalue[0];
        let midx = this.currentvalue[1];
        let didx = this.currentvalue[2];
        this.endOutTimer = setTimeout(() => {
          let yv = this.years[yidx];
          let mv = this.months[midx].value;

          let strkey = yv + mv;
          let dayArr = this.tempDayObject[strkey]
          this.days = dayArr;

          if(didx >= dayArr.length){
            didx = dayArr.length - 0
          }


          let dv = this.days[didx].value;

          let val = yv + '-' + mv + '-' + dv;

          let timer = new Date(val).getTime();

          this.endTimerResult = timer;
          this.endTimerInfoResult = val;

          this.endtimerinfo = yv + '年' + (mv < 10 ? '0' + mv : mv) + '月' + (dv < 10 ? '0' + dv : dv) + '日'
          // console.log(timer)



          // this.columnArr2[2] = dayArr;

          console.log(this.columnArr2)
        }, 600);
      }


    }
  },
  // lifetimes: {
  //   attached() {

  //   }
  // }

  watch:{
    starttimer(n){
      this.startTimerResult = n;
    },
    endtimer(n){
      this.endTimerResult = n;
    },
  }
};
</script>

<style lang="scss" scoped>


/* pages/applications/bill/component/rangtimer/index.wxss */
/* pages/applications/bill/component/selecttype/index.wxss */
.flex1 {
  flex: 1;
}
.nodate {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
}

.checkbox-t {
  display: flex;
  width: 100%;
  font-size: 24rpx;
  border-bottom: 1rpx solid #dbdbdb;
  height: 100rpx;
  align-items: center;
  box-sizing: border-box;
}
.start {
  padding: 0 40rpx;
}
.start.active {
  // color: var(--themeColor);
  color: $topicC;
}
.start:first-child {
  border-right: 1rpx solid #dbdbdb;
}
.start-t {
  font-size: 32rpx;
}
.btn {
  font-size: 32rpx;
  margin-right: 20rpx;
  // color: var(--themeColor);
  color: $topicC;
}

.showbottommodel {
  z-index: 999;
  top: calc(100vh - 600rpx) !important;
}
.ceil {
  line-height: 50px;
  text-align: center;
}
</style>
