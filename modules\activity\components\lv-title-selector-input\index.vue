
<template>
  <view class="title-input  d-flex" :class="{'bdt':defaultConfig.bdt,'bdb':defaultConfig.bdb && !child}">
    <view class="l-l" :style="{'color': defaultConfig.titleColor}" :class='defaultConfig.titleClass'>
      {{defaultConfig.label}}
      <text class="star" v-if="defaultConfig.required">*</text>
    </view>
    <view class="l-r">
      <!-- 123 -->
      <lv-title-selector-auto :border='defaultConfig.border' :disabled='disabled' style='width: 100%;height: 80upx;' :arr='array' @change='returnFn' :placeholder="defaultConfig.placeholder"  :select='selectobj'></lv-title-selector-auto>
    </view>
  </view>
</template>

<script>
import lvTitleSelectorAuto from '@/modules/activity/components/lv-title-selector-auto/index.vue'
export default {
  data() {
    return {
      form: {
        data: {
          val: ''
        }
      },
      selectobj:{},
      array: [],
      index: 0,
      defaultConfig: {
        bdt: false,
        bdb: true,
        titleColor: '#333',
        textColor: '#333',
        label: '单行输入框',
        name: 'input',
        required: false,
      }
    }
  },
  components:{
    lvTitleSelectorAuto
  },
  watch: {


    cData: {
      handler(val) {
        console.log("0x",val)
        this.watchDataMain(val)
      },
      immediate: true,
      deep: true
    },
    config: {
      handler(val) {
        console.log('变了',val)
        this.copyConfig()
      },
      immediate: true,
      deep: true
    }
  },
  props: {
    onlyKey:{
      type:String,
      default:'id'
    },
    child:{
      type:Boolean,
      default:false,
    },
    // 是否禁用disable
    disabled: {
      type: Boolean,
      default() {
        return false
      }
    },
    // 初始值传值，用于回显
    cData: {
      type: [String, Number],
      required: false,
      default() {
        return ''
      }
    },
    // 参数设置
    config: {
      type: Object,
      required: false,
      default: () => {
        return {}
      }
    }
  },
  computed: {

  },
  mounted() {
    this.getDic(() => {
      this.copyConfig()
      this.watchVal()
      if (!this.$validate.isNull(this.cData)) {
        this.watchDataMain(this.cData)
      }
    })
  },
  methods: {
    /**
     *
     * */
    watchVal () {
      this.array.forEach((item,index) => {
        if(item.value === this.value) {
          // this.form.data.select = item.value
          // this.form.data.label = item.label
          // this.index = index
          this.selectobj = item
        }
      })
    },
    /**
     * 获取字典数据组
     */
    getDic(callBack) {
      const that = this
      const ar = that.config.array || []
      if (ar.length > 0 || !that.config.dicKey) {
        that.array = ar
        callBack()
        return
      }
    },
    /**
       * 初始化拷贝config对象
       */
    copyConfig() {
      const that = this
      const obj = that.config
      Object.keys(obj).forEach(function(key) {
        that.defaultConfig[key] = obj[key]
      })

      that.getDic(() => {
        if (!that.$validate.isNull(that.cData)) {
          that.watchDataMain(that.cData)
        }
      })
    },
   /**
    * 监听cData主逻辑方法
    */
   watchDataMain(val) {
     const that = this
     // that.form.data.select = val.toString()
     let isVal = true
     // console.log('that.array',that.array)
     for (let i = 0; i < that.array.length; i++) {
       if (isVal && val.toString() === that.array[i][that.onlyKey]) {
         console.log('选中值',that.array[i])
         that.selectobj = that.array[i]
         break
         // isVal = false
         // that.defaultConfig.placeholder = that.array[i].value
         // that.index = i
       }
     }
     // if (this.$validate.isNull(val)) {
       // that.index = 0
       // that.defaultConfig.placeholder = that.config.placeholder
     // }
     // that.defaultConfig = Object.assign({}, that.defaultConfig)
   },
    /**
       * picker触发选中事件
       * @param e
       */
    returnFn(obj) {
      console.log('hui',obj)
      const that = this
      if (that.disabled) return
      console.log('0000')
      that.$emit('updateForm', { key: '' + that.config.name, value: obj.selectItem })
    }
  }
}
</script>

<style lang="scss" scoped>
  .d-flex{
    display: flex;
    align-items:center;
  }

  .color-topicC{
    color: $topicC !important;
  }
  .title-input{
    background-color:#ffffff;
    .l-l{
      line-height: 88upx;
      color: #333333;
      font-weight: 600;
      font-size:30upx;
    }
    .l-l.font36{
      font-size: 36upx;
    }
    .l-r{
      flex: 1;
      // margin-bottom: 5px;
      display: flex;
      align-items: center;
          // padding-bottom: 20rpx;
          height: 100%;
      // input {
      //   height: 80upx;
      //   line-height: 80upx;
      //   color: #333;
      //   font-size: 32upx;
      //   text-align: left;
      //   display: inline-block;
      //   vertical-align: middle;
      //   width: calc(100% - 64upx);
      //   border-bottom: 1upx solid $borderColor;
      //   border-radius: 10upx;
      //   padding: 0 20rpx;

      // }
      // input.disabled{
      //     background-color: #dbdbdb;
      //     color: #fff;
      // }
      // .util{
      //   width: 64upx;
      //   font-size: 28upx;
      //       overflow: hidden;
      //       text-align: center;

      // }
    }
  }
  .star{
    color: #F85E4C;
    padding-top: 6upx;
    font-size: 32upx;
    display: inline-block;
        margin-left: 10rpx;
  }
</style>
