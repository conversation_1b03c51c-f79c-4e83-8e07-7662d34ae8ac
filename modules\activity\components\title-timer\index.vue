<template>
  <view >
    <view class="timerbox">
      <template v-for="(item, index) in list">
        <view :key="index" class="timeritem" @click="toggle(index)" :class="[activeindex == index ? 'active' : '']">
        {{ item.label }}
        <view class="tip" v-if="item.type == 'other' && item.timerinfo && item.timerinfo != ''">
          ({{item.timerinfo}})
        </view>
        </view>
      </template>

      <!-- <title-timerpick></title-timerpick>
       -->
    </view>
    <title-timerpick :visible="rangeVisible" :max="max" @cannel='cannel' @query="queryTimer"></title-timerpick>
  </view>
</template>

<script>
import titleTimerpick from '../title-timerpick/index.vue';

export default {
  name: 'titleTimer',
  props: {
    current: {
      type: Number,
      default: 0
    }
  },
  components: {
    titleTimerpick
  },
  data() {
    return {
      list: [],
      activeindex: 0,
       rangeVisible:false,
          max:10,
          // 开始时间
          starttimer:new Date().getTime(),
          endtimer:new Date().getTime() + 24 * 60 * 60 * 1000 * 3
    };
  },
  mounted() {
    this.initTimer();
    this.activeindex = this.current;
  },

  methods: {
    cannel(){
     this.rangeVisible = false;

     if(this.list[this.activeindex].startimer){
       this.$emit('query',{
         index: this.activeindex,
         item:{
           starttimer:this.starttimer,
           endtimer:this.endtimer,
         },
       })
     }
     this.$emit('cannel')
    },
    queryTimer(obj){
    console.log(obj)
      const { start,end } = obj;

      this.list[this.activeindex].timerinfo = start.label + '至' + end.label;
      this.list[this.activeindex].startimer = start.value;
      this.list[this.activeindex].endtimer = start.value;
      this.rangeVisible = false;
      this.$emit('cannel')
      this.$emit('query',{
        index: this.activeindex,
        item:{
          starttimer:start.label + '  00:00:00',
          endtimer:end.label + '  23:59:59',
          starttime:this.starttimer,
          endtime:this.endtimer,
        },
      })

      // label: "2023-3-22"
      // value: 1679457464069
    },
    toggle(index) {
      this.activeindex = index;

      if(this.list[this.activeindex].type == 'other'){

        this.$emit('toggle', {
          index: index,
          item:this.list[this.activeindex],
        });

        this.rangeVisible = true
      }else{
        this.$emit('query',{
          index: index,
          item:this.list[this.activeindex],
        })
      }

    },
    getHMS(time){
      let date = new Date(time);

      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      let day = date.getDate();

      let hour = date.getHours();
      let minute = date.getMinutes();
      let second = date.getSeconds();


      return year + '-' + month + '-' + day + '  ' + hour + ':' + minute + ':' + second;

    },
    initTimer() {
      let list = [];
      let date = new Date();

      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      let days = date.getDate();
      let nowtimer = date.getTime();

      // 本月
      let str = year + '-' + month + '-' + '01' + '  ' + '00:00:00';
      // let lastmonth = month + 1;
      let endstr;
      if (month + 1 > 12) {
        // lastmonth = '01'
        endstr = year + 1 + '-' + '01' + '-' + '01' + '  ' + '00:00:00';
      } else {
        endstr = year + '-' + (month + 1) + '-' + '01' + '   ' + '00:00:00';
      }

      let startimer = new Date(str).getTime();
      let endtimer = new Date(endstr).getTime();

      list.push({
        label: '本月',
        type: 'timer',
        starttimer: str,
        endtimer: endstr,
        starttime: startimer,
        endtime: endtimer,
      });

      // 当天
      let str2 = year + '-' + month + '-' + days + '   ' + '00:00:00';
      let date2 = new Date(str2);

      // 近三天
      let startimer1 = date2.setTime(date2.getTime() - 3600 * 1000 * 24 * 3);

      list.push({
        label: '近3天',
        type: 'timer',
        starttimer: this.getHMS(startimer1),
        endtimer: this.getHMS(nowtimer),
        starttime: startimer1,
        endtime: nowtimer
      });
      // console.log(date3)
      // let startimer1 = date3.getTime();
      // 近七天
      let startimer2 = date2.setTime(date2.getTime() - 3600 * 1000 * 24 * 7);

      list.push({
        label: '近7天',
        type: 'timer',
        starttimer: this.getHMS(startimer2),
        endtimer: this.getHMS(nowtimer),
        starttime: startimer2,
        endtime: nowtimer
      });

      // 近一个月
      let startimer3 = date2.setTime(date2.getTime() - 3600 * 1000 * 24 * 31);

      list.push({
        label: '近一个月',
        type: 'timer',
        starttimer: this.getHMS(startimer3),
        endtimer: this.getHMS(nowtimer),
        starttime: startimer3,
        endtime: nowtimer
      });

      list.push({
        label: '全部',
        type: 'all'
        // starttimer:startimer3,
        // endtimer:nowtimer,
      });

      list.push({
        label: '其他',
        type: 'other'
      });

      this.list = list;

      console.log('startimer1', startimer1);
    }
  }
};
</script>

<style scoped lang="scss">
.timerbox {
  height: 100upx;
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 0 20upx;
  .timeritem {
    padding: 0 15upx;
    font-size: 28upx;
    line-height: 2;
    display: inline-flex;
    align-items: center;
  }
  .timeritem.active {
    border-bottom: 1upx solid $topicC;
    color: $topicC;
  }
  .tip{
        font-size: 12upx;
        max-width: 100upx;
        margin-left: 10upx;
  }
}
</style>
