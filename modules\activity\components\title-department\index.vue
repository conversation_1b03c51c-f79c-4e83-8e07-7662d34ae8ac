
<template>
  <view
    class="title-input"
    :class="{ bdt: defaultConfig.bdt, bdb: defaultConfig.bdb && !child }"
  >
    <view
      class="l-l"
      :style="{ color: defaultConfig.titleColor }"
      :class="defaultConfig.titleClass"
    >
      {{ defaultConfig.label }}
      <text class="star" v-if="defaultConfig.required">*</text>
    </view>
    <view class="l-r">
      <!-- 123 -->
      <lv-title-selector-auto
        :border="defaultConfig.border"
        :disabled="disabled"
        style="width: 100%; height: 80upx"
        :arr="array"
        label='codeName'
        value='id'
        @change="returnFn"
        :placeholder="defaultConfig.placeholder"
        :select="selectobj"
      ></lv-title-selector-auto>
    </view>
  </view>
</template>

<script>
import lvTitleSelectorAuto from "./lv-title-selector-auto/index.vue";
import store from '@/store'
export default {
  data() {
    return {
      form: {
        data: {
          val: "",
        },
      },
      selectobj: {},
      array: [],
      index: 0,
      defaultConfig: {
        bdt: false,
        bdb: true,
        titleColor: "#333",
        textColor: "#333",
        label: "单行输入框",
        name: "input",
        required: false,
      },
    };
  },
  components: {
    lvTitleSelectorAuto,
  },
  watch: {
    cData: {
      handler(val) {
        console.log("0x", val);
        this.watchDataMain(val);
      },
      immediate: true,
      deep: true,
    },
    config: {
      handler(val) {
        console.log("变了", val);
        this.copyConfig();
      },
      immediate: true,
      deep: true,
    },
  },
  props: {
    onlyKey: {
      type: String,
      default: "codeName",
    },
    child: {
      type: Boolean,
      default: false,
    },
    // 是否禁用disable
    disabled: {
      type: Boolean,
      default() {
        return false;
      },
    },
    // 初始值传值，用于回显
    cData: {
      type: [String, Number],
      required: false,
      default() {
        return "";
      },
    },
    // 参数设置
    config: {
      type: Object,
      required: false,
      default: () => {
        return {};
      },
    },
  },
  computed: {},
  mounted() {
    this.getDic(() => {
      this.copyConfig();
      // this.watchVal();
      console.log('科室',this.cData)
      if (!this.$validate.isNull(this.cData)) {
        this.watchDataMain(this.cData);
      }
    });
  },
  methods: {
    /**
     *
     * */
    watchVal() {
      this.array.forEach((item, index) => {
        if (item.value === this.value) {
          this.selectobj = item;
        }
      });
    },
    /**
     * 获取字典数据组
     */
    async getDic(callBack) {
      const that = this;
      const data = await store.dispatch(
        "template/getDepartmentResult",
        {}
      );

      // console.log('data',data)

      const ar = data;
      // if (ar.length > 0 || !that.config.dicKey) {
      that.array = ar;
      callBack();
      //   return;
      // }
    },
    /**
     * 初始化拷贝config对象
     */
    copyConfig() {
      const that = this;
      const obj = that.config;
      Object.keys(obj).forEach(function (key) {
        that.defaultConfig[key] = obj[key];
      });

      that.getDic(() => {
        if (!that.$validate.isNull(that.cData)) {
          that.watchDataMain(that.cData);
        }
      });
    },
    /**
     * 监听cData主逻辑方法
     */
    watchDataMain(val) {
      const that = this;
      // that.form.data.select = val.toString()
      let isVal = true;
      // console.log('that.array',that.array)
      for (let i = 0; i < that.array.length; i++) {
        if (isVal && val.toString() === that.array[i][that.onlyKey]) {
          console.log("选中值", that.array[i]);
          that.selectobj = that.array[i];
          break;
        }
      }
    },
    /**
     * picker触发选中事件
     * @param e
     */
    returnFn(obj) {
      console.log("hui", obj);
      const that = this;
      if (that.disabled) return;
      console.log("0000");
      that.$emit("updateForm", {
        key: "" + that.config.name,
        value: obj.selectItem.codeName,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.d-flex {
  display: flex;
  align-items: center;
}

.color-topicC {
  color: $topicC !important;
}
.title-input {
  background-color: #ffffff;
  .l-l {
    line-height: 88upx;
    color: #333333;
    font-weight: 600;
    font-size: 30upx;
  }
  .l-l.font36 {
    font-size: 36upx;
  }
  .l-r {
    flex: 1;
    display: flex;
    align-items: center;
    height: 100%;
  }
}
.star {
  color: #f85e4c;
  padding-top: 6upx;
  font-size: 32upx;
  display: inline-block;
  margin-left: 10rpx;
}
</style>
