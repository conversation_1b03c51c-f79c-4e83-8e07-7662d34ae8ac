<template>
	<view class="lb-select" >
		<view class="lb-select-t" @click="show" :class="{
      border:border,
    }">
			<input
				class="selectInput"
				:class="visible ? 'show' : ''"
				:name="name"
				:placeholder="placeholder"
				:slecttable="false"
				type="text"
        @input="findInputPoint"
        @blur="inputBlur"
        :disabled='disabled'
				v-model="selectValue"
			>
      <em class="jump"></em>
		</view>
		<template v-if="visible">
			<view class="fix-hbg" @click="hide()" />
			<view
				class="lb-dialog"
				:class="[level,hot,visible ? 'active' : '']"
				:style="{
					'max-height': height
				}"
			>
      <template v-if="filterInput == ''">

        <template v-for="item,index in arr">
        	<view
        		:key="item.id"
        		class="selectItem"
        		@click="selectChange(index)"
        	>
        		{{ item[label] }}
        	</view>
        </template>
      </template>
      <template v-else>
        <view class="nodata"  v-if="filterArr.length === 0">
          暂无匹配数据
        </view>
        <template v-for="item,index in filterArr">

        	<view
        		:key="item.id"
        		class="selectItem"
        		@click="selectChange(index,2)"
        	>
        		{{ item[label] }}
        	</view>


        </template>
      </template>

			</view>
		</template>
	</view>
</template>

<script>

export default {
	props: {
    // 边框
    border:{
      type:Boolean,
      default:false
    },
    disabled:{
      type:Boolean,
      default:false
    },
    placeholder:{
      type:String,
      default:null
    },
		// 选中对象
		select: {
			type: Object,
			default: {}
		},
		// 下拉框name值
		name: {
			type: String,
			default: 'select'
		},
		// 下拉框显示值
		label: {
			type: String,
			default: 'label'
		},
		//下拉框选中值
		value: {
			type: String,
			default: 'id'
		},
		// 下拉框数据
		arr: {
			type: Array,
			default: function () {
        return [
				{
					label: '下拉框一',
					id: '1'
				}, {
					label: '下路客人',
					id: 2
				}
			]
      }
		},
		// 下拉框最大高度，自动出滚动条
		height: {
			type: String,
			default: '200px'
		},
		// multipleSlots: true, // 在组件定义时的选项中启用多slot支持
		width: {
			type: String,
			default: '150px'
		},
	},

	data(){
		return {
      filterArr:[],
      // 输入值
      filterInput:'',
      pointValue:"",
			// placeholder: '请选择内容',
			selectItem: {},
			// 选中值
			selectValue: '',
			visible: false, // 显示弹出层
			level: 'left', // 水平方向
			hot: 'top', // 垂直方向
		}
	},
	mounted(){
		const res = uni.getSystemInfoSync()
		let statuHeight = res.statusBarHeight //状态栏的高度
		// let titleHeight = 45 //导航栏高度，这个一般是固定的
		var radio = res.windowWidth
		var height =(res.windowHeight - statuHeight - 45) / 2
		// console.log(height)
		this.spacewidth = radio / 2
		this.spaceheight = height

		this.selectItem = this.select
		this.selectValue = this.select[this.label]

	},
  watch:{
    select(n) {
      // console.log('n',n)

      if(n && ((this.selectItem && n[this.value] != this.selectItem[this.value]) || !this.selectItem))
      this.selectItem = n;
      this.selectValue = n[this.label]
      this.filterInput = this.selectValue
      this.pointValue = this.selectValue
      this.filterArr = this.arr.filter(item => item.label.indexOf(this.filterInput) != -1)
    }
  },

	/**
    * 组件的方法列表
  */
	methods: {
    inputBlur(){
      // console.log('jjk',this.selectValue)
     this.selectValue = this.pointValue

    },
    findInputPoint(e){
      // console.log('this.selectValue',e)
      if(this.timer){
        clearTimeout(this.timer)
      }
      this.timer = setTimeout(() => {
        this.filterInput = e.detail.value

        this.filterArr = this.arr.filter(item => item.label.indexOf(this.filterInput) != -1)
      },200)
    },
		selectChange(index,type){
			// var index = e.currentTarget.dataset.index
			// console.log(index)
      if(type === 2){
        this.selectItem = this.filterArr[index]
        this.selectValue = this.selectItem[this.label]
        this.filterInput = this.selectValue
        this.pointValue = this.selectValue
        this.visible = false
        this.filterArr = this.arr.filter(item => item.label.indexOf(this.filterInput) != -1)
      }else{
        this.selectItem = this.arr[index]
        this.selectValue = this.selectItem[this.label]
        this.filterInput = this.selectValue
        this.pointValue = this.selectValue
        this.visible = false
        this.filterArr = this.arr.filter(item => item.label.indexOf(this.filterInput) != -1)
      }


			this.$emit('change', {
				selectValue: this.selectItem[this.label],
				selectItem: this.selectItem,
			})

		},
		show() {
      if(this.disabled){
        return
      }
			var query = uni.createSelectorQuery().in(this)
			var id = '.lb-select'
			var that = this
			var spacewidth = this.spacewidth
			var spaceheight = this.spaceheight
			var temp
			query.select(id).boundingClientRect(function (res) {
				// res.top // #the-id 节点的上边界坐标（相对于显示区域）
				// console.log(res)
			})
			query.selectViewport().scrollOffset()
			var hot
			query.exec(function (res) {
				console.log(res)
				temp = res[0].top - res[1].scrollTop
				var type
				if (temp > spaceheight) {
					hot = 'top'
					type = 'top'
				} else {
					hot = 'bottom'
					type = 'bottom'
				}
				var level

				if (res[0].left > spacewidth) {
					level = 'right'
				} else {
					level = 'left'
				}
				that.level = level
				that.visible = true
				that.hot = hot
				that.type = type

			})
		},
		hide() {
			this.visible = false

		}
	},
}

</script>

<style scoped lang="scss">
  .lb-select-t.border{
    border: 1upx solid #dbdbdb;
  }
  .nodata{
    min-height: 150upx;
    display: flex;
    align-items: center;
    justify-content: center;

  }
  .jump {
    display: inline-block;
    vertical-align: middle;
    @include iconImg(34, 34, "/business/icon-gengduo.png");
  }
  .selectInput{
    height: 100%;
    // height: 80upx;
    width: 100%;
    // background-color: #f9f9f9;
    // box-shadow: 1px 1px 3px #eeee;
    padding: 0 20upx;
    box-sizing: border-box;
    position: relative;
    text-align: right;
  }
  // .selectInput::after{
  //   position: absolute;
  //   right:20upx;
  //   top:50%;
  //   transform: translateY(-50%) rotate(-90deg);
  //   content: "*";
  //   font-size: 0;
  //   width: 30upx;
  //   height: 30upx;
  //   background-size: 100%;
  //   transform: all 1s;
  // }
  // .selectInput.show::after{
  //   transform: translateY(-50%) rotate(0deg);
  // }
  .selectItem{
    height: 80upx;
    line-height: 80upx;
    padding: 0 20upx;
    white-space: nowrap;
    overflow-x: auto;

    border-bottom: 1upx solid #ccc;
  }
  .lb-select {
    position: relative;
    width: 100%;
    height: 100%;
    /* display: inline-block; */
    /* height: 100%; */
    /* display: inline; */

  }

  .lb-select-t {
    /* min-height: 80upx; */
    /* line-height: 80upx; */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100vw;
    height: 100%;
    box-sizing: border-box;
    display: flex;
    align-items: center;
  }

  .fix-hbg {
    position: fixed;
    z-index: 999;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    /* background-color: rgba(0, 0, 0, 0.2); */
  }

  .lb-dialog.bottom {
    top: 100%;
    width: 100%;
    /* top: 90upx; */
  }

  .lb-dialog.top {
    /* top:-20upx */
    bottom: 100%;
    width: 100%;
    /* bottom: 90upx; */
    /* bottom: 90upx; */
  }

  .lb-dialog.left {
    /* left: 10upx; */
  }

  .lb-dialog.right {
    /* right: 10upx; */
  }

  .tt {
    /* z-index: 1000 */
    z-index: -1
  }

  .tt.left {
    left: 20upx;
  }

  .tt.right {
    right: 20upx;
  }

  .lb-dialog {
    position: absolute;
    z-index: 999;
    border-radius: 20upx;
    overflow-y: auto;
    /* padding: 0 20upx; */
    /* right: 0; */

    /* left: 10upx; */
    /* height: 400upx; */
    /* width: 300upx; */
    /* overflow-y: auto; */
    background-color: rgb(255, 255, 255);
    box-shadow: 4upx 4upx 8upx 2upx rgb(122, 118, 118);
    opacity: 0;
    transition: all 2s;
  }

  .lb-dialog.active {
    opacity: 1;
  }

  .lb-jt {
    position: absolute;
    /* content: "*"; */
    font-size: 0;
    width: 0;
    height: 0;
    border: 20upx solid #fff;
    border-right-color: transparent;
    border-bottom-color: transparent;
    transform: rotate(-135deg);
    bottom: -15upx;
    /* left: 20upx; */
    box-shadow: 4upx -4upx 8upx 2upx rgb(122, 118, 118);
    z-index: -1
  }

  .lb-jtt {
    content: '*';
    font-size: 0;
    height: 0upx;
    width: 0upx;
    border: 20upx solid #fff;
    /* background-color: #fff; */
    position: absolute;
    bottom: -15upx;
    /* left: 20upx; */
    transform: rotate(-135deg);
  }

  .lb-jtb {
    position: absolute;
    content: "*";
    font-size: 0;
    width: 0;
    height: 0;
    border: 20upx solid #fff;
    border-right-color: transparent;
    border-bottom-color: transparent;
    transform: rotate(45deg);
    top: -15upx;
    /* left: 20upx; */
    box-shadow: 4upx 4upx 8upx 2upx rgb(122, 118, 118);
    z-index: -1;
  }

  .lb-jttb {
    content: '*';
    font-size: 0;
    height: 0upx;
    width: 0upx;
    border: 20upx solid #fff;
    position: absolute;
    top: -15upx;
    /* left: 20upx; */
    transform: rotate(45deg);
  }

  /* .lb-dialog .lb-dialog-box{
    width: 100%;
    height: 100%;
    background-color: #fff;
    opacity: 0;
    transition: all 2s;

  } */
</style>
