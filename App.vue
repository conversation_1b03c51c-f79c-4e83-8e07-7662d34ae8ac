<!--主要程序模板页面-->
<script>
import common from '@/common/util/main'
import ext from '@/service/ext'
export default {
  // #ifndef H5
  // #endif
  onLaunch: function(option) {
    this.$ext.wechat.getOpenId()
    // 直播离开时间
    let liveCookieInfo = common.getCache('liveCookieInfo')
    if(liveCookieInfo && liveCookieInfo instanceof Object){
      common.setCache('liveCookieInfo','')
      this.meetingviewlogvisit2(liveCookieInfo.id,liveCookieInfo.val)

    }

    //微信小程序ios上音频播放没有声音
    // #ifdef MP-WEIXIN
    uni.setInnerAudioOption({
      obeyMuteSwitch: false
    });
    // #endif

    // const that = this
    // const query = this.$Route.query
    if (!this.$validate.isNull(option)) {
      if (!this.$validate.isNull(option.query)) {
        common.setKeyVal('system', 'launchParams', option.query, true)
      }
    }
    // ext.common.parseAppScene(option)
    // this.$ext.wechat.getUnionid()
    const that = this
    const query = this.$Route.query
    if (!this.$validate.isNull(query)) {
      if (query.storeId) {
        common.setKeyVal('user', 'curSelectStoreId', query.storeId, true)
      }
    }
    ext.common.parseAppScene(option)
    this.addMinichannellinklogvisit(1)

    // this.$ext.wechat.getUnionid()
  },
  onShow: function(option) {
    if (!this.$validate.isNull(option) && !this.$validate.isNull(option.query) && !this.$validate.isNull(option.query.scene)) {
      const sceneResult = {
        scene: option.scene,
        path: option.path,
        sceneCode: option.query.scene
      }
      common.setKeyVal('system', 'scene', sceneResult, true)
    } else {
      common.setKeyVal('system', 'scene', {}, true)
    }

    // 重新链接
    if (!this.$ext.webSocket.webSocket) {
      this.$ext.webSocket.webSocketInit()
    }
    const that = this

    this.addMinichannellinklogvisit(3)
  },
  onHide: function() {
    this.addMinichannellinklogvisit(2)
    // 断开链接
    if (!this.$validate.isNull(this.$ext.webSocket.webSocket)) {
      this.$ext.webSocket.webSocketIsReconnect = false
      this.$ext.webSocket.webSocketPingTimer = null
      // #ifdef H5
      this.$ext.webSocket.webSocket.close();
      // #endif

      // #ifndef H5
      this.$ext.webSocket.webSocket.close({})
      // #endif
    }
    // console.log('App Hide')
  },
  methods: {
    meetingviewlogvisit2(id,cookieId) {
      this.$api.cloudClassroom
        .meetingviewlogvisit({
          mainId: id,
          source: 2,
          cookieId:cookieId,
        })
        .then(ret => {});
    },
    /**
     * 添加渠道链访问记录
     * @param {number} visitType 1-进入，2-离开 3-显示
     */
    addMinichannellinklogvisit (visitType) {
      const gbScene = common.getKeyVal('system', 'gbScene', true)
      if (!gbScene) return
      const param = {
        visitType,
        code: common.getKeyVal('system', 'gbScene', true),
        accountId: common.getKeyVal('user', 'accountId', true),
        // #ifdef H5
        terminalType: 2, // 1-小程序，2-H5
        // #endif
        // #ifdef MP-WEIXIN
        terminalType: 1, // 1-小程序，2-H5
        // #endif
      }
      this.$api.sys.minichannellinklogVisit(param)
    }
  }
}
</script>

<style lang="scss">
  @import './components/community/min.css';
  // @import './components/community/icon.css';
  em,i{font-style:normal}

  text, view, button {
    font-family: -apple-system, BlinkMacSystemFont, Segoe UI, PingFang SC, HarmonyOS_Sans_SC, MiSans, OPPOSans, vivoSans, HONOR Sans, Roboto, Microsoft YaHei, Helvetica, Arial, sans-serif;
  }

  uni-view{
    line-height: 1;
  }
  /*checkbox选中后样式  */
  uni-checkbox .uni-checkbox-input.uni-checkbox-input-checked{
      border-color: #22daad!important;
      background-color: #22daad!important;
      color: #FFFFFF!important;
  }
  /*单选按钮*/
  .uni-radio-input-checked{
    border-color: #22daad!important;
    background-color: #22daad!important;
    color: #FFFFFF!important;
  }
  uni-tabbar .uni-tabbar-border{
    background-color:#F7F7F7!important;
  }
  .uni-checkbox .uni-checkbox-input{
    width: 40upx!important;
    height: 40upx!important;
  }
  [alt]{
    max-width:100%;
  }
  .isNoLogin{
    position: fixed;
    bottom: 20rpx;
    z-index: 999;
    background-color: #00B484;
    padding: 30rpx;
    border-radius: 30rpx;
    left: 50%;
    color: white;
    transform: translateX(-50%);
  }
</style>
