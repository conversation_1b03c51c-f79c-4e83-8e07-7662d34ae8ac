<template>
  <view :class="'moduleBox-' + skinColor">
    <view class="service-project accompany-box moduleBox-teacher" style="margin:20rpx 0rpx 20rpx;">
    <view class="service-project-head">
      <view class="service-project-head-l">
        {{ employeeTitle }}
        <view class="pzsTitleIcon">
          <image class="pzsTitleLogo" :src="pzsTitleLogo" mode=""></image>
          持证率100%
        </view>
      </view>
      <view class="service-project-head-r" v-if="displayAccompanyList.length > 1" @tap="$navto.push('accompanyList',{isShow:true,cityName:cityName,employeeTitle:employeeTitle})">更多<image class="head-r-img" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-service-right-arrows.png'"></image></view>
    </view>
    <view class="accompany-List" v-if="displayAccompanyList.length">
      <view class="accompany-box" v-for="item in displayAccompanyList" :key="item.id" @click="handleJump(item.id)">
        <image class="accompany-Icon" mode="aspectFit" :src="item.avatar"></image>
        <view class="accompany-text">
          <view class="accompany-username">
            {{item.username}}
            <image class="pzsNameLogo" :src="pzsNameLogo"></image>
              <view class="certificate-nums" v-if="item.certificate">持证{{item.certificate.split(',').length}}本</view>
          </view>
          <view class="accompany-experience" v-if="item.experience">{{item.experience}}</view>
          <view class="languageMap" v-if="item.language && item.language.filter(l => l).length > 0">
            <view class="languageItem" :key="index" v-for="(languageItem,index) in sortLanguages(item.language.filter(l => l)).slice(0, 2)">{{languageItem}}</view>
          </view>
          <view class="languageMap" v-else>
            <view class="languageItem" :key="index" v-for="(languageItem,index) in sortLanguages([]).slice(0, 2)">{{languageItem}}</view>
          </view>
      </view>
    </view>
    </view>
    <view class="empty" v-else>
      <view class="empty-img"><image class="img" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-empty.png'"></image></view>
      暂无数据~
    </view>
  </view>
  </view>
</template>

<script>
import { isDomainUrl } from '@/utils/index.js'

export default {
  data() {
    return {
      file_ctx: this.file_ctx,
      pzsTitleLogo: this.file_ctx + 'static/image/business/hulu-v2/pzsTitleLogo.png',
      pzsNameLogo: this.file_ctx + 'static/image/business/hulu-v2/pzsNameLogo.png',
      displayAccompanyList: [],
      allAccompanyList: [],
      timer: null,
      maxDisplayCount: 3,
      isPageActive: true, // 添加页面活动状态标记
      prevCityName: '' // 添加记录前一个城市名
    }
  },
  props: {
    accompanylist: {
      type: Array,
      default: () => []
    },
    cityName: {
      type: String,
      default: ''
    },
    employeeTitle: {
      type: String,
      default: '本地陪诊师'
    }
  },
  watch: {
    accompanylist: {
      handler(newVal) {
        if (newVal && newVal.length) {
          this.allAccompanyList = [...newVal];
          this.randomizeDisplayList();
        }
      },
      immediate: true
    },
    // 监听城市变化
    cityName: {
      handler(newVal, oldVal) {
        if (oldVal && newVal !== oldVal) {
          // 城市变化时，清空陪诊师数据
          this.clearAccompanyData();
        }
        this.prevCityName = newVal;
      },
      immediate: true
    }
  },
  mounted() {
    // 设置定时器，每5秒随机切换一次数据
    this.startRandomTimer();

    // 监听页面显示/隐藏事件
    uni.$on('accompanyHome:show', this.handlePageShow);
    uni.$on('accompanyHome:hide', this.handlePageHide);
  },
  beforeDestroy() {
    // 组件销毁时清除定时器和事件监听
    this.clearRandomTimer();
    uni.$off('accompanyHome:show', this.handlePageShow);
    uni.$off('accompanyHome:hide', this.handlePageHide);
  },
  methods: {
    // 清空陪诊师数据
    clearAccompanyData() {
      console.log('城市变更，清空陪诊师数据');
      this.allAccompanyList = [];
      this.displayAccompanyList = [];
    },
    handlePageShow() {
      this.isPageActive = true;
      this.startRandomTimer();
    },
    handlePageHide() {
      this.isPageActive = false;
      this.clearRandomTimer();
    },
    handleJump(id) {
      this.$navto.push('accompanyDoctorDetails', { id, employeeTitle: this.employeeTitle })
    },
    sortLanguages(languages) {
      // 定义语言优先级顺序：1.英语 2.粤语 3.普通话
      const languagePriority = {
        '英语': 1,
        '粤语': 2,
        '普通话': 3
      };

      // 对语言数组进行排序
      return [...languages].sort((a, b) => {
        const priorityA = languagePriority[a] || 999;
        const priorityB = languagePriority[b] || 999;
        return priorityA - priorityB;
      });
    },
    startRandomTimer() {
      // 如果页面不活跃，不启动定时器
      if (!this.isPageActive) return;

      // 首先清除可能存在的定时器，避免重复创建
      this.clearRandomTimer();

      // 首次加载时先显示一次
      this.randomizeDisplayList();

      // 设置5秒定时器
      this.timer = setInterval(() => {
        // 如果页面不再活跃，清除定时器
        if (!this.isPageActive) {
          this.clearRandomTimer();
          return;
        }
        this.randomizeDisplayList();
      }, 5000);

      console.log('陪诊师定时器已启动');
    },
    clearRandomTimer() {
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
        console.log('陪诊师定时器已清除');
      }
    },
    randomizeDisplayList() {
      // 如果页面不活跃，不执行随机显示
      if (!this.isPageActive) return;

      // 如果全部陪诊师数据小于等于最大显示数量，直接显示全部
      if (this.allAccompanyList.length <= this.maxDisplayCount) {
        this.displayAccompanyList = [...this.allAccompanyList];
        return;
      }

      // 随机选择陪诊师数据
      const shuffled = [...this.allAccompanyList].sort(() => 0.5 - Math.random());
      this.displayAccompanyList = shuffled.slice(0, this.maxDisplayCount);

      // 打印当前显示的陪诊师数量（调试用）
    //   if (this.isPageActive) {
    //     console.log('当前显示的陪诊师数量:', this.displayAccompanyList.length);
    //     console.log('总陪诊师数量:', this.allAccompanyList.length);
    //   }
    },
    // 公开的刷新方法，可以被父组件调用
    refresh() {
      this.randomizeDisplayList();
    }
  }
}
</script>

<style lang="scss" scoped>
  @import '../../style/blueSkin.scss';
.service-project.accompany-box {
  margin: 20rpx 0;
  padding: 24rpx 24rpx 0;
  background: linear-gradient(180deg, #E9F7F4 0%, #FFFFFF 100%);
  border-radius: 16rpx;

  .service-project-head {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .service-project-head-l {
      display: flex;
      align-items: center;
      font-size: 32rpx;
      color: #1D2029;
      line-height: 44rpx;
    }

    .service-project-head-r {
      display: flex;
      align-items: center;
      font-size: 24rpx;
      color: #868C9C;
      line-height: 34rpx;

      .head-r-img {
        display: flex;
        width: 32rpx;
        height: 32rpx;
      }
    }
  }

  .pzsTitleIcon {
    display: flex;
    width: 196rpx;
    height: 40rpx;
    align-items: center;
    background: #E7E7F7;
    font-weight: 500;
    font-size: 24rpx;
    color: #5959F7;
    margin-left: 16rpx;
    border-radius: 40rpx;

    .pzsTitleLogo {
      width: 44rpx;
      height: 44rpx;
      margin-right: 8rpx;
    }
  }

  .accompany-List {
    display: flex;
    margin-top: 24rpx;

    .accompany-box {
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding-bottom: 32rpx;

      &:not(:last-child) {
        margin-right: 26rpx;
      }

      .accompany-Icon {
        width: 198rpx;
        height: 198rpx;
        border-radius: 8rpx;
      }
      .accompany-experience{
        display: none;
      }
      .accompany-username {
        display: flex;
        align-items: center;
        font-weight: 600;
        font-size: 30rpx;
        color: #1D2029;
        margin-top: 12rpx;

        .pzsNameLogo {
          width: 36rpx;
          height: 34rpx;
        }
        .certificate-nums{
          display: none;
        }
      }
    }
  }

  .languageMap {
    display: flex;
    margin-top: 8rpx;
    min-height: 32rpx;

    .languageItem {
      height: 36rpx;
      padding: 2rpx 8rpx;
      margin-right: 8rpx;
      background: #EBF7F5;
      border-radius: 4rpx;
      font-weight: 400;
      font-size: 22rpx;
      color: #007959;
    }
  }

  .empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 50rpx 0;
    width: 100%;
    .empty-img {
      width: 286rpx;
      height: 212rpx;
      margin-bottom: 20rpx;
      .img{
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>
