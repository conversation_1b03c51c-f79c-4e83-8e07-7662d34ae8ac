<template>
  <view>
    <picker :mode="mode" :start="start" :end="end" v-model="selected" :value="initialIndex" :range="range" @change="onChange">
    <!-- <picker :mode="mode"  v-model="selected" :value="initialIndex" :range="range" @change="onChange"> -->
      <view class="picker">
          <view class="picker-l">{{ label }}</view>
          <view class="picker-r">
            <view v-if="mode=='time'" :class="isActive ?'time-text text':'text'">{{ selectedValue }}</view>
            <view v-else :class="isActive && selectedValue!='请选择' ? 'text active' :'text'">{{ selectedValue }}</view>
            <uni-icons style="margin-left: 10rpx;" :size="14" color="#b3b3b3" type="right" />
          </view>
      </view>
    </picker>
  </view>
</template>
 
<script>
import UniIcons from '@/components/uni/uni-icons/uni-icons'
export default {
  components: {
    UniIcons,
  },
  props: {
    mode:{
      type:String,
      default: 'selector'
    },
    range: {
      type: Array,
      default: () => null
    },
    value: {
      type: [String,Number],
      default: ''
    },
    label:{
      type: String,
      default: ''
    },
    start: {
      type: String,
    },
    end: {
      type: String,
    }
  },
  data() {
    return {
      selected: 0, // 当前选中的索引  
      initialIndex: 0, // 初始索引  
      selectedValue: this.value, // 当前显示的值 
      isActive:false,
    };
  },
  watch: {
    // value(){
    //   console.log(this.value,'value000000')
    //   // const index = this.range.findIndex(item=>item === this.value)
    //   // if(index !== -1){
    //   //   this.initialIndex = index;  
    //   //   this.selected = index;  
    //   //   this.selectedValue = this.value;  
    //   // }
    //   this.isActive = true
    //   this.selectedValue = this.value;  

    // },
    value:{
      handler() {
        this.$nextTick(()=>{
          console.log(this.value,'value000000')
          this.isActive = true
          this.selectedValue = this.value;  
        })
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    onChange(e) {
      console.log(e,'e----')
      let val = null
      if(this.range && this.range.length > 0){
        val = this.range[e.detail.value]
      } else {
        val = e.detail.value
      }
      this.selectedValue = val;
      this.$emit('input', val);
    },

    //获取当前的时间
    getCurrentformatDate(date) {  
      const year = date.getFullYear();  
      const month = this.padZero(date.getMonth() + 1); // 月份是从0开始的，所以要+1  
      const day = this.padZero(date.getDate());  
      return `${year}-${month}-${day}`;  
    },  
    padZero(num) {  
      return num < 10 ? '0' + num : num;  
    },  
  }
};
</script>
 
<style lang='scss' scoped>
.picker {
  display: flex;
  justify-content: space-between;
  align-items: center;
  // font-size: 30rpx;
  .picker-l{}
  .picker-r{
    display: flex;
    align-items: center;
    padding-right: 30rpx;
    .text{
      color: #b3b3b3;
    }
    .active{
      color: #575757;
    }
    .time-text{
      color: #09a07d;
      font-weight: 700;
      padding: 8rpx;
      border-radius: 13rpx;
      background: #e7f7f3;
      border: 1rpx solid #7dd5c0;
    }
  }
}
</style>