// 初始路径
let original = 'https://file.greenboniot.cn/static/image/business/accompany-doctor';
// 默认icon路径
let iconNameList = [
  'icon-home-active.png',
  'icon-accompany-bottom-service-active.png',
  'icon-accompany-bottom-order-active.png',
  'icon-my-active.png'
]
// 换肤颜色
let skinColorList = ['greenSkin','blueSkin']
let tabBarList = skinColor=>iconNameList.map((icon,index)=>({
      selectedIconPath:skinColor ? `${original}/${skinColor}-${icon}` : `${original}/${icon}`,
      index
    }))
// 换肤tab配置
let skinColorTabOptions = {
  // 绿色皮肤
  greenSkin:{
    selectedColor:'#00D29D',
    tabBarList:()=>tabBarList()
  },
  // 蓝色皮肤 字体选中颜色 tabbaricon配置
  blueSkin:{
    selectedColor:'#489DF0',
    tabBarList
  }
}
export {skinColorList,skinColorTabOptions}
