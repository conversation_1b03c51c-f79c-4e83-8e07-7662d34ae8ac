<!-- 列表list组件 <list label="label" :allLine="allLine" :background="background">
          <view slot="l-left">1</view>
          <view slot="l-right">2</view>
          <view slot="l-bottom">3</view>
          <view slot="r-left">4</view>
          <view slot="value">5</view>
          <view slot="r-right">6</view>
        </list> -->
<template>
  <view
    class="list"
    :style="{ background: background }"
    :class="{ bdb: allLine && border, disabled: disabled }"
    @click="clickFn('list')"
  >
    <view
      class="list-body"
      :style="{ alignItems: rightPosition }"
      :class="{ bdb: !allLine && border }"
    >
      <view class="list-l">
        <view class="list-l-t">
          <slot name="avatar" />
          <view class="list-l-t-text">
            <slot name="l-left" />
            <view class="label" @click="clickFn('label')" :style="labelStyle">{{
              label
            }}</view>
            <slot name="l-right" />
          </view>
        </view>
      </view>
      <view class="list-r">
        <slot name="r-left" />
        <slot name="value" />
        <slot name="r-right" />
      </view>
    </view>
    <view class="list-b" :class="descClass" @click="clickFn('desc')">
      <span v-if="desc">{{ desc }}</span>
      <slot name="l-bottom" />
    </view>
  </view>
</template>

<script>
export default {
  props: {
    label: {
      type: String,
      default: ''
    },
    background: {
      type: String,
      default() {
        return '#fff'
      }
    },
    disabled: {
      type: Boolean,
      default: false
    },
    border: {
      type: Boolean,
      default: true
    },
    descClass: {
      type: String,
      default: ''
    },
    desc: {
      type: String,
      default: null
    },
    allLine: {
      type: Boolean,
      default: false
    },
    rightPosition: {
      type: String,
      default: 'center'
    },
    labelStyle: {
      type: Object,
      default: function () {
        return {}
      }
    },
    cData: {
      type: Object,
      default: function () {
        return {}
      }
    }

  },
  methods: {
    clickFn(string) {
      this.$emit(`${string}Click`, this.cData)
    }

  }
}
</script>

<style lang="scss" scoped>
.allLine {
  border-bottom: 2upx solid #E4E4E4;
}
.disabled {
  background-color: rgba(255, 255, 255, 0.5) !important;
  opacity: 0.5 !important;
}
.list {
  .list-body {
    margin: 0 30upx;
    display: flex;
    // align-items: center;
    justify-content: space-between;

    .list-l {
      max-width: 75%;
      .list-l-t {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        .list-l-t-text {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          .label {
            color: #333333;
            font-size: 32upx;
            height: 88upx;
            line-height: 88upx;
            display: inline-block;
            vertical-align: middle;
            @include ellipsis(1);
          }
        }
      }
    }
    .list-r {
      min-height: 88upx;
      display: flex;
      align-items: center;
      justify-content: flex-end;
    }
  }
  .list-b {
    margin: 0 30upx;
    span {
      line-height: 44upx;
      margin-bottom: 22upx;

      color: #333333;
      font-size: 32upx;
      height: 44upx;
      display: inline-block;
      vertical-align: middle;
      @include ellipsis(1);
    }
  }
}
</style>
