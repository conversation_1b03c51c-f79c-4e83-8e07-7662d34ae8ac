<template>
    <uniPopup type="bottom" ref='uniPopup' @change="changeShow">
      <view class="confirm">
        <view class="">勾选后,本次抵扣一次</view>
        <scroll-view :scroll-top="scrollTop" scroll-y="true" class="scroll-Y" @scrolltolower="upper">
          <view v-for="(item,index) in comboDataMap" :key="item.id">
            <view class="headerTab" @click="selectIndex = index">
              <image class="serverIcon" :src="file_ctx + item.listImg" mode=""></image>
              <!-- 服务名称 -->
              <view class="serviceName">
                <view class="">{{item.comboName}}</view>
                <view class="">
                  {{item.serviceName}}剩余:{{item.remainNum}}次
                </view>
                <view class="">使用有效期:{{timestampToDateTime(item.startTime)}}~{{timestampToDateTime(item.endTime)}}</view>
              </view>
              <image v-if="selectIndex === index" class="selectBox" :src="iconPostSucess" mode=""></image>
              <view v-else class="selectBox"></view>
            </view>
          </view>
        </scroll-view>
        <!-- 底部 -->
        <view class="bottomBtn">
          <view class="comBtn" @click="selectCombo">确定</view>
        </view>
      </view>
    </uniPopup>
</template>

<script>
  import { mapState } from "vuex";
  import uniPopup from '@/components/uni/uni-popup/uni-popup.vue'
  export default{
    components: {
        uniPopup
    },
    props:{
      openFlag:{
        type:Boolean,
        default:false
      },
      comboDataMap:{
        type:Array,
        default:[]
      }
    },
    watch:{
      openFlag(n){
          if(n){
            this.$refs.uniPopup.open()
          }else{
            this.$refs.uniPopup.close()
          }
      }
    },
    data(){
      return {
        iconPostSucess: this.$static_ctx + "image/business/hulu-v2/icon-post-sucess.png",
        selectIndex:'',
        file_ctx: this.file_ctx,
      }
    },
    computed: {
      ...mapState("user", {
        accountId: (state) => state.accountId,
        fansRecord: (state) => state.fansRecord,
      }),
    },
     mounted() {
    },
    methods:{
      timestampToDateTime(timestamp) {
        // 创建一个新的Date对象，传入的时间戳是以毫秒为单位的
        var date = new Date(timestamp);
        // 获取年、月、日、时、分、秒
        var year = date.getFullYear();
        var month = ("0" + (date.getMonth() + 1)).slice(-2); // 月份是从0开始的，所以+1，并补零
        var day = ("0" + date.getDate()).slice(-2); // 获取天数，并补零
        // 返回格式化的字符串
        return `${year}-${month}-${day}`;
      },
      changeShow(res){
        !res.show && this.$emit('change',res.show)
      },
      selectCombo(options){
        this.$emit('selectCombo',this.comboDataMap[this.selectIndex]);
        this.$emit('change',false)
      },
      close(){
        this.$emit('change',false)
      },
      upper(){
        console.log('触底');
        if(this.loadEnd) return
        this.current++;
      },

    }
  }
</script>

<style lang="scss">
  .bottomBtn{
    width: 750rpx;
    height: 180rpx;
    background: #FFFFFF;
    box-shadow: 0rpx -1rpx 0rpx 0rpx #EAEBF0;
    position: fixed;
    bottom: 0;
    left: 0;
    .comBtn{
      width: 686rpx;
      height: 88rpx;
      background: #00B484;
      border-radius: 44rpx;
      text-align: center;
      line-height: 88rpx;
      font-weight: 500;
      font-size: 32rpx;
      color: #FFFFFF;
      margin: 24rpx auto;
    }
  }
  .selectBox{
    width: 40rpx;
    height: 40rpx;
    border-radius: 50%;
    border: 2rpx solid rgb(220, 221, 226);
    background-color: rgba(1, 1, 1, 0);
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 32rpx;
  }
.confirm{
  position: fixed;
  bottom: 0;
  width: 750rpx;
  height: 796rpx;
  background: #F4F6FA;
  border-radius: 24rpx 24rpx 0rpx 0rpx;
  padding: 32rpx 32rpx 88rpx 32rpx;
  box-sizing: border-box;
  .headerTab{
    position: relative;
    width: 686rpx;
    height: 192rpx;
    background: #FFFFFF;
    border-radius: 16rpx;
    padding: 24rpx;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    .serverIcon{
      width: 144rpx;
      height: 144rpx;
      background: #D8D8D8;
      border-radius: 12rpx;
      border: 1rpx solid #D9DBE0;
      margin-right: 20rpx;
    }
    .serviceName{
      font-weight: 500;
      font-size: 32rpx;
      color: #1D2029;
      .signal{
        font-weight: 400;
        font-size: 22rpx;
        color: #FF5500;
      }
      .serverNum{
        font-weight: 500;
        font-size: 36rpx;
        color: #FF5500;
      }
      .tag{
        font-weight: 400;
        font-size: 20rpx;
        color: #868C9C;
      }
    }
    .changeServer{
      width: 148rpx;
      height: 52rpx;
      background: #FFFFFF;
      border-radius: 36rpx;
      border: 1rpx solid #D9DBE0;
      margin-left: auto;
    }
  }
}
.scroll-Y{
  height: 528rpx;
}
</style>
