import request from '@/common/util/request'
import env from '@/config/env'

/**
 * 系统请求工具类，
 * 注意：API接口只做请求数据和格式化数据的事情，其他事务一概不处理
 */
export default {
  /**
   * 消息聊天列表--分页列表查询
   * @param resolve
   * @param reject
   */
  chatlistQueryPage(param) {
    const url = env.ctx + 'im/api/v1/chatlist/query/page'
    return request.postJson(url, param)
  },
  /**
   * 单聊-消息--分页列表查询
   * @param resolve
   * @param reject
   */
  singlemsgQueryPage(param) {
    const url = env.ctx + 'im/api/v1/singlemsg/query/page'
    return request.postJson(url, param)
  },
  // 聊天列表 - 不分页
  chatlistPatientQueryList (param) {
    const url = env.ctx + 'im/api/v1/chatlist/patient/query/list'
    return request.get(url, param)
  },
  // 获取就诊人档案列表
  patientinfoFindSimpleList (param) {
    const url = env.ctx + 'dm/api/v1/patientinfo/find/simple/list'
    return request.get(url, param)
  },
  // 保存就诊人档案
  patientinfoSimpleSave (param) {
    const url = env.ctx + 'dm/api/v1/patientinfo/simple/save'
    return request.postJson(url, param)
  },
  // 获取订单详情
  orderGetById (param) {
    const url = env.ctx + 'im/api/v1/order/get/by/id'
    return request.get(url, param)
  },
  // 咨询订单列表
  orderQueryConsultList (param) {
    const url = env.ctx + 'im/api/v1/order/query/consult/list'
    return request.get(url, param)
  },
  // 查询咨询系统配置
  consultingconfigQueryconfig (param) {
    const url = env.ctx + 'im/api/v1/consultingconfig/queryconfig'
    return request.get(url, param)
  },
  // 登录页面配置
  loginconfigQueryConfig (param) {
    const url = env.ctx + 'im/api/v1/loginconfig/query/config'
    return request.get(url, param)
  },
  // 查询咨询节点回复配置列表
  nodereplyconfigQueryConfigList (param) {
    const url = env.ctx + 'im/api/v1/nodereplyconfig/query/config/list'
    return request.get(url, param)
  }
}
