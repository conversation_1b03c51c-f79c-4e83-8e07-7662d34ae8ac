/**
 * meta.index 页面等级，从1开始，tabbar页为1，只有tabbar页才需配置meta.tabIndex
 * http://hhyang.cn/src/router/start/quickstart.html
 */

// import Vue from 'vue'
import { createRouter as Router } from 'uni-simple-router'

// 系统配置
import systemIndex from './modules/system/index'

// 业务配置
import businessIndex from './modules/business/index'


import directseedingIndex from './modules/directseeding/index.js'

/**
 * 合并其他业务路由
 * @type {Array}
 */
// let routeMerge = []
// routeMerge = routeMerge.concat(
//
// )
export default Router({
  platform: process.env.VUE_APP_PLATFORM,
  // loading: false,
  // encodeURI: true, // 默认为true
  // h5: {
  //   paramsToQuery: true,
  //   // vueRouterDev: true,
  //   loading: true,
  //   resetStyle: () => { // 对样式进行追加
  //     return {
  //       style: `
  // 			#router-loadding .loadding {
  // 				background-color: #f00 !important;
  // 				box-shadow: 0 0 15px #f00 !important;
  // 			}
  // 			`
  //     }
  //   }
  // },
  // loading: false,
  // routes: routeMerge
  h5: {
    paramsToQuery: true,
    loading: false,
    vueRouterDev: false,
    useUniConfig: true,
    resetStyle: () => { // 对样式进行追加
      return {
        style: `
				#router-loadding .loadding {
					background-color: #f00 !important;
					box-shadow: 0 0 15px #f00 !important;
				}
				`
      }
    }
  },
  APP: {
    loddingPageStyle: () => JSON.parse('{"backgroundColor":"#96DBF3"}'),
    loddingPageHook: (view) => {
      view.drawBitmap('/static/wait.gif', {}, {
        top: 'auto',
        left: 'auto',
        width: '200px',
        height: '200px'
      })
    },
    holdTabbarStyle: () => JSON.parse('{"backgroundColor":"#FF0000","opacity":0.2}'),
  },
  encodeURI: false,
  // #ifdef H5
  // #endif
  // #ifdef MP-WEIXIN
  // #endif
  debugger: true,
  routes: [
    ...systemIndex,
    ...businessIndex,
    ...directseedingIndex
  ]
})
// Vue.use(Router)
