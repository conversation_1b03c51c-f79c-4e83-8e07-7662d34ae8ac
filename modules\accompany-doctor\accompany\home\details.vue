<template>
<view class="page">
      <uni-nav-bar
        color="#1d2029"
        :border="false"
        :showBtnsRight='false'
        :fixed="false"
        statusBar
        title="服务信息"
        @clickLeft="back"
        left-icon="left"
        left-width="48rpx"
        right-width="100px"
        backgroundColor="background: #F4F6FA;"
      >
      </uni-nav-bar>
  <view class="" v-if="orderDetails.receiveState !== 3 && !isServer">
    <view v-if="!orderDetails.isSelf && orderDetails.mode == 1" class='warning'>
      <image class="warningImg" :src="warning" mode=""></image>
      该订单为他人订单
    </view>
    <view v-if="orderDetails.isSelf && orderDetails.mode === 1 && orderDetails.orderState === 4" class='warning'>
      <image class="warningImg" :src="warning" mode=""></image>
      该订单为指派订单，请务必接单
    </view>
    <view v-if="!isTimeHasCome" class='warning'>
      <image class="warningImg" :src="warning" mode=""></image>
      还未到服务时间
    </view>
  </view>
  <view class="pageBox">
    <view class="detailsTop">
      <view class="detailsTitle">{{orderDetails.serviceName}}</view>
      <template v-if="!isServer">
        <view class="orderTap" v-if="orderDetails.receiveState === 2 && !orderDetails.isSelf">已被接单</view>
        <view class="orderTap" v-else-if="orderDetails.receiveState === 3">已拒绝</view>
        <view
          v-if="orderDetails.receiveState === 2 && orderDetails.isSelf"
          class="orderTap"
          :style="{color:orderStateMap[orderDetails.orderState].color}"
        >
          {{orderStateMap[orderDetails.orderState].text}}
          <text v-if="orderDetails.orderState === 2">{{orderDetails.payPrice/100}}</text>
        </view>
      </template>
      <template v-if="isServer">
        <view
          class="orderTap"
          :style="{color:orderStateMap[orderDetails.orderState].color}"
        >
          <template v-if="orderDetails.orderState === 8">
            {{ getRefundDisplayText }}
          </template>
          <template v-else>
            {{orderStateMap[orderDetails.orderState].text}}
            <text v-if="orderDetails.orderState === 2">{{ orderDetails.payPrice / 100 }}</text>
          </template>
        </view>
      </template>
    </view>
    <view class="titleTimeBox">
      <view class="titleTime">服务时间</view>
      <view class="timeValue">
        {{timestampToDateTime(orderDetails.startTime)}}
        <text v-if="orderDetails.endTime">~ {{timestampToDateTime(orderDetails.endTime)}}</text>
      </view>
    </view>
    <view class="infoBox" v-if="orderDetails.orderState === 7 && orderDetails.star">
      <view class="service-read">
         <view class="evaluations">服务评价</view>
        <view>
          <view  v-if="orderDetails.star == 1"><image class="img evaluate-img1" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-very-evaluate1.png'" ></image></view>
          <view  v-if="orderDetails.star == 2"><image class="img evaluate-img2" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-very-evaluate2.png'" ></image></view>
          <view  v-if="orderDetails.star == 3"><image class="img evaluate-img3" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-very-evaluate3.png'" ></image></view>
          <view  v-if="orderDetails.star == 4"><image class="img evaluate-img4" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-very-evaluate4.png'"></image></view>
          <view  v-if="orderDetails.star == 5"><image class="img evaluate-img5" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-very-evaluate5.png'" ></image></view>
        </view>
      </view>
      <view class="service-comment">
        {{ orderDetails.comment}}
      </view>
    </view>
   <view class="infoBox" v-if="shouldShowInfoBox()">
      <view class="infoTitle">{{ orderDetails.mode === 1 ? '指派陪诊师' : '抢单' }}</view>
      <view class="employee">
        <text class="tabTitle">陪诊师</text>:
        <image class="avatar" :src="file_ctx + avatar" mode=""></image>
        {{orderDetails.employeeName}}
        </view>
    </view>

    <view class="infoBox" v-if="orderDetails.orderState === 8">
      <view class="infoTitle">取消原因</view>
      <view>{{orderDetails.cancelReason}}</view>
    </view>

    <view class="infoBox">
      <view class="infoTitle">预约信息</view>
      <template v-for="(item,index) in reserveInfoMap">
        <view class="infoItemBox" v-if="item.type === 'text' && orderDetails[item.value]">
          <view class="infoItemBoxTitle">{{item.title}}</view>
          <view class="infoItemBoxValue">{{orderDetails[item.value]}}</view>
        </view>
        <view class="infoItemBox" v-if="item.type === 'remark' && orderDetails.orderState === 5 && orderDetails.source === 1">
          <view class="infoItemBoxTitle">{{item.title}}</view>
          <view class="infoItemBoxValue">{{orderDetails[item.value]}}</view>
        </view>
        <view class="infoItemBox" v-if="item.type === 'imgMap' && orderDetails[item.value]">
          <view class="infoItemBoxTitle">补充内容</view>
          <view class="infoImageMap">
            <image class="infoImage" v-for="(item,index) in backupImg" :key="index" @click="previewImage(file_ctx + item)" :src="file_ctx + item" mode=""></image>
          </view>
        </view>
      </template>
    </view>
    <!-- 服务记录 -->
    <view class="service-record"  v-if="orderDetails.orderState >= 6 && orderDetails.orderState !== 8">
    <view class="infoTitle">服务记录</view>
      <view>
        <view class="clock-start" v-for="(item,index) in clockMap" :key="index">
          <view class="clock-start-h">
            <view class="clock-start-h-l">
              <view class="time">{{timestampToDateTime(item.createTime)}}</view>
              <view class="title">
                {{['签到','接到客户','签出'][index]}}打卡，{{ index === 2 ? '服务结束' : '开始服务' }}
              </view>
            </view>
          </view>
          <view class="clock-start-b" v-if="item.imagePath">
            <image :src="file_ctx + imageItem" class="clock-start-item" v-for="imageItem in item.imagePath.split(',')" :key="imageItem"></image>
          </view>
        </view>

        <!-- 医嘱反馈 -->
        <view class="clock-start medical-advice" v-if="medicalAdvice">
          <view class="clock-start-h">
            <view class="clock-start-h-l">
              <view class="time time-title">
                {{timestampToDateTime(medicalAdvice.createTime)}}
                <image class="edit-icon" :src="file_ctx + 'static/image/business/hulu-v2/edit-icon.png'" @click="editMedicalAdvice" v-if="isServer || orderDetails.isSelf"></image>
              </view>
              <view class="advice-wrapper">
                <view class="advice-content-box" :class="{'expanded': isAdviceExpanded}">
                  <text class="title-prefix">医嘱反馈：</text>
                  <text class="advice-content">{{medicalAdvice.advice}}</text>
                  <view class="expand-btn" v-if="shouldShowExpandBtn && !isAdviceExpanded" @click="isAdviceExpanded = true">
                    <text>展开</text>
                    <!-- <image class="expand-icon" :src="file_ctx + 'static/image/business/hulu-v2/expand-down.png'"></image> -->
                  </view>
                  <view class="collapse-btn" v-if="isAdviceExpanded" @click="isAdviceExpanded = false">
                    <text>收起</text>
                    <!-- <image class="expand-icon" :src="file_ctx + 'static/image/business/hulu-v2/expand-up.png'"></image> -->
                  </view>
                </view>
              </view>
            </view>
          </view>
          <view class="clock-start-b" v-if="medicalAdvice.image">
            <image :src="file_ctx + imageItem" class="clock-start-item" v-for="(imageItem, idx) in medicalAdvice.image.split(',')" :key="idx"></image>
          </view>
        </view>
      </view>
    </view>
    <view class="infoBox" @click="goToAccompanyRecord" v-if="[5, 6, 7].includes(orderDetails.orderState) && orderDetails.patientFileSee !== 3 && !res.from">
      <view class="record-box">
        <view class="record-title">就诊人陪诊记录</view>
        <image class="arrow-icon" :src="file_ctx + 'static/image/business/hulu-v2/recoder-right.png'" mode="aspectFit"></image>
      </view>
    </view>
    <view class="infoBox">
      <view class="infoTitle">订单信息</view>
      <view class="infoItemBox">
        <view class="infoItemBoxTitle">订单号</view>
        <view class="infoItemBoxValue">{{orderDetails.id}}<text class="copy" @click='copyOrder(orderDetails.id)'>复制</text></view>
      </view>
      <view class="infoItemBox">
        <view class="infoItemBoxTitle">服务项目</view>
        <view class="infoItemBoxValue">{{orderDetails.serviceName}}</view>
      </view>
      <!-- <view class="infoItemBox" v-if="orderDetails.dispatchTime">
        <view class="infoItemBoxTitle">派单时间</view>
        <view class="infoItemBoxValue">{{timestampToDateTime(orderDetails.dispatchTime)}}</view>
      </view>
      <view class="infoItemBox" v-if="orderDetails.startTime">
        <view class="infoItemBoxTitle">创建时间</view>
        <view class="infoItemBoxValue">{{timestampToDateTime(orderDetails.createTime)}}</view>
      </view>
      <view class="infoItemBox">
        <view class="infoItemBoxTitle">订单类型</view>
        <view class="infoItemBoxValue">自营订单</view>
      </view>
      <view class="infoItemBox" v-if="orderDetails.mode">
        <view class="infoItemBoxTitle">派单模式</view>
        <view class="infoItemBoxValue">{{['','指定派单','抢单'][orderDetails.mode]}}</view>
      </view>
      <view class="infoItemBox" v-if="orderDetails.payType">
        <view class="infoItemBoxTitle">支付方式</view>
        <view class="infoItemBoxValue">{{orderDetails.comboPay ? '套餐抵扣' : '微信支付'}}</view>
      </view>
      <view class="infoItemBox" v-if="orderDetails.orderState !== 8">
        <view class="infoItemBoxTitle">支付金额</view>
        <view class="infoItemBoxValue">￥{{orderDetails.payPrice/100}}</view>
      </view>
      <view class="infoItemBox" v-if="orderDetails.payTime">
        <view class="infoItemBoxTitle">支付时间</view>
        <view class="infoItemBoxValue">{{timestampToDateTime(orderDetails.payTime)}}</view>
      </view> -->
    </view>
    <!-- 拒绝状态下不展示按钮 -->
    <view class="bottomBtn" v-if="(isServer || orderDetails.receiveState !== 3) && orderDetails.orderState !== 7 && (isServer && orderDetails.orderState < 6 || isTimeHasCome) && (res.from !== 'myCustomer')">
      <!-- 小按钮集合 -->
      <view class="miniBtnMap" v-if="isServer">
        <view class="miniBtn" @click="openModel(item.type)" v-for="item in getMinBtnMap" :key="item">
          <image class="miniBtnIcon" :src="item.icon" mode=""></image>
          <view class="miniBtnTitle">{{item.title}}</view>
        </view>
      </view>
      <view class="miniBtnMap" v-if="orderDetails.orderState == 4 && orderDetails.mode == 1 && !isServer">
        <view class="miniBtn clearBtn" @click="rejectOrder">
          <view class="miniBtnTitle">拒绝</view>
        </view>
      </view>
      <template v-if="isServer">
        <view class="clockIn" @click="openModel('server')" v-if="orderDetails.orderState == 1">创建服务单</view>
        <view class="clockIn" @click="openModel('code')" v-if="orderDetails.orderState == 2">订单二维码</view>
        <view class="clockIn" @click="openModel('dispatcher')" v-if="orderDetails.orderState == 3 && serverOptionsSource !== 1">派单</view>
        <view class="clockIn" @click="openModel('dispatcher')" v-if="orderDetails.orderState == 4 && serverOptionsSource !== 1">重新派单</view>
        <view class="clockIn" @click="openModel('clear')" v-if="orderDetails.orderState == 5 && serverOptionsSource !== 1">取消订单</view>
        <view class="clockIn" @click="openModel('finish')" v-if="orderDetails.orderState == 6 && serverOptionsSource !== 1">结束服务</view>
      </template>
      <template v-if="!isServer">
        <view class="clockIn" @click="meetOrder" v-if="orderDetails.orderState == 4 && orderDetails.mode == 1">接单</view>
        <view class="clockIn" @click="robOrder" v-if="orderDetails.orderState == 4 && orderDetails.mode == 2">抢单</view>
        <view class="clockIn" @click="openFlag = true" v-if="getClockInType === 1 && orderDetails.isSelf && (orderDetails.orderState == 5 || orderDetails.orderState == 6)">签到打卡</view>
        <view class="clockIn" @click="openFlag = true" v-if="getClockInType === 4 && orderDetails.isSelf && (orderDetails.orderState == 5 || orderDetails.orderState == 6)">接到客户打卡</view>
        <view class="clockIn" @click="openFlag = true" v-if="getClockInType === 2 && orderDetails.isSelf && (orderDetails.orderState == 5 || orderDetails.orderState == 6)">结束服务</view>
      </template>
    </view>

    <!-- 已完成状态下显示上传诊断报告按钮 -->
    <view class="bottomBtn" v-if="orderDetails.orderState === 7 && (isServer || orderDetails.isSelf)">
      <!-- 当两个按钮都存在时，并排显示 -->
      <view class="dual-buttons" v-if="showUploadMedicalAdviceBtn && showRefundButton">
        <view class="clockIn half-width" @click="showMedicalAdvicePopup = true">上传诊断报告</view>
        <view class="clockIn half-width refund-btn" @click="openRefundModal">订单退款</view>
      </view>
      <!-- 只有上传诊断报告按钮时 -->
      <view class="clockIn" v-else-if="showUploadMedicalAdviceBtn" @click="showMedicalAdvicePopup = true">上传诊断报告</view>
      <!-- 只有订单退款按钮时 -->
      <view class="clockIn refund-btn" v-else-if="showRefundButton" @click="openRefundModal">订单退款</view>
    </view>
  </view>
  <clockIn :openFlag='openFlag' @change='flag=>openFlag = flag' @lcockIn='lcockIn' :clockInType="getClockInType" :orderDetails='orderDetails'></clockIn>
  <modelPopup :orderDetails='orderDetails' @finish='finishModel' :modelType='modelType' @change='flag=>openFlagModel = flag' :openFlag='openFlagModel'></modelPopup>

  <!-- 医嘱反馈弹窗 -->
  <view class="medical-advice-popup" v-if="showMedicalAdvicePopup">
    <view class="medical-advice-container">
      <view class="medical-advice-header">
        <text>上传诊断报告</text>
        <image class="close-icon" :src="file_ctx + 'static/image/business/hulu-v2/icon-post-menu-close.png'" @click="showMedicalAdvicePopup = false"></image>
      </view>
      <view class="medical-advice-body">
        <view class="medical-advice-title">医嘱反馈</view>
        <textarea
          class="medical-advice-textarea"
          placeholder="请输入医嘱内容（1000字以内）"
          :value="adviceForm.advice"
          @input="handleAdviceInput"
          @focus="handleAdviceFocus"
          :maxlength="10000"
        ></textarea>
        <view class="text-count" :class="{'text-count-exceed': adviceForm.advice.length > 1000}">{{adviceForm.advice.length}}/1000</view>

        <!-- 使用滚动区域包裹图片上传组件 -->
        <scroll-view class="images-scroll-container" scroll-y>
          <!-- 使用formList组件处理图片上传 -->
          <formList
            :serverMap="adviceImageMap"
            @emitChange="emit"
            @previewImage="previewImage"
            @closeImgage="closeImgage"
          ></formList>
        </scroll-view>
      </view>
      <view class="medical-advice-footer">
        <view class="submit-btn" @click="submitMedicalAdvice">提交</view>
      </view>
    </view>
  </view>

  <!-- 隐藏的上传组件 -->
  <view class="upload-hidden-container">
    <title-img :config="{hideBaseView: true, multiSelectCount: 18}" ref="upDataImage" @returnFn="imgReturnFn" :cData="queryOptions.imageObj"></title-img>
  </view>

  <!-- 退款弹窗 -->
  <view class="refund-popup" v-if="showRefundPopup">
    <view class="refund-modal">
      <view class="refund-header">
        <text class="refund-title">订单退款</text>
        <view class="refund-close" @click="showRefundPopup = false">
          <image :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-service-error.png'"></image>
        </view>
      </view>
      <view class="refund-content">
        <!-- 服务费显示 -->
        <view class="refund-item">
          <text class="refund-label">服务费</text>
          <text class="refund-value">￥{{orderDetails.payPrice / 100}}</text>
        </view>
        <!-- 退款原因 -->
        <view class="refund-item">
          <text class="refund-label required">退款原因</text>
        </view>
        <textarea
          class="refund-reason-input"
          v-model="refundReason"
          placeholder="请输入退款原因（必填）"
          maxlength="200"
        ></textarea>
        <!-- 退款金额 -->
        <view class="refund-item">
          <text class="refund-label required">退款金额</text>
          <text class="refund-hint">（单位：元，最大退款金额：{{orderDetails.payPrice / 100}}元）</text>
        </view>
        <input
          class="refund-amount-input"
          type="digit"
          v-model.number="refundAmount"
          placeholder="请输入退款金额"
          :max="orderDetails.payPrice / 100"
          step="0.01"
        />
      </view>
      <view class="refund-footer">
        <button class="refund-cancel-btn" @click="showRefundPopup = false">取消</button>
        <button class="refund-confirm-btn" @click="confirmRefund">确认退款</button>
      </view>
    </view>
  </view>
</view>
</template>

<script>
  import uniNavBar from '@/components/uni/uni-nav-bar/uni-nav-bar.vue'
  import clockIn from '../../components/clockIn.vue'
  import modelPopup from './components/model.vue'
  import formList from './components/formList.vue'
  import TitleImg from "@/components/business/module/title-img/index.vue"
  import common from '@/common/util/main'
  import serverOptions from '@/config/env/options'
  export default{
    data(){
      return {
        file_ctx:this.file_ctx,
        openFlag:false,
        orderDetails:{},
        orderStateMap:[
          '',
          {text:'等待人工导诊',color:'#1687F7'},
          {text:'待支付￥',color:'#FF5500'},
          {text:'待派单',color:'#1687F7'},
          {text:'待接单',color:'#1687F7'},
          {text:'待服务',color:'#00B484'},
          {text:'服务中',color:'#00B484'},
          {text:'已完成',color:'#1D2029'},
          {text:'已取消',color:'#868C9C'},
        ],
        minBtnMap:[
          {title:'取消订单',icon:'clipboardClose',type:'clear',showMap:[1,2,3,4]},
          {title:'更改订单',icon:'refresh',type:'refresh',showMap:[2]},
          {title:'更改预约',icon:'refresh',type:'refreshOrder',showMap:[3,4,5]},
          {title:'更改陪诊师',icon:'Vector',type:'Vector',showMap:[5]},
        ],
        res:{},
        isTimeHasCome:true,
        clockMap:[],
        warning: this.$static_ctx + "image/business/hulu-v2/warning.png",
        clipboardClose: this.$static_ctx + "image/business/hulu-v2/clipboard-close.png",
        refresh: this.$static_ctx + "image/business/hulu-v2/refresh-2.png",
        Vector: this.$static_ctx + "image/business/hulu-v2/Vector.png",
        isServer:false,
        backupImg:[],
        openFlagModel:false,
        modelType:'',
        avatar:'',
        reserveInfoMap:[
          {title:'预约人',value:'bookName',type:'text'},
          {title:'预约人电话',value:'bookPhone',type:'text'},
          {title:'就诊人医院',value:'hospitalName',type:'text'},
          {title:'补充内容',value:'backupImg',type:'imgMap'},
          {title:'平台备注',value:'remark',type:'remark'},
          {title:'备注',value:'remark',type:'text'},
          {title:'取消原因',value:'cancelReason',type:'text'},
        ],
        userInfo:{},
        currentProvider:{},
        // 医嘱相关数据
        medicalAdvice: null,
        isAdviceExpanded: false,
        showMedicalAdvicePopup: false,
        adviceForm: {
          advice: '',
          imageList: []
        },
        // 图片上传配置
        adviceImageMap: [
          {
            title: '上传图片',
            type: 'image',
            textType: 'adviceImage',
            value: []
          }
        ],
        queryOptions: {
          imageObj: []
        },
        isEditingAdvice: false,
        // 退款相关数据
        showRefundPopup: false,
        refundReason: '',
        refundAmount: '',
        // 是否从陪诊师端进入
        fromProvider: false
      }
    },
    components: {
      uniNavBar,
      clockIn,
      modelPopup,
      formList,
      TitleImg
    },
    async onLoad(res) {
      this.res = res;
      this.isServer = res.isServer;
      this.fromProvider = res.fromProvider === 'true' || res.fromProvider === true;
      const userId = serverOptions.getUserId(this);
      this.currentProvider = (await this.$api.accompanyDoctor.accompanyproviderQueryOne({id:serverOptions.providerId})).data
      if(!userId){
        uni.showToast({title:'请先登录',icon:'none'})
        return;
      }
      uni.showLoading({title:'加载中'})
      let {data} = await this.$api.accompanyDoctor.getAccompanyemployeeOneByUserId({userId})
      this.userInfo = data;
      await this.getLoadData()
      await this.getMedicalAdvice()
      uni.hideLoading()
    },
    computed:{
      serverOptionsSource(){
        return serverOptions.source;
      },
      getClockInType(){
        let typeMap = [1,4,2];
        if(!typeMap[this.clockMap.length]) return 0
        return typeMap[this.clockMap.length]
      },
      getMinBtnMap(){
        return this.minBtnMap.filter(e=>{
          return e.showMap.indexOf(this.orderDetails.orderState) >= 0
        }).map(e=>{
          let o = {...e};
          o.icon = this[o.icon]
          return o
        })
      },
      shouldShowExpandBtn() {
        // 判断文本是否超过4行
        return this.medicalAdvice && this.medicalAdvice.advice && this.medicalAdvice.advice.length > 80;
      },
      showUploadMedicalAdviceBtn() {
        return this.orderDetails.orderState === 7 && this.clockMap.length === 3 && (this.isServer || this.orderDetails.isSelf) && !this.medicalAdvice;
      },
      // 判断是否显示退款按钮
      showRefundButton(){
        // 订单状态：已完成状态(orderState=7)
        // 订单类型：自营订单(source === 2)
        // 非联合订单：没有联合订单ID(!combineOrderId)
        return this.orderDetails.orderState === 7 &&
               this.orderDetails.source === 2 &&
               !this.orderDetails.combineOrderId &&
               !this.fromProvider;
      },
      // 获取退款显示文本
      getRefundDisplayText(){
        // 如果有refundAmount字段
        if (this.orderDetails.refundAmount && this.orderDetails.refundAmount > 0) {
          return `已取消，已退款 ￥${this.orderDetails.refundAmount / 100}`;
        }
        // 如果没有refundAmount但有payPrice和payTime
        if (this.orderDetails.payPrice > 0 && this.orderDetails.payTime) {
          return `已取消，已退款 ￥${this.orderDetails.payPrice / 100}`;
        }
        // 其他情况显示已取消
        return '已取消';
      }
    },
    methods:{
      // 处理各种formList事件
      emit(type) {
        console.log('触发',type);
        if (type === 'adviceImage') {
          // 调用上传组件
          this.$refs.upDataImage.uploadImage();
        }
      },

    // 处理图片上传后的回调
    imgReturnFn(imageObj) {
      let oldValue = this.adviceImageMap[0].value;

      // 将新上传的图片添加到 adviceImageMap 中
      oldValue.push(...imageObj.map(e => e.filePath));

      // 去重处理
      this.adviceImageMap[0].value = [...new Set(oldValue)];

      this.queryOptions.imageObj = imageObj.filter(img =>
        this.adviceImageMap[0].value.includes(img.filePath)
      );
    },

      // 图片预览
      previewImage(url) {
        uni.previewImage({urls:[url]});
      },

      // 图片删除
      closeImgage(index) {
        // 保存要删除的图片路径
        const deletedImagePath = this.adviceImageMap[0].value[index];

        // 从 adviceImageMap 中删除
        this.adviceImageMap[0].value.splice(index, 1);

        // 从 queryOptions.imageObj 中也删除对应的图片
        if (this.queryOptions.imageObj && this.queryOptions.imageObj.length > 0) {
          this.queryOptions.imageObj = this.queryOptions.imageObj.filter(
            img => img.filePath !== deletedImagePath
          );
        }
      },

      // 获取医嘱信息
      async getMedicalAdvice() {
        try {
          let { data } = await this.$api.accompanyDoctor.accompanyadviceQueryAccompany({
            accompanyId: this.res.id
          });
          if (data) {
            this.medicalAdvice = data;
          }
        } catch (error) {
          console.error('获取医嘱失败', error);
        }
      },

      // 编辑医嘱
      editMedicalAdvice() {
        this.isEditingAdvice = true;
        this.adviceForm.advice = this.medicalAdvice.advice || '';

        // 处理现有图片
        if (this.medicalAdvice.image) {
          this.adviceImageMap[0].value = this.medicalAdvice.image.split(',');
        } else {
          this.adviceImageMap[0].value = [];
        }

        this.showMedicalAdvicePopup = true;
      },

      // 处理医嘱输入焦点
      handleAdviceFocus() {
        // 检查登录状态或执行其他需要的操作
        console.log('医嘱输入框获得焦点');
      },

      // 处理医嘱输入
      handleAdviceInput(e) {
        const newValue = e.detail.value;
        this.adviceForm.advice = newValue;

        // 如果超过字符限制，显示提示
        if (newValue.length > 1000) {
          uni.showToast({
            title: `已超过1000个字符限制，提交时将无法通过验证`,
            icon: 'none',
            duration: 2000
          });
        }
      },

      // 提交医嘱
      async submitMedicalAdvice() {
        if (!this.adviceForm.advice.trim()) {
          uni.showToast({
            title: '请输入医嘱内容',
            icon: 'none'
          });
          return;
        }

        // 检查字符数是否超过限制
        if (this.adviceForm.advice.length > 1000) {
          uni.showToast({
            title: '医嘱内容不能超过1000个字符',
            icon: 'none'
          });
          return;
        }

        uni.showLoading({title: '正在提交...'});

        try {
          const params = {
            accompanyId: this.orderDetails.id,
            advice: this.adviceForm.advice,
            image: this.adviceImageMap[0].value.join(',')
          };

          if (this.isEditingAdvice && this.medicalAdvice && this.medicalAdvice.id) {
            // 更新现有医嘱
            params.id = this.medicalAdvice.id;
            await this.$api.accompanyDoctor.accompanyadviceUpdate(params);
          } else {
            // 创建新医嘱
            await this.$api.accompanyDoctor.accompanyadviceInsert(params);
          }

          uni.showToast({
            title: this.isEditingAdvice ? '更新成功' : '提交成功',
            icon: 'success'
          });

          this.showMedicalAdvicePopup = false;
          this.isEditingAdvice = false;
          this.adviceForm.advice = '';
          this.adviceImageMap[0].value = [];

          // 重新获取医嘱数据
          await this.getMedicalAdvice();
        } catch (error) {
          console.error('提交医嘱失败', error);
          uni.showToast({
            title: '提交失败，请重试',
            icon: 'none'
          });
        } finally {
          uni.hideLoading();
        }
      },
      previewImage(url){
        uni.previewImage({
        			urls: [url],
        })
      },
      shouldShowInfoBox () {
        const { orderState, mode } = this.orderDetails
        console.log('orderState', orderState);
        console.log('orderState', this.orderDetails);
        return this.avatar && (
          [5, 6, 7].includes(orderState) || (orderState === 4 && mode === 1)
        )
      },
      copyOrder(data){
        uni.setClipboardData({
          data,
          success: () =>
            uni.showToast({
              title: '复制成功'
            })
        })
      },
      // 拒绝接单
      async rejectOrder(){
        let queryOptions = {
          id:this.orderDetails.id,
          employeeId:this.userInfo.id
        }
        let {data:{records,total}} = await this.$api.accompanyDoctor.accompanybookReject(queryOptions);
        uni.showToast({
          title:'拒绝成功',
          icon:'none'
        })
        this.back()
      },
      async meetOrder(){
        let queryOptions = {
          id:this.orderDetails.id,
          employeeId:this.userInfo.id
        }
        let {data:{records,total}} = await this.$api.accompanyDoctor.accompanybookAccept(queryOptions);
        uni.showToast({
          title:'接单成功',
          icon:'none'
        })
        this.back()
      },
      async robOrder(){
        const codeUserInfo = common.getKeyVal('user', 'codeUserInfo',true);
        let queryOptions = {
          id:this.orderDetails.id,
          employeeId:this.userInfo.id
        }
        let {data:{records,total}} = await this.$api.accompanyDoctor.accompanybookCompete(queryOptions);
        uni.showToast({
          title:'抢单成功',
          icon:'none'
        })
        this.back()
      },
      finishModel(){
        console.log('触发弹窗完成');

        this.getLoadData()
      },
      openModel(type){
        if(type === 'code' && this.currentProvider.poster){
          return this.$navto.push('accompanyPosterLogo',{
            orderId: this.orderDetails.id,
            bookName: this.orderDetails.bookName,
            serviceName: this.orderDetails.serviceName,
            payPrice: this.orderDetails.payPrice,
            providerId: this.providerId,
          })
        }
        this.modelType = type;
        this.openFlagModel = true;
      },
      async getLoadData(){
        let {data} = await this.$api.accompanyDoctor.getAccompanybookOne(this.res);
        this.orderDetails = data;
        this.backupImg = data.backupImg.split(',')
        // 判断为签到还是签入
        let newDate = +new Date();
        let futureTime = this.getClockInType === 1 ? this.orderDetails.startTime : this.orderDetails.endTime;
        // 如果未来时间减去当前时间 小于三小时 那么说明时间已到可以打卡了
        // this.isTimeHasCome = (futureTime - newDate) <= 1000 * 60 * 60 * 3;
        // 获取打卡记录
        let {data:clockMap} = await this.$api.accompanyDoctor.signinlogGetLogList({
          businessType:4,
          businessId:this.res.id
        })
        this.clockMap = clockMap;
        if(!this.orderDetails.employeeId) return
        let {data:{avatar}} = await this.$api.accompanyDoctor.getAccompanyemployeeQueryOne({
          id:this.orderDetails.employeeId
        })
        this.avatar = avatar;
      },
      handleCopyOrder(){
        uni.setClipboardData({
          data: this.orderDetails.id,
          success: function () {
            uni.showToast({
              title: '复制成功',
              icon: 'success'
            });
          },
          fail: function (error) {
            // console.error('复制失败', error);
            uni.showToast({
              title: '复制失败',
              icon: 'none'
            });
          }
        });
      },
      async lcockIn(err){
        // 显示加载中提示
        uni.showLoading({title: '加载中'})

        try {
          // 如果有错误对象被传入但有模拟位置数据，仍然继续流程
          if(err && err.latitude && err.longitude) {
            console.log('使用模拟位置继续流程:', JSON.stringify(err))
          }
          // 如果有错误对象被传入且没有模拟位置，显示错误
          else if(err) {
            console.error('打卡错误详情:', JSON.stringify(err))
            uni.showModal({
              title: '打卡失败',
              content: '无法获取位置信息，请检查定位权限并重试',
              showCancel: false
            })
            uni.hideLoading()
            return
          }

          // 获取数据后关闭弹窗
          await this.getLoadData()
          // 关闭打卡弹窗
          this.openFlag = false
        } catch (err) {
          console.error('获取数据失败:', err)
          uni.showToast({
            title: '获取数据失败',
            icon: 'none'
          })
        } finally {
          uni.hideLoading()
        }
      },
      timestampToDateTime(timestamp) {
        // 创建一个新的Date对象，传入的时间戳是以毫秒为单位的
        var date = new Date(timestamp);
        // 获取年、月、日、时、分、秒
        var year = date.getFullYear();
        var month = ("0" + (date.getMonth() + 1)).slice(-2); // 月份是从0开始的，所以+1，并补零
        var day = ("0" + date.getDate()).slice(-2); // 获取天数，并补零
        var hour = ("0" + date.getHours()).slice(-2); // 获取小时，并补零
        var minute = ("0" + date.getMinutes()).slice(-2); // 获取分钟，并补零
        var second = ("0" + date.getSeconds()).slice(-2); // 获取秒数，并补零

        // 返回格式化的字符串
        return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
      },
      back() {
        let pages = getCurrentPages() // 获取栈实例
        if (pages.length > 1) {
          this.$navto.back()
        } else {
          this.$navto.replaceAll('Index')
        }
      },
      goToAccompanyRecord(){
        this.$navto.push('accompanyRecord', {
          id: this.orderDetails.id,
          providerId: this.orderDetails.providerId,
          city: this.orderDetails.city,
          province: this.orderDetails.province,
          from: 'detail'
        });
      },
      // 打开退款弹窗
      openRefundModal() {
        this.refundReason = '';
        this.refundAmount = '';
        this.showRefundPopup = true;
      },
      // 确认退款
      async confirmRefund() {
        // 验证退款原因
        if (!this.refundReason.trim()) {
          uni.showToast({ title: '请输入退款原因', icon: 'none' });
          return;
        }

        // 验证退款金额
        const amount = Number(this.refundAmount);
        const maxRefundAmount = this.orderDetails.payPrice / 100;
        if (isNaN(amount) || amount <= 0 || amount > maxRefundAmount) {
          uni.showToast({ title: `退款金额需在0-${maxRefundAmount}元之间`, icon: 'none' });
          return;
        }

        try {
          uni.showLoading({ title: '退款中...', mask: true });

          await this.$api.accompanyDoctor.accompanybookFinishOrderRefund({
            id: this.orderDetails.id,
            refundReason: this.refundReason,
            refundAmount: Math.round(amount * 100) // 转换为分
          });

          uni.hideLoading();
          uni.showToast({ title: '退款申请提交成功', icon: 'success' });
          this.showRefundPopup = false;

          // 刷新订单数据
          await this.getLoadData();

        } catch (error) {
          uni.hideLoading();
          uni.showToast({ title: error.msg || '退款失败', icon: 'none' });
        }
      }
    }
  }
</script>

<style lang='scss'>
  .copy{
    font-weight: 400;
    font-size: 26rpx;
    color: #316EAB;
    margin-left: 8rpx;
  }
  .employee{
    font-weight: 400;
    font-size: 26rpx;
    color: #1D2029;
    display: flex;
    margin-top: 30rpx;
    align-items: center;
    .tabTitle{
      font-weight: 400;
      font-size: 26rpx;
      color: #1D2029;
    }
    .avatar{
      width: 40rpx;
      height: 40rpx;
      margin-left: 20rpx;
      border-radius: 50%;
      margin-right: 8rpx;
    }
  }
  .service-record{
    margin-top: 20rpx;
    padding:32rpx 24rpx;
    background: #FFFFFF;
    border-radius: 16rpx;
    overflow: hidden;
    .title{
      font-size: 28rpx;
      color: #1D2029;
      line-height: 40rpx;
    }
    .clock-start{
      position: relative;
      padding-left: 20rpx;
      box-sizing: border-box;
      &::before{
        content: '.';
        color: #00B484;
        font-size: 16rpx;
        border-radius: 50%;
        width: 16rpx;
        height: 16rpx;
        background: #00B484;
        box-shadow: 0 0 0 8rpx rgba(204, 240, 230, 1);
        position: absolute;
        top: 8rpx;
        left: 0;
        transform: translateX(-50%);
      }
      &:not(:last-child){
        &::after{
          content: ' ';
          height: calc(100% - 24rpx);
          width: 4rpx;
          position: absolute;
          top: 48rpx;
          left: 0;
          transform: translateX(-50%);
          background-color: #CCF0E6;
        }
      }
      &:last-of-type{
        margin-top: 40rpx;
      }
      .clock-start-h{
        display: flex;
        justify-content: space-between;
        .clock-start-h-l{
          width: 100%;
          .time{
            font-size: 28rpx;
            color: #4E5569;
            line-height: 40rpx;
          }
          .title{
            margin-top: 16rpx;
            font-size: 28rpx;
            color: #1D2029;
            line-height: 40rpx;
          }
          .time-title{
            display: flex;
            align-items: center;
            justify-content: space-between;
          }
        }
        .clock-start-h-r{
          display: flex;
          align-items: center;
          justify-content: center;
          width: 184rpx;
          height: 64rpx;
          background: #FFFFFF;
          border-radius: 8rpx;
          border: 1rpx solid #D9DBE0;
          font-size: 28rpx;
          color: #1D2029;
        }
      }
      .clock-start-b{
        display: flex;
        flex-wrap: wrap;
        padding: 16rpx;
        background: #F4F6FA;
        border-radius: 16rpx;
        overflow: hidden;
        .clock-start-item{
          width: 170rpx;
          height: 170rpx;
          border-radius: 16rpx;
          margin-right: 16rpx;
          margin-bottom: 18rpx;
          &:nth-child(3n){
            margin-right: 0;
          }
        }
      }
    }
  }

  .order-message{
    margin-top: 20rpx;
    padding:32rpx 24rpx;
    background: #FFFFFF;
    border-radius: 16rpx;
    overflow: hidden;
    .title{
      font-size: 32rpx;
      color: #1D2029;
      line-height: 44rpx;
      margin-bottom: 32rpx;
    }
    .order-num,.service-num{
      display: flex;
      align-items: center;
      justify-content: space-between;
      .num-l{
        font-size: 26rpx;
        color: #1D2029;
      }
      .num-r{
        font-size: 26rpx;
        color: #4E5569;
        span{
          font-size: 26rpx;
          color: #316EAB;
        }
      }
    }
    .service-num{
      margin: 16rpx 0;
    }
  }
  .page{
    width: 100vw;
    height: 100vh;
    top: 0;
    left: 0;
    background: #F4F6FA;
    position: fixed;
    overflow: scroll;
  }
  .pageBox{
    padding: 40rpx 32rpx 188rpx 32rpx;
    box-sizing: border-box;
  }
  .warning{
    width: 100vw;
    height: 64rpx;
    background: #FFEEE6;
    font-weight: 500;
    font-size: 24rpx;
    padding: 18rpx 32rpx;
    box-sizing: border-box;
    color: #DB4800;
    display: flex;
    align-items: center;
    .warningImg{
      width: 28rpx;
      height: 28rpx;
      margin-right: 8rpx;
    }
  }
  .detailsTop{
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    .detailsTitle{
      flex: 1;
      font-weight: 500;
      font-size: 36rpx;
      color: #1D2029;
      line-height: 50rpx;
      text-align: left;
      font-style: normal;
      margin-right: 20rpx;
      /* 限制最多显示2行，超出显示省略号 */
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
    }
    .orderTap{
      font-weight: 500;
      font-size: 32rpx;
      color: #00B484;
      flex-shrink: 0;
      min-width: 120rpx;
      text-align: right;
    }
  }
  .titleTimeBox{
    margin: 8rpx 0 40rpx 0;
    display: flex;
    .titleTime{
      width: 130rpx;
      height: 36rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 26rpx;
      color: #4E5569;
      line-height: 36rpx;
      text-align: left;
      font-style: normal;
    }
    .timeValue{
      height: 36rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 26rpx;
      color: #1D2029;
      line-height: 36rpx;
      text-align: left;
      font-style: normal;
    }
  }
  .infoTitle{
    font-weight: 600;
    font-size: 32rpx;
    color: #1D2029;
    margin-bottom: 32rpx;
  }
  .infoBox{
    margin-top: 20rpx;
    width: 686rpx;
    background: #FFFFFF;
    border-radius: 16rpx;
    padding: 32rpx 24rpx;
    box-sizing: border-box;

    .infoItemBox{
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 16rpx;
      .infoItemBoxTitle{
        font-weight: 400;
        font-size: 26rpx;
        color: #1D2029;
        flex-shrink: 0;
        min-width: 120rpx;
      }
      .infoItemBoxValue{
        flex: 1;
        font-weight: 400;
        font-size: 26rpx;
        color: #4E5569;
        text-align: right;
        margin-left: 20rpx;
        /* 限制最多显示3行，超出显示省略号 */
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all;
        line-height: 1.4;
      }
      .infoImageMap{
        width: 502rpx;
        display: flex;
        flex-wrap: wrap;
        column-gap: calc((502rpx - 112rpx * 4) / 3);
        row-gap: 10rpx;
        justify-content: flex-end;
        .infoImage{
          width: 112rpx;
          height: 112rpx;
          border-radius: 12rpx;
        }
      }
    }
  }
  .bottomBtn{
    position: fixed;
    bottom: 0;
    left: 0;
    width: 750rpx;
    height: 188rpx;
    background: #FFFFFF;
    box-shadow: 0rpx -1rpx 0rpx 0rpx #EAEBF0;
    padding: 20rpx 30rpx 80rpx;
    box-sizing: border-box;
    display: flex;
    .miniBtnMap{
      display: flex;
      row-gap: 40rpx;
      margin-right: 32rpx;
      .clearBtn{
        width: 196rpx !important;
        height: 88rpx;
        background: #FFFFFF;
        border-radius: 44rpx;
        border: 1rpx solid #D9DBE0;
        margin-right: 0 !important;
      }
      .miniBtn{
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        justify-content: center;
        width: 120rpx;
        &:nth-of-type(1){
          margin-right: 40rpx;
        }
        .miniBtnTitle{
          font-weight: 400;
          font-size: 24rpx;
          color: #1D2029;
          width: 100%;
          text-align: center;
          margin-top: 6rpx;
        }

        .miniBtnIcon{
          width: 36rpx;
          height: 36rpx;
        }
      }
    }
    .clockIn{
      flex: 1;
      height: 88rpx;
      background: #00B484;
      border-radius: 44rpx;
      font-weight: 500;
      font-size: 32rpx;
      color: #FFFFFF;
      text-align: center;
      line-height: 88rpx;
    }
  }
  .service-read {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .evaluate-img1{
    width: 204rpx;
    height: 60rpx;
  }
  .evaluate-img2{
    width: 204rpx;
    height: 60rpx;
  }
  .evaluate-img3{
    width: 204rpx;
    height: 60rpx;
  }
  .evaluate-img4{
    width: 204rpx;
    height: 60rpx;
  }
  .evaluate-img5{
    width: 204rpx;
    height: 60rpx;
  }
  .service-comment {
    margin-top: 32rpx;
  }
  .evaluations {
    font-weight: 600;
    font-size: 32rpx;
    color: #1D2029;
  }
  .arrow-icon{
    width: 32rpx;
    height: 32rpx;
  }
  .record-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .record-title {
    font-size: 28rpx;
    color: #1D2029;
    line-height: 40rpx;
  }

  /* 医嘱反馈样式 */
  .medical-advice {
    margin-top: 40rpx;

    .edit-icon {
      width: 28rpx;
      height: 28rpx;
      margin-left: 10rpx;
      vertical-align: middle;
    }

    .advice-wrapper {
      position: relative;
      margin-top: 16rpx;
    }

    .advice-content-box {
      max-height: 160rpx; /* 约4行文字高度 */
      overflow: hidden;
      transition: max-height 0.3s ease;
      word-break: break-word;

      &.expanded {
        max-height: none;
      }
    }

    .title-prefix {
      font-weight: 500;
      font-size: 28rpx;
      color: #1D2029;
    }

    .advice-content {
      font-size: 28rpx;
      line-height: 40rpx;
      color: #4E5569;
    }

    .expand-btn, .collapse-btn {
      color: #316EAB;
      font-size: 26rpx;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      padding: 0 10rpx;

      .expand-icon {
        width: 24rpx;
        height: 24rpx;
        margin-left: 8rpx;
        vertical-align: middle;
      }
    }

    .expand-btn {
      position: absolute;
      right: 0;
      bottom: 0;
      background: linear-gradient(to left, #FFFFFF 80%, transparent);
    }
  }

  /* 医嘱反馈弹窗样式 */
  .medical-advice-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;

    .medical-advice-container {
      width: 90%;
      max-width: 650rpx;
      max-height: 90vh; /* 限制最大高度为视口高度的90% */
      background: #FFFFFF;
      border-radius: 16rpx;
      overflow: hidden;
      display: flex;
      flex-direction: column;

      .medical-advice-header {
        padding: 30rpx;
        font-size: 32rpx;
        font-weight: 600;
        border-bottom: 1rpx solid #EAEBF0;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .close-icon {
          width: 32rpx;
          height: 32rpx;
        }
      }

            .medical-advice-body {
        padding: 30rpx;
        flex: 1;
        overflow-y: auto;

        .medical-advice-title {
          font-size: 28rpx;
          font-weight: 500;
          margin-bottom: 20rpx;
        }

        .medical-advice-textarea {
          width: 100%;
          height: 200rpx;
          background: #F4F6FA;
          border-radius: 8rpx;
          padding: 20rpx;
          box-sizing: border-box;
          font-size: 28rpx;
        }

        .text-count {
          text-align: right;
          font-size: 24rpx;
          color: #868C9C;
          margin: 10rpx 0 30rpx 0;

          &.text-count-exceed {
            color: #FF5500;
            font-weight: bold;
          }
        }

        .images-scroll-container {
          max-height: 500rpx; /* 设置最大高度，超出部分可滚动 */
          overflow-y: auto;
          margin-bottom: 20rpx;
          padding-right: 10rpx; /* 为滚动条预留空间 */
        }

        .upload-images {
          display: flex;
          flex-wrap: wrap;
          gap: 20rpx;

          .image-preview {
            width: 160rpx;
            height: 160rpx;
            position: relative;

            image {
              width: 100%;
              height: 100%;
              border-radius: 8rpx;
            }

            .delete-btn {
              position: absolute;
              top: -10rpx;
              right: -10rpx;
              width: 40rpx;
              height: 40rpx;
              background: rgba(0, 0, 0, 0.5);
              color: #FFFFFF;
              font-size: 28rpx;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }

          .upload-btn {
            width: 160rpx;
            height: 160rpx;
            background: #F4F6FA;
            border-radius: 8rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 60rpx;
            color: #868C9C;
          }
        }
      }

      .medical-advice-footer {
        padding: 20rpx 30rpx 40rpx 30rpx;

        .submit-btn {
          width: 100%;
          height: 88rpx;
          background: #00B484;
          border-radius: 44rpx;
          font-weight: 500;
          font-size: 32rpx;
          color: #FFFFFF;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }

  /* 隐藏上传组件的容器 */
  .upload-hidden-container {
    position: absolute;
    width: 0;
    height: 0;
    overflow: hidden;
    opacity: 0;
    z-index: -999;
  }

  /* 双按钮布局样式 */
  .dual-buttons {
    width: 100%;
    display: flex;
    gap: 16rpx;
  }

  .half-width {
    flex: 1;
    margin: 0 !important;
  }

  /* 双按钮布局样式 */
  .dual-buttons {
    display: flex;
    gap: 16rpx;
  }

  .half-width {
    flex: 1;
    margin: 0 !important;
  }

  /* 退款按钮样式 */
  .refund-btn {
    background: #FF5500 !important;
  }

  /* 退款弹窗样式 */
  .refund-popup {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: flex;
    align-items: flex-end;
  }

  .refund-modal {
    width: 100%;
    background: #FFFFFF;
    border-radius: 16rpx 16rpx 0 0;
    padding: 0;
    max-height: 80vh;
  }

  .refund-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 32rpx 32rpx 0;
    border-bottom: 1rpx solid #EAEBF0;
  }

  .refund-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #1D2029;
  }

  .refund-close {
    width: 48rpx;
    height: 48rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .refund-close image {
    width: 24rpx;
    height: 24rpx;
  }

  .refund-content {
    padding: 32rpx;
  }

  .refund-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24rpx;
  }

  .refund-label {
    font-size: 28rpx;
    color: #1D2029;
  }

  .refund-label.required::before {
    content: '*';
    color: #FF5500;
    margin-right: 8rpx;
  }

  .refund-value {
    font-size: 28rpx;
    color: #FF5500;
    font-weight: 500;
  }

  .refund-hint {
    font-size: 24rpx;
    color: #868C9C;
    margin-left: 16rpx;
  }

  .refund-reason-input {
    width: 100%;
    min-height: 120rpx;
    padding: 16rpx;
    border: 1rpx solid #D9DBE0;
    border-radius: 8rpx;
    font-size: 28rpx;
    color: #1D2029;
    margin-bottom: 24rpx;
    box-sizing: border-box;
  }

  .refund-amount-input {
    width: 100%;
    height: 88rpx;
    padding: 0 16rpx;
    border: 1rpx solid #D9DBE0;
    border-radius: 8rpx;
    font-size: 28rpx;
    color: #1D2029;
    box-sizing: border-box;
  }

  .refund-footer {
    display: flex;
    padding: 32rpx;
    gap: 24rpx;
  }

  .refund-cancel-btn {
    flex: 1;
    height: 88rpx;
    background: #FFFFFF;
    border: 1rpx solid #D9DBE0;
    border-radius: 44rpx;
    font-size: 32rpx;
    color: #4E5569;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .refund-confirm-btn {
    flex: 1;
    height: 88rpx;
    background: #FF5500;
    border-radius: 44rpx;
    font-size: 32rpx;
    color: #FFFFFF;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* 退款按钮样式 */
  .refund-btn {
    background: #FF5500 !important;
    margin-left: 16rpx;
  }

  /* 退款弹窗样式 */
  .refund-popup {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: flex;
    align-items: flex-end;
  }

  .refund-modal {
    width: 100%;
    background: #FFFFFF;
    border-radius: 16rpx 16rpx 0 0;
    padding: 0;
    max-height: 80vh;
  }

  .refund-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 32rpx 32rpx 0;
    border-bottom: 1rpx solid #EAEBF0;
  }

  .refund-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #1D2029;
  }

  .refund-close {
    width: 48rpx;
    height: 48rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .refund-close image {
    width: 24rpx;
    height: 24rpx;
  }

  .refund-content {
    padding: 32rpx;
  }

  .refund-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24rpx;
  }

  .refund-label {
    font-size: 28rpx;
    color: #1D2029;
  }

  .refund-label.required::before {
    content: '*';
    color: #FF5500;
    margin-right: 8rpx;
  }

  .refund-value {
    font-size: 28rpx;
    color: #FF5500;
    font-weight: 500;
  }

  .refund-hint {
    font-size: 24rpx;
    color: #868C9C;
    margin-left: 16rpx;
  }

  .refund-reason-input {
    width: 100%;
    min-height: 120rpx;
    padding: 16rpx;
    border: 1rpx solid #D9DBE0;
    border-radius: 8rpx;
    font-size: 28rpx;
    color: #1D2029;
    margin-bottom: 24rpx;
    box-sizing: border-box;
  }

  .refund-amount-input {
    width: 100%;
    height: 88rpx;
    padding: 0 16rpx;
    border: 1rpx solid #D9DBE0;
    border-radius: 8rpx;
    font-size: 28rpx;
    color: #1D2029;
    box-sizing: border-box;
  }

  .refund-footer {
    display: flex;
    padding: 32rpx;
    gap: 24rpx;
  }

  .refund-cancel-btn {
    flex: 1;
    height: 88rpx;
    background: #FFFFFF;
    border: 1rpx solid #D9DBE0;
    border-radius: 44rpx;
    font-size: 32rpx;
    color: #4E5569;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .refund-confirm-btn {
    flex: 1;
    height: 88rpx;
    background: #FF5500;
    border-radius: 44rpx;
    font-size: 32rpx;
    color: #FFFFFF;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
  }
</style>
