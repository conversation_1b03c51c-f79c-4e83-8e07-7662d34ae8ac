{"dependencies": {"@dcloudio/uni-ui": "^1.1.8", "babel-eslint": "^10.1.0", "crypto-js": "^3.3.0", "flv.js": "^1.6.2", "js-md5": "^0.7.3", "jsencrypt": "^3.2.1", "jweixin-module": "^1.6.0", "mini-html-parser2": "^0.3.0", "qs": "^6.9.1", "save": "^2.4.0", "tki-qrcode": "^0.1.6", "uni-simple-router": "^2.0.7", "unifly": "^1.0.2", "vconsole": "^3.3.4", "vue-clipboard2": "^0.3.3", "weixin-js-sdk": "^1.6.0", "wxml2canvas": "^1.0.1"}, "scripts": {"fix-memory-limit": "cross-env LIMIT=2048 increase-memory-limit", "publish": "node ./bulidPush/index.js", "publish-interactive": "node ./bulidPush/publish-interactive.js"}, "devDependencies": {"cross-env": "^5.0.5", "eslint": "^6.7.2", "eslint-config-standard": "^10.2.1", "eslint-friendly-formatter": "^3.0.0", "eslint-loader": "^1.7.1", "eslint-plugin-html": "^4.0.3", "eslint-plugin-import": "^2.20.1", "eslint-plugin-node": "^5.2.0", "eslint-plugin-promise": "^3.8.0", "eslint-plugin-standard": "^3.1.0", "eslint-plugin-vue": "^10.0.0", "increase-memory-limit": "^1.0.3"}}