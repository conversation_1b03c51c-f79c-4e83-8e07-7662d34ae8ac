<template>
  <picker
    mode="selector"
    :range="defaultConfig.range"
    :range-key="defaultConfig.rangeKey"
    @change="change"
  >
    <slot>
      <view class="flex-box">
        <text class="label">
          {{ selectLabel || defaultConfig.placeholder }}
        </text>
        <text class="triangle"></text>
        <image class="superscriptIcon" :src="file_ctx + 'static/image/business/moer/icon-filter-superscript.png'" mode="aspectFit"></image>
      </view>
    </slot>
  </picker>
</template>

<script>
export default {
  props: {
    config: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      file_ctx: this.file_ctx,
      defaultConfig: {
        range: [],
        rangeKey: 'name',
        rangeValueKey: 'id',
        placeholder: '全部'
      },
      selectLabel: null,
      selectValue: null
    }
  },
  watch: {
    config: {
      handler() {
        this.initConfig()
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    initConfig(config) {
      config = config || this.config
      Object.keys(config).forEach(key => {
        this.defaultConfig[key] = config[key]
      })
    },
    change(e) {
      const { range, rangeKey, rangeValueKey } = this.defaultConfig
      this.selectLabel = range[e.target.value][rangeKey]
      this.selectValue = range[e.target.value][rangeValueKey]
      this.$emit('change', this.selectValue)
    }
  }
}
</script>

<style lang="scss" scoped>
.flex-box {
  display: flex;
  border-radius: 4rpx;
  padding: 8rpx 24rpx;
  background: linear-gradient( 126deg, #E3C793 0%, #FCF6C3 24%, #D7B67F 56%, #FCF6C3 100%);
}
.label {
  font-size: 24rpx;
  color: #1C202A;
  line-height: 34rpx;
}
.superscriptIcon {
  width: 8rpx;
  height: 8rpx;
  margin-top: 20rpx;
  margin-left: 2rpx;
}
</style>