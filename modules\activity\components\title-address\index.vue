
<template>
  <view class="title-textarea clear-float" :class="{'bdt':defaultConfig.bdt,'bdb':defaultConfig.bdb  && !child}">
    <view class="l-l" :style="{'color': defaultConfig.titleColor}" :class='defaultConfig.titleClass'>
      {{defaultConfig.label}}
      <text class="star" v-if="defaultConfig.required">*</text>
    </view>
    <view class="l-r">
      <title-select-address :disabled="disabled" :config="addressConfig" :cData="addressConfig.array"
                            @updateForm="updateAddress" />
    </view>
    <view class="l-r">
      <textarea placeholder="街道信息" :disabled="disabled" v-model="form.data.val" @blur="returnFn" auto-height />
    </view>
  </view>
</template>

<script>
import TitleSelectAddress from '@/modules/activity/components/title-select-address'
export default {
  components: {
    TitleSelectAddress
  },
  data() {
    return {
      form: {
        data: {
          val: '',
          extendValue: ''
        }
      },
      array: [],
      index: 0,
      defaultConfig: {
        bdt: false,
        bdb: true,
        titleColor: '#333',
        textColor: '#333',
        label: '多行输入框',
        name: 'input',
        required: false,
      },
      addressConfig: {
        label: '邮寄地址',
        required: false,
        isRequired: true,
        key: 'extendValue',
        array: []
      }
    }
  },
  watch: {
    cData: {
      handler(val) {
        this.watchDataMain(val)
      },
      deep: true
    },
    config: {
      handler(val) {
        this.copyConfig()
      },
      deep: true
    }
  },
  props: {
    child:{
      type:Boolean,
      default:false,
    },
    // 是否禁用disable
    disabled: {
      type: Boolean,
      default() {
        return false
      }
    },
    // 初始值传值，用于回显
    cData: {
      type: Object,
      required: false,
      default() {
        return {}
      }
    },
    // 参数设置
    config: {
      type: Object,
      required: false,
      default: () => {
        return {}
      }
    }
  },
  computed: {

  },
  mounted() {
      this.copyConfig()
      if (!this.$validate.isNull(this.cData)) {
        this.watchDataMain(this.cData)
      }
  },
  methods: {
    updateAddress (obj) {
      this.form.data.extendValue = obj.value
    },
    /**
       * 初始化拷贝config对象
       */
    copyConfig() {
      const that = this
      const obj = that.config
      Object.keys(obj).forEach(function(key) {
        that.defaultConfig[key] = obj[key]
      })
    },
    /**
       * 监听Data主逻辑方法
       */
    watchDataMain(val) {
      const {value,extendValue} = val
      this.form.data.val = value
      const obj = extendValue ? JSON.parse(extendValue) : []
      const arr = Object.keys(obj).map(key => obj[key])
      this.addressConfig.array = arr
    },
    /**
       * picker触发选中事件
       * @param e
       */
    returnFn(e) {
      const that = this
      if (that.disabled) return
      const [province,city,district] = that.form.data.extendValue
      that.$emit('updateForm', { key: '' + that.config.name, value: [{ value: that.form.data.val,extendValue: {province,city,district} }] })
    }
  }
}
</script>

<style lang="scss" scoped>
  .color-topicC{
    color: $topicC !important;
  }
  .title-textarea{
    background-color:#ffffff;
    .l-l{
      line-height: 88upx;
      color: #333333;
      font-weight: 600;
      font-size:30upx;
    }
    .l-l.font36{
      font-size: 36upx;
    }
    .l-r{
      margin-bottom: 5px;
      textarea {
        min-height: 150upx;
        //line-height: 80upx;
        color: #333;
        font-size: 32upx;
        text-align: left;
        display: inline-block;
        vertical-align: middle;
        width: calc(100% - 64upx);
        border: 2upx solid #ede9e9;
        border-radius: 10upx;
        padding: 20upx;
      }
    }
  }
  .star{
    color: #F85E4C;
    padding-top: 6upx;
    font-size: 32upx;
    display: inline-block;
    // margin-left: 5upx;
    margin-left: 10rpx;
  }

</style>
