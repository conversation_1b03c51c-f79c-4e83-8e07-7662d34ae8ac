<template>
  <view class="page">
    <!-- 顶部 -->
    <view class="balancesTap" :style="{backgroundImage:getbgImage(serverBalancesBg)}">
      <view class="balancesTapTitle">我的余额（元）</view>
      <view class="balancesTapContent">{{balance / 100}}</view>
     <view class="gotoWithdraw" @click="handleClickJump('serverWithdraw')">
        去提现
        <image class="iconRightArrow" :src="iconRightArrow" mode=""></image>
      </view>
      <!-- <image class="corners" :src="corners" mode=""></image>
      <view class="bankCard">
        <image class="bankCardIcon" :src="card" mode=""></image>
        银行卡
      </view> -->
    </view>
    <!-- 财务记录分页表 -->
    <view class="financialRecord">
      <!-- 财务记录表头部 -->
      <view class="financialRecordTop">
        <view class="financialRecordTitle">财务记录</view>
        <view class="financialRecordScreen">
          <view class="screenItem" @click="$refs.selectTime.show()">
            {{timeMap[0]}}-{{timeMap[1]}}
          </view>
          <view class="screenItem" @click="showSelect = true">{{['','订单分佣','提现'][businessType]}}</view>
          <view class="selectbusiness" v-if="showSelect">
            <view class="screenItem" @click='selectBussiness(1)'>订单分佣</view>
            <view class="screenItem" @click='selectBussiness(2)'>提现</view>
          </view>
        </view>
      </view>
      <!-- 分页表 -->
      <scroll-view scroll-y="true" class="scroll-Y" @scrolltolower="upper">
        <view class="financialRecordItem" v-for="(item,index) in serverMap" :key="index">
          <view class="itemTitle">{{['','订单分佣','提现'][item.businessType]}}</view>
          <view class="itemMiddle">
            关联陪诊订单：{{item.accompanyBookId}}
            <view class="add" v-if="item.operatorType === 2">+￥{{item.amount / 100}}</view>
            <view class="minus" v-if="item.operatorType === 1">-￥{{item.amount / 100}}</view>
          </view>
          <view class="itemTime">{{item.updateTime}}</view>
        </view>
      </scroll-view>

    </view>
    <view class="lineHide">
      <timePicker ref="selectTime" v-model="timeMap" type="daterange" @change="changeTime" />
    </view>
  </view>
</template>

<script>
  import common from '@/common/util/main'
  import timePicker from '../components/uni-datetime-picker/uni-datetime-picker'
  import serverOptions from '@/config/env/options'

  export default {
    components: {
      timePicker,
    },
    data(){
      return {
        file_ctx:this.file_ctx,
        $constant: this.$constant,
        $static_ctx: this.$static_ctx,
        serverBalancesBg: this.$static_ctx + "image/business/hulu-v2/serverBalancesBg.png",
        card: this.$static_ctx + "image/business/hulu-v2/card.png",
        corners: this.$static_ctx + "image/business/hulu-v2/corners.png",
        iconRightArrow: this.$static_ctx + "image/business/hulu-v2/icon-right-arrow.png",
        balance:'',
        current:0,
        loadEnd:false,
        businessType:1,
        timeMap:[],
        serverMap:[],
        showSelect:false,
        providerId:''
      }
    },
    async onLoad() {
      const userId = serverOptions.getUserId(this);
      if(!userId){
        uni.showToast({title:'请先登录',icon:'none'})
        return;
      }
      let {data:id} = await this.$api.accompanyDoctor.accompanyproviderUserProvider({userId})
      this.providerId = id;
      let queryOne = await this.$api.accompanyDoctor.accompanyproviderQueryOne({id})
      this.balance = queryOne?.data?.balance;
      this.timeMap = this.getDatesOfTodayAndLastWeek();
      this.loadData();
    },
    methods:{
      handleClickJump(routerName){
        this.$navto.push(routerName)
      },
      formatDate(date) {
          const year = date.getFullYear();
          const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要加1
          const day = String(date.getDate()).padStart(2, '0');
          return `${year}-${month}-${day}`;
      },
      getDatesOfTodayAndLastWeek() {
          const dates = [];
          const today = new Date();
          // 获取一周前的今天
          const oneWeekAgo = new Date(today);
          oneWeekAgo.setDate(today.getDate() - 7);
          dates.push(this.formatDate(oneWeekAgo));
          // 获取今天的日期
          dates.push(this.formatDate(today));
          return dates;
      },
      async loadData(flag){
        if(flag){
          this.current = 0;
          this.serverMap.length = 0;
          this.loadEnd = false;
        }
        let {data:{records,total}} = await this.$api.accompanyDoctor.accompanyfinanceQueryPage({
          current:this.current,
          condition:{
            startCreateTime:this.timeMap[0] + ' 00:00:00',
            endCreateTime:this.timeMap[1] + ' 23:59:59',
            businessType:this.businessType,
            providerId:this.providerId
          }
        })
        console.log('records',records);
        this.serverMap.push(...records.map(e=>{
          e.updateTime = this.formatTimestamp(e.updateTime);
          return e
        }))
        if(total <= this.serverMap.length){
          this.loadEnd = true
        }
      },
      formatTimestamp(timestamp) {
          const date = new Date(timestamp);
          const year = date.getFullYear();
          const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要加1
          const day = String(date.getDate()).padStart(2, '0');
          const hours = String(date.getHours()).padStart(2, '0');
          const minutes = String(date.getMinutes()).padStart(2, '0');
          return `${year}-${month}-${day} ${hours}:${minutes}`;
      },
      changeTime(){
        this.loadData(true);
      },
      selectBussiness(businessType){
        this.businessType = businessType;
        this.showSelect = false;

        this.loadData(true);
      },
      upper(){
        if(this.loadEnd) return
        this.current++;
        this.loadData(true);
      },
      getbgImage(src){
        return `url(${src})`
      }
    }
  }
</script>

<style lang="scss">
  .lineHide{
    width: 0;
    overflow: hidden;
    height: 0;
  }
  .scroll-Y{
    height: 800rpx;
  }
  .page{
    padding: 48rpx 32rpx;
    box-sizing: border-box;
    width: 100vw;
    background: #F4F6FA;
    height: 100vh;
    overflow: scroll;
    .balancesTap{
      width: 686rpx;
      height: 252rpx;
      background-size: 100%;
      position: relative;
      padding: 64rpx 32rpx;
      box-sizing: border-box;
      .balancesTapTitle{
        font-weight: 400;
        font-size: 28rpx;
        color: #FFFFFF;
        z-index: 1;
      }
      .balancesTapContent{
        font-weight: 500;
        font-size: 48rpx;
        color: #FFF7C4;
        margin-top: 8rpx;
      }
      .gotoWithdraw{
        width: 148rpx;
        height: 72rpx;
        background: #FFFFFF;
        border-radius: 200rpx 0rpx 0rpx 200rpx;
        position: absolute;
        right: 0;
        top: 64rpx;
        font-weight: 400;
        font-size: 28rpx;
        color: #1D2029;
        display: flex;
        align-items: center;
        justify-content: center;
        .iconRightArrow{
          width: 32rpx;
          height: 32rpx;
        }
      }
      .corners{
        width: 24rpx;
        height: 24rpx;
        position: absolute;
        right: 0;
        bottom: 64rpx;
      }
      .bankCard{
        width: 192rpx;
        height: 64rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #1AB48A;
        border-radius: 16rpx 0rpx 16rpx 0rpx;
        font-weight: 400;
        font-size: 28rpx;
        color: #FFFFFF;
        position: absolute;
        bottom: 0;
        right: 0;
        .bankCardIcon{
          width: 32rpx;
          height: 32rpx;
          margin-right: 12rpx;
        }
      }
    }
    .financialRecord{
      width: 100%;
      .financialRecordTop{
        display: flex;
        justify-content: space-between;
        margin-top: 40rpx;
        position: relative;

        .financialRecordTitle{
          font-weight: 600;
          font-size: 30rpx;
          color: #1D2029;
        }
        .financialRecordScreen{
          display: flex;
          .screenItem{
            height: 50rpx;
            line-height: 50rpx;
            background: #FFFFFF;
            border-radius: 8rpx;
            border: 1rpx solid #EAEBF0;
            padding: 0rpx 26rpx 16rpx;
            box-sizing: border-box;
            margin-left: 16rpx;
          }
          .selectbusiness{
            position: absolute;
                right: 0;
                top: 50rpx;
                z-index: 99999;
          }
        }
      }
      .financialRecordItem{
        margin-top: 32rpx;
        width: 686rpx;
        height: 168rpx;
        background: #FFFFFF;
        border-radius: 8rpx;
        padding: 24rpx;
        box-sizing: border-box;
        .itemTitle{
          font-weight: 600;
          font-size: 28rpx;
          color: #000000;
        }
        .itemMiddle{
          margin-top: 6rpx;
          font-weight: 400;
          font-size: 24rpx;
          color: #4E5569;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .add{
            font-weight: 600;
            font-size: 36rpx;
            color: #FF553B;
          }
          .minus{
            font-weight: 600;
            font-size: 36rpx;
            color: #1D2029;
          }
        }
        .itemTime{
          margin-top: 6rpx;
          font-weight: 400;
          font-size: 24rpx;
          color: #868C9C;
        }
      }
    }
  }
</style>
