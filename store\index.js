import Vue from 'vue'
import Vuex from 'vuex'
import user from './modules/user'
import system from './modules/system'
import navigation from './modules/navigation'
import upload from './modules/upload'
import business from './modules/business'
import getters from './getters'
import chat from './modules/chat'
import address from './modules/address.js'
import template from './modules/template'

Vue.use(Vuex)

const store = new Vuex.Store({
    modules: {
        user,
        system,
        navigation,
        upload,
        business,
        chat,
        address,
        template
    },
    getters
})

export default store
