<template>
  <page>
    <view class="main-body" slot="content">

    </view>
  </page>
</template>

<script>
export default {
  name: 'MiddlePage',
  data() {
    return {
      readOnlyStorage: {}
    }
  },
  onShow() {
    const that = this
    const readOnlyStorage = this.$common.getKeyVal('system', 'readOnlyStorage', true)
    that.$uniPlugin.loading('正在跳转...', true)
    setTimeout(() => {
      if (JSON.stringify(readOnlyStorage) == '{}' || !readOnlyStorage) {
        that.$navto.replaceAll('Index')
        that.$uniPlugin.hideLoading()
      } else {
        that.$navto.push(readOnlyStorage.routerName, { id: readOnlyStorage.id || '', curIndex: readOnlyStorage.curIndex })
        that.$common.setKeyVal('system', 'readOnlyStorage', {}, true)
        that.$uniPlugin.hideLoading()
      }
    }, 2000)
  },
  method: {}
}
</script>

<style lang="scss" scoped>

  .main-body{
    height: 100%;
  }
</style>
