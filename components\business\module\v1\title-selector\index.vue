<!--
*
*config  [Object] 参数设置
bdt: true,              上划线border-top
bdb: true,              下划线border-bottom
label: '下拉框',         左边name值
name: 'select',         字段名
placeholder: '请选择',   提示
required: false,        是否必填
array: [],              展示数组
dicKey: '',             查询字典字段key
disabled: false         是否禁用
-------------------------------------------

*returnFn    [function]     回调函数
*cData        [String]       (默认选中)key传入方式
*getData     [String]      做数据处理首次默认值watch执行监听
*
*字典选择器(已经放到全局使用) <dictionary-selector  :config="xxx" :cData="xxx"  @returnFn = "xxx"></dictionary-selector>
 -->
<template>
  <view
    class="title-jump"
    :class="{ bdt: defaultConfig.bdt, bdb: defaultConfig.bdb,bdb2: defaultConfig.bdb2 }"
  >
    <picker
      :disabled="disabled"
      @change="returnFn"
      :value="index"
      :range="array"
      range-key="label"
    >
      <view class="flex-box">
        <view class="l-t" :class="[defaultConfig.iconurl ? 'font28' : '',defaultConfig.titleClass]" :style="{ color: defaultConfig.titleColor, width: defaultConfig.titleWidth }"
          v-if="defaultConfig.showLabel"
          >
          <em class="star" v-if="defaultConfig.required && !defaultConfig.iconurl && !defaultConfig.nextRequest">*</em>
           <image :src="defaultConfig.iconurl" class="iconsize" v-if="defaultConfig.iconurl"></image>
           <slot name="label" :data="defaultConfig">
            {{ defaultConfig.label }}
           </slot>
          <em class="star" v-if="defaultConfig.required && !defaultConfig.iconurl &&  defaultConfig.nextRequest">*</em>
          </view>
        <view
          class="l-r"
          :style="{ color: defaultConfig.textColor }"
          :class="{ 'color-999': disabled,'font28': defaultConfig.iconurl}"
        >
          <template v-if="form.data.label">
            {{ form.data.label }}
          </template>
          <template v-else>
            <slot v-if="$slots.placeholder" name="placeholder" />
            <template v-else>{{ defaultConfig.placeholder }}</template>
          </template>
        </view>
        <em class="jump" v-if="defaultConfig.showJump"></em>
      </view>
    </picker>
  </view>
</template>

<script>

export default {
  data() {
    return {
      form: {
        data: {
          select: '',
          label: ''
        }
      },
      array: [],
      index: 0,
      defaultConfig: {
        bdt: false,
        bdb: true,
        titleColor: '#333',
        textColor: '#333',
        label: '下拉框',
        name: 'select',
        placeholder: '请选择',
        required: false,
        array: [],
        dicKey: '',
        showLabel: true,
        showJump: true
      }
    }
  },
  watch: {
    value: {
      handler () {
        this.form.data.select = this.value
        this.form.data.label = ''
        this.index = 0
        this.watchVal()
      },
      deep: true,
      immediate: true
    },
    "form.data.select": {
      handler () {
        this.$emit('input', this.form.data.select)
      }
    },
    cData: {
      handler(val) {
        this.watchDataMain(val)
      },
      immediate: true,
      deep: true
    },
    config: {
      handler(val) {
        this.copyConfig()
      },
      immediate: true,
      deep: true
    }
  },
  props: {
    value: [Number,String],
    // 是否禁用disable
    disabled: {
      type: Boolean,
      default() {
        return false
      }
    },
    // 初始值传值，用于回显
    cData: {
      type: [String, Number],
      required: false,
      default() {
        return ''
      }
    },
    // 参数设置
    config: {
      type: Object,
      required: false,
      default: () => {
        return {}
      }
    },
    // 数组下标
    arrayIndex: {
      type: [String, Number],
      default() {
        return ''
      }
    },
    // 对象key值
    objectKey: {
      type: String,
      default() {
        return ''
      }
    }
  },
  computed: {

  },
  mounted() {
    this.getDic(() => {
      this.copyConfig()
      this.watchVal()
      if (!this.$validate.isNull(this.cData)) {
        this.watchDataMain(this.cData)
      }
    })
  },
  methods: {
    /**
     *
     * */
    watchVal () {
      this.array.forEach((item,index) => {
        if(item.value === this.value) {
          this.form.data.select = item.value
          this.form.data.label = item.label
          this.index = index
        }
      })
    },
    /**
     * 初始化拷贝config对象
     */
    copyConfig() {
      const that = this
      const obj = JSON.parse(JSON.stringify(that.config))
      Object.keys(obj).forEach(function (key) {
        that.defaultConfig[key] = obj[key]
      })
      that.defaultConfig = Object.assign({}, that.defaultConfig)
      that.getDic(() => {
        if (!that.$validate.isNull(that.cData)) {
          that.watchDataMain(that.cData)
        }
      })
    },
    /**
     * 监听cData主逻辑方法
     */
    watchDataMain(val) {
      console.log(val,'val22222')
      const that = this
      that.form.data.select = val.toString()
      let isVal = true
      for (let i = 0; i < that.array.length; i++) {
        if(that.array[i].id == val){
          that.form.data.label = that.array[i].label 
        }
        if (isVal && val.toString() === that.array[i].key) {
          isVal = false
          that.defaultConfig.placeholder = that.array[i].value
          that.index = i
        }
      }
      if (this.$validate.isNull(val)) {
        that.index = 0
        that.defaultConfig.placeholder = that.config.placeholder
      }
      that.defaultConfig = Object.assign({}, that.defaultConfig)
    },
    /**
     * 获取字典数据组
     */
    getDic(callBack) {
      const that = this
      const ar = that.config.array || []
      if (ar.length > 0 || !that.config.dicKey) {
        that.array = ar
        callBack()
        return
      }
      const params = {
        dictType: that.config.dicKey
      }
      that.$ext.dic.getDicInfo(params, (res) => {
        that.array = res
        callBack()
      }, () => {
        this.array = []
        callBack()
      })
    },
    /**
     * picker触发选中事件
     * @param e
     */
    returnFn(e) {
      const that = this
      const obj = that.array[e.target.value]
      that.form.data.select = obj.value
      that.form.data.label = obj.label
      const returnVal = {
        key: that.config.name,
        value: that.form.data.select
      }
      if (String(that.arrayIndex)) {
        returnVal.index = that.arrayIndex
      }
      if (!that.$validate.isNull(that.objectKey)) {
        returnVal.objectKey = that.objectKey
      }
      that.defaultConfig = Object.assign({}, that.defaultConfig)
      that.$emit('updateForm', returnVal)
    }
  }
}
</script>

<style lang="scss" scoped>
  .bdb2{
    border-bottom: 2upx dotted #9e9e9e;
  }
  .iconsize{
    width: 24upx;
    height: 24upx;
    vertical-align: middle;
    margin-right: 10upx;
  }
.color-999 {
  color: #999 !important;
}
.title-jump {
  height: 88upx;
  overflow: hidden;
  background-color: #ffffff;
  // padding: 0 30upx;
  box-sizing: border-box;
  .l-t {
    display: inline-block;
    vertical-align: middle;
    line-height: 88upx;
    color: #333333;
    font-size: 32upx;
    width: 320upx;
    @include ellipsis(1);
  }
  .l-t.font28{
    font-size: 28upx;
  }
  .l-t.font36{
    font-size: 36upx;
  }
  .l-r {
    flex: 1;
    // width: calc(100% - 354upx);
    display: inline-block;
    vertical-align: middle;
    font-size: 32upx;
    color: #000000;
    line-height: 88upx;
    text-align: right;
    @include ellipsis(1);
  }
  .l-r.font28{
    font-size: 28upx;
  }
  .jump {
    display: inline-block;
    vertical-align: middle;
    @include iconImg(34, 34, "/business/icon-gengduo.png");
  }
}
.star {
  color: #f85e4c;
  padding-top: 6upx;
  font-size: 32upx;
  display: inline-block;
  margin: 0 10upx;
}
.flex-box {
  display: flex;
  width: 100%;
  align-items: center;
}
</style>
