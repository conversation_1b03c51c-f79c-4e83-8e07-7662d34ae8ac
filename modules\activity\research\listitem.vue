<template>
  <view class="caselist">
    <template v-for="(item, idx) in list">
      <!-- 病例列表 -->
      <view class="case-item" :key="item.id" @tap="navtoGo('questionnaireIndex', { id: item.activityId, caseCollectSubmitLogId: item.id, tenantId: item.tenantId })">
        <view class="case-item-t">{{ item.title }}</view>
        <!--  -->
        <!-- <template v-if="item.auditStatus == 2"> -->
          <view class="case-item-info">问卷编号：{{ item.id }}</view>
          <view class="case-item-info">提交时间：{{ item.updateTimeText }}</view>
          <!-- <view class="case-item-info">审核状态：待审核</view> -->
          <!-- <view class="tip-ico">待审核</view> -->
        <!-- </template> -->
        <!-- 审核通过 -->
        <!-- <template v-if="item.auditStatus == 2">
          <view class="case-item-info">问卷编号：{{ item.id }}</view>
          <view class="case-item-info">提交时间：{{ item.updateTimeText }}</view>
          <view class="case-item-info">审核状态：已通过</view>

        </template> -->

        <!-- 审核不通过 -->
        <!-- <template v-if="item.auditStatus == 3">
          <view class="case-item-info">发布时间：{{ item.issueDateText }}</view>
          <view class="case-item-info">最后更新时间：{{ item.id }}</view>
          <view class="case-item-info">审核时间：{{ item.updateTimeText }}</view>
          <view class="case-item-info">填写进度：{{ item.progressText }} %</view>
          <view class="case-item-info">不通过原因：{{ item.desc }}</view>
        </template> -->
      </view>
    </template>
    <view class="o-space">

    </view>
  </view>
</template>

<script>
export default {
  name: 'questionListitem',
  props: {
    list: {
      type: Array,
      default: function() {
        return [];
      }
    }
  },
  data() {
    return {
      activitytype:2,
    };
  },
  methods: {
    navtoGo(url, obj = {}) {
      this.$navto.push(url, obj)
    }
  }
};
</script>

<style lang="scss" scoped>
  .o-space{
    height: 100px;
    width: 100%;
  }
  // .caselist{
  //   padding-bottom: 110upx;
  // }
.cannel {
  display: inline-block;
  padding: 5upx 30upx;
  background-color: #dbdbdb;
  color: #fff;
  border-radius: 10upx;
  font-size: 24upx;
  margin: 10rpx 0;
}
.success {
  display: inline-block;
  padding: 5upx 30upx;
  background-color: $topicC;
  color: #fff;
  border-radius: 10upx;
  font-size: 24upx;
  margin: 10rpx 0;
}
.add-item {
  display: flex;
  align-items: center;
  height: 80upx;
  // border-top: 1upx solid #dbdbdb;
  background-color: #fff;
  margin: 0 20upx;
  justify-content: center;
  font-size: 28upx;
  color: #b7685b;
  border-radius: 10upx;
}
.add-ico {
  width: 20upx;
  height: 2upx;
  background-color: #bacad9;
  margin-right: 10upx;
}
.case-item {
  background-color: #fff;
  margin: 20upx;
  padding: 40upx 30upx;
  border-radius: 10upx;
  overflow: hidden;
  position: relative;
  .tip-ico {
    position: absolute;
    right: 0;
    top: 0;
    color: #c55e57;
    background-color: #fde9e6;
    border-bottom-left-radius: 35upx;
    border-top-right-radius: 35upx;
    padding: 5upx 30upx;
    font-size: 24upx;
  }
  .case-item-t {
    font-size: 32upx;
    font-weight: 550;
    margin-bottom: 40upx;
  }

  .case-item-info {
    font-size: 24upx;
    line-height: 2;
  }
  .case-item-timer {
    color: #7c7c7c;
    font-size: 24upx;
    line-height: 1.5;
  }
  .case-item-btns {
    margin-top: 20upx;
  }
  .case-item-btn {
    display: inline-flex;
    align-items: center;
    height: 70upx;
    border-radius: 10upx;
    overflow: hidden;
    margin-top: 10upx;
    background-color: #a4adb3;
    padding: 0 60upx;
    color: #fff;
    margin-right: 20rpx;
  }

  .case-item-btn.submit {
    border: 1upx solid #dbdbdb;
    color: #000;
    background-color: #fff;
  }

  .case-item-btn-info {
    background-color: #34cbc7;
    color: #fff;
    height: 100%;
    display: flex;
    align-items: center;
    padding: 0 20upx;
    font-size: 24upx;
  }
  .case-item-btn-t {
    background-color: $topicC;
    color: #fff;
    height: 100%;
    display: flex;
    align-items: center;
    padding: 0 20upx;
    font-size: 24upx;
  }
}
</style>
