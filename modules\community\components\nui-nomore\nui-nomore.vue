<template>
	<view class="nui-nomore-class nui-loadmore-none" :style="defaultStyle">
		<view :class="[isDot?'nui-nomore-dot':'nui-nomore']">
			<view :style="{backgroundColor:backgroundColor}" :class="[isDot?'nui-dot-text':'nui-nomore-text']">{{isDot?dotText:text}}</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "nui-nomore",
		props: {
			//当前页面背景颜色
			backgroundColor: {
				type: String,
				default: "#fafafa"
			},
			//是否以圆点代替 "没有更多了"
			isDot: {
				type: Boolean,
				default: false
			},
			//isDot为false时生效
			text: {
				type: String,
				default: "没有更多了"
			},
			defaultStyle: [String,Object]
		},
		data() {
			return {
				dotText: "●"
			};
		}
	}
</script>

<style scoped>
	.nui-loadmore-none {
		width: 50%;
		margin: 1.5em auto;
		line-height: 1.5em;
		font-size: 24rpx;
		display: flex;
		justify-content: center;
	}

	.nui-nomore {
		width: 100%;
		height: 100%;
		position: relative;
		display: flex;
		justify-content: center;
		margin-top: 10rpx;
		padding-bottom: 6rpx;
	}

	.nui-nomore::before {
		content: ' ';
		position: absolute;
		border-bottom: 1rpx solid #e5e5e5;
		-webkit-transform: scaleY(0.5);
		transform: scaleY(0.5);
		width: 100%;
		top: 18rpx;
		left: 0;
	}

	.nui-nomore-text {
		text-align: center;
		padding: 0 16rpx;
		height: 34rpx;
    font-size: 24rpx;
    color: #A5AAB8;
    line-height: 34rpx;
		position: relative;
		z-index: 1;
	}

	.nui-nomore-dot {
		position: relative;
		text-align: center;
		-webkit-display: flex;
		display: flex;
		-webkit-justify-content: center;
		justify-content: center;
		margin-top: 10rpx;
		padding-bottom: 6rpx;
	}

	.nui-nomore-dot::before {
		content: '';
		position: absolute;
		border-bottom: 1rpx solid #e5e5e5;
		-webkit-transform: scaleY(0.5)  translateX(-50%);
		transform: scaleY(0.5)  translateX(-50%);
		width: 360rpx;
		top: 18rpx;
		left: 50%;
	}

	.nui-dot-text {
		position: relative;
		color: #e5e5e5;
		font-size: 10px;
		text-align: center;
		width: 50rpx;
		height: 36rpx;
		line-height: 36rpx;
		-webkit-transform: scale(0.8);
		transform: scale(0.8);
		-webkit-transform-origin: center center;
		transform-origin: center center;
		z-index: 1;
	}
</style>
