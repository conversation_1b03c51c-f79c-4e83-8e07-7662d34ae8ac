<template>
  <view class="comment">
    <hb-comment
      ref="hbComment"
      :moreLoading="moreLoading"
      :deleteTip="deleteTip"
      :cmData="cmData"
      :isShowBtn="isShowBtn"
      @add="$emit('add', $event)"
      @del="$emit('del', $event)"
      @like="$emit('like', $event)"
      @focusOn="$emit('focusOn', $event)"
      @loadMore="(e,e1) => {$emit('loadMore', e, e1)}"
      @comment="$emit('comment', $event)"
      @replyBefore="$emit('replyBefore')"
      @changeVal="
        (e) => {
          target = e;
        }
      "
    ></hb-comment>
    <comment-input
      :isJumpWx="isJumpWx"
      :target="target"
      :isStar="postmessageInfo.collectSubscribeStatus == 1"
      :starNumber="postmessageInfo.showCollectNumber"
      :isLike="postmessageInfo.likeSubscribeStatus == 1"
      :likeNumber="postmessageInfo.showLikeNumber"
      :isShare="postmessageInfo.shareSubscribeStatus == 1"
      :shareNumber="postmessageInfo.showShareNumber"
      :commentNumber="postmessageInfo.comment_number"
      :postmessageInfo="postmessageInfo"
      @changeHeight="$emit('changeCommentInputHeight', $event)"
      @input="clickInput"
      @collect="$emit('collectPosts', $event)"
      @like="$emit('likePosts', $event)"
      @share="$emit('sharePosts', $event)"
      @scrollcomment="$emit('scrollcomment', $event)"
      @confirm="$emit('comment', $event)"
    />
  </view>
</template>

<script>
import hbComment from '@/modules/community/components/hb-comment/hb-comment.vue'
import commentInput from '@/modules/community/components/comment-input/comment-input.vue'
export default {
  components: {
    hbComment,
    commentInput
  },
  props: {
    isJumpWx: {
      type: Boolean,
      default: false
    },
    cmData: {
      type: Object,
      default: function () {
        return {
          readNumer: 0,
          commentSize: 0,
          comment: [],
          isHasMore: false,
          hbComment: null,
          loading: true
        }
      }
    },
    postmessageInfo: {
      type: Object,
      default: () => {
        () => {
          return {
            collectSubscribeStatus: 2,
            collectNumber: 0,
            likeSubscribeStatus: 2,
            likeNumber: 0,
            shareSubscribeStatus: 2,
            shareNumber: 0,
          }
        }
      }
    },
    deleteTip: String,
    moreLoading: {
      type: Boolean,
      default: false
    },
    isShowBtn:{
      type: Boolean,
      default: true
    },
  },
  data() {
    return {
      $validate: this.$validate,
      target: {}
    }
  },
  mounted() {
    this.hbComment = this.$refs.hbComment
  },
  methods: {
    clickInput() {
      this.hbComment.commentInput()
    },
    clearInput() {
      this.hbComment.clearInput()
    }
  }
}
</script>

<style scoped lang="scss">
</style>
