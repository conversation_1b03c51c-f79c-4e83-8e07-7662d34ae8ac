import request from '@/common/util/request'
import env from '@/config/env'
import serverOptions from '@/config/env/options'
/**
 * 社区请求工具类，
 * 注意：API接口只做请求数据和格式化数据的事情，其他事务一概不处理
 */
// 因为数据分割 所以重写该模块的请求方法
let providerId = serverOptions.providerId
request.accompanyDoctorGet = function (url, param){
  param = {...param,providerId}
  return request.get(url, param)
}
request.accompanyDoctorPostForm = function (url, param){
  param = {...param,providerId}
  return request.postForm(url, param)
}
request.accompanyDoctorPost = function (url, param){
  param = {...param,providerId}
  if(param.condition){
    param.condition.providerId = providerId
  }else{
    param = {...param,providerId,condition:{providerId}}
  }
  return request.postJson(url, param)
}
request.accompanyDoctorPutJson = function (url, param){
  param = {...param,providerId}
  return request.putJson(url, param)
}
export default {
    // 帖子分页列表
    postmessageQueryPage (param) {
        const url = env.ctx + 'dm/api/v1/postmessage/query/page'
        return request.accompanyDoctorPost(url, param)
    },
    // 帖子详情
    postmessageQueryOneV1 (param) {
        const url = env.ctx + 'dm/api/v1/postmessage/query/one'
        return request.accompanyDoctorGet(url, param)
    },
    // 帖子详情
    postmessageQueryOne (param) {
        const url = env.ctx + 'dm/api/v2/postmessage/query/one'
        return request.accompanyDoctorGet(url, param)
    },
    // 获取评论层级-分页
    commentQueryLevelPage (param) {
        const url = env.ctx + 'dm/api/v1/comment/query/level/page'
        return request.accompanyDoctorPost(url, param)
    },
    // 评论新增点赞数
    commentIncreaseLikenumber (param) {
        const url = env.ctx + 'dm/api/v1/comment/increase/likenumber'
        return request.accompanyDoctorPostForm(url, param)
    },
    // 取消点赞
    commentReduceLikenumber (param) {
        const url = env.ctx + 'dm/api/v1/comment/reduce/likenumber'
        return request.accompanyDoctorPostForm(url, param)
    },
    // 新增评论
    commentPostMessageComment (param) {
        const url = env.ctx + 'dm/api/v1/comment/post/message/comment'
        return request.accompanyDoctorPost(url, param)
    },
    // 删除评论
    commentDeleteOne (param) {
        const url = env.ctx + 'dm/api/v1/comment/delete/one/' + param.id
        return request.delete(url, param)
    },
    // 收藏帖子
    postmessageAddCollection (param) {
        const url = env.ctx + 'dm/api/v1/postmessage/add/collection'
        return request.accompanyDoctorPostForm(url, param)
    },
    // 取消收藏
    postmessageCancelCollection (param) {
        const url = env.ctx + 'dm/api/v1/postmessage/cancel/collection'
        return request.accompanyDoctorPostForm(url, param)
    },
    // 点赞功能
    postmessageAddLike (param) {
        const url = env.ctx + 'dm/api/v1/postmessage/add/like'
        return request.postForm(url, param)
    },
    // 取消点赞功能
    postmessageCancelLike (param) {
        const url = env.ctx + 'dm/api/v1/postmessage/cancel/like'
        return request.accompanyDoctorPostForm(url, param)
    },
    // 获取推荐帖子列表
    postmessageQueryRecommendPage (param) {
        const url = env.ctx + 'dm/api/v1/postmessage/query/recommend/page'
        return request.accompanyDoctorPost(url, param)
    },
    // 获取医生热点帖子列表
    postmessageQueryPhysicianHotPage (param) {
        const url = env.ctx + 'dm/api/v1/postmessage/query/physician/hot/page'
        return request.postJson(url, param)
    },
    // 获取精华帖子分页
    postmessageQueryEssencePage (param) {
        const url = env.ctx + 'dm/api/v1/postmessage/query/essence/page'
        return request.accompanyDoctorPost(url, param)
    },
    // 新增帖子
    postmessageInsert (param) {
        const url = env.ctx + 'dm/api/v1/postmessage/app/insert'
        return request.accompanyDoctorPost(url, param)
    },
    // 删除帖子
    postmessageDeleteOne (param) {
        const url = env.ctx + 'dm/api/v1/postmessage/delete/one/' + param.id
        return request.delete(url, param)
    },
    // 浏览帖子
    postmessageVisit (param) {
        const url = env.ctx + 'dm/api/v1/postmessage/visit'
        return request.get(url, param)
    },
    // 首页查询
    postmessageSearch (param) {
        const url = env.ctx + 'dm/api/v1/postmessage/search'
        return request.accompanyDoctorPost(url, param)
    },
    // 编辑帖子
    postmessageUpdate (param) {
        const url = env.ctx + 'dm/api/v1/postmessage/app/update'
        return request.putJson(url, param)
    },
    // 批量邀请评论
    inviteBatch(param) {
      const url = env.ctx + 'dm/api/v1/postmessage/invite/batch'
      return request.postJson(url, param)
    },
    // 帖子分页列表
    postmessageQueryAppPage (param) {
      const url = env.ctx + 'dm/api/v1/postmessage/query/app/page'
      return request.postJson(url, param)
    },
    // 首页搜索框热度词
    searchbuzzwordQueryList (param) {
      const url = env.ctx + 'dm/api/v1/searchbuzzword/query/list'
      return request.get(url, param)
    },
}
