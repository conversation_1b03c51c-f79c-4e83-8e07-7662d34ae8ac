import request from '@/common/util/request'
import env from '@/config/env'
import Request from '@/common/plugins/luch-request/request'
const http = new Request()
http.interceptor.response((res) => {return res.data}, () => {})

const doUpload = env.ctx + '/basics/api/v1/attachment/upload'
    // const doUpload = 'https://upload.greenboniot.cn/api/v1/attachment/upload'
    // const doUpload = 'http://192.168.3.15:7001/api/v1/attachment/upload'

export default {
  //小程序二维码分享表根据多参数进行单一查询
  getSmallProgramQrcode(data) {
    return request.get(`${env.ctx}manage/api/v1/wxsmallprogramqrcode/query/param`, data);
  },
  doUpload,
  //根据性别获取关注二维码
  getGenderRecommendQrCode(data) {
      return request.get(`${env.ctx}manage/api/wx/getGenderRecommendQrCode`, data);
  },

  //根据ID获取领袋信息
  updateByGender(data) {
      return request.putJson(`${env.ctx}manage/api/associate/updateByGender`, data);
  },

  //根据ID获取领袋信息
  packetRecordFindById(data) {
      return request.get(`${env.ctx}manage/api/packetRecord/findById`, data);
  },

  //已关注获取IP信息进行出袋
  attentionOutPacketByIpInfo(data) {
      return request.postJson(`${env.ctx}manage/api/wx/attentionOutPacketByIpInfo`, data);
  },

  //个微出袋
  personalSubscribeOutPacket(data) {
    return request.postJson(`${env.ctx}manage/api/wx/personalSubscribeOutPacket`, data);
  },

  //获取会员进入落地页展示内容信息
  getEntryLandingPageInfo(data) {
      return request.get(`${env.ctx}manage/api/landingPageConfig/getEntryLandingPageInfo`, data);
  },

  //科室评价领袋按钮点击统计
  countClickButton(data) {
      return request.postJson(`${env.ctx}manage/api/userEvaluatePacket/countClickButton`, data);
  },
  //保存科室评价领袋标签
  saveEvaluatePacketTag(data) {
      return request.postJson(`${env.ctx}manage/api/wx/saveEvaluatePacketTag`, data);
  },

  //科室评价领袋H5用户授权
  evaluatePacketH5Authorize(data) {
      return request.postJson(`${env.ctx}manage/api/wx/evaluatePacketH5Authorize`, data);
  },

  // 返回一个领袋状态
  getPacketRecordReceiveStatus(data) {
      return request.get(`${env.ctx}manage/api/wx/getPacketRecordReceiveStatus`, data);
  },

  //新增活动用户表
  activityPlanUserAdd(data) {
      return request.postJson(`${env.ctx}manage/api/activityPlanUser/add`, data);
  },

  //新进活动领袋
  goInActivityPacket(data) {
      return request.postForm(`${env.ctx}manage/api/wx/goInActivityPacket`, data);
  },


  //新增医嘱填写记录
  addDoctorAdviceRecord(data) {
      return request.postJson(`${env.ctx}manage/api/userEvaluate/addDoctorAdviceRecord`, data);
  },

  //新增模板推送后详情用户评价
  addEvaluate(data) {
      return request.postJson(`${env.ctx}manage/api/userEvaluate/addEvaluate`, data);
  },


  //创建h5页面的用户基本数据
  getUserInfo(data) {
      return request.get(`${env.ctx}manage/api/userEvaluate/getUserInfo`, data);
  },


  //标签会员绑定标签
  bindTag(data) {
      return request.postJson(`${env.ctx}manage/api/gift/bindTag`, data);
  },

  //标签会员绑定手机号码
  bindMobile(data) {
      return request.postJson(`${env.ctx}manage/api/gift/bindMobile`, data);
  },

  //标签会员领取礼物更新联系信息和标签
  collectGift(data) {
      return request.postJson(`${env.ctx}manage/api/gift/collectGift`, data);
  },

  //根据code获取标签会员信息
  createGiftAccountSubscribe(data) {
      return request.postJson(`${env.ctx}manage/api/gift/createGiftAccountSubscribe`, data);
  },

  //获取礼物获取第三方跳转地址
  getGiftJumpUrl(data) {
      return request.get(`${env.ctx}manage/api/gift/getJumpUrl`, data);
  },

  //获取第三方跳转地址
  getJumpUrl(data) {
      return request.postJson(`${env.ctx}manage/api/wx/getJumpUrl`, data);
  },

  //根据ID获取h5页面配置详情
  findById(data) {
      return request.get(`${env.ctx}manage/api/h5Config/findById`, data);
  },

  //好大夫免登录
  generateHaodfInquiryLink(data) {
      return request.postForm(`${env.ctx}manage/api/haodf/generateHaodfInquiryLink`, data);
  },

  //视频号关注出袋
  scanVideoQrCode(data) {
      return request.postJson(`${env.ctx}manage/api/wx/scanVideoQrCode`, data);
  },


  //完善跳转到第三方页面的基本数据
  fillHistory(data) {
      return request.postJson(`${env.ctx}manage/api/wx/fillHistory`, data);
  },

  //根据父节点id查询字典表
  findByParentId(data) {
      return request.get(`${env.ctx}manage/api/code/findByParentId`, data);
  },

  //创建h5跳转第三方页面的基本数据
  createHistory(data) {
      return request.postForm(`${env.ctx}manage/api/wx/createHistory`, data);
  },

  //取消支付
  cancelOrder(data) {
      return request.postForm(`${env.ctx}manage/api/wx/cancelOrder`, data);
  },

  //系统用户绑定公众号用户
  bindWxMpUser(data) {
      return request.postJson(`${env.ctx}manage/api/user/bindWxMpUser`, data);
  },

  //用户反馈
  addDeviceFeedbackForWx(data) {
      return request.postJson(`${env.ctx}manage/api/deviceFeedback/addDeviceFeedbackForWx`, data);
  },

  //签名
  createJsapiSignature(data) {
      return request.postForm(`${env.ctx}manage/api/wx/createJsapiSignature`, data);
  },


  //付费出袋
  pay(data, headers) {
      return request.postForm(`${env.ctx}manage/api/wx/pay`, data);
  },

  //免费出袋
  free(data, headers) {
      return request.postForm(`${env.ctx}manage/api/wx/free`, data);
  },

  //扫h5码
  scanH5QrCode(data) {
      return request.postForm(`${env.ctx}manage/api/wx/scanH5QrCode`, data);
  },
  // 添加推荐缓存
  updateAccountRecommendWxIdCache(data) {
    return request.putJson(`${env.ctx}manage/api/wx/updateAccountRecommendWxIdCache`, data);
  },

  // 获取用户信息
  alipayGetUserInfo(data) {
    return request.get(`${env.ctx}manage/api/v1/alipay/get/user/info`, data)
  },

  // 扫码记录
  aggregatecodeuserScanCode (data) {
    return request.postForm(`${env.ctx}manage/api/v1/aggregatecodeuser/scan/code`, data)
  },

  // 曝光，跳第三方页面时调用
  alipayInsertExposure (data) {
    return request.postJson(`${env.ctx}manage/api/v1/alipay/insert/exposure`, data)
  },

  // 判断用户是否有免费取袋的机会
  alipayGetUseridIsFree (data) {
    return request.get(`${env.ctx}manage/api/v1/alipay/get/userid/is/free`, data)
  },

  // 支付宝免费领袋
  alipayFreeStartPacket (data) {
    return request.postForm(`${env.ctx}manage/api/v1/alipay/free/start/packet`, data)
  },

  // 支付宝查询出袋结果
  alipayGetPacketRecordReceiveStatus (data) {
    return request.get(`${env.ctx}manage/api/v1/alipay/get/packet/record/receive/status`, data)
  },

  // 获取中央用户基本信息
  sysuserBasicsinfo(paramObj) {
    const url = env.ctx + 'auth/api/v2/app/sysuser/basicsinfo'
    return request.get(url, paramObj)
  },
  sysuserUpdate(paramObj) {
    const url = env.ctx + 'auth/api/v1/sysuser/update'
    return request.putJson(url, paramObj)
  },
  // 分页查看平台消息接口
  platformmessageitemQueryPage(paramObj) {
    const url = env.ctx + 'basics/api/v1/platformmessageitem/query/page'
    return request.get(url, paramObj)
  },
  // 查看详情消息接口
  noticelogQueryAppOne(paramObj) {
    const url = env.ctx + 'dm/api/v1/noticelog/query/app/one'
    return request.get(url, paramObj)
  },
  // 查询未读租户条数接口
  noticelogQueryAppRecordStatUnread(paramObj) {
    const url = env.ctx + 'dm/api/v1/noticelog/query/app/record/stat'
    return request.get(url, paramObj)
  },
  // 分页查询消息接口
  noticelogQueryPage(paramObj) {
    const url = env.ctx + 'dm/api/v1/noticelog/query/app/page'
    return request.postJson(url, paramObj)
  },
  //  改变阅读状态
  commondynamicChangeRead(paramObj) {
    return new Promise((resolve, reject) => {
      const url = env.ctx + 'affairs/api/v1/commondynamic/change/read'
      request.get(url, paramObj).then((res) => {
        if (res.code === 0) {
          resolve(res.result)
        } else {
          reject()
        }
      }).catch(e => {
        reject(e)
      })
    })
  },
  // 查询动态汇总未读消息条数(租户消息-未读数)
  commondynamicQueryUnread(paramObj) {
    return new Promise((resolve, reject) => {
      const url = env.ctx + 'affairs/api/v1/commondynamic/query/unread'
      request.get(url, paramObj).then((res) => {
        if (res.code === 0) {
          resolve(res.result)
        } else {
          reject()
        }
      }).catch(e => {
        reject(e)
      })
    })
  },
  // 查询待办数量接口
  todotasksitemQueryCount(paramObj) {
    return new Promise((resolve, reject) => {
      const url = env.ctx + 'dm/api/v1/todotasksitem/query/count'
      request.get(url, paramObj).then((res) => {
        if (res.code === 0) {
          resolve(res.result)
        } else {
          reject()
        }
      }).catch(e => {
        reject(e)
      })
    })
  },
  // 二维码分享
  smallprogramqrcodeQueryParam(paramObj) {
    return new Promise((resolve, reject) => {
      const url = env.ctx + 'wechat/api/v1/smallprogramqrcode/query/param'
      request.get(url, paramObj).then((res) => {
        if (res.code === 0) {
          resolve(res.result)
        } else {
          reject()
        }
      }).catch(e => {
        reject(e)
      })
    })
  },
  // 横幅信息--列表查询
  bannerQueryList(paramObj) {
    const url = env.ctx + 'dm/api/v1/banner/query/list'
    return request.get(url, paramObj)
  },
  // 获取周边POI列表分页查询
  homepageQueryPoi(paramObj) {
    return new Promise((resolve, reject) => {
      const url = env.ctx + 'basics/api/v1/amap/query/poi'
      request.get(url, paramObj).then((res) => {
        if (res.code === 0) {
          resolve(res.data)
        } else {
          reject()
        }
      }).catch(e => {
        reject(e)
      })
    })
  },
  // 根据经纬度查询地址位置
  homepageQueryCity(paramObj) {
    return new Promise((resolve, reject) => {
      const url = env.ctx + 'basics/api/v1/amap/query/city'
      request.get(url, paramObj).then((res) => {
        if (res.code === 0) {
          resolve(res.data)
        } else {
          reject()
        }
      }).catch(e => {
        reject(e)
      })
    })
  },
  // 根据scene获取二维码分享
  smallprogramqrcodeGetCodeByScene(paramObj) {
    return new Promise((resolve, reject) => {
      const url = env.ctx + 'basics/api/v1/smallprogramqrcode/getCodeByScene'
      request.get(url, paramObj).then((res) => {
        if (res.code === 0) {
          resolve(res)
        } else {
          reject()
        }
      }).catch(e => {
        reject(e)
      })
    })
  },
  // 根据父id获取字典
  // findByParentId(paramObj) {
  //   return new Promise((resolve, reject) => {
  //     const url = env.ctx + 'manage/api/code/findByParentId'
  //     request.get(url, paramObj).then((res) => {
  //       if (res.code === 0) {
  //         resolve(res)
  //       } else {
  //         reject()
  //       }
  //     }).catch(e => {
  //       reject(e)
  //     })
  //   })
  // },
  // 根据图片urL上传图片到oss
  attachmentUrlMediaUploadOssByUrl (param) {
    const url = env.ctx + 'basics/api/v1/attachment/url/mediaUploadOssByUrl'
    return request.get(url, param)
  },
  // 签名
  // createJsapiSignature(param) {
  //   const url = env.ctx + 'manage/api/wx/createJsapiSignature'
  //   return request.postForm(url, param)
  // },
  // 微信通用包装授权链接
  wxGetCommonOauth2WrapUrl(param) {
    return new Promise((resolve, reject) => {
      http.request({
        method: 'GET',
        url: env.ctx + 'manage/api/wx/get/common/oauth2/wrap/url',
        // data: params,
        params: param,
        dataType: 'json'
      }).then(res => {
        resolve(res)
      }).catch(rej => {
        reject(rej)
      })
    })
  },
  // 订阅消息操作记录
  wxsubscribemessagelogInsert (param) {
    const url = env.ctx + 'manage/api/v1/wxsubscribemessagelog/insert'
    return request.postJson(url, param)
  },
  // 批量订阅消息操作记录
  wxsubscribemessagelogInsertBatch (param) {
    const url = env.ctx + 'manage/api/v1/wxsubscribemessagelog/insert/batch'
    return request.postJson(url, param)
  },
  // 查询订阅消息记录
  wxsubscribemessagelogQueryAccountIdAndTemplateId(param) {
    const url = env.ctx + 'manage/api/v1/wxsubscribemessagelog/query/accountIdAndTemplateId'
    return request.postForm(url, param)
  },
  // 获取地址
  getAreaListArea(param){
    const url = env.ctx + 'manage/api/area/listArea'
    return request.get(url,param)
  },
  // 公众号 - 查询用户是否关注
  accountattentionSubscribeOrNot(param){
    // const url = env.ctx + 'manage/api/v1/accountattention/subscribeOrNot'
    const url = env.ctx + 'manage/api/v1/authFans/get/by/unionId'
    return request.postForm(url,param)
  },
  // 小葫芦健康公众号关注与否
  postmessageQueryAttention(param){
    const url = env.ctx + 'dm/api/v1/postmessage/query/attention'
    return request.postForm(url,param)
  },
  minichannellinkQueryOne (param) {
    const url = env.ctx + 'dm/api/v1/minichannellink/query/one/code'
    return request.get(url, param)
  },
  // 保存数据
 codeInsert (param) {
  const url = env.ctx + 'dm/api/v1/minichannellink/insert'
  return request.postJson(url,param)
}
}
