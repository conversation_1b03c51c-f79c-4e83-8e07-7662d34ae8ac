<template>
    <view>
      <swiper :current="current" @change="(e)=>current = e.detail.current" class="swiper" :class="'serverShow-' + skinColor" circular :autoplay="false" :interval="5000" :duration="500" indicator-active-color="#00B484">
        <swiper-item class="swiper-item" v-for="pageNum in serverPageNum" :key="pageNum">
          <view class="item">
            <view class="accompany-bott-item" @tap="gotoOrder(item)" v-for="(item,index) in serverMap.slice(pageNum * pageItemNum,(pageNum + 1) * pageItemNum)" :key="item.id" :style="{backgroundImage:'url(' + file_ctx + 'static/image/business/accompany-doctor/icon-accompany-doctor-era-examine-bg'+ (index+1) +'.png' + ')','background-repeat':'no-repeat','background-size': '100%'}">
              <image :src="file_ctx + item.icon" class="icon"></image>
              <view class="title">{{item.serviceName}}</view>
              <view class="text">{{item.description}}</view>
            </view>
          </view>
        </swiper-item>
      </swiper>
      <view class="swiper-dot" v-if="showDots">
        <view class="swiper-dot-item" :class="{active:current === pageNum}" @click="current = pageNum" v-for="pageNum in serverPageNum" :key="pageNum"></view>
      </view>
    </view>
</template>
<script>
    export default {
        name: 'Carousel',
        props: {
            serverMap: { // 轮播图列表
                type: Array,
                default() {
                    return []
                }
            }
        },
        computed: {
          showDots(){
            let showMap = ['','blueSkin']
            return showMap.includes(this.skinColor)
          }
        },
        watch:{
          serverMap(val){
            this.setPageItem()
          },
          skinColor(){
            this.setPageItem()
          },
        },
        data() {
            return {
              file_ctx: this.file_ctx,
              dotsStyles:{
                width:6,
                bottom:8,
                selectedBackgroundColor:"rgba(72, 157, 240,1)"
              },
                current:0,
                file_ctx: this.file_ctx,
                skinColorMap:{
                    blueSkin:{
                        mapLength:0,
                        serverPageNum: function () {
                          return Math.ceil(this.mapLength / this.pageItemNum)
                        },
                        pageItemNum: 8,
                    },
                    greenSkin:{
                        mapLength:0,
                        serverPageNum: function () {
                            return 1
                        },
                        pageItemNum: 4,
                    }
                },
                // 轮播图分页
                serverPageNum:0,
                // 分页每页显示数量
                pageItemNum:0
            }
        },
        mounted(){

        },
        methods: {
            setPageItem(){
              console.log('this.serverMap',this.serverMap,this.skinColor);
              this.skinColorMap[this.skinColor].mapLength = this.serverMap.length;
              this.serverPageNum = this.skinColorMap[this.skinColor].serverPageNum();
              this.pageItemNum = this.skinColorMap[this.skinColor].pageItemNum;
            },
            // 跳转订单详情页
            gotoOrder(item) {
              const city = this.$common.getKeyVal('user','cityName',true)
                this.$navto.push('ServiceDetail',{
                    id: item.id,
                    city,
                    classifyId: item.classifyId || '',
                    name: item.classifyName || ''
                })
            },
        }
    }
</script>
<style lang="scss">
  @import '../../style/blueSkin.scss';
  .swiper-dot{
    position: absolute;
    bottom: 12rpx;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    .swiper-dot-item{
      width: 12rpx;
      height: 12rpx;
      background: rgba(51,51,51,0.1);
      border-radius: 50%;
      margin-right: 12rpx;
    }
    .swiper-dot-item.active{
      background: #489DF0;
    }
  }
  .swiper{
      width: 100%;
      height: 100%;
      border-radius: 16rpx;
      overflow: hidden;
      position: absolute;
      .swiper-item{
        width: 100%;
        height: 100%;
        flex-direction: column;
        .item{
          width: 100%;
          height: 100%;
          display: flex;
          flex-wrap: wrap;
          box-sizing: border-box;
        }
      }
    }
    .accompany-bott-item{
        height: 136rpx;
        width: 310rpx;
        margin-right: 18rpx;
        margin-bottom: 18rpx;
        padding: 24rpx;
        box-sizing: border-box;
        .icon{
          display: none;
        }
        .title{
          font-size: 30rpx;
          color: #1D2029;
          line-height: 42rpx;
          font-weight: 600;
          white-space: nowrap;      /* 防止文本换行 */
          overflow: hidden;         /* 超出部分隐藏 */
          text-overflow: ellipsis;  /* 显示省略号 */
        }
        .text{
          width: 207rpx;
          margin-top: 2rpx;
          font-size: 22rpx;
          color: #A5AAB8;
          line-height: 32rpx;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
        }
        &:nth-child(2n){
          margin-right: 0;
        }
      }
</style>
