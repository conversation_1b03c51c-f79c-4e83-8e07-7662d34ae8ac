<template>
  <view class="panel">
    <view class="data-item" v-for="(item, index) in panelList" :key="index">
      <view class="data-title">{{ item.title }}</view>
      <view class="data-content">
        <text class="data-num">{{ item.num }}</text>
        <text class="data-unit">{{ item.unit }}</text>
      </view>
      <view class="data-sub-content">
        <text class="data-sub-title">{{ item.subTitle }}</text>
        <text class="data-sub-num">{{ item.subNum }}</text>
        <text class="data-sub-unit">{{ item.subUnit }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import env from '@/config/env'
import { mapState } from 'vuex'
export default {
  props: {
    statisticsData: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    ...mapState('user', {
      recordUserInfo: state => state.recordUserInfo
    }),
    panelList() {
      const {
        myCommissionThisMonth = 0,
        myGuestsThisMonth = 0,
        mySalesThisMonth = 0,
        totalMyCommission = 0,
        totalMyGuests = 0,
        totalMySales = 0

      } = this.statisticsData || {}
      return [
        {
          title: '本月收益',
          num: myCommissionThisMonth || 0,
          unit: '元',
          subTitle: '总收益',
          subNum: totalMyCommission || 0,
          subUnit: '元'
        },
        {
          title: '本月关联客户',
          num: myGuestsThisMonth,
          unit: '人',
          subTitle: '总客户',
          subNum: totalMyGuests,
          subUnit: '人'
        },
        {
          title: '本月客户预约',
          num: mySalesThisMonth,
          unit: '单',
          subTitle: '总订单',
          subNum: totalMySales,
          subUnit: '单'
        }
      ]
    }
  }
}
</script>

<style lang="scss" scoped>
.panel {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}

.data-item {
  width: 33.3%;
  padding: 24rpx 0 24rpx 24rpx;
  border-left: 1px solid #F4F6FA;
  &:nth-child(3n+1) {
    padding-left: 0;
    border-left: none;
  }
  &:nth-child(n+4) {
    border-top: 1px solid #F4F6FA;
  }
}

.data-title {
  font-size: 22rpx;
  color: #4E5569;
  line-height: 32rpx;
  margin-bottom: 8rpx;
}

.data-content {
  display: flex;
  align-items: flex-end;
  margin-bottom: 8rpx;
}

.data-num {
  font-weight: 500;
  font-size: 32rpx;
  color: #1D2029;
  line-height: 44rpx;
}

.data-unit {
  font-weight: 500;
  font-size: 20rpx;
  color: #868C9C;
  line-height: 28rpx;
  padding: 0 0 6rpx 2rpx;
}

.data-sub-content {
  display: flex;
  align-items: center;
}

.data-sub-title {
  font-size: 22rpx;
  color: #4E5569;
  line-height: 32rpx;
}

.data-sub-num {
  font-weight: 500;
  font-size: 26rpx;
  color: #1D2029;
  line-height: 36rpx;
  padding-left: 2rpx;
}

.data-sub-unit {
  font-weight: 500;
  font-size: 20rpx;
  color: #868C9C;
  line-height: 28rpx;
}
</style>