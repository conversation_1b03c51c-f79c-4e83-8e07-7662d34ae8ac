<!--
*
*config  [Object] 参数设置
backgroundColor: #f7f7f7     背景色
titleColor: #333,            文本字体颜色
label: 提示,                 文本
buttonText: 登录,            按钮文本
buttonBgColor: #FFB924,     按钮背景色
required: false,            是否重点提醒
buttonTextColor: #fff,      按钮字体颜色
-------------------------------------------
*disabled: false            是否禁用
*returnFn    [function]     回调函数
*
*局部引入使用 <explain-button  :config="xxx" @returnFn="xxx"></explain-button>
 -->
<template>
  <view v-if="!isLogin">
    <view class="explain-button" :style="{'background': defaultConfig.backgroundColor}">
      <view class="explain-button-l" :style="{'color': defaultConfig.titleColor}">
        {{defaultConfig.label}}
        <text class="star" v-if="defaultConfig.required">*</text>
      </view>
      <view class="explain-button-r clear-float">
        <view @tap="returnFn()" class="e-btn f-r" :style="{'color': defaultConfig.buttonTextColor, 'background': defaultConfig.buttonBgColor}">
          {{defaultConfig.buttonText}}
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex'
export default {
  data() {
    return {
      defaultConfig: {
        titleColor: '#333',
        backgroundColor: '#fff',
        buttonText: '登录/注册',
        buttonBgColor: '#fe4966',
        buttonTextColor: '#fff',
        label: '登录手机号，同步自己的个人记录哦！',
        required: false
      }
    }
  },
  computed: {
    ...mapState('user', {
      isLogin: state => state.isLogin
    })
  },
  watch: {
    config: {
      handler(val) {
        this.copyConfig()
      },
      deep: true
    }
  },
  props: {
    // 是否禁用disable
    disabled: {
      type: Boolean,
      default() {
        return false
      }
    },
    // 参数设置
    config: {
      type: Object,
      required: false,
      default: () => {
        return {}
      }
    },
    routerName: {
      type: String,
      default() {
        return 'Login'
      }
    }
  },
  mounted() {
    this.copyConfig()
  },
  methods: {
    navtoGo(url, obj = {}) {
      this.$navto.push(url, obj)
    },
    /**
       * 初始化拷贝config对象
       */
    copyConfig() {
      const that = this
      const obj = that.config
      Object.keys(obj).forEach(function(key) {
        that.defaultConfig[key] = obj[key]
      })
      that.defaultConfig = Object.assign({}, that.defaultConfig)
    },
    /**
       * picker触发选中事件
       * @param v
       */
    returnFn(v) {
      if (this.disabled) return
      this.navtoGo('Login', { 'redirect': this.routerName })
      // this.$emit('returnFn')
    }
  }
}
</script>

<style lang="scss" scoped>
  .explain-button{
    border-top: 1px solid #f1f1f1;
    border-bottom: 1px solid #f1f1f1;
    padding: 0 30upx;
    height: 88upx;
    .explain-button-l{
      display: inline-block;
      vertical-align: middle;
      font-size: 28upx;
      line-height: 88upx;
      @include ellipsis(1);
      width: calc(100% - 200upx);
      margin-right: 20upx;
    }
    .explain-button-r{
      display: inline-block;
      vertical-align: middle;
      width: 180upx;
      padding: 10upx 0;
      height: 68upx;
      .e-btn{
        @include rounded(6upx);
        max-width: 140upx;
        padding: 0 20upx;
        font-size: 28upx;
        text-align: center;
        line-height: 68upx;
        @include ellipsis(1);
      }
    }
  }
  .star{
    color: #F85E4C;
    padding-top: 6upx;
    font-size: 32upx;
    display: inline-block;
  }
</style>
