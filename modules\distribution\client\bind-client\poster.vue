<template>
  <page>
    <view slot="content" class="page-content">
      <m-nav-bar title="关联客户" :bg-overflow="false" />
      <view class="main">
        <!-- 此处用于展示生成的图片 -->
        <view class="poster-box">
          <image
            :src="posterBg"
            class="poster-bg"
            :style="{
              width: posterStyle.width + 'rpx',
              height: posterStyle.height + 'rpx'
            }"
          ></image>
          <image
            :src="posterQrcode"
            class="poster-qrcode"
            :style="{
              width: posterQrcodeStyle.width + 'rpx',
              height: posterQrcodeStyle.height + 'rpx',
              top: posterQrcodeStyle.y + 'rpx',
              left: posterQrcodeStyle.x + 'rpx',
            }"
          ></image>
        </view>
        <!-- 生成图片 -->
        <poster
          ref="poster"
          :list="list"
          background-color="#FFF"
          :width="posterStyle.width"
          :height="posterStyle.height"
          @on-success="posterSuccess"
          @on-error="posterError"
        ></poster>
      </view>
      <view class="bottom-bar">
        <button class="btn-bg save-btn" type="default" @click="start" :loading="startLoading">
          <image mode="aspectFit" :src="file_ctx + 'static/image/business/moer/icon-download.png'" class="icon-download"></image>
          保存图片
        </button>
      </view>
    </view>
  </page>
</template>

<script>
import { mapState } from 'vuex'
import Poster from '@/components/business/zhangyuhao-poster/Poster.vue'
import { isDomainUrl } from '@/utils/index';
export default {
  components: {
    Poster
  },
  data() {
    return {
      posterBg: this.file_ctx + 'static/image/business/moer/client-invite-bg.jpg',
      posterQrcode: this.file_ctx + 'static/image/business/moer/client-invite-bg.jpg',
      file_ctx: this.file_ctx,
      poster: '',
      list: [],
      startLoading: false
    }
  },
  computed: {
    posterStyle() {
      return {
        type: 'image',
        // path替换成你自己的图片，注意需要在小程序开发设置中配置域名
        path: this.posterBg,
        x: 0,
        y: 0,
        width: 750,
        height: 1316
      }
    },
    posterQrcodeStyle() {
      return {
        type: 'image',
        path: this.posterQrcode,
        x: 275,
        y: 490,
        width: 250,
        height: 250
      }
    },
    ...mapState('user', {
      recordUserInfo: state => state.recordUserInfo
    })
  },
  onLoad() {
    this.getStoreDetail()
  },
  methods: {
    async getStoreDetail() {
      this.$uniPlugin.loading()
      let { storeQrCode } = this.recordUserInfo || {}
      if (!storeQrCode) {
        this.$uniPlugin.hideLoading()
        this.$uniPlugin.toast('获取海报失败，请刷新页面重试')
        return
      }
      if (storeQrCode && storeQrCode.indexOf('http') !== -1 && storeQrCode.indexOf('https') === -1) {
        storeQrCode = storeQrCode.replace('http', 'https')
      }
      this.posterQrcode = isDomainUrl(storeQrCode)
      this.$uniPlugin.hideLoading()
    },
    posterError(err) {
      this.startLoading = false
      console.log(err)
    },
    posterSuccess(url) {
      // 生成成功，会把临时路径在这里返回
      this.poster = url;
      this.$uniPlugin.saveImageToPhotosAlbum({
        filePath: this.poster,
        success: e => {
          this.startLoading = false
          this.$uniPlugin.hideLoading()
          this.$uniPlugin.successToast('海报保存成功')
          console.log('图片保存成功', e)
        },
        fail: e => {
          this.$uniPlugin.hideLoading()
          this.$uniPlugin.toast('图片保存失败')
          console.log('图片保存失败', e)
        }
      }).catch(() => {
        this.startLoading = false
        this.$uniPlugin.hideLoading()
      })
      console.log(url)
    },
    start() {
      this.startLoading = true
      // 赋值需要渲染的信息
      this.list = [this.posterStyle, this.posterQrcodeStyle];
      // 生成图片
      this.$nextTick(() => {
        this.$uniPlugin.loading()
        // 要放在$nextTick()里，不然会空白
        this.$refs.poster.create();
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.poster-box {
  position: relative;
}
.poster-bg {}

.poster-qrcode {
  position: absolute;
}
.poster-text {
  position: absolute;
}
.page-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.main {
  flex: 1;
  overflow: auto;
}

.bottom-bar {
  background-color: #fff;
  padding-bottom: calc(constant(safe-area-inset-bottom) + 26rpx);
  padding-bottom: calc(env(safe-area-inset-bottom) + 26rpx);
}

.save-btn {
  display: flex;
  align-items: center;
  margin-top: 12rpx;
  justify-content: center;
}

.icon-download {
  width: 36rpx;
  height: 36rpx;
  margin-right: 8rpx;
}
</style>
