<template>
  <view class="account-statement">
    <!-- 顶部导航栏 -->
    <m-nav-bar title="分账流水" left-icon="left" @clickLeft="back" />

    <!-- 日期选择区域 -->
    <view class="filter-bar-wrapper">
      <view class="filter-bar">
        <view class="filter-item" @click="selectFilter('time')">
          <text class="filter-text">时间</text>
          <image class="filter-arrow" :src="iconRightArrow" mode="aspectFit"></image>
        </view>
      </view>
    </view>

    <!-- 筛选时间范围 -->
    <view class="filter-time">
      <text class="filter-time-label">筛选时间范围：</text>
      <text class="filter-time-value">{{ filterTimeText || '全部' }}</text>
    </view>

    <!-- 分销员信息栏 -->
    <view class="distributor-info">
      <view class="info-card">
        <view class="info-row">
          <text class="label">分销金额：</text>
          <text class="amount-value">¥ {{ totalAmount }}</text>
        </view>
        <!-- <view class="custom-button" @tap="navtoGo('DistributionWithdrawal')">
          <text>查看全部余额</text>
        </view> -->
      </view>
    </view>

    <!-- 流水记录列表容器 -->
    <view class="record-list">
      <scroll-refresh
        bgColor='#f7f7f7'
        class="scroll-refresh-main"
        :isShowEmptySwitch="false"
        :fixed="false"
        :up="upOption"
        :down="downOption"
        @returnFn="returnFn"
        @scrollInit="scrollInit"
      >
        <view v-if="statementList && statementList.length > 0" class="statement-list">
      <view
        v-for="(record, index) in statementList"
        :key="index"
        class="statement-item"
        @click="goToDetail(record.id)"
      >
        <view class="statement-left">
          <view class="statement-status" :class="{'completed': record.status === '已分账'}">
            {{ record.status }}
          </view>
          <view class="statement-row">
            <text class="statement-label">订单号：</text>
            <text class="statement-value">{{ record.orderNo }}</text>
          </view>
          <view class="statement-time">日期：{{ record.time }}</view>
        </view>
        <view class="statement-right">
          <text class="amount-text">+ ¥{{ record.amount }}</text>
          <text class="rate-text">比例：{{ record.rate }}</text>
        </view>
      </view>
      </view>
      </scroll-refresh>

      <!-- 空数据提示 -->
      <view class="empty-tip" v-if="isDataLoaded && (!statementList || statementList.length === 0)">
        <image src="/static/image/distribution/icon-empty.png" class="empty-icon" mode="aspectFit"></image>
        <view class="empty-text">暂无分账流水数据</view>
      </view>
    </view>

    <!-- 日期选择弹窗 -->
    <view class="picker-container">
      <timePicker
        ref="timePicker"
        :value="timeMap"
        type="daterange"
        @change="handleTimeChange"
        :show="timePickerVisible"
        @cancel="timePickerVisible = false"
      ></timePicker>
    </view>
  </view>
</template>

<script>
import uniNavBar from '@/components/uni/uni-nav-bar/uni-nav-bar.vue'
import timePicker from '../components/uni-datetime-picker/uni-datetime-picker.vue'
import serverOptions from '@/config/env/options'

export default {
  components: {
    uniNavBar,
    timePicker
  },
  data() {
    return {
      iconArrowDown: this.$static_ctx + "image/business/moer/icon-arrow-down.png",
      iconRightArrow: this.$static_ctx + "image/business/hulu-v2/icon-right-arrow.png",
      // 筛选条件
      filterParams: {
        startTime: '', // 开始时间
        endTime: '', // 结束时间
      },
      // 筛选时间文本
      filterTimeText: '',
      // 统计数据
      totalAmount: '0.00',
      // 日期选择
      timePickerVisible: false,
      timeMap: [], // 时间选择器的值
      // 日期选项
      dateOptions: [],
      selectedDateIndex: 0,
      // 分页参数
      currentPage: 1,
      pageSize: 10,
      hasMoreData: true,
      isRefreshing: false,
      isDataLoaded: false,
      // 记录列表
      statementList: [],
      // 下拉刷新配置
      downOption: {
        auto: false
      },
      upOption: {
        auto: false,
        empty: {
          top: 0,
          zIndex: 999,
        },
        onScroll: true
      },
      mescroll: null
    }
  },
  mounted() {
    // 设置默认时间为最近一周
    this.setDefaultWeekRange();
    // 获取总金额
    this.getTotalAmount();
  },
  methods: {
    // 设置默认时间为最近一周
    setDefaultWeekRange() {
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(endDate.getDate() - 6); // 默认显示7天数据（当天+前6天）

      // 格式化起止时间
      const formattedStartDate = this.formatDate(startDate);
      const formattedEndDate = this.formatDate(endDate);

      // 设置时间选择器的值
      this.timeMap = [formattedStartDate, formattedEndDate];

      // 设置筛选参数
      this.filterParams.startTime = formattedStartDate + ' 00:00:00';
      this.filterParams.endTime = formattedEndDate + ' 23:59:59';

      // 设置筛选时间文本
      this.setFilterTimeText(startDate, endDate);

      console.log('默认时间范围：', {
        start: this.filterParams.startTime,
        end: this.filterParams.endTime
      });
    },

    // 格式化日期为YYYY-MM-DD格式
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },

    // 设置筛选时间文本
    setFilterTimeText(startDate, endDate) {
      if (!startDate || !endDate) {
        this.filterTimeText = '';
        return;
      }

      const formatDate = (date) => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
      };

      const startStr = formatDate(startDate);
      const endStr = formatDate(endDate);

      if (startStr === endStr) {
        this.filterTimeText = startStr;
      } else {
        this.filterTimeText = `${startStr} 至 ${endStr}`;
      }
    },

    // 获取分销员ID
    async getDistributorId() {
      try {
        // 从本地存储获取当前用户信息
        const codeUserInfo = this.$common.getKeyVal('user', 'codeUserInfo', true);
        if (!codeUserInfo || !codeUserInfo.id) {
          console.error('未获取到用户ID');
          return null;
        }

        // 调用接口获取分销员信息
        const res = await this.$api.distribution.accompanydistributorQueryOneByUserId({
          userId: codeUserInfo.id,
          providerId: serverOptions.providerId
        });

        // 如果接口返回成功且有分销员信息
        if (res.code === 0 && res.data && res.data.id) {
          console.log('获取到分销员ID:', res.data.id);
          return res.data.id; // 返回分销员ID
        } else {
          console.warn('用户不是分销员或获取分销员信息失败');
          return null;
        }
      } catch (error) {
        console.error('获取分销员信息异常:', error);
        return null;
      }
    },

    // 获取分账总金额
    async getTotalAmount() {
      try {
        uni.showLoading({
          title: '加载中...'
        });

        // 构建查询参数
        const condition = {
          // providerId: serverOptions.providerId || null
        };

        // 添加时间筛选条件
        if (this.filterParams.startTime) {
          condition.startPayTime = this.filterParams.startTime;
        }
        if (this.filterParams.endTime) {
          condition.endPayTime = this.filterParams.endTime;
        }

        // 获取分销员ID并添加到查询条件
        const distributorId = await this.getDistributorId();
        if (distributorId) {
          condition.distributorId = distributorId;
        }
        condition.providerId = serverOptions.providerId;
        // 调用分销流水金额查询接口
        const params = {
          condition: condition
        };
        const res = await this.$api.distribution.accompanydistriburecordQueryAmount(params);

        if (res && res.code === 0) {
          // 从响应中获取总金额并进行格式化
          this.totalAmount = res.data ? (res.data / 100).toFixed(2) : '0.00';
        }

        uni.hideLoading();
      } catch (error) {
        console.error('获取总金额失败:', error);
        uni.hideLoading();
        this.totalAmount = '0.00';
      }
    },

    // 返回上一页
    back() {
      uni.navigateBack();
    },

    // 格式化日期时间
    formatDateTime(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },

    // 初始化滚动组件
    scrollInit(scroll) {
      scroll.optUp.page.num = 1;
      scroll.optUp.page.size = 10;
      this.mescroll = scroll;

      // 在初始化滚动组件后立即触发下拉刷新
      this.refreshRecords();
    },

    // 下拉刷新回调
    returnFn(obj) {
      const that = this;

      // 标记数据尚未加载完成
      this.isDataLoaded = false;

      setTimeout(() => {
        // 处理必要的参数
        const processedParams = {
          // providerId: serverOptions.providerId || null
        };

        // 添加时间筛选条件
        if (this.filterParams.startTime) {
          processedParams.startPayTime = this.filterParams.startTime;
        }
        if (this.filterParams.endTime) {
          processedParams.endPayTime = this.filterParams.endTime;
        }

        // 获取分销员ID并添加到查询条件
        this.getDistributorId().then(distributorId => {
          if (distributorId) {
            processedParams.distributorId = distributorId;
          }

          console.log('请求参数：', processedParams);

          let params = {
            current: obj.pageNum,
            size: obj.pageSize,
            condition: processedParams
          };

          // 调用分销流水分页查询API
          that.$api.distribution.accompanydistriburecordQueryPage(params).then(res => {
            // 从响应中获取数据
            let data = res.data?.records || [];

            // 如果是第一页，清空列表
            if (obj.pageNum === 1) {
              that.statementList = [];
            }

            // 处理数据
            if (Array.isArray(data)) {
              // 处理数据格式化
              const formattedData = data.map(item => {
                return {
                  id: item.id,
                  orderNo: item.businessId || item.accompanyId || '--', // 使用businessId作为订单号，与详情页保持一致
                  status: this.getSeparateStatusText(item.separateStatus !== undefined ? item.separateStatus : item.commission), // 优先使用separateStatus字段
                  amount: item.separateAmount ? (item.separateAmount / 100).toFixed(2) : '0.00',
                  rate: this.formatRate(item.separateRate),
                  time: item.tradeTime ? that.formatShortDate(item.tradeTime) : (item.payTime ? that.formatShortDate(item.payTime) : '--') // 优先使用tradeTime字段
                };
              });

              that.statementList = [...that.statementList, ...formattedData];
          }

            // 标记数据已加载
            that.isDataLoaded = true;

            // 调用回调
            obj.successCallback && obj.successCallback(data, {
              curPageLen: data.length,
              totalPage: res.data?.pages || 1,
              totalSize: res.data?.total || 0
            });
          }).catch(err => {
            console.error('获取数据失败:', err);
            that.isDataLoaded = true;

            // 错误回调
            obj.successCallback && obj.successCallback([], {
              curPageLen: 0,
              totalPage: 1,
              totalSize: 0
            });

            uni.showToast({
              title: '获取数据失败',
              icon: 'none'
            });
          });
        }).catch(error => {
          console.error('获取分销员ID失败:', error);
          that.isDataLoaded = true;

          // 错误回调
          obj.successCallback && obj.successCallback([], {
            curPageLen: 0,
            totalPage: 1,
            totalSize: 0
          });

          uni.showToast({
            title: '获取分销员ID失败',
            icon: 'none'
          });
        });
      }, 500);
    },

    // 刷新记录
    refreshRecords() {
      this.$nextTick(() => {
        if (this.mescroll) {
          this.mescroll.triggerDownScroll();
        }
      });
    },

    // 选择筛选条件
    selectFilter(type) {
      if (type === 'time') {
        this.timePickerVisible = true;
        this.$nextTick(() => {
          this.$refs.timePicker && this.$refs.timePicker.show();
        });
      }
    },

    // 跳转
    navtoGo(url, obj = {}) {
      this.$navto.push(url, obj)
    },

    // 时间选择回调
    handleTimeChange(e) {
      console.log('时间选择变化：', e);
      if (e.length === 2) {
        const [start, end] = e;
        this.timeMap = [start, end];

        // 直接使用日期字符串，添加时分秒
        this.filterParams.startTime = start + ' 00:00:00';
        this.filterParams.endTime = end + ' 23:59:59';

        // 设置筛选时间文本
        const startDate = new Date(start);
        const endDate = new Date(end);
        this.setFilterTimeText(startDate, endDate);

        console.log('设置的时间范围：', {
          start: this.filterParams.startTime,
          end: this.filterParams.endTime
        });
      } else {
        this.timeMap = [];
        this.filterParams.startTime = '';
        this.filterParams.endTime = '';
        this.filterTimeText = '';
      }
      this.timePickerVisible = false;

      // 更新分账总金额
      this.getTotalAmount();

      // 重新加载数据
      this.refreshRecords();
    },

    // 格式化短日期
    formatShortDate(dateString) {
      if (!dateString) return '2024.4.23 12:00';

      try {
        // 尝试处理不同格式的日期
        let date;
        if (typeof dateString === 'string' && dateString.length === 14) {
          // 处理格式: yyyyMMddHHmmss
          const year = dateString.substring(0, 4);
          const month = dateString.substring(4, 6).replace(/^0/, '');
          const day = dateString.substring(6, 8).replace(/^0/, '');
          const hours = dateString.substring(8, 10);
          const minutes = dateString.substring(10, 12);
          return `${year}.${month}.${day} ${hours}:${minutes}`;
        } else {
          // 尝试作为标准日期处理
          date = new Date(dateString);
          const year = date.getFullYear();
          const month = date.getMonth() + 1;
          const day = date.getDate();
          const hours = String(date.getHours()).padStart(2, '0');
          const minutes = String(date.getMinutes()).padStart(2, '0');
          return `${year}.${month}.${day} ${hours}:${minutes}`;
        }
      } catch (e) {
        return dateString;
      }
    },

    // 根据状态码获取状态文本
    getSeparateStatusText(status) {
      const statusMap = {
        '0': '待分账',
        '1': '已分账'
      };
      return statusMap[status] || '未知状态';
    },

    // 格式化分账比例
    formatRate(rate) {
      if (!rate) return '0%';

      // 尝试将rate转为数字并乘以100
      try {
        const rateValue = parseFloat(rate);
        // 转为整数百分比，如果是整数则不显示小数点后的零
        const percent = (rateValue * 100);
        return percent % 1 === 0 ? Math.floor(percent) + '%' : percent.toFixed(2) + '%';
      } catch (e) {
        return rate + '%';
      }
    },

    // 跳转到详情页面
    goToDetail(id) {
      if (!id) {
        uni.showToast({
          title: '无效的记录ID',
          icon: 'none'
        });
        return;
      }

      // 仅传递ID，避免中文和复杂数据结构导致的乱码问题
      this.$navto.push('DistributStatementDetail', {
        id: id
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.account-statement {
  min-height: 100vh;
  height: 100vh;
  background-color: #f7f7f7;
  display: flex;
  flex-direction: column;
}

/* 新增样式 - 状态文本 */
.statement-status {
  font-size: 36rpx;
  font-weight: 500;
  margin-bottom: 15rpx;
  color: #ff9500; /* 待分账颜色 */
}

.statement-status.completed {
  color: #00b484; /* 已分账颜色 */
}

/* 日期选择区域 */
.filter-bar-wrapper {
  background-color: #FFFFFF;
  padding: 0;
  border-bottom: 1rpx solid #F2F2F2;
  width: 100%;
}

.filter-bar {
  display: flex;
  width: 100%;
  height: 88rpx;
}

.filter-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  position: relative;
}

.filter-text {
  font-size: 28rpx;
  color: #333333;
  margin-right: 8rpx;
  font-weight: 500;
}

.filter-arrow {
  width: 24rpx;
  height: 24rpx;
  transform: rotate(90deg);
  position: relative;
  top: 2rpx;
}

/* 筛选时间范围 */
.filter-time {
  background-color: #FFFFFF;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #F2F2F2;
  width: 100%;
}

.filter-time-label {
  font-size: 28rpx;
  color: #333;
  margin-right: 10rpx;
}

.filter-time-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 分销员信息栏 */
.distributor-info {
  padding: 20rpx 30rpx;
  background-color: #f7f7f7; /* 与整体背景保持一致 */
}

.info-card {
  background-color: #ffffff;
  border-radius: 10rpx;
  padding: 25rpx 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 15rpx; /* 添加底部外边距，与订单项保持一致 */
}

.label {
  font-size: 28rpx;
  color: #333;
}

.amount-value {
  font-size: 28rpx;
  color: #FF5000;
  font-weight: 500;
}

.custom-button {
  font-size: 26rpx;
  color: #00b484;
  padding: 10rpx 20rpx;
  background-color: rgba(0, 180, 132, 0.1);
  border-radius: 30rpx;
}

/* 流水记录列表容器 */
.record-list {
  flex: 1;
  background-color: #f7f7f7;
  position: relative;
  display: flex;
  flex-direction: column;
  padding-top: 15rpx; /* 顶部添加间距 */
}

.statement-list {
  padding: 0 30rpx;
}

.scroll-refresh-main {
  flex: 1;
  height: 100%;
  position: relative;
  background-color: #f7f7f7;
  /* 确保安卓上显示正确 */
  min-height: 500rpx;
}

.statement-item {
  background-color: #ffffff;
  border-radius: 15rpx;
  margin-bottom: 20rpx;
  padding: 25rpx;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  box-shadow: 0 1rpx 5rpx rgba(0, 0, 0, 0.02);
  box-sizing: border-box;
  width: 100%;
}

.statement-left {
  flex: 1;
  min-width: 0;
  padding-right: 20rpx;
  box-sizing: border-box;
}

.statement-row {
  display: flex;
  margin-bottom: 12rpx;
  width: 100%;
}

.statement-label {
  font-size: 28rpx;
  color: #333;
  flex-shrink: 0;
  margin-right: 2rpx;
}

.statement-value {
  font-size: 28rpx;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

.statement-time {
  font-size: 24rpx;
  color: #999;
  margin-top: 6rpx;
}

.statement-right {
  width: 180rpx;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: center;
}

.amount-text {
  font-size: 32rpx;
  color: #FF5000;
  font-weight: 500;
  margin-bottom: 6rpx;
  text-align: right;
}

.rate-text {
  font-size: 24rpx;
  color: #FF5000;
  text-align: right;
  white-space: nowrap;
}

/* 空数据提示 */
.empty-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #f7f7f7;
  z-index: 1;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 日期选择器容器 */
.picker-container {
  position: absolute;
  width: 0;
  height: 0;
  overflow: hidden;
}
</style>
