<template>
	<view>
		<title-showmodel :show='visible' :autotitle='true' @cancel='cancel' :clickmask='false'
			popper-class='showpickermodel'>
			<view class="checkbox-t" slot='title'></view>
			<view class="checkbox-t">

				<view class="checkbox-l" @click="cannel">取消</view>
				<view class="flex1"></view>
				<view class="checkbox-r" @click="query">确认</view>
			</view>
			<view class="pickerbox" v-if="update && type == 1">
				<!-- <view>{{year}}年{{month}}月{{day}}日</view> -->
				<picker-view indicator-style="height: 50px;" style="width: 100%; height: 300px;" :value="currentvalue"
					@change="bindChange">
					<picker-view-column class="flex2">
						<block v-for="item,index in optionArr" :key="item.id">
							<view class="ceil">{{item.label}}</view>
						</block>
					</picker-view-column>
					<picker-view-column class="flex1">
						<view v-for="item,index in hours" :key="item.label" class="ceil">{{item.label}}</view>
					</picker-view-column>
					<picker-view-column class="flex1">
						<view v-for="item,index in minutes" :key="item.label" class="ceil">{{item.label}}</view>
					</picker-view-column>
				</picker-view>
			</view>
		</title-showmodel>
	</view>
</template>

<script>
	import titleShowmodel from '../title-showmodel/index.vue';
	const date = new Date()


	export default {
		name: "ly-date-timer-picker",
		components: {
			titleShowmodel
		},
		mounted() {
			const years = []
			this.currentTimer = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();
			// console.log(this.data.currentTimer)
			// 生成时间
			this.creatTimer();







			this.years = years;

			// this.setData({
			// 	years: years,

			// 	// months: months,
			// 	// days: days
			// })
		},
		props: {
			visible: {
				type: Boolean,
				default: false,

			},
			current: {
				type: Array,
				default: [],

			},

			type: {
				type: Number,
				default: 1,
			}
		},
		watch: {
			visible(news) {
				if (!this.currentvalue[2] && news) {
					this.currentvalue[2] = 0
				}
				if (!this.currentvalue[1] && news) {
					this.currentvalue[1] = 0
				}
			},
			current(news) {
				// console.log(news)
				var one = this.objecttimer[news[0]];
				// console.log(one)
				// var two = this.objecttimer[news[1]]
				if (one) {
					this.currentvalue = [one, news[1], news[2]]
					// this.setData({
					// 	currentvalue: [one, news[1], news[2]]
					// })
				} else {
					this.currentvalue[1] = 0;
					this.currentvalue[2] = 0;
					// this.currentvalue =
					// this.setData({
					// 	currentvalue: this.currentvalue
					// })
					// this.setData({
					//   currentvalue: [one, news[1], news[2]]
					// })
				}
			}
		},
		data() {
			return {

				year: date.getFullYear(),
				currentvalue: [0, 0, 0],
				// 支持
				step: 5,
				hours: [{
					"label": "01",
					"value": 1
				}, {
					"label": "02",
					"value": 2
				}, {
					"label": "03",
					"value": 3
				}, {
					"label": "04",
					"value": 4
				}, {
					"label": "05",
					"value": 5
				}, {
					"label": "06",
					"value": 6
				}, {
					"label": "07",
					"value": 7
				}, {
					"label": "08",
					"value": 8
				}, {
					"label": "09",
					"value": 9
				}, {
					"label": 10,
					"value": 10
				}, {
					"label": 11,
					"value": 11
				}, {
					"label": 12,
					"value": 12
				}, {
					"label": 13,
					"value": 13
				}, {
					"label": 14,
					"value": 14
				}, {
					"label": 15,
					"value": 15
				}, {
					"label": 16,
					"value": 16
				}, {
					"label": 17,
					"value": 17
				}, {
					"label": 18,
					"value": 18
				}, {
					"label": 19,
					"value": 19
				}, {
					"label": 20,
					"value": 20
				}, {
					"label": 21,
					"value": 21
				}, {
					"label": 22,
					"value": 22
				}, {
					"label": 23,
					"value": 23
				}], // 小时数
				minutes: [{
					"label": "00",
					"value": 0
				}, {
					"label": "01",
					"value": 1
				}, {
					"label": "02",
					"value": 2
				}, {
					"label": "03",
					"value": 3
				}, {
					"label": "04",
					"value": 4
				}, {
					"label": "05",
					"value": 5
				}, {
					"label": "06",
					"value": 6
				}, {
					"label": "07",
					"value": 7
				}, {
					"label": "08",
					"value": 8
				}, {
					"label": "09",
					"value": 9
				}, {
					"label": 10,
					"value": 10
				}, {
					"label": 11,
					"value": 11
				}, {
					"label": 12,
					"value": 12
				}, {
					"label": 13,
					"value": 13
				}, {
					"label": 14,
					"value": 14
				}, {
					"label": 15,
					"value": 15
				}, {
					"label": 16,
					"value": 16
				}, {
					"label": 17,
					"value": 17
				}, {
					"label": 18,
					"value": 18
				}, {
					"label": 19,
					"value": 19
				}, {
					"label": 20,
					"value": 20
				}, {
					"label": 21,
					"value": 21
				}, {
					"label": 22,
					"value": 22
				}, {
					"label": 23,
					"value": 23
				}, {
					"label": 24,
					"value": 24
				}, {
					"label": 25,
					"value": 25
				}, {
					"label": 26,
					"value": 26
				}, {
					"label": 27,
					"value": 27
				}, {
					"label": 28,
					"value": 28
				}, {
					"label": 29,
					"value": 29
				}, {
					"label": 30,
					"value": 30
				}, {
					"label": 31,
					"value": 31
				}, {
					"label": 32,
					"value": 32
				}, {
					"label": 33,
					"value": 33
				}, {
					"label": 34,
					"value": 34
				}, {
					"label": 35,
					"value": 35
				}, {
					"label": 36,
					"value": 36
				}, {
					"label": 37,
					"value": 37
				}, {
					"label": 38,
					"value": 38
				}, {
					"label": 39,
					"value": 39
				}, {
					"label": 40,
					"value": 40
				}, {
					"label": 41,
					"value": 41
				}, {
					"label": 42,
					"value": 42
				}, {
					"label": 43,
					"value": 43
				}, {
					"label": 44,
					"value": 44
				}, {
					"label": 45,
					"value": 45
				}, {
					"label": 46,
					"value": 46
				}, {
					"label": 47,
					"value": 47
				}, {
					"label": 48,
					"value": 48
				}, {
					"label": 49,
					"value": 49
				}, {
					"label": 50,
					"value": 50
				}, {
					"label": 51,
					"value": 51
				}, {
					"label": 52,
					"value": 52
				}, {
					"label": 53,
					"value": 53
				}, {
					"label": 54,
					"value": 54
				}, {
					"label": 55,
					"value": 55
				}, {
					"label": 56,
					"value": 56
				}, {
					"label": 57,
					"value": 57
				}, {
					"label": 58,
					"value": 58
				}, {
					"label": 59,
					"value": 59
				}, {
					"label": 60,
					"value": 60
				}], // 分钟数

				dayArr: [],
				optionArr: [],
				objecttimer: {},
				// visible: true,
				timerArr: [{
						label: "上午",
						value: 1
					},
					{
						label: "下午",
						value: 2
					}
				],
				update: false,
			};
		},
		methods: {
			query() {
				if (this.type == 1) {
					var indx1 = this.currentvalue[0];
					var indx2 = this.currentvalue[1];
					var indx3 = this.currentvalue[2];
					console.log(this.currentvalue)
					var year = this.optionArr[indx1];
					var hour = this.hours[indx2] || this.hours[0];
					var minute = this.minutes[indx3] || this.minutes[0];

					// console.log()
					var obj = {
						year,
						hour,
						minute,
					}
					console.log(obj)
					this.$emit('query', obj)
				} else {
					var indx1 = this.currentvalue[0];
					var indx2 = this.currentvalue[1];
					var year = this.optionArr[indx1];
					var hour = this.timerArr[indx2];
					var obj = {
						year,
						hour,
					}
					// console.log(obj)
					this.$emit('query', obj)
				}



			},
			cannel() {
				this.$emit('cannel', {})
			},


			bindChange: function(e) {
				const val = e.detail.value
				console.log(val)
				this.currentvalue = val;
				//   this.setData({
				//     year: this.years[val[0]],
				//     month: this.months[val[1]],
				//     day: this.days[val[2]]
				//   })
			},
			getYearsList(num) {
				const cur = date.getFullYear();

				let cur1, cur2;

				const before = [];

				const after = [];

				for (let i = 0; i < num; i++) {
					cur1 = cur - 1;

					cur2 = cur + 1;

					before.unshift(cur1); // 在开头添加

					after.push(cur2); // 在结尾添加
				}

				return [...before, cur, ...after];
			},
			creatTimer() {
				var dataArr = this.getYearsList(2);
				//   console.log(dataArr);
				var optionArr = [];
				var weeks = new Array(
					"星期日",
					"星期一",
					"星期二",
					"星期三",
					"星期四",
					"星期五",
					"星期六"
				);
				for (var i = 0; i < dataArr.length; i++) {
					this.createTimerOption(dataArr[i], optionArr, weeks);
				}
				//   console.log(this.optionArr);
				// 时间和日期
				// dayArr = JSON.parse(JSON.stringify(optionArr));

				//   this.years = optionArr;
				this.update = true;
				// console.log(this.currentvalue)
			},
			createTimerOption(year, optionArr, weeks) {
				var myDate = new Date(year + "-01-01"); // 填入 特定的 日期 '2021-01-20'
				// 获取年的一年多少天
				var yearDatas = 0;
				var yearDatas_d = null;
				var day;
				for (var i = 1; i < 13; i++) {
					yearDatas_d = new Date(year, i, 0);
					yearDatas += yearDatas_d.getDate();
				}

				for (var i = 0; i < yearDatas; i++) {
					var dateTemp;
					if (myDate.getFullYear() != this.year) {
						dateTemp =
							myDate.getFullYear() +
							"年" +
							(myDate.getMonth() + 1) +
							"月" +
							myDate.getDate() +
							"号  " +
							weeks[myDate.getDay()];
					} else {
						dateTemp =

							(myDate.getMonth() + 1) +
							"月" +
							myDate.getDate() +
							"号  " +
							weeks[myDate.getDay()];
					}
					let one = myDate.getFullYear() +
						"-" +
						(myDate.getMonth() + 1) +
						"-" +
						myDate.getDate();
					if (one == this.currentTimer) {
						this.currentvalue[0] = this.optionArr.length;
						dateTemp = '今天'
					}
					this.objecttimer[one] = this.optionArr.length
					// console.log(dateTemp);
					this.optionArr.push({
						label: dateTemp,
						value: one,
						children: [{
								label: "上午",
								value: 1,
							},
							{
								label: "下午",
								value: 2,
							},
						],
						id: myDate.getTime()
					});
					this.dayArr.push({
						label: dateTemp,
						value: myDate.getFullYear() +
							"-" +
							(myDate.getMonth() + 1) +
							"-" +
							myDate.getDate(),
						leaf: false,
					});
					myDate.setDate(myDate.getDate() + 1);
				}

				console.log(yearDatas);
			}
		},
	}
</script>

<style scoped>
	.pickerbox {
		background-color: #fff;
	}

	.flex2 {
		flex: 2
	}

	.flex1 {
		flex: 1
	}

	.ceil {
		line-height: 50px;
		text-align: center;
	}

	.showpickermodel {
		top: calc(100vh - 400px) !important
	}

	.checkbox-t {
		display: flex;
		width: 100%;
		font-size: 34rpx;
		border-bottom: 1rpx solid #dbdbdb;
		height: 100rpx;
		padding: 0 20rpx;
		align-items: center;
		box-sizing: border-box;
	}

	.checkbox-l {
		/* flex:1; */
		color: #7c7c7c;
	}

	.checkbox-r {
		color: var(--themeColor);
	}
</style>
