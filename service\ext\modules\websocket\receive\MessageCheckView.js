import MessageDefault from "./MessageDefault";

export default class MessageCheckView extends MessageDefault {
    constructor () {
        super()
    }

    // 异步获取元素信息
    getEl (getDiv) {
        return new Promise((resolve, reject) => {
            // #ifdef H5
            resolve(getDiv.getBoundingClientRect())
            // #endif
            
            // #ifndef H5
            if (getDiv.boundingClientRect) {
                getDiv.boundingClientRect(data => {
                    resolve(data)
                }).exec()
            }
            // #endif
        })
    }

    // 判断DIV 是否隐藏
    isDivDisplay (el) {
        let ishidden = false
        // #ifdef H5
        if (el.style.display === 'none') {
            ishidden = true
        }
        // #endif

        // #ifndef H5
        // if (el.boundingClientRect) {
        //   el.boundingClientRect(data => {
        //     console.log('isDivDisplay------------------', data)
        //     // inputHeight = data.height
        //   }).exec()
        // }
        // #endif
        return ishidden
    }

    // 判断是否在可视区域
    isElementInViewport (getDiv) {
        return new Promise((resolve, reject) => {
            this.inputHeight = this.$common.getKeyVal('chat', 'bottomBoxHeight', false)
            uni.getSystemInfo({
                success: async (res) => {
                    let viewPortHeight = res.windowHeight
                    const elInfo = await this.getEl(getDiv)
                    let top = 0
                    if (!this.$validate.isNull(elInfo)) {
                        top = elInfo.top
                    } else {
                        resolve(false)
                    }
                    viewPortHeight = viewPortHeight - this.inputHeight
                    resolve(top <= viewPortHeight && top >= 0)
                },
                fail: (err) => {reject(err)}
            })
        })
    }
}