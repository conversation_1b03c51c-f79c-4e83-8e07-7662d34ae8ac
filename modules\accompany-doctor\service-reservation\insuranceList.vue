<template>
  <view class="main">
    <view class="l-main">
      <tabs-sticky :fontBigger="true" :bdb="false" :overflowX="true" :overflowY="false" v-model="curIndex" :tabs="tabs" @change="changeTab"></tabs-sticky>
    </view>
    <scroll-refresh class="scroll-refresh-main" :isShowEmptySwitch="true" :fixed="false" :up="upOption" :down="downOption" @returnFn="returnFn" @scrollInit="scrollInit">
      <view class="order-list">
        <view class="order-item" v-for="item in contentList" :key="item.id">
          <view class="order-item-box">
            <view class="order-item-t">
              <view class="order-item-t-l">{{serverOptions.productCodeMap.filter(e=>e.productCode === item.productCode)[0].name}}</view>
              <view
                class="order-item-t-r"
                :style="{color:getColorMap[item.insureStatus]}">
                  {{ handleFilterOrderState(item.insureStatus,getInsuranceNum(item)) || '' }}
              </view>
            </view>
            <view class="order-item-b">
              <view class="item">
                  <view class="itemTitle">购买人：</view><span>{{ item.name }}</span>
              </view>
            <view class="item">
                  <view class="itemTitle">有效时间：</view><span>{{ timestampToDateTime(item.startDate) }}~{{ timestampToDateTime(item.endDate) }}</span>
              </view>
              <view class="item">
                  <view class="itemTitle">服务费用：</view><span>￥{{getInsuranceNum(item)}}</span>
              </view>
              <view class="item">
                  <view class="itemTitle">订单号：</view><span>{{ item.policyNo }}</span>
                  <text class="copy" @click="handleCopyOrder(item.policyNo)">复制</text>
              </view>
          </view>
          <view class="action">
            <view class="actionItem" @click="ConfirmPurchaseInsurance('invoiceOpen',item.accompanyId,item.id,item.insureStatus,item.invoiceOpen,item.invoiceUrl)">{{['开票', '开票中', '开票完成', '开票'][item.invoiceOpen]}}</view>
            <view class="actionItem" @click="getEpolicyUrl(item.policyUrl,item.insureStatus)">门诊无忧服务单</view>
            <view class="actionItem" @click="ConfirmPurchaseInsurance('surrender',item.accompanyId,item.id,item.insureStatus)">取消保障</view>
          </view>
          <!-- 底部开票错误 -->
          <view class="errorBottom" v-if="item.invoiceOpen == 3">备注：开票失败点击重新开票</view>
        </view>
        </view>
      </view>
    </scroll-refresh>
    <modelPopup @finish='finishModel' :modelType='modelType' @secondaryfinish='secondaryfinish' @change='flag=>openFlagModel = flag' :openFlag='openFlagModel'></modelPopup>
    <customerService 
      ref="customerService" 
      :customTitle="'客服'" 
      :popupTitle="'门诊无忧服务'" 
      serviceType="insurance"
      servicePhone="4000785968"
      managementPhone="956030"
    ></customerService>
  </view>
</template>

<script>
  import TabsSticky from '@/components/basics/tabs-sticky-v3'
  import { mapState } from 'vuex'
  import serverOptions from '@/config/env/options'
  import modelPopup from './components/model.vue'
  import customerService from '../components/customerService.vue'
  export default {
    components:{
      TabsSticky,
      modelPopup,
      customerService
    },
    data(){
      return{
        serverOptions,
        file_ctx: this.file_ctx,
        curIndex:0,
        statusBarHeight: 0,
        tabs:[{name:'全部',id:null},{name:'待生效',id:0},{name:'保障中',id:1},{name:'已完成',id:2},{name:'已取消保障',id:3}],
        getColorMap:['','#1687F7','#FFB130','#1D2029','#00926B'],
        mescroll: null, // mescroll实例对象
        downOption: {
          auto: false // 不自动加载
        },
        upOption: {
          auto: false, // 不自动加载
          empty: {
            top: 0,
            zIndex: 999,
          }
        },
        contentList:[],
        insureStatus:null,
        right_o: this.file_ctx + 'static/image/business/hulu-v2/right-o.png',
        baox: this.file_ctx + 'static/image/business/hulu-v2/baox.png',
        openFlagModel:false,
        modelType:'',
        accompanyId:'',
        currentId:''
      }
    },
    computed: {
      ...mapState('user', {
        curSelectUserInfo: state => state.curSelectUserInfo
      }),
    },
    async onLoad({accompanyId}){
      console.log('accompanyId',accompanyId);

      if(accompanyId){
        let {data} = await this.$api.accompanyDoctor.accompanyinsureQuery({accompanyId})
        console.log('data',data)
        if(!data) return
        this.ConfirmPurchaseInsurance('invoiceOpen',data.accompanyId,data.id)
      }
    },
    mounted(){
      this.$nextTick(() => {
        this.init()
      })
      this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight // 获取设备的状态栏高度
    },
    methods:{
      timestampToDateTime(timestamp, flag) {
        if (!timestamp) return "";
        // 创建一个新的Date对象，传入的时间戳是以毫秒为单位的
        var date = new Date(timestamp);
        // 获取年、月、日、时、分、秒
        var year = date.getFullYear();
        var month = ("0" + (date.getMonth() + 1)).slice(-2); // 月份是从0开始的，所以+1，并补零
        var day = ("0" + date.getDate()).slice(-2); // 获取天数，并补零
        var hour = ("0" + date.getHours()).slice(-2); // 获取小时，并补零
        var minute = ("0" + date.getMinutes()).slice(-2); // 获取分钟，并补零
        if (flag) return `${month}.${day} ${hour}:${minute}`;
        // 返回格式化的字符串
        return `${year}.${month}.${day} ${hour}:${minute}`;
      },
      getInsuranceNum(item){
        return this.serverOptions.productCodeMap.filter(e=>e.productCode === item.productCode)[0]?.insuranceNum
      },
      // 唤醒模块弹窗
      ConfirmPurchaseInsurance(type,accompanyId,id,insureStatus,invoiceOpen,invoiceUrl){
        if(insureStatus === 3) return uni.showToast({ title: "该订单已退单", icon: "none" });
        console.log('type',type);
        // 开票
        if(type === 'invoiceOpen'){
          switch (invoiceOpen){
            case 1:
              uni.showModal({
              	title: '正在开票中',
              	content: '将于 5 个工作日完成开票并通过短信发送，请留意短信通知'
              });
              return
              break;
            case 2:
            if(!invoiceUrl) return uni.showToast({ title: "暂无发票", icon: "none" });
            return uni.previewImage({
              			urls: [invoiceUrl],
              			longPressActions: {
              				itemList: ['发送给朋友', '保存图片', '收藏'],
              				success: function(data) {
              					console.log('选中了第' + (data.tapIndex + 1) + '个按钮,第' + (data.index + 1) + '张图片');
              				},
              				fail: function(err) {
              					console.log(err.errMsg);
              				}
              			}
              		});
              break;
            default:
              break;
          }
        }
        this.openFlagModel = true;
        this.modelType = type;
        this.accompanyId = accompanyId;
        this.currentId = id;
      },
      async finishModel({modelType,options}){
        console.log('{modelType,options}',{modelType,options});
        let apiFunc;
        switch (modelType){
            // 取消保障
            case 'surrender':
              apiFunc = this.$api.accompanyDoctor.accompanyinsureCancel;
              options.accompanyId = this.accompanyId;
              this.init()
            break;
            // 取消保障成功
            case 'surrenderCG':
            break;
            // 开票
            case 'invoiceOpen':
            apiFunc = this.$api.accompanyDoctor.accompanyinsureInvoiceOpen;
            options.orderId = this.accompanyId;
            break;
          default:
            break;
        }
        uni.showLoading()
        if(!apiFunc) apiFunc = ()=>{}
        let data = await apiFunc(options);
        uni.hideLoading()
        this.openFlagModel = false;
        if(modelType === 'surrender'){
          //唤醒取消保障成功弹窗
          this.ConfirmPurchaseInsurance('surrenderCG')
          this.init()
        }
        if(modelType === 'invoiceOpen'){
          //唤醒打开发票弹窗
          this.ConfirmPurchaseInsurance('invoiceOpenCG')
          this.init()
        }
      },
      secondaryfinish({modelType}){
        switch (modelType){
            case 'surrender':
              this.openFlagModel = false;
              break;
          default:
            break;
        }
      },
      handleCopyOrder(text) {
        uni.setClipboardData({
          data: text,
          success: function () {
            uni.showToast({ title: "复制成功", icon: "success" });
          },
        });
      },
      async getEpolicyUrl(url,insureStatus){
        if(insureStatus === 3) return uni.showToast({ title: "该订单已退单", icon: "none" });
        if(!url) return uni.showToast({ title: "暂无门诊无忧服务单", icon: "none" });
        uni.showLoading({title:'加载中',mask:true})
        wx.downloadFile({ //将文档下载到本地
                url,//pdf链接
                success(res) {
                  wx.openDocument({ //打开文档
                    filePath: res.tempFilePath,//本地文档路径
                    fileType: "pdf",//文档类型
                    showMenu: true,
                    success: function (res) {
                      wx.showToast({
                        title: '打开文档成功',
                      })
                      uni.hideLoading()
                    },
                    fail: function (res) {
                      wx.showToast({
                        title: '打开文档失败',
                      })
                      uni.hideLoading()
                    },
                  })
                },
                fail(res) {
                  console.log('下载文档失败',res);
                  
                  wx.showToast({
                    title: '下载文档失败',
                  })
                  uni.hideLoading()
                }
              })
      },
      showInsuranceTips(type){
        let showList = [2,3,4,5];
        if(showList.indexOf(type) >= 0) return true
        return false
      },
      handleFilterOrderState(type,insuranceNum){
        switch(type){
          case 0:
            return '待生效'
          case 1:
            return '保障中'
          case 2:
            return '已完成'
          case 3:
            return `已取消保障,已退款￥${insuranceNum}`
        }
      },
      changeTab(index) {
        this.insureStatus = this.tabs[index].id
        this.init()
      },
      returnFn(obj) {
        const that = this
        const providerId = this.$common.getKeyVal('user','providerId',true)
        setTimeout(function () {
          let params = {
            current: obj.pageNum,
            size: obj.pageSize,
            condition: {
              insureStatus:that.insureStatus,
              providerId:providerId,
            }
          }
          that.$api.accompanyDoctor.accompanyinsureQueryMiniPage(params).then(res => {
            // let data = res.data.records.map(item=>({...item,listCover:isDomainUrl(item.listCover)}))
            let data = res.data.records.map(item=>(
              {
                ...item,
                startTime:that.$common.formatDate(new Date(item.startTime), 'yyyy-MM-dd').replace(/-/g, '.'),
                endTime: item.endTime ? that.$common.formatDate(new Date(item.endTime), 'yyyy-MM-dd').replace(/-/g, '.') : '',
              }))
            if (obj.pageNum === 1) {
              that.contentList = []
            }
            that.contentList = [...that.contentList, ...data]
            obj.successCallback && obj.successCallback(data)
          }).catch(error => {
            if(error.msg == '账号信息不能为空'){
              uni.showToast({
                title: '请先登录',
                icon:'none'
              });
            }
            obj.successCallback && obj.successCallback([])
            return false
          })
        }, that.$constant.noun.scrollRefreshTime)
      },

      scrollInit(scroll) {
        scroll.optUp.page.num = 1
        scroll.optUp.page.size = 10
        this.mescroll = scroll
      },
      init() {
        this.$nextTick(() => {
          this.mescroll.triggerDownScroll()
        })
      },
    },
 }
</script>

<style lang='scss' scoped>
  .action{
    padding: 0 24rpx;
    display: flex;
    justify-content: center;
    .actionItem{
      margin-top: 16rpx;
      width: 33.33333333%;
      box-sizing: border-box;
      text-align: center;
      color: #316EAB;
      &:nth-of-type(2){
        border-left: 2rpx solid #777777;
        border-right: 2rpx solid #777777;
      }
    }
  }
  .copy {
    font-weight: 400;
    font-size: 24rpx;
    color: #316EAB;
    margin-left: 20rpx;
  }
  .main{
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow-x: hidden;
    background-color: #fff;
  }
  .top-nav{
    width: calc(100% - 16rpx);
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 40px;
    line-height: 40px;
    padding: 0 16rpx;
    .top-nav-l{
      display: flex;
      width: 48rpx;
      height: 48rpx;
      image{
        width: 100%;
        height: 100%;
      }
    }
    .top-nav-c{
      flex: 1;
      text-align: center;
      height: 44rpx;
      font-weight: 500;
      font-size: 32rpx;
      color: #2D2F38;
      line-height: 44rpx;
      margin-right: 48rpx;
    }
  }
  .l-main{
    position: sticky;
    top: 0;
    z-index: 9;
    padding: 22rpx 32rpx 22rpx 32rpx;
    ::v-deep.tabs-sticky{
      overflow-y: hidden;
      .tabs-sticky-body{
        padding: 0;
        .tab{
          text{
            padding: 0;
          }
          &:last-child{
            padding-right: 50rpx;
          }
        }
      }
    }
  }
  .img{
    width: 100%;
    height: 100%;
  }
  .scroll-refresh-main{
    flex: 1;
    // height: calc(100% - 60rpx);
    overflow-x: hidden;
    // padding-bottom: 166rpx;
    background-color: #F4F6FA;
    ::v-deep .mescroll-empty-box{
      // min-height: 0%;
      position: absolute !important;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
    .order-list{
      overflow-x: hidden;
      padding: 0 32rpx;
      .order-item{
        margin-top: 20rpx;
        background-color: #fff;
        border-radius: 16rpx;
        position: relative;
        .order-item-box{
          padding:32rpx 0rpx;
        }
        .inInsuranceTips{
          background: #E0F4EF !important;
        }
        .insuranceTips{
          width: 100%;
          height: 72rpx;
          background: #FFEBCB;
          padding: 20rpx 24rpx;
          box-sizing: border-box;
          border-radius: 0rpx 0rpx 16rpx 16rpx;
          font-weight: 400;
          font-size: 22rpx;
          display: flex;
          justify-content: space-between;
          .insuranceContent{
            color: #777777;
            font-weight: 400;
            font-size: 22rpx;
            color: #777777;
          }
          .notPurchased{
            font-weight: 500;
            font-size: 22rpx;
            color: #FFB130;
            display: flex;
            justify-content: center;
            .right_o{
              width: 32rpx;
              height: 32rpx;
              margin-right: 12rpx;
            }

          }
          .purchased{
            font-weight: 500;
            font-size: 22rpx;
            color: #00B484;
            display: flex;
            justify-content: center;
            .baox{
              width: 32rpx;
              height: 32rpx;
              margin-left: 12rpx;
            }
          }
        }
        .order-item-t{
          padding: 0 24rpx;
          display: flex;
          justify-content: space-between;
          .order-item-t-l{
            font-size: 32rpx;
            color: #1D2029;
            line-height: 44rpx;
          }
          .order-item-t-r{
            font-size: 28rpx;
            color: #1687F7;
            line-height: 40rpx;
          }
        }
        .order-item-b{
          padding: 0 24rpx;
          margin-top: 16rpx;
          .item{
            display: flex;
            font-weight: 400;
            font-size: 24rpx;
            color: #1D2029;
            .itemTitle{
              width: 120rpx;
              font-weight: 400;
              font-size: 24rpx;
              color: #4E5569;
              text-align: right;
            }
          }
          .time{
            margin: 8rpx 0;
          }
          .hospital{}
          .accompany-teacher{
            display: flex;
            .teacher-box{
              display: flex;
              align-items: center;
              .img{
                width: 32rpx;
                height: 32rpx;
                border-radius: 50%;
                image{
                  width: 100%;
                  height: 100%;
                }
              }
              span{
                margin-left: 8rpx;
                font-size: 22rpx;
                color: #1D2029;
                line-height: 32rpx;
              }
            }
          }
          .evaluate{
            display: flex;
            align-items: center;
            justify-content: center;
            height: 72rpx;
            width: 100%;
            margin-top: 24rpx;
            font-size: 26rpx;
            color: #00B484;
            background: #FFFFFF;
            border-radius: 36rpx;
            border: 1rpx solid #00B484;
            &::after{
              border: none !important;
            }
          }
          .my-evaluate{
            display: flex;
            align-items: center;
            height: 72rpx;
            background: #F4F6FA;
            border-radius: 8rpx;
            padding: 18rpx 16rpx 20rpx;
            box-sizing: border-box;
            margin-top: 24rpx;
            // .evaluate-img{
            //   display: flex;
            //   width: 132rpx;
            //   height: 32rpx;
            // }
            .evaluate-img1{
              width: 204rpx;
              height: 48rpx;
            }
            .evaluate-img2{
              width: 156rpx;
              height: 48rpx;
            }
            .evaluate-img3{
              width: 132rpx;
              height: 48rpx;
            }
            .evaluate-img4{
              width: 132rpx;
              height: 48rpx;
            }
            .evaluate-img5{
              width: 180rpx;
              height: 48rpx;
            }
          }
        }
        .errorBottom{
          transform: translateY(32rpx);
          width: 100%;
          height: 56rpx;
          background: rgba(215,74,31,0.15);
          border-radius: 0rpx 0rpx 16rpx 16rpx;
          font-weight: 400;
          font-size: 24rpx;
          color: #D74A1F;
          padding: 8rpx 0 14rpx 24rpx;
          box-sizing: border-box;
        }
      }
    }
  }
</style>
