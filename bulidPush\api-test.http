### 获取小程序列表
GET http://localhost:3001/api/miniprogram/list
Content-Type: application/json

### 执行推送任务
POST http://localhost:3001/api/miniprogram/publish
Content-Type: application/json

{
  "description": "测试推送功能",
  "idList": [1, 2],
  "version": "1.0.0"
}

### 获取推送状态
GET http://localhost:3001/api/miniprogram/status
Content-Type: application/json

### 获取推送日志
GET http://localhost:3001/api/miniprogram/logs?limit=20
Content-Type: application/json

### 清空日志
DELETE http://localhost:3001/api/miniprogram/logs
Content-Type: application/json

### 停止推送任务
POST http://localhost:3001/api/miniprogram/stop
Content-Type: application/json

### 健康检查
GET http://localhost:3001/api/health
Content-Type: application/json

### 推送单个小程序
POST http://localhost:3001/api/miniprogram/publish
Content-Type: application/json

{
  "description": "修复医院数据筛选错误",
  "idList": [1],
  "version": "1.2.3"
}

### 推送多个小程序
POST http://localhost:3001/api/miniprogram/publish
Content-Type: application/json

{
  "description": "更新用户界面优化",
  "idList": [1, 2, 3, 4],
  "version": "1.3.0"
}