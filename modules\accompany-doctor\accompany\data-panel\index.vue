<template>
  <view class='data-panel'>
    <view :style="'height:' + statusBarHeight + 'px;'"></view>
    <view class="top-nav">
      <view class="top-nav-l" @click.stop="handleBack">
        <image mode="aspectFit" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-service-left-arrows.png'" class="header-search-img"/>

      </view>
      <!-- <view class="top-nav-c">数据面板</view> -->
      <view class="top-nav-c">
        <view class="lineHide">
          <!-- <timePicker ref="selectTime" v-model="timeMap" type="daterange" :end="endTime" @change="changeTime"></timePicker> -->
          <!-- <uni-icons class="my-icons" :size="12" color="#1D2029" type="bottom" /> -->
        </view>
      </view>
    </view>
    <view class="l-main">
      <tabs-sticky class="tabs-sticky" :fontBigger="true" :bdb="false" background="none" :overflowX="true" :overflowY="false" v-model="curIndex" :tabs="tabs" @change="changeTab"></tabs-sticky>
    </view>
    <scroll-view class="my-scroll" :scroll-y="true" :scroll-into-view="scrollIntoView" :show-scrollbar="false">
      <!-- 订单 -->
      <view class="order" id="tab-0">
        <view class="order-header">
          <view class="order-header-l">
            <view class="title">订单</view>
            <view class="time">数据时间：{{ timeMap[1] }}</view>
          </view>
          <view class="order-header-r" @click="handleClickTarget">
            展示指标<view class="order-img"><image :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-filter.png'" class="header-search-img"></image></view>
          </view>
        </view>
        <view class="order-content">
          <view class="order-item" v-for="(item,index) in orderStatisticList" :key="index">
            <view class="title">{{ item.name }}</view>
            <view class="num">{{ item.count }}</view>
            <!-- <view class="percentage" :style="{color:item.yesterdayChain.includes('+') ? '#008763' : '#FF5500'}">{{ item.yesterdayChain }}<uni-icons class="my-icons" :size="10" color="#A5AAB8" type="right" /></view> -->
          </view>
        </view>
      </view>

      <!-- 陪诊师 -->
      <view class="accompany" id="tab-1">
        <view class="order-header">
          <view class="order-header-l">
            <view class="title">陪诊师</view>
            <view class="time">数据时间：{{ timeMap[1] }}</view>
          </view>
        </view>
        <view class="order-content">
          <view class="order-item">
            <view class="title">总人数</view>
            <view class="num">{{ employeeSexStatisticNum }}</view>
          </view>
          <view class="order-item" v-for="(item,index) in employeeSexStatisticList" :key="index">
            <view class="title">{{ item.sex }}</view>
            <view class="num">{{ item.percent }}</view>
          </view>
        </view>
        <view v-if="employeeRankStatisticList.length">
          <view class="order-border"></view>
          <view class="performance">
            <view class="performance-l">
              <view class="title">业绩统计</view>
              <view class="time">按已完成订单进行统计</view>
            </view>
            <view class="performance-r" @click="handleToBusinessTarget()" v-if="employeeRankStatisticList.length>3">查看更多<image class="head-r-img" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-service-right-arrows.png'"></image></view>
          </view>
        </view>
        <view class="business-content">
          <view class="business-item" v-for="(item,index) in employeeRankStatisticList.slice(0,3)" :key="item.employeeId">
            <view class="business-item-l">
              <view class="item-img" v-if="index == 0"><image :src="file_ctx + oneUrl"></image></view>
              <view class="item-img" v-else-if="index == 1"><image :src="file_ctx + twoUrl"></image></view>
              <view class="item-img" v-else-if="index == 2"><image :src="file_ctx + threeUrl"></image></view>
              <view class="profile"><image :src="file_ctx + item.avatar"></image></view>
              <view class="name">{{ item.employeeName }}</view>
            </view>
            <view class="business-item-r">{{ item.orderCount }} 单</view>
          </view>
        </view>
      </view>

      <!-- 帖子 -->
      <view class="post" id="tab-2">
        <view class="order-header">
          <view class="order-header-l">
            <view class="title">帖子</view>
            <view class="time">数据时间：{{ timeMap[1] }}</view>
          </view>
        </view>
        <view class="order-content">
          <view class="order-item" v-for="(item,index) in postStatisticList" :key="index">
            <view class="title">{{ item.name }}</view>
            <view class="num">{{ item.count }}</view>
            <!-- <view class="percentage" :style="{color:item.yesterdayChain.includes('+') ? '#008763' : '#FF5500'}">{{ item.yesterdayChain }}<uni-icons class="my-icons" :size="10" color="#A5AAB8" type="right" /></view> -->
          </view>
        </view>
      </view>

      <!-- 财务 -->
      <view class="financing" id="tab-3">
        <view class="order-header">
          <view class="order-header-l">
            <view class="title">财务</view>
            <view class="time">数据时间：{{ timeMap[1] }}</view>
          </view>
        </view>
        <view class="order-content">
          <view class="order-item">
            <view class="title">收益</view>
            <view class="num">￥{{ incomeStatisticObj.count }}</view>
            <!-- <view class="percentage" :style="{color:incomeStatisticObj.yesterdayChain.includes('+') ? '#008763' : '#FF5500'}">{{ incomeStatisticObj.yesterdayChain }}<uni-icons class="my-icons" :size="10" color="#A5AAB8" type="right" /></view> -->
          </view>
        </view>
      </view>
    </scroll-view>


    <uni-popup ref="carouselPopup" type="bottom">
      <view class="carousel-content">
        <view class="title">展示指标</view>
        <view class="error" @click="$refs.carouselPopup.close()"><image :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-service-error.png'"></image></view>
        <view class="carousel-list">
          <view :class="item.default ? 'carousel-item active' : 'carousel-item'" v-for="(item,index) in targetList" :key="index" @click="handleClickActive(index)"><view class="carousel-img" v-if="item.default"><image :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-performance-gou.png'"></image></view>{{ item.name}}</view>
        </view>
        <view class="guard-detail-bottom" @click="handleTargetConfirm"><button>确定</button></view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  import TabsSticky from '@/components/basics/tabs-sticky-v3'
  import uniPopup from '@/components/uni/uni-popup'
  import timePicker from '../../components/uni-datetime-picker/uni-datetime-picker'
  import UniIcons from '@/components/uni/uni-icons/uni-icons'
  export default {
    components:{
      TabsSticky,
      uniPopup,
      timePicker,
      UniIcons
    },
    data(){
      return{
        file_ctx:this.file_ctx,
        statusBarHeight: 0,
        curIndex:0,
        tabs:[{name:'订单',id:1},{name:'陪诊师',id:2},{name:'帖子',id:3},{name:'财务',id:4}],
        tabName:'',
        targetList:[
          {name:'总订单',default:true},
          {name:'自营订单',default:true},
          {name:'平台订单',default:true},
          {name:'待接入',default:true},
          {name:'待派单',default:true},
          {name:'待支付',default:true},
          {name:'待接单',default:true},
          {name:'待服务',default:true},
          {name:'服务中',default:true},
          {name:'已完成',default:true},
          {name:'已取消',default:true}
        ],
        orderStatisticList:null,
        newArr:[],
        postReadCountStatisticObj:null,
        incomeStatisticObj:null,
        employeeRankStatisticList:null,
        employeeSexStatisticList:[],
        postStatisticList:[],
        timeMap:[],
        scrollIntoView:'tab-0',
        oneUrl:'static/image/business/accompany-doctor/icon-accompany-performance-one.png',
        twoUrl:'static/image/business/accompany-doctor/icon-accompany-performance-two.png',
        threeUrl:'static/image/business/accompany-doctor/icon-accompany-performance-three.png',
        // 获取今天晚上23:59:59的时间戳
        endTime: new Date(new Date().setHours(23, 59, 59, 999)).getTime()
      }
    },
    computed: {
      employeeSexStatisticNum(){
        let total = this.employeeSexStatisticList.length && this.employeeSexStatisticList.reduce((total,item)=>total + item.count,0) || 0
        return total
      }
    },
    onLoad(){
      this.timeMap = this.getDatesOfTodayAndLastWeek();
      if(this.timeMap.length){
        this.handleSearch()
      }
    },
    mounted(){
      this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight // 获取设备的状态栏高度
    },
    methods:{
      handleClickTarget(){
        this.$refs.carouselPopup.open()
      },
      handleTargetConfirm(){
        let result = this.newArr.filter(item => {
          return this.targetList.find(target => target.name === item.name && target.default === true) !== undefined;
        });
        if(result.length){
          this.orderStatisticList = result
        } else {
          this.orderStatisticList = this.newArr
        }
        this.$refs.carouselPopup.close()
      },
      changeTime(){
        this.handleSearch()
      },
      formatDate(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要加1
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
      },
      // getDatesOfTodayAndLastWeek() {
      //   const dates = [];
      //   const today = new Date();
      //   // 获取一周前的今天
      //   const oneWeekAgo = new Date(today);
      //   oneWeekAgo.setDate(today.getDate() - 7);
      //   dates.push(this.formatDate(oneWeekAgo));
      //   // 获取今天的日期
      //   dates.push(this.formatDate(today));
      //   return dates;
      // },
      getDatesOfTodayAndLastWeek() {
        const dates = [];
        const today = new Date();
        const lastMonth = new Date(today);
        lastMonth.setMonth(lastMonth.getMonth() - 1);

        if (lastMonth.getMonth() === 11 && today.getMonth() === 0) {
          lastMonth.setFullYear(lastMonth.getFullYear() - 1);
        }

        lastMonth.setDate(1);
        dates.push(this.$common.formatDate(lastMonth, 'yyyy-MM-dd'))
        dates.push(this.$common.formatDate(today, 'yyyy-MM-dd'))
        return dates;
      },
      changeTab(index){
        this.tabName =  this.tabs[index].name
        this.scrollIntoView = `tab-${index}`;
      },
      handleBack(){
        this.$navto.back(1)
      },
      handleClickActive(index){
        this.targetList[index].default = !this.targetList[index].default
        console.log(this.targetList,'this.targetList00888')
      },
      handleSearch(){
        this.accompanyBookTotalOrderStatistic()
        this.accompanyBookPostStatistic()
        this.accompanyBookPostReadCountStatistic()
        this.accompanyBookIncomeStatistic()
        this.accompanyBookEmployeeRankStatistic()
        this.accompanyBookEmployeeSexStatistic()
      },
      // 数据统计-订单统计
      accompanyBookTotalOrderStatistic(){
        const providerId = this.$common.getKeyVal('user', 'providerId',true)
        let params = {
          startTime:this.timeMap[0] + ' 00:00:00',
          endTime:this.timeMap[1] + ' 23:59:59',
          providerId:providerId
        }
        this.$api.accompanyDoctor.accompanyBookTotalOrderStatistic(params).then(res=>{
          this.orderStatisticList = res.data
          this.newArr = JSON.parse(JSON.stringify(res.data))
        })
      },
      // 数据统计-帖子统计
      accompanyBookPostStatistic(){
        const providerId = this.$common.getKeyVal('user', 'providerId',true)
        let params = {
          startTime:this.timeMap[0] + ' 00:00:00',
          endTime:this.timeMap[1] + ' 23:59:59',
          providerId:providerId
        }
        this.$api.accompanyDoctor.accompanyBookPostStatistic(params).then(res=>{
          this.postStatisticList = res.data
        })
      },
      // 数据统计-帖子阅读数统计
      accompanyBookPostReadCountStatistic(){
        const providerId = this.$common.getKeyVal('user', 'providerId',true)
        let params = {
          startTime:this.timeMap[0] + ' 00:00:00',
          endTime:this.timeMap[1] + ' 23:59:59',
          providerId:providerId
        }
        this.$api.accompanyDoctor.accompanyBookPostReadCountStatistic(params).then(res=>{
          this.postReadCountStatisticObj = res.data
        })
      },
      // 数据统计-收益统计
      accompanyBookIncomeStatistic(){
        const providerId = this.$common.getKeyVal('user', 'providerId',true)
        let params = {
          startTime:this.timeMap[0] + ' 00:00:00',
          endTime:this.timeMap[1] + ' 23:59:59',
          providerId:providerId
        }
        this.$api.accompanyDoctor.accompanyBookIncomeStatistic(params).then(res=>{
          this.incomeStatisticObj = res.data
        })
      },
      // 数据统计-陪诊师排名Top10
      accompanyBookEmployeeRankStatistic(){
        const providerId = this.$common.getKeyVal('user', 'providerId',true)
        let params = {
          startTime:this.timeMap[0] + ' 00:00:00',
          endTime:this.timeMap[1] + ' 23:59:59',
          providerId:providerId
        }
        this.$api.accompanyDoctor.accompanyBookEmployeeRankStatistic(params).then(res=>{
          this.employeeRankStatisticList = res.data
        })
      },
      // 数据统计-陪诊师性别统计
      accompanyBookEmployeeSexStatistic(){
        const providerId = this.$common.getKeyVal('user', 'providerId',true)
        let params = {
          startTime:this.timeMap[0] + ' 00:00:00',
          endTime:this.timeMap[1] + ' 23:59:59',
          providerId:providerId
        }
        this.$api.accompanyDoctor.accompanyBookEmployeeSexStatistic(params).then(res=>{
          this.employeeSexStatisticList = res.data
        })
      },
      handleToBusinessTarget(){
        this.$navto.push('BusinessTarget')
      }
    },
 }
</script>

<style lang='scss' scoped>
  .data-panel{
    // height: 100vh;
    height: 100%;
    padding-bottom: 32rpx;
    display: flex;
    flex-direction: column;
    overflow-x: hidden;
    background-color: #F4F6FA;
  }
  .top-nav{
    // position: fixed;
    width: calc(100% - 16rpx);
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 40px;
    line-height: 40px;
    padding: 0 16rpx;
    // z-index: 999;
    // padding: 0 32rpx 0 24rpx;
    .top-nav-l{
      display: flex;
      width: 48rpx;
      height: 48rpx;
      margin-top: 16rpx;
      image{
        width: 100%;
        height: 100%;
      }
    }
    .top-nav-c{
      flex: 1;
      text-align: center;
      height: 44rpx;
      font-weight: 500;
      font-size: 32rpx;
      color: #2D2F38;
      line-height: 44rpx;
      margin-right: 48rpx;
      .lineHide{
        position: relative;
        .my-icons{
          position: absolute;
          top: 8rpx;
          right: 268rpx;
        }
        ::v-deep.uni-date{
          width: 390rpx;
          .uni-date-editor{
            .uni-date-editor--x{
              height: 64rpx;
              border-radius: 32rpx;
              background: #fff;
              overflow: hidden;
              .uni-date-x{
                flex: none;
                padding-left: 32rpx;
                .range-separator{
                  margin: 0 10rpx;
                }
                .uni-date__x-input{
                  flex: none;
                }
              }
            }
          }
        }
      }
    }
  }
  .l-main{
    margin: 16rpx 0 28rpx;
    ::v-deep.tabs-sticky{
      .tabs-sticky-body{
        .tab{
          flex: 1;
          margin-right: 0;
        }
      }
    }
  }
  .my-scroll{
    height: calc(100vh - 150px);
  }
  .header-search-img{
    width: 100%;
    height: 100%;
  }
  .order,.accompany,.post,.financing{
    padding: 32rpx 24rpx;
    background-color: #fff;
    border-radius: 16rpx;
    margin: 0 32rpx;
    .order-header{
      display: flex;
      justify-content: space-between;
      .order-header-l{
        .title{
          font-size: 32rpx;
          color: #1D2029;
          line-height: 44rpx;
          margin-bottom: 8rpx;
        }
        .time{
          font-size: 24rpx;
          color: #A5AAB8;
          line-height: 34rpx;
        }
      }
      .order-header-r{
        display: flex;
        align-items: center;
        justify-content: center;
        width: 156rpx;
        height: 56rpx;
        background: #F4F6FA;
        border-radius: 8rpx;
        font-size: 24rpx;
        color: #1D2029;
        .order-img{
          display: flex;
          width: 24rpx;
          height: 24rpx;
          margin-left: 4rpx;
        }
      }
    }
    .order-content{
      display: flex;
      flex-wrap: wrap;
      margin-top: 32rpx;
      padding: 0 22rpx 0 28rpx;
      .order-item{
        display: flex;
        // flex: 1;
        // flex: 33.33333%;
        width: 33.33333%;
        flex-direction: column;
        margin-bottom: 48rpx;
        // margin-left: 24rpx;
        // flex: calc(33.33333% - 72rpx);
        .title{
          font-size: 24rpx;
          color: #1D2029;
          line-height: 34rpx;
        }
        .num{
          font-size: 32rpx;
          font-weight: 600;
          color: #1D2029;
          line-height: 44rpx;
        }
        .percentage{
          display: flex;
          align-items: center;
          font-size: 24rpx;
          color: #008763;
          line-height: 34rpx;
        }
        &:nth-child(n+10){
          margin-bottom: 0;
        }
      }
    }
  }
  .accompany{
    margin: 20rpx 32rpx;
    .order-border{
      height: 2rpx;
      width: 100%;
      background: #F4F6FA;
      margin-bottom: 28rpx;
    }
    .performance{
      display: flex;
      justify-content: space-between;
      .performance-l{
        .title{
          font-size: 28rpx;
          color: #1D2029;
          line-height: 40rpx;
          margin-bottom: 8rpx;
        }
        .time{
          font-size: 24rpx;
          color: #A5AAB8;
          line-height: 34rpx;
        }
      }
      .performance-r{
        display: flex;
        align-items: center;
        font-size: 24rpx;
        color: #868C9C;
        line-height: 34rpx;
        .head-r-img{
          display: flex;
          width: 32rpx;
          height: 32rpx;
        }
      }
    }
    .business-content{
      padding: 32rpx 24rpx 0;
      background: #FFFFFF;
      border-radius: 16rpx;
      .business-item{
        display: flex;
        justify-content: space-between;
        align-items: center;
      margin-bottom: 32rpx;
      .business-item-l{
        display: flex;
        align-items: center;
        .item-img{
          display: flex;
          width: 44rpx;
          height: 44rpx;
          image{
            width: 100%;
            height: 100%;
          }
        }
        .profile{
          width: 64rpx;
          height: 64rpx;
          border-radius: 50%;
          margin: 0 12rpx 0 26rpx;
          image{
            width: 100%;
            height: 100%;
          }
        }
        .name{
          font-size: 28rpx;
          color: #1D2029;
          line-height: 40rpx;
        }
      }
      .business-item-r{
        font-size: 28rpx;
        color: #1D2029;
        line-height: 40rpx;
      }
      &:last-child{
        margin-bottom: 0;
      }
      }
    }
  }
  .financing{
    margin-top: 20rpx;
  }
  .carousel-content{
    position: relative;
    width: 100%;
    background-color: #fff;
    padding: 32rpx 0;
    border-radius: 24rpx 24rpx 0rpx 0rpx;
    .error{
      position: absolute;
      right: 34rpx;
      top: 38rpx;
      width: 32rpx;
      height: 32rpx;
      image{
        width: 100%;
        height: 100%;
      }
    }
    .title{
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 32rpx;
      font-weight: 600;
      color: #1D2029;
    }
    .carousel-list{
      position: relative;
      display: flex;
      flex-wrap: wrap;
      padding: 0 32rpx 40rpx;
      margin: 40rpx 0 200rpx;
      .carousel-item{
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 154rpx;
        height: 70rpx;
        background: #F4F6FA;
        border-radius: 8rpx;
        margin: 0 20rpx 20rpx 0;
        font-size: 26rpx;
        color: #1D2029;
        box-sizing: border-box;
        &:nth-child(4n){
          margin-right: 0;
        }
        .carousel-img{
          position: absolute;
          display: flex;
          right: -2rpx;
          bottom: -2rpx;
          width: 26rpx;
          height: 26rpx;
          // background-color: #00B484;
          // border-radius: 16rpx 0rpx 8rpx 0rpx;
          image{
            width: 100%;
            height: 100%;
          }
        }
      }
      .active{
        border: 1rpx solid #00B484;
        background: #D7FAF1;
        color: #00B484;
      }
    }
    .guard-detail-bottom{
      position: absolute;
      height: 180rpx;
      width: 100%;
      bottom: 0;
      padding-top: 24rpx;
      background-color: #fff;
      button{
        display: flex;
        align-items: center;
        justify-content: center;
        height: 88rpx;
        background: #00B484;
        border-radius: 44rpx;
        font-size: 32rpx;
        color: #FFFFFF;
        margin:0 32rpx;
        &::after{
          border: none !important;
        }
      }
    }
  }
</style>
