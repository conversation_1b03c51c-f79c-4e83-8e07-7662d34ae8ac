<template>
  <page>
    <view class="main-body" slot="content">
      <scroll-view scroll-y="true" class="main">
        <view class="m-main">
          <text class="m-main-t">免责条款</text> <br>
          <text class="m-main-t-info">
            <text class="red">{{serverOptionsTitle}}</text>（以下称”本平台”）是公益性、医学专业、资源整合平台，以传播医学专业信息，分享健康知识为目的。因为互联网信息来源广泛，因此，可能涉及到相关信息权益问题，在此进行声明。
          </text>
          <br>
          <text class="m-main-c">
            1. 本平台为用户提供互动式信息发布论坛平台服务，本平台网络用户不得利用本平台提供的互动式信息发布论坛平台服务实施侵权行为，包括但不限于：（1）泄露用户的隐私和个人信息，或者未经患者用户同意公开其病历资料的；（2）未经著作权人授权侵害他人著作权的；（3）以侮辱、诽谤等方式侵害他人的名誉权；（4）其他侵权行为。
          </text>
          <br>
          <text class="m-main-c">1.1 本平台禁止制作、复制、发布含有下列内容的违法信息：</text>
          <br>
          <text class="m-main-c">
            （一）反对宪法所确定的基本原则的；
          </text>
          <br>
          <text class="m-main-c">
            （二）危害国家安全，泄露国家秘密，颠覆国家政权，破坏国家统一的；
          </text>
          <br>
          <text class="m-main-c">
            （三）损害国家荣誉和利益的；
          </text>
          <br>
          <text class="m-main-c">
            （四）歪曲、丑化、亵渎、否定英雄烈士事迹和精神，以侮辱、诽谤或者其他方式侵害英雄烈士的姓名、肖像、名誉、荣誉的；
          </text>
          <br>
          <text class="m-main-c">
            （五）宣扬恐怖主义、极端主义或者煽动实施恐怖活动、极端主义活动的；
          </text>
          <br>
          <text class="m-main-c">
            （六）煽动民族仇恨、民族歧视，破坏民族团结的；
          </text>
          <br>
          <text class="m-main-c">
            （七）破坏国家宗教政策，宣扬邪教和封建迷信的；
          </text>
          <br>
          <text class="m-main-c">
            （八）散布谣言，扰乱经济秩序和社会秩序的；
          </text>
          <br>
          <text class="m-main-c">
            （九）散布淫秽、色情、赌博、暴力、凶杀、恐怖或者教唆犯罪的；
          </text>
          <br>
          <text class="m-main-c">
            （十）侮辱或者诽谤他人，侵害他人名誉、隐私和其他合法权益的；
          </text>
          <br>
          <text class="m-main-c">
            （十一）法律、行政法规禁止的其他内容。
          </text>
          <br>
          <text class="m-main-c">
             1.2 本平台将采取措施，防范和抵制用户制作、复制、发布含有下列内容的不良信息：
          </text>
          <br>
          <text class="m-main-c">
            （一）使用夸张标题，内容与标题严重不符的；
          </text>
          <br>
          <text class="m-main-c">
            （二）炒作绯闻、丑闻、劣迹等的；
          </text>
          <br>
          <text class="m-main-c">
            （三）不当评述自然灾害、重大事故等灾难的；
          </text>
          <br>
          <text class="m-main-c">
            （四）展现血腥、惊悚、残忍等致人身心不适的；
          </text>
          <br>
          <text class="m-main-c">
            （五）煽动人群歧视、地域歧视等的；
          </text>
          <br>
          <text class="m-main-c">
            （六）可能引发未成年人模仿不安全行为和违反社会公德行为、诱导未成年人不良嗜好等的；
          </text>
          <br>
          <text class="m-main-c">
            （七）其他对网络生态造成不良影响的内容。
          </text>
          <br>
          <text class="m-main-t-info">
             2. 关于本平台
          </text>
          <br>
          <br>
          <br>
          <text class="m-main-c">
             2.1 由于与本平台所造成之个人资料泄露及由此而导致的任何法律争议和后果，本平台均不承担任何法律责任。
          </text>
          <br>
          <text class="m-main-c">
             2.2 本平台所有用户注册的密码都采用不可逆加密算法，任何人都无法知晓原始密码。由于用户将个人密码告知他人或与他人共享注册帐户，由此导致的任何个人资料泄露，本平台不负任何责任。
          </text>
          <br>
          <text class="m-main-c">
             2.3 当政府司法机关依照法定程序要求本平台披露个人资料时，我们将根据执法单位之要求或为公共安全之目的提供个人资料。在此情况下之任何披露，本平台均不承担任何法律责任。
          </text>
          <br>
          <text class="m-main-c">
             2.4 凡以任何方式登陆本平台或直接、间接使用本平台资料者，视为自愿接受本平台声明的约束。
          </text>
          <br>
          <text class="m-main-c">
             2.5 本平台内所有内容并不反映任何本平台之意见及观点。本平台内容有“{{serverOptionsTitle}}”原创信息（来源标注为“{{serverOptionsTitle}}”），以及用户自行产生的内容（UGC内容）， 以及部分授权转载内容。对于用户自行产生内容，本平台仅作存储目的，不对内容本身的版权、合法性和权威性进行判定，如果有违规<text class="red">或侵权的</text>，由原始发布用户承担。
             <text class="red">同时，本平台部分所载文章及其他信息来源于互联网，仅为用户提供更多资讯，并不构成任何学术或医疗建议。如果认为该信息或转载侵犯了相应主体的权益，请及时与我们联系，</text>本<text class="red">平台</text>将在2日内删除。本平台对资料或信息版权及其后果免责。
          </text>
          <br>
          <text class="m-main-c">
             2.6 本平台登载此文出于传递更多信息之目的，并不意味着赞同其观点或证实其描述。
          </text>
          <br>
          <text class="m-main-c">
             2.7 本平台不完全保证平台内容的真实性，准确性，完整性，也不保证未来内容不会发生变更，信息仅供专业的医生参考！本平台医学内容仅供参考，不作为诊断及医疗依据。涉及到疾病诊疗，以及健康指导的意见，请咨询当地专科医生。凡因为观看本平台信息而作出医疗或健康推荐，发生的任何后果，与本平台无关。
          </text>
          <br>
          <text class="m-main-c">
             2.8 站内地图不作为行政划分依据。
          </text>
          <br>
          <text class="m-main-c">
             2.9 本平台对资料、图片版权、或信息版权及其后果均不承担任何法律责任。发布的文件中若含有版权信息、版权图片、版权文件，由原始发布人、或原始作者承担责任以及后果。如果版权方要求本平台删除，本站将根据版权方要求进行删除，并进行证据保全。
          </text>
          <br>
          <text class="m-main-t-info">
             3. 关于平台运行
          </text>
          <br>
          <text class="m-main-c">
             3.1 本平台如因系统维护或升级而需暂停服务时，将事先公告。若因线路及非本公司控制范围外的硬件故障或其它不可抗力而导致暂停服务，于暂停服务期间造成的一切不便与损失，本平台不负任何责任。
          </text>
          <br>
          <text class="m-main-c">
            3.2 任何由于计算机网络问题、黑客攻击、计算机病毒侵入或因政府管制而造成的暂时性关闭等影响网络正常经营的不可抗力而造成的个人资料泄露、丢失、被盗用或被窜改等，本平台均不承担任何法律责任。
          </text>
          <br>
          <text class="m-main-c">
            3.3 在本平台中发布不合适的评论等，本平台有权进行删除，并保留证据。
          </text>
          <br>
          <br>
          <br>
          <text class="m-main-t-info">
            4. 关于法律责任
          </text>
          <br>
          <text class="m-main-c">
            4.1 本平台使用者因为违反本声明的规定而触犯中华人民共和国法律的，一切后果自己负责，本平台不承担任何责任。
          </text>
          <br>
          <text class="m-main-c">
            4.2 凡以任何方式登陆本平台或直接、间接使用本平台资料者，视为自愿接受本平台声明的约束。
          </text>
          <br>
          <text class="m-main-c">
            4.3 本声明未涉及的问题参见国家有关法律法规，当本声明与国家法律法规冲突时，以国家法律法规为准。
          </text>
          <br>
          <br>
          <br>
          <text class="m-main-t-info">
            5. 互相尊重，对自己的言论和行为负责
          </text>
          <br>
          <text class="m-main-c">
            5.1 为了更好的为你提供服务，本公司可能会向您注册的邮箱内或手机中发送产品相关或专业相关等信息，如果您不希望收到类似邮件，短信或电话，请拨打客服电话020-<text class="red">88520969</text>，也可以在后台留言 ，感谢您的支持！
          </text>
          <br>
          <br>
          <br>
          <text class="m-main-c">
            本平台之免责条款以及其修改权、更新权和最终解释权均属广州{{serverOptionsTitle}}健康科技有限公司所有。
          </text>
        </view>
      </scroll-view>
    </view>
  </page>
</template>

<script>
import { mapState } from 'vuex'
import serverOptions from '@/config/env/options'
export default {
  components: {

  },
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $accurateConversion: this.$accurateConversion,
      file_ctx: this.file_ctx,
      $static_ctx: this.$static_ctx,
      $timePlugin: this.$timePlugin,
      $validate: this.$validate,
      id: undefined,
      idUpdate: true,
      regForm: {}
    }
  },
  computed: {
    ...mapState('user', {

    }),
    ...mapState('system', {
      temporaryStorage: state => state.temporaryStorage
    }),
    serverOptionsTitle() {
      return serverOptions.title 
    }
  },
  // 离开当前页面后执行
  onUnload() {
    // this.$common.setKeyVal('system', 'temporaryStorage', {})
  },
  onLoad(paramsObj) {
    const query = this.$Route.query
    if (!this.$validate.isNull(query)) {
      this.id = query.id
    }
    this.init()
  },
  onShow() {},
  methods: {
    init() {
      this.$nextTick(() => {

      })
    },
    navtoGo(url, obj = {}) {
      this.$navto.push(url, obj)
    },
    getDetail(id) {
      const that = this

    }
  }

}
</script>

<style lang="scss" scoped>
  .red {
    // color: red;
  }
  .m-main-c {
    line-height: 2;
  }
  .m-main-t {
    font-size: 36upx;
    font-weight: 550;
    line-height: 2;
    margin-bottom: 40upx;
  }
  .m-main-t-info {
    line-height: 2;
    font-weight: 550;
  }
  .m-l-16{
    margin-left: 16upx;
  }
  .star{
    color: #F85E4C;
    font-size: 32upx;
    display: inline-block;
  }
  .main-body{
    height: 100%;
    .main{
      height: 100%;
      background: #fff;
      .m-main{
        background: #fff;
        padding: 30upx;
      }
    }
  }
  .title {
    font-size: 36upx;
    line-height: 54upx;
    text-align: center;
  }
  .text-title {
    font-size: 28upx;
    line-height: 42upx;
    margin-top: 16upx;
    color: #333;
  }
  .text {
    font-size: 24upx;
    line-height: 36upx;
    margin-top: 16upx;
    color: #666;
  }
  .text-indent-40 {
    text-indent: 40upx;
  }
  .p-b-8 {
    padding-bottom: 8upx;
  }
</style>
