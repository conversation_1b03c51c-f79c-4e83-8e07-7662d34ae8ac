/**
 * 存储字段名词，用于系统FINAL_NAME
 */
import env from '@/config/env'
export default {
  packagingEnvironment: 'test', // 打包环境(test-测试/pro-投产)
  // 下拉刷新时间
  scrollRefreshTime: 300, // 下拉刷新延迟请求
  delayedOperationTime: 500, // 延迟操作关闭窗口
  delayedOperationTime1000: 1000, // 延迟操作关闭窗口
  uploadInterface: env.ctx + 'basics/api/v1/attachment/upload', // 上传文件路径
  emptyDataPrompt: '~ 暂无相关数据 ~', // 空数据的提示语
  systemId: env.systemId, // 系统模块id

  account: 'GB_USER_ACCOUNT',

  // 角色类型
  staffCode: '1537', // 员工
  parentsCode: '1538', // 家长

  // 性别
  gender1428: '1428', // 男
  gender1429: '1429', // 女
  gender1441: '1441', // 未知
  gender1515: 2,// 女

  // 排课方式
  way1587: '1587', // 规则排课
  way1588: '1588', // 日历排课

  // 规则排课-结束方式
  endWay1591: '1591', // 不结束
  endWay1592: '1592', // 限日期
  endWay1593: '1593', // 限次数

  // 规则排课-重复
  repeatType1589: '1589', // 每周
  repeatType1590: '1592', // 隔周

  // 点名状态(classTimeStatus)
  classTimeStatus1622: '1622', // 未点名
  classTimeStatus1623: '1623', // 已点名

  // 跟进状态(followStatus)
  followStatus1617: '1617', // 待跟进
  followStatus1618: '1618', // 跟进中
  followStatus1619: '1619', // 已约课
  followStatus1620: '1620', // 已体验
  followStatus1621: '1621', // 已失效

  // 意向级别(intentionLevel)
  intentionLevel1614: '1614', // 低
  intentionLevel1615: '1615', // 中
  intentionLevel1616: '1616', // 高

  // 学员来源(studentSource)
  studentSourcel1609: '1609', // 电话邀约
  studentSourcel1610: '1610', // 租户到访

  // 学生类型 意向级别(studentType)
  studentType1611: '1611', // 意向学员
  studentType1612: '1612', // 在读学员
  studentType1613: '1613', // 历史学员
  studentType1859: '1859', // 失效学员

  // 登陆类型 true为正常登录 false为自动登录
  logType: true,

  // 课程类型和非课程类型
  courseType: '1578',
  unCourseType: '1579',

  // 业务配置-业务类型(businessType)
  businessType1578: '1578', // 课程类业务
  businessType1579: '1579', // 托管类业务
  businessType1580: '1580', // 托育类业务
  businessType1882: '1882', // 幼儿园

  // 班级版本类型(versionType)
  versionType1: '1', // 托育
  versionType2: '2', // 早教

  // 营业类别(operatingStatus)
  operatingStatus1883: '1883', // 正在营业
  operatingStatus1884: '1884', // 尚未营业
  operatingStatus1885: '1885', // 暂停营业

  // 点名状态(callStatus)
  callStatus1627: '1627', // 到课
  callStatus1628: '1628', // 迟到
  callStatus1629: '1629', // 请假
  callStatus1630: '1630', // 未到

  // 扣次数规则(deductType)
  numberOfButtons1585: '1585', // 扣
  numberOfButtons1586: '1586', // 不扣

  // 补课状态(repairStatus)
  repairStatus1624: '1624', // 待补课
  repairStatus1625: '1625', // 补课中
  repairStatus1626: '1626', // 已补课
  repairStatus1636: '1636', // 无效
  repairStatus1637: '1637', // 取消补课

  // 是否
  trueOrFalse1436: '1436', // 是
  trueOrFalse1437: '1437', // 否

  // 授课模式(numberType)
  numberType1583: '1583', // 一对多
  numberType1584: '1584', // 一对一

  // 员工类型(employeeType)
  employeeType1659: '1659', // 实习
  employeeType1658: '1658', // 兼职
  employeeType1657: '1657', // 全职
  employeeType1660: '1660', // 劳务外包

  // 员工状态(employeeStatus)
  employeeStatus1663: '1663', // 离职
  employeeStatus1662: '1662', // 正式
  employeeStatus1661: '1661', // 试用

  // 工作状态(workStatus)
  workStatus1069: '1069', // 在职
  workStatus1071: '1071', // 离职

  // 报课类型
  signType1596: '1596', // 扩科
  signType1595: '1595', // 续报
  signType1594: '1594', // 新报

  // 看店状态(watchStoreType)
  watchStoreType1712: '1712', // 未到店
  watchStoreType1713: '1713', // 直接到店

  // 跟进状态(followType)
  followType1709: '1709', // 笔记
  followType1710: '1710', // 预约试听
  followType1711: '1711', // 预约看店

  // 用户发起类型 roleType(archives_type)
  roleType1537: '1537', // 员工
  roleType1538: '1538', // 家长

  // 审核状态 auditStatus(checkStatus)
  auditStatus1568: '1568', // 待审核
  auditStatus1569: '1569', // 审核通过
  auditStatus1570: '1570', // 审核不通过
  auditStatus1572: '1572', // 撤销报名

  // 请假类型（askforleaveType）
  askforleaveType1478: '1478', // 事假
  askforleaveType1479: '1479', // 病假

  // 病假类型（sickLeaveType）
  sickLeaveType1480: '1480', // 发热
  sickLeaveType1481: '1481', // 呕吐
  sickLeaveType1482: '1482', // 腹泻
  sickLeaveType1483: '1483', // 咳嗽
  sickLeaveType1484: '1484', // 流鼻血
  sickLeaveType1485: '1485', // 手足口病
  sickLeaveType1486: '1486', // 水痘
  sickLeaveType1487: '1487', // 幼儿急疹
  sickLeaveType1488: '1488', // 肺炎
  sickLeaveType1489: '1489', // 腹痛
  sickLeaveType1490: '1490', // 哮喘
  sickLeaveType1491: '1491', // 哮喘
  sickLeaveType1492: '1492', // 支气管炎
  sickLeaveType1493: '1493', // 湿疹
  sickLeaveType1494: '1494', // 过敏性皮炎
  sickLeaveType1495: '1495', // 阑尾炎
  sickLeaveType1496: '1496', // 胃肠炎
  sickLeaveType1497: '1497', // 泪囊炎
  sickLeaveType1498: '1498', // 斜视
  sickLeaveType1499: '1499', // 过敏性结膜炎
  sickLeaveType1500: '1500', // 贫血
  sickLeaveType1501: '1501', // 其他

  // 试听状态 auditionStatus
  auditionStatus1706: '1706', // 待试听
  auditionStatus1707: '1707', // 已试听
  auditionStatus1708: '1708', // 已取消
  auditionStatus1719: '1719', // 预约未试听

  // 看店状态 toStoreStatus
  toStoreStatus1716: '1716', // 待到店
  toStoreStatus1717: '1717', // 已到店
  toStoreStatus1718: '1718', // 已取消
  toStoreStatus1719: '1719', // 预约未到店

  // 收费方式
  chargeWay1581: '1581', // 按课时/次收费
  chargeWay1582: '1582', // 按月收费
  chargeWay1733: '1733', // 按天收费

  // 支付方式(payWay)
  payWay1604: '1604', // 支付宝
  payWay1605: '1605', // 微信
  payWay1606: '1606', // 现金
  payWay1608: '1608', // 银行卡

  discountsWay1599: '1599', // 金额
  discountsWay1600: '1600', // 折扣

  // 价格类型
  priceType1597: '1597', // 单价
  priceType1598: '1598', // 定价

  // 上架状态(onStatus)
  onStatus1649: '1649', // 下架
  onStatus1650: '1650', // 上架

  // 保育记录
  logType1664: '1664', // 体温
  logType1665: '1665', // 喝水
  logType1666: '1666', // 喝奶
  logType1667: '1667', // 臭臭
  logType1668: '1668', // 睡眠
  logType1669: '1669', // 正餐
  logType1670: '1670', // 辅食
  logType1671: '1671', // 健康状况
  logType1672: '1672', // 代用药
  logType1673: '1673', // 生长发育
  logType1797: '1797', // 换尿布
  logType1793: '1793', // 尿尿
  logType1848: '1848', // 保育情绪状态
  logType1849: '1849', // 保育精神状态

  // 流水类型
  payLogType1720: '1720', // 消费
  payLogType1721: '1721', // 充值
  payLogType1722: '1722', // 退回

  // 门禁授权状态 authiruzedStatus
  authiruzedStatus1782: '1782', // 有效
  authiruzedStatus1783: '1783', // 失效
  authiruzedStatus1784: '1784', // 取消

  // 门禁权限类型 authorityType
  authorityType0: '0', // 访客
  authorityType1: '1', // 业主
  authorityType2: '2', // 管理员

  // 门禁有效期类型 expiryDateType
  expiryDateType1: '1', // 绝对值
  expiryDateType2: '2', // 小时制
  expiryDateType3: '3', // 绝对时间

  // 离园/入园
  code1646: '1646', // 离园
  code1645: '1645', // 入园

  // 离园/入园
  noticeType1390: '1390', // 班级通知
  noticeType1391: '1391', // 校园通知

  // 支付状态
  payStatus1794: '1794', // 已支付
  payStatus1795: '1795', // 待支付
  payStatus1796: '1796', // 过期已失效
  payStatus1798: '1798', // 取消

  // 园所状态(dynamic)
  dynamicNotice: 'dynamic_notice', // 通知
  dynamicAlbum: 'dynamic_album', // 相册
  dynamicHomework: 'dynamic_homework', // 亲子作业
  dynamicDietNotice: 'dynamic_diet_notice', // 食谱预告
  dynamicCareCollect: 'dynamic_care_collect', // 每日叮嘱
  dynamicWeekcareTeachingReport: 'dynamic_weekcare_teaching_report', // 周保教汇总
  dynamicNotifications: 'dynamic_notifications', // 通知提醒

  // 代用药用法（usage）
  usage1802: '1802', // 内服
  usage1803: '1803', // 外敷

  // 代用药用量单位（pharmacyUnit）
  pharmacyUnit1690: '1690', // 支
  pharmacyUnit1693: '1693', // 毫克
  pharmacyUnit1692: '1692', // 克
  pharmacyUnit1691: '1691', // 毫升
  pharmacyUnit1687: '1687', // 片
  pharmacyUnit1689: '1689', // 袋
  pharmacyUnit1688: '1688', // 粒

  // 工资确认状态(settleStatus)
  settleStatus1799: '1799', // 待确认
  settleStatus1800: '1800', // 已确认

  // 退款状态
  refundStatus1804: '1804', // 申请退款
  refundStatus1805: '1805', // 退款申请中
  refundStatus1806: '1806', // 退款成功
  refundStatus1807: '1807', // 退款审核不通过
  refundStatus1808: '1808', // 撤销申请
  refundStatus1814: '1814', // 审核通过

  // 模板明细类型(dayDetailType)
  dayDetailType1815: '1815', // 固定活动项目
  dayDetailType1816: '1816', // 教学活动
  dayDetailType1817: '1817', // 素质课程

  // 备课管理状态(lessonsAudit)
  lessonsAudit1818: '1818', // 待审核
  lessonsAudit1819: '1819', // 审核通过
  lessonsAudit1820: '1820', // 存档草稿
  lessonsAudit1821: '1821', // 审核不通过

  // 食谱发布类型(publishType)
  publishType1833: '1833', // 草稿
  publishType1834: '1834', // 发布
  publishType1835: '1835', // 撤销

  // 上架状态(putawayStatus)
  putawayStatus1855: '1855', // 草稿
  putawayStatus1856: '1856', // 上架
  putawayStatus1857: '1857', // 下架

  // 素材管理类目信息(logType)
  category1861: '1861', // 素材管理

  // 入睡情况sleepType(sleepType)
  sleepType1680: '1680', // 入睡容易
  sleepType1681: '1681', // 较难入睡
  sleepType1682: '1682', // 不睡

  // 委托状态（entrustStatus）
  entrustStatus1868: '1868', // 监护人发起
  entrustStatus1869: '1869', // 监护人撤销
  entrustStatus1870: '1870', // 职工确认收到
  entrustStatus1871: '1871', // 职工核实接回
  entrustStatus1872: '1872', // 监护人确认同意
  entrustStatus1873: '1873', // 监护人不同意

  // 委托状态（studentTempType）
  studentTempType1631: '1631', // 临时插班
  studentTempType1632: '1632', // 补考课
  studentTempType1635: '1635', // 正常
  studentTempType1876: '1876', // 小时托
  studentTempType1877: '1877', // 预约试听

  // 约课状态（lessonAboutStatus）
  lessonAboutStatus1889: '1889', // 未知
  lessonAboutStatus1890: '1890', // 约调课
  lessonAboutStatus1891: '1891', // 约到课

  // 委托状态（signInWay）
  signInWay1647: '1647', // 扫码
  signInWay1648: '1648', // 人脸
  signInWay1836: '1836', // 手工
  signInWay1879: '1879', // 委托接回

  // 班级状态（optionalClassStatus）
  optionalClassStatus1850: '1850', // 分班
  optionalClassStatus1851: '1851', // 离班
  optionalClassStatus1852: '1852', // 换班

  /*
  * 对象结构
  * */
  // 订单种类
  marketingConfigType: {
    1: '默认',
    2: '小时托',
    3: '限时折扣',
    4: '体验课',
    5: '体验课',
    6: '拼团',
    7: '小时包'
  },

  /*
  * 数组结构
  * */
  // 优惠券类型
  bigType: [{
    value: '社区券',
    key: '1'
  }, {
    value: '销售券',
    key: '2'
  }, {
    value: '异业券',
    key: '3'
  }],
  // 优惠券形式
  smallType: [{
    value: '折扣券',
    key: '1'
  }, {
    value: '满减券',
    key: '2'
  }, {
    value: '代金券',
    key: '3'
  }, {
    value: '小时券',
    key: '4'
  }],
  // 有效期类型
  validityType: [{
    value: '固定时间',
    key: '1'
  }, {
    value: '领券当日开始计算有效期',
    key: '2'
  }, {
    value: '领券次日开始计算有效期',
    key: '3'
  }]
}
