<template>
  <view class="week-tab-main">
    <view class="week-tab">
      <view class="title">
        <text class="l" @tap="open">{{years}}<text class="icon-arrow-drop-down down"></text></text>
        <text class="r" @tap="updatedDateFn()">回到今天</text>
        <text class="r whole" :class="{'color-blue': isWholeSwitch}" v-if="isWhole" @tap="wholeFn()">全部</text>
      </view>
      <view class="week-tlite" v-for="(date,index) in weekDate" :key="index" @tap="clickDate(index)">
        <view class="week-name-li">
          {{date.dayChinese}}
        </view>
        <view class="week-date-li">
          <view class="week-date-li-text" :class="{'on': current === index}">
            {{date.day}}
          </view>
        </view>
        <view v-if="date.isRemindDot" class="remind-dot"></view>
      </view>
    </view>
    <uni-calendar
      ref="calendar"
      :date="currentDate"
      :insert="false"
      :multiSelect="false"
      @confirm="confirm"
    ></uni-calendar>
  </view>
</template>

<script>

  import uniCalendar from '@/components/uni/uni-calendar/uni-calendar'
  export default {
    components: {
      uniCalendar
    },
    props: {
      // 是否开启全部功能（就是去除当前选中的日期）
      isWhole: {
        type: Boolean,
        default() {
          return false
        }
      },
      // 初始值
      cDate: {
        type: String,
        default() {
          return ''
        }
      },
      cCurrent: {
        type: Number,
        default() {
          return 0;
        }
      }
    },
    data() {
      return {
        weeklyStartDate: '', // 每周的开始日期
        weeklyEndDate: '', // 每周的结束日期
        weeklyStartEndDate: { // 每周的开始结束日期对象
          startDate: '',
          endDate: ''
        },
        years: '', // 显示时间这一周的年月
        weekDate: [], // 这一周的时间数组
        currentDate: '', // 当前时间
        current: 0, // 当前数组选中的下标
        isWholeSwitch: false, // 全部功能开关
        storageData: {} // 用于临时存储当前的时间节点和下标节点
      }
    },
    watch: {
      cDate: {
        handler(val) {
          this.watchDataMain(val)
        },
        deep: true
      },
      cCurrent: {
        handler(val) {
          this.chooseDate(val)
        },
        deep: true
      }
    },
    mounted() {
      this.watchDataMain(this.cDate)
    },
    methods: {
      watchDataMain(val) {
        if (this.$validate.isNull(val)) return
        this.currentDate = val
        const setDate = val.replace(/-/g, '/')
        this.updatedDateFn(new Date(setDate))
      },
      returnFn(obj) {
        const parameter = obj
        parameter.weeklyStartDate = this.weeklyStartDate
        parameter.weeklyEndDate = this.weeklyEndDate
        parameter.weeklyStartEndDate = this.weeklyStartEndDate
        this.$emit('returnFn', parameter)
      },
      open() {
        this.$refs.calendar.open()
      },
      confirm(e) {
        this.updatedDateFn(new Date(e.fulldate))
      },
      chooseDate(current) {
        this.current = current
      },
      // 全部功能
      wholeFn() {
        this.isWholeSwitch = !this.isWholeSwitch
        if (this.isWholeSwitch) {
          this.storageData = {
            current: this.current,
            currentDate: this.currentDate
          }
          this.current = ''
          this.currentDate = ''
          this.$emit('returnWholeFnFn', {})
        } else {
          this.watchDataMain(this.storageData.currentDate)
        }
      },
      clickDate(current) {
        // if (current === this.current) return
        this.current = current
        this.currentDate = this.weekDate[this.current].time
        const parameter = {
          currentDateObj: this.weekDate[this.current],
          currentDate: this.weekDate[this.current].time,
          weekDate: this.weekDate,
          current: this.current
        }
        this.returnFn(parameter)
      },
      updatedDateFn(date = new Date()) {
        const setDate = this.$common.formatDate(date, 'yyyy-MM-dd').replace(/-/g, '/')
        const returnToday = this.$common.formatDate(date, 'yyyy-MM-dd').replace(/-/g, '/')
        this.setDate(new Date(setDate))
        this.returnToday(new Date(returnToday))
      },
      returnToday(date = new Date()) {
        for (let i = 0; i < this.weekDate.length; i++) {
          if (this.weekDate[i].day === this.formatDate(date).day) {
            this.currentDate = this.weekDate[i].time
            this.current = i
            const parameter = {
              currentDateObj: this.weekDate[i],
              currentDate: this.weekDate[i].time,
              weekDate: this.weekDate,
              current: this.current
            }
            this.returnFn(parameter)
          }
        }
      },
      formatDate(date) {
        const year = date.getFullYear()
        let month = (date.getMonth() + 1)
        month = month <= 9 ? '0' + month : '' + month
        let day = date.getDate()
        if (day <= 9) {
          day = '0' + day
        } else {
          day = '' + day
        }
        const dayChinese = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'][date.getDay()]
        return {
          year: year, // 年
          month: month, // 月
          day: day, // 日
          dayChinese: dayChinese, // 周几
          time: year + '-' + month + '-' + day, // yyyy-MM-dd
          years: year + '年' + month + '月', // yyyy年MM月
          isRemindDot: false // 是否开启提示红点
        }
      },
      // 父组件控制子组件（自身）的回调数据改变开启提示红点
      dataRemindDotFn(data) {
        for (let i = 0; i < this.weekDate.length; i++) {
          this.weekDate[i].isRemindDot = false
          for (let j = 0; j < data.length; j++) {
            if (data[j].time === this.weekDate[i].time && data[j].isRemindDot) {
              this.weekDate[i].isRemindDot = true
            }
          }
        }
      },
      addDate(date, n) {
        date.setDate(date.getDate() + n)
        return date
      },
      setDate(date) {
        this.weekDate = []
        if (date.getDay() === 0) { // 从周一开始需要的判断
          const currentDate = new Date(this.$common.formatDate(date, 'yyyy-MM-dd').replace(/-/g, '/'))
          const lastDayOfTheMonth = new Date(this.$common.formatDate(date, 'yyyy-MM-dd').replace(/-/g, '/'))
          if (currentDate.getDate() - 7 < 0) {
            const lastDate = this.$common.timestampToTime(lastDayOfTheMonth.setDate(0))
            const num = lastDayOfTheMonth.getDate() - Math.abs(currentDate.getDate() - 7)
            date = this.$common.timestampToTime(lastDate.setDate(num))
          } else {
            const num = date.getDate() - 7
            date.setDate(num)
          }
        }
        const week = date.getDay() - 1
        date = this.addDate(date, week * -1)
        this.currentFirstDate = new Date(date) // 这周周一
        for (let i = 0; i < 7; i++) {
          this.weekDate.push(this.formatDate(i === 0 ? date : this.addDate(date, 1))) // 星期一开始
          // this.weekDate.push(this.formatDate(i === 0 ? this.addDate(date, -1) : this.addDate(date, 1))) // 星期日开始
        }
        if (this.weekDate[0].month === this.weekDate[6].month) {
          this.years = this.weekDate[0].years
        } else {
          this.years = this.weekDate[0].years + '~' + this.weekDate[6].years
        }
        this.weeklyStartDate = this.weekDate[0].time
        this.weeklyEndDate = this.weekDate[6].time
        this.weeklyStartEndDate = {
          startDate: this.weekDate[0].time,
          endDate: this.weekDate[6].time
        }
      },
    }
  }
</script>

<style lang="scss" scoped>
  .week-tab-main{
    .week-tab{
      width: 100%;
      height: 202upx;
      background-color: #FFFFFF;
      padding:0 30upx;
      box-sizing: border-box;
      border-bottom: 2upx solid $borderColor;
      .title{
        width: 100%;
        overflow: hidden;
        height: 72upx;
        line-height: 72upx;
        .l{
          float: left;
          font-size: 28upx;
          color: #333333;
          .down{
            width: 32upx;
            height: 24upx;
            margin-left: 10upx;
          }
        }
        .r{
          float: right;
          font-size: 28upx;
          color: $topicC;
        }
        .whole {
          margin-right: 16upx;
          color: #333;
        }
      }
      .week-tlite {
        display: inline-block;
        width: calc(100%/7);
        float: left;
        padding: 0 20upx;
        box-sizing: border-box;
        position: relative;
        .week-name-li{
          text-align: center;
          font-size: 24upx;
          color: #999999;
        }
        .week-date-li{
          margin-top: 14upx;
          .week-date-li-text{
            margin: 0 auto;
            height: 40upx;
            line-height: 40upx;
            @include rounded(50%);
            font-size: 24upx;
            color: #999999;
            text-align: center;
            width: 40upx;
          }
          .on{
            background-color: $topicC;
            color: #fff;
          }
        }
        .remind-dot {
          width: 12upx;
          height: 12upx;
          @include rounded(50%);
          background: #187FFF;
          margin: 4upx auto 0;
        }
      }
    }
  }
</style>
