import MessageDefault from "./MessageDefault"
export default class HandleMessageUpdate extends MessageDefault {
    constructor (websocket) {
        super()
        this.websocket = websocket
    }

    match (dataJson) {
        const { CHATLIST_UPDATE_CMD } = this.chatCmd
        return dataJson.cmd === CHATLIST_UPDATE_CMD
    }

    processMessage (dataJson) {
        let dd = dataJson.data
        let chatlist = this.$common.getKeyVal('chat', 'chatlist',false)
        for (var i = 0;i < chatlist.length; i++){
            if (chatlist[i].id+'' == dd.id+'') {
                chatlist.splice(i,1);
            }
        }
        chatlist = this.$validate.isNull(chatlist) ? [] : chatlist
        chatlist.unshift(dd)
        this.$common.setKeyVal('chat', 'chatlist', chatlist, false)
    }
}