<template>
  <page>
    <view slot="content" class="body-main">
      <view class="my-data">
        <!-- #ifdef MP-WEIXIN || H5 -->
        <view>
          <status-bar-height></status-bar-height>
          <view class="top-nav">
            <view class="top-nav-l" @click="back"><image mode="aspectFit" :src="file_ctx + 'static/image/business/hulu-v2/icon-circle-left-arrows.png'" class="header-search-img"/></view>
          </view>
        </view>
        <!-- #endif -->
      </view>
      <view class="form-wrapper">
        <view class="form-box">
          <view class="form-item-list">
            <!-- <view class="line-bottom">
              <title-radio
                :config="config.materialType"
                :cData="form.materialType"
                @updateForm="updateForm"
              />
            </view> -->
            
            <view class="line-bottom">
              <title-input
                v-model="form.title"
                :config="config.title"
                :placeholder="config.title.placeholder"
                :maxlength="25"
                :show-word-limit="true"
                placeholderStyle="color: #A5AAB8;font-size: 32rpx;"
              ></title-input>
            </view>
            <view class="" style="padding-top: 32upx" v-show="config.content.show">
              <title-textarea
                style="width: 100%"
                v-model="form.content"
                :config="config.content"
                :placeholder="config.content.placeholder"
                placeholderStyle="color: #A5AAB8;font-size: 28rpx;"
              />
            </view>
            <view class="" style="padding-top: 32upx" v-show="config.intro.show">
              <title-textarea
                style="width: 100%"
                v-model="form.intro"
                :config="config.intro"
                :placeholder="config.intro.placeholder"
                placeholderStyle="color: #A5AAB8;font-size: 28rpx;"
              />
            </view>
            <view class="flex-box-start">
              <title-img
                v-show="config.videosPath.show"
                :config="config.videosPath"
                fileType="video"
                @returnFn="
                  (obj) => {
                    imgReturnFn(obj, 'videosPath');
                  }
                "
                :cData="cDataVideosPath"
                style="margin-right: 30upx;"
              >
                <view slot="upload-after" class="upload-after">
                  <view class="upload-tips">上传视频</view>
                </view>
              </title-img>
              <title-img
                :config="config.imagesPath"
                @returnFn="
                  (obj) => {
                    imgReturnFn(obj, 'imagesPath');
                  }
                "
                :cData="cDataImagesPath"
              >
                <view slot="upload-after" class="upload-after">
                  <view class="upload-tips">{{ form.materialType === 2 ? '上传视频封面' : '上传图片' }}</view>
                  <view class="default-tips" v-if="form.materialType === 2">默认视频第一帧</view>
                </view>
              </title-img>
            </view>
            <view class="my-picker-select" v-if="frequencyOptions2.length">
              <my-picker class="my-picker" mode="selector" label="选择角色" :range="frequencyOptions2" v-model="form.takeMedicineUser"></my-picker>
            </view>
            <view class="line-bottom">
              <title-selector
                :config="config.circleClassifyId"
                v-model="form.circleClassifyId"
                :cData="'805444437623984135'"
                :disabled="true"
              >
                <view class="selector-label" slot="label" slot-scope="{ data }">
                  <view class="selector-label-title">
                    <view class="img">
                      <image :src="$static_ctx2 + 'image/business/hulu-v2/icon-post-circle.png'"></image>
                    </view>
                    <span>{{ data.label }}</span>
                  </view>
                  <!-- <view class="selector-label-value" v-if="!form.circleClassifyId">
                    <view slot="placeholder" class="selector-placeholder-box">
                      <view class="selector-placeholder">精准链接病友、医生、药企，获取帮助</view>
                    </view>
                  </view> -->
                </view>
              </title-selector>
            </view>
          </view>
          <!-- <view class="tips-text" v-if="form.materialType === 2">发布后需要等待审核</view> -->
          <button class="submit-btn" type="primary" @click="submit" :loading="submitLoading">
            {{ submitLoading ? "发布中..." : "点击发布" }}
            <span v-if="form.materialType === 2">发布后需要等待审核</span>
          </button>
        </view>
      </view>
    </view>
  </page>
</template>

<script>
import { mapState } from 'vuex'
import TitleInput from "@/components/business/module/v1/title-input/index"
import TitleTextarea from "@/components/business/module/v1/title-textarea/index"
import TitleSelector from "@/components/business/module/v1/title-selector/index"
import TitleImg from "@/components/business/module/title-img/index"
import TitleRadio from "@/components/business/module/title-radio/index.vue"
import StatusBarHeight from '@/components/business/module/status-bar-height/index'
import myPicker from "@/components/business/module/v1/my-picker/index"
import env from '@/config/env'
const defaultForm = {
  title: '',
  content: '',
  circleClassifyId: '805444437623984135',
  isAnonymity: 2,
  imagesPath: '',
  materialType: 1,
  videosPath: '',
  takeMedicineUser:'',
}
const defaultConfig = {
  title: {
    show: true,
    disabled: false,
    required: true,
    maxlength: 25,
    placeholder: '请输入标题',
    showLabel: false,
    label: '标题',
    style: "border: none;font-size: 31rpx;font-weight: 500;line-height: 42rpx;outline: none;width: calc(100% - 80rpx);"
  },
  content: {
    show: true,
    style: 'border: none',
    disabled: false,
    required: true,
    placeholder: '请输入描述',
    label: '帖子正文',
    showLabel: false,
    inputStyle: 'border: none;font-size: 28rpx;line-height: 42rpx;min-height: 300rpx;padding: 0;background-color: #fff;',
    maxlength: -1
  },
  imagesPath: {
    multiSelectCount: 9,
    count: 9,
    show: true,
    padding: '32rpx 0'
  },
  circleClassifyId: {
    show: true,
    label: '选择发帖圈子',
    required: false, // 这里应该为必选，只是为了兼容样式
    array: [],
    titleWidth: '410rpx',
    showJump: false
  },
  materialType: {
    show: true,
    name: 'materialType',
    // label: '帖子类型',
    required: false, // 这里应该为必选，只是为了兼容样式
    array: [{ key: 1, value: '图文' }, { key: 2, value: '短视频' }],
    marginStyle: '0'
  },
  intro: {
    label: '短视频简介',
    style: 'border: none',
    disabled: false,
    placeholder: '请输入求助需求描述...',
    showLabel: false,
    // inputStyle: 'border: none;font-size: 27rpx;line-height: 42rpx;min-height: 300rpx;padding: 0;',
    inputStyle: 'border: none;font-size: 28rpx;line-height: 42rpx;min-height: 300rpx;padding: 0;background-color: #fff;',
    maxlength: -1
  },
  videosPath: {
    multiSelectCount: 1,
    count: 1,
    label: '短视频',
    padding: '32rpx 0',
    required: true,
    show: false
  },
}
export default {
  components: {
    TitleInput,
    TitleTextarea,
    TitleImg,
    TitleSelector,
    TitleRadio,
    StatusBarHeight,
    myPicker,
  },
  data() {
    return {
      $appId: this.$appId,
      file_ctx: this.file_ctx,
      $static_ctx2: this.$static_ctx,
      cDataImagesPath: [],
      cDataVideosPath: [],
      isAnonymous: false, // 是否匿名
      form: JSON.parse(JSON.stringify(defaultForm)),
      config: JSON.parse(JSON.stringify(defaultConfig)),
      submitLoading: false,
      circleClassifyInfoId:null,
      publishObj:{},
      shareImg:null,
      frequencyOptions2:[],
    }
  },
  onLoad(options) {
    const query = this.$validate.isNull(this.$Route.query) ? options : this.$Route.query
    if(query?.shareImg){
      this.shareImg = query?.shareImg
    }
    this.getCircleclassifyQueryList()
    this.accompanyBookUserRole()
  },
  onShow() {
    // #ifdef MP-WEIXIN
    this.setTabBarIndex(2);
    // #endif

  },
  onHide(){
    this.$common.setKeyVal('business','circleClassifyInfoObj',{})
  },
  computed: {
    ...mapState('user', {
      accountId: state => state.accountId,
      fansRecord: state => state.fansRecord,
      curSelectUserInfo: state => state.curSelectUserInfo, // 当前选中的用户信息
      publishInfo:state => state.publishInfo,
      codeUserInfo:state => state.codeUserInfo
    }),
    ...mapState('business', {
      circleClassifyInfoObj: state => state.circleClassifyInfoObj,
    })

  },
  // watch: {
  //   publishInfo:{
  //     handler(val) {
  //       this.publishObj.circleClassifyId = val.circleClassifyId
  //     },
  //     immediate: true
  //   }
  // },
  methods: {
    accompanyBookUserRole(){
      const isUserRole = this.$common.getKeyVal('user','isUserRole',true)
      if(isUserRole !=="" && isUserRole.hasEmployee){
        this.frequencyOptions2.push('陪诊师') 
      } else if(isUserRole !=="" && isUserRole.hasProvider){
        this.frequencyOptions2.push('服务商') 
      }
    },
    back() {
      this.$navto.back(1)
    },
    updateForm(obj) {
      this.form[obj.key] = obj.value
      if (obj.key === 'materialType') {
        Object.keys(this.config).forEach(key => {
          if (['content'].includes(key)) {
            this.config[key].show = obj.value !== 2
          } else if (['videosPath', 'intro'].includes(key)) {
            this.config[key].show = obj.value === 2
          } else if (key === 'imagesPath') {
            this.config[key].multiSelectCount = obj.value === 2 ? 1 : 9
            this.config[key].count = obj.value === 2 ? 1 : 9
            this.form[key] = ''
            this.cDataImagesPath = []
          }
        })
      }
    },
    /**
     * 公众号订阅
     */
    accountSubscribe() {
      return new Promise(async (resolve, reject) => {
        const unionid = await this.$ext.wechat.getUnionId().catch(() => {
          this.submitLoading = false
        })
        if (!unionid) resolve()
        const res = await this.$api.common.accountattentionSubscribeOrNot({
          unionid,
          wxId: 'wx0918ff821e41f5c4'
        })
        if (!this.$validate.isNull(res.data)) resolve()
        this.$uniPlugin.modal('您还没关注消息助手，请前往开启', '', {
          showCancel: true, // 是否显示取消按钮，默认为 true
          cancelText: '取消', //  取消按钮的文字，默认为"取消"，最多 4 个字符
          cancelColor: '#000000', //  取消按钮的文字颜色，默认为"#000000"(H5、微信小程序、百度小程序)
          confirmText: '确定', //  确定按钮的文字，默认为"确定"，最多 4 个字符
          confirmColor: '#15c398', //  确定按钮的文字颜色，H5平台默认为"#007aff"，微信小程序平台默认为"#3CC51F"，百度小程序平台默认为"#3c76ff"（H5、微信小程序、百度小程序）
          fn: async (n) => {
            if (n) {
              this.clearForm()
              this.navtoGo('WebHtmlView', { src: env.subscribeUrl, title: '消息订阅' })
              reject()
            } else {
              resolve()
            }
          }
        })
      })
    },
    async submit() {
      if (this.submitLoading) return
      await this.$ext.user.authCommunityFansInfo()
      // 表单校验
      const config = JSON.parse(JSON.stringify(this.config))
      // config.circleClassifyId.required = true
      config.materialType.required = true
      for (const key in this.form) {
        if (!this.$validate.isNull(config[key])) {
          if (config[key].show !== false && config[key].required && !this.form[key]) {
            this.$uniPlugin.toast(`${config[key].label}不得为空！`)
            return
          }
        }
      }
      this.submitLoading = true
      this.form.isAnonymity = this.isAnonymous ? 1 : 2

      let openId = await this.$ext.wechat.getOpenId().catch(() => {
        this.submitLoading = false
      })
      const param = {
        ...this.form,
        imagesPath: !this.$validate.isNull(this.form.imagesPath) ? this.form.imagesPath.join(',') : '',
        source: 1, // 作者类型：1-用户，2-马甲
        type: 2, // 内容产生类型：1-PGC(管理后台新增)；2-UGC(小程序新增)
        isPhysician: 2, // 是否医生帖：1-是，2-否
        accountId: this.accountId,
        isComment: 1, // 是否开启评论：1-是，2-否
        putawayTime: this.$common.formatDate(new Date(), 'yyyy-MM-dd HH:mm:ss'),
        imgShowType: 1, // 帖子详情是否显示图片集合
        openid: openId,
        orientParams: 5, // 1-内部运营 2-广告 3-企业 4-真实用户
        entryType:this.publishInfo.id && 2, // 入口类型病友分享
        productId:this.publishInfo && this.publishInfo.id, //产品id
        brandId:this.publishInfo && this.publishInfo.brandId,
        circleClassifyId:'805444437623984135', //默认带上圈子
        videosPath:Array.isArray(this.form.videosPath) ? this.form.videosPath.join(',') : this.form.videosPath,
        roleType:this.form.takeMedicineUser == '服务商' ? 1 : 2,
        materialType:1,//默认图文
        appId:this.$appId,
      }

      // console.log('param',param)
      // return;
      const res = await this.$api.postmessage.postmessageInsert(param).catch(() => {
        this.submitLoading = false
      })
      this.$common.setKeyVal('business','circleClassifyInfoObj',{})      
      this.$uniPlugin.toast(res.msg)

      if (env.isOpenMsgSubscribe) {
        // 消息订阅
        const allTmplIds = [
          ...this.$constant.system.postsBeLikeTmplIds, // 帖子被赞
          ...this.$constant.system.commentReplyTmplIds, // 评论回复
          ...this.$constant.system.commentReplyOtherTmplIds, // 帖子评论回复
        ]
        await this.$uniPlugin.subscribeMessage(allTmplIds).catch(() => {
          this.submitLoading = false
        })
        const subscribeMessageRes = await this.requestSubscribeMessage(allTmplIds).catch(() => {
          this.submitLoading = false
        })
  
        const { centerUserId = '' } = this.curSelectUserInfo || {};
        const logParamList = Object.keys(subscribeMessageRes).filter(key => {
          return allTmplIds.includes(key)
        }).map(key => {
          let businessType = null
          // 点赞: 2
          // 帖子回复评论: 3
          // 回复评论: 4
          if (this.$constant.system.postsBeLikeTmplIds.includes(key)) {
            businessType = 2
          } else if (this.$constant.system.commentReplyTmplIds.includes(key)) {
            businessType = 4
          } else if (this.$constant.system.commentReplyOtherTmplIds.includes(key)) {
            businessType = 3
          }
          return {
            appId: this.$appId,
            templateId: key,
            openId: openId,
            subscribeStatus: subscribeMessageRes[key],
            businessType,
            businessId: res.data,
            accountId: this.accountId,
            userId: centerUserId
          }
        })
        this.$api.common.wxsubscribemessagelogInsertBatch({ wxSubscribeMessageLogList: logParamList })
        // 消息订阅调起成功，返回值'accept'、'reject'、'ban'分别代表用户对此条订阅是同意、拒绝、后台禁用
      }

      if (env.isOpenAccountSubscribe) {
        await this.accountSubscribe().catch(() => {
          this.submitLoading = false
        })
      }
      this.submitLoading = false
      this.clearForm()
      if (param.materialType === 2) {
        this.$uniPlugin.modal('提示', '发布成功，等待审核', {
          showCancel: false, // 是否显示取消按钮，默认为 true
          cancelText: '取消', //  取消按钮的文字，默认为"取消"，最多 4 个字符
          cancelColor: '#000000', //  取消按钮的文字颜色，默认为"#000000"(H5、微信小程序、百度小程序)
          confirmText: '前往查看', //  确定按钮的文字，默认为"确定"，最多 4 个字符
          confirmColor: '#15c398', //  确定按钮的文字颜色，H5平台默认为"#007aff"，微信小程序平台默认为"#3CC51F"，百度小程序平台默认为"#3c76ff"（H5、微信小程序、百度小程序）
          fn: async (n) => {
            if (n) {
              this.navtoGo('PersonalMyPosts', { processStatus: 1,isShowBtn:false })
            }
          }
        })
      } else {
        this.navtoGo('PostsDetail', { id: res.data,isShowBtn:false })
      }
    },
    // 消息订阅
    requestSubscribeMessage(tmplIds) {
      return new Promise((resolve, reject) => {
        try {
          this.$uniPlugin.requestSubscribeMessage(tmplIds, (res) => {
            let status = true
            tmplIds.forEach(item => {
              if (res[item.toString()] !== 'accept') status = false
            })
            if (status) {
              this.isShow = false
              this.$uniPlugin.toast('订阅成功')
              resolve(res)
            } else {
              this.isShow = true
              this.$uniPlugin.toast('订阅失败')
              resolve(res)
            }
          })
          this.$uniPlugin.hideLoading()
        } catch (err) {
          reject(err)
        }
      })
    },
    navtoGo(url, obj = {}) {
      this.$navto.push(url, obj)
    },
    clearForm() {
      this.isAnonymous = false, // 是否匿名
      this.form = JSON.parse(JSON.stringify(defaultForm))
      this.config = JSON.parse(JSON.stringify(defaultConfig))
      this.cDataImagesPath = []
      this.cDataVideosPath = []
      this.$forceUpdate()
    },
    changeAnonymous(e) {
      this.isAnonymous = e.detail.value.includes('anonymous')
    },
    // 获取圈子列表
    getCircleclassifyQueryList() {
      const that = this
      that.$api.circleclassify.circleclassifyQueryList().then(res => {
        const data = res.data.map(item => {
          return {
            ...item,
            value: item.id,
            label: item.name
          }
        })
        that.config.circleClassifyId.array = data
      })
    },
    imgReturnFn(obj, key) {
      if (key === 'videosPath') {
        this.cDataVideosPath = obj
        if (this.$validate.isNull(this.cDataImagesPath) || this.cDataImagesPath[0].dir.indexOf('?x-oss-process=video/snapshot,t_0000,f_jpg') !== -1) {
          if (this.$validate.isNull(obj)) {
            this.cDataImagesPath = []
            this.form.imagesPath = []
            return
          }
          const dir = `${obj[0].dir}?x-oss-process=video/snapshot,t_0000,f_jpg`
          this.cDataImagesPath = [{ url: this.file_ctx + dir, filePath: dir, dir: dir }]
          this.form.imagesPath = [dir]
        }
      } else if (key === 'imagesPath') {
        this.cDataImagesPath = obj
      }
      this.form[key] = obj.map(item => item.dir)
    }
  }
}
</script>

<style scoped lang="scss">
.body-main {
  display: flex;
  flex-direction: column;
  height: 100%;
  // background-color: #fff;
  box-sizing: border-box;
  // #ifdef MP-WEIXIN
  padding-bottom: calc(56px + env(safe-area-inset-bottom));
  // #endif
    .my-data{
      position: relative;
      .top-nav{
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 40px;
        line-height: 40px;
        padding: 0 32rpx 0 12rpx;
        .top-nav-l{
          display: flex;
          width: 48rpx;
          height: 48rpx;
          image{
            width: 100%;
            height: 100%;
          }
        }
      }
      .pdl40.my-user {
        padding-left:40upx
      }
      .my-user{
        position: relative;
        height: 120upx;
        padding: 24upx 32upx 64upx;
        line-height: 0;
      }
    }

  .form-wrapper {
    width: 100%;
    // height: calc(100vh - 530rpx);
    padding-bottom: 80rpx;
    border-radius: 24rpx 24rpx 0rpx 0rpx;
    // overflow-y: scroll;
    background-color: #fff;
  }
  .form-item-list {
    padding: 0 32rpx 20rpx;
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    box-sizing: border-box;
  }
  .selector-placeholder {
    height: 32rpx;
    line-height: 32rpx;
    font-size: 22rpx;
    color: #A5AAB8;
  }
  .submit-btn {
    position: relative;
    width: calc(100% - 64rpx);
    font-size: 32upx;
    font-weight: 500;
    color: #ffffff;
    height: 88rpx;
    line-height: 88upx;
    background: #00B484;
    border-radius: 16rpx;
    margin-bottom: 20rpx;
    overflow: inherit;
    span{
      position: absolute;
      top:-6rpx;
      right: 56rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 222rpx;
      height: 48rpx;
      font-weight: 500;
      font-size: 22rpx;
      color: #FF712B;
      background: #FFEEE6;
      border-radius: 24rpx 24rpx 24rpx 0rpx;
      z-index:999;
    }
  }
  .upload-after {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    align-items: center;
    // padding-left: 12rpx;
    .upload-tips {
      // color: #7f7f7f;
      // font-size: 22rpx;
      // letter-spacing: 2rpx;
      height: 36rpx;
      line-height: 36rpx;
      font-size: 24rpx;
      color: #4E5569;
    }
    .default-tips{
      height: 28rpx;
      line-height: 28rpx;
      font-size: 20rpx;
      color: #868C9C;
    }
  }
  .selector-label {
    display: flex;
    flex-direction: column;
    .selector-label-title{
      display: flex;
      align-items: center;
      .img{
        display: flex;
        width: 32rpx;
        height: 32rpx;
        margin-right: 4rpx;
        image{
          width: 100%;
          height: 100%;
        }
      }
      span{
        display: inline-block;
        height: 40rpx;
        line-height: 40rpx;
        font-size: 28rpx;
        color: #1D2029;
      }
    }
    .selector-label-value{
      margin: 4rpx 0 0 36rpx;
    }
  }
}
.my-picker-select{
  ::v-deep.my-picker{
    .picker{
      .picker-r{
        padding-right: 0;
      }
    }
  }
}
.tips-text {
  color: #666;
  text-align: center;
  font-size: 26rpx;
  line-height: 42rpx;
  color: $warningColor;
}
.flex-box-start {
  display: flex;
  align-items: flex-start;
}
</style>
