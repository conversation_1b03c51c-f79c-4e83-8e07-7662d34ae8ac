<template>
  <view class="container">
    <page>
      <view slot="content">
        <view class="main" v-if="regForm.PlatformMessage">
          <view class="top">
            <view class="l">
              <image mode="scaleToFill" :src="$static_ctx + 'image/business/icon-news-logo.png'"/>
            </view>
            <view class="m">
              {{regForm.PlatformMessage.author}}
            </view>
            <view class="r">
              {{$timePlugin.formatDate(new Date().valueOf(), regForm.PlatformMessage.createTime)}}
            </view>
          </view>
          <view class="title">
            {{regForm.PlatformMessage.title}}
          </view>
          <view class="rich">
            <rich-text :nodes="regForm.PlatformMessage.content"></rich-text>
          </view>
        </view>
      </view>
    </page>

  </view>
</template>

<script>
import { mapState } from 'vuex'
export default {
  components: {

  },
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      file_ctx: this.file_ctx,
      $static_ctx: this.$static_ctx,
      id: undefined,
      regForm: {}
    }
  },
  computed: {
    ...mapState('system', {
      temporaryStorage: state => state.temporaryStorage
    })
  },
  // 离开当前页面后执行
  onUnload() {
    this.$common.setKeyVal('system', 'temporaryStorage', {})
  },
  onLoad(paramsObj) {
    const query = this.$Route.query
    if (!this.$validate.isNull(query)) {
      this.id = query.id
    }
    this.init()
  },
  onShow() {},
  methods: {
    init() {
      this.$nextTick(() => {
        if (this.id) {
          this.getDetail(this.id)
        }
      })
    },
    getDetail(id) {
      const that = this
      that.$uniPlugin.loading('加载中', true)
      that.$api.common.platformmessageitemQueryOne({ id: id }).then(res => {
        that.regForm = res
        that.$uniPlugin.hideLoading()
        that.regForm = Object.assign({}, that.regForm)
      })
    }
  }

}
</script>

<style lang="scss" scoped>

  .m-t-10{
    margin-top: 10upx;
  }

  .m-tb-20{
    margin: 20upx auto;
  }

  .m-t-40{
    margin-top: 40upx;
  }
  .main{
    padding: 50upx;
    background-color: #fff;
    .top{
      margin-bottom: 30upx;
      .l{
        vertical-align: middle;
        display: inline-block;
        width: 90upx;
        height: 90upx;
        overflow: hidden;
        @include rounded(50%);
        margin-right: 20upx;
        overflow: hidden;
        image{
          width: 100%;
          height: 100%;
        }
      }
      .m{
        display: inline-block;
        width: calc(100% - 290upx);
        color: #666666;
        font-size: 32upx;
        line-height: 48upx;
        @include ellipsis(1);
        vertical-align: middle;
      }
      .r{
        vertical-align: middle;
        margin-left: 20upx;
        display: inline-block;
        width: 160upx;
        font-size: 24upx;
        line-height: 36upx;
        color: #999;
        text-align: right;
        @include ellipsis(1);
      }
    }
    .title{
      color: #333;
      font-size: 32upx;
      line-height: 48upx;
      font-family:PingFang SC;
      font-weight:500;
    }
    .rich{

    }
  }
</style>
