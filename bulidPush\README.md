# 小程序推送服务

这是一个基于 Express.js 的 Node.js 服务，用于通过 API 接口执行小程序的批量推送功能。

## 功能特性

- 🚀 **批量推送**: 支持同时推送多个小程序
- 📊 **实时状态**: 实时查看推送进度和状态
- 📝 **日志记录**: 详细的推送日志记录
- 🔄 **异步处理**: 非阻塞的异步推送任务
- 🛡️ **参数验证**: 完整的请求参数验证
- 📋 **小程序管理**: 获取所有可用小程序列表

## 快速开始

### 1. 安装依赖

```bash
cd bulidPush
npm install
```

### 2. 启动服务

```bash
# 生产环境启动
npm start

# 开发环境启动（支持热重载）
npm run dev
```

服务将在 `http://localhost:3001` 启动

## API 接口文档

### 1. 获取小程序列表

```http
GET /api/miniprogram/list
```

**响应示例:**
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "title": "小葫芦陪诊",
      "registerTitle": "广州绿葆网络发展有限公司",
      "appId": "wxfe1794ce063a251d",
      "providerId": "2124021789005144070",
      "source": 1,
      "icon": "LOGO-xiaohulupeizhen.png",
      "miniProgram": "accompany-ps"
    }
  ]
}
```

### 2. 执行推送任务

```http
POST /api/miniprogram/publish
Content-Type: application/json

{
  "description": "修复医院数据筛选错误",
  "idList": [1, 2, 3, 4],
  "version": "1.2.3"
}
```

**参数说明:**
- `description` (string, 必填): 推送描述信息
- `idList` (array, 必填): 要推送的小程序ID列表
- `version` (string, 必填): 版本号

**响应示例:**
```json
{
  "code": 200,
  "message": "推送任务已启动",
  "data": {
    "taskId": 1703123456789,
    "description": "修复医院数据筛选错误",
    "idList": [1, 2, 3, 4],
    "version": "1.2.3"
  }
}
```

### 3. 获取推送状态

```http
GET /api/miniprogram/status
```

**响应示例:**
```json
{
  "code": 200,
  "message": "获取状态成功",
  "data": {
    "isRunning": true,
    "currentTask": {
      "description": "修复医院数据筛选错误",
      "idList": [1, 2, 3, 4],
      "version": "1.2.3"
    },
    "progress": 2,
    "total": 4,
    "logs": [...]
  }
}
```

### 4. 获取推送日志

```http
GET /api/miniprogram/logs?limit=50
```

**参数说明:**
- `limit` (number, 可选): 返回的日志条数，默认50

### 5. 清空日志

```http
DELETE /api/miniprogram/logs
```

### 6. 停止推送任务

```http
POST /api/miniprogram/stop
```

### 7. 健康检查

```http
GET /api/health
```

## 使用示例

### 命令行方式（原有功能）

```bash
# 推送指定小程序
npm run publish description=修复医院数据筛选错误 idList=1,2,3,4 version=1.2.3
```

### API 方式（新增功能）

```javascript
// 使用 fetch 调用 API
const response = await fetch('http://localhost:3001/api/miniprogram/publish', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    description: '修复医院数据筛选错误',
    idList: [1, 2, 3, 4],
    version: '1.2.3'
  })
});

const result = await response.json();
console.log(result);
```

## 错误处理

服务提供了完整的错误处理机制：

- **400**: 参数错误（缺少必填参数、参数格式错误等）
- **409**: 冲突（已有推送任务在运行）
- **500**: 服务器内部错误

## 日志系统

- 自动记录推送过程中的所有关键步骤
- 支持不同级别的日志（info、warning、error）
- 自动清理旧日志（保持最新100条）
- 提供时间戳和详细信息

## 注意事项

1. **并发限制**: 同一时间只能运行一个推送任务
2. **缓冲时间**: 推送任务之间有10秒缓冲时间，防止资源锁冲突
3. **依赖环境**: 需要确保 HBuilderX CLI 工具已正确安装和配置
4. **密钥文件**: 确保 `keys/` 目录下有对应的小程序密钥文件

## 文件结构

```
bulidPush/
├── server.js          # Express 服务器主文件
├── index.js           # 原有的命令行推送脚本
├── utils.js           # 推送工具函数
├── package.json       # 项目配置和依赖
├── api-test.http      # API 测试文件
├── README.md          # 说明文档
└── keys/              # 小程序密钥文件目录
    ├── xiaohulu.key
    ├── Uyou.key
    └── ...
```

## 开发调试

使用提供的 `api-test.http` 文件可以快速测试所有 API 接口。如果使用 VS Code，推荐安装 REST Client 扩展来直接运行测试。

## 技术栈

- **Node.js**: 运行时环境
- **Express.js**: Web 框架
- **CORS**: 跨域支持
- **ES Modules**: 模块系统

## 许可证

MIT License