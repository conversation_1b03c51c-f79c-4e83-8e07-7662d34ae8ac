<template>
  <view>
    <page>
      <view class="content" slot="content">
        <view class="main">
          <view class="main-logo">
            <image :src="$static_ctx + 'image/system/logo/icon-logo.png'"/>
          </view>
          <view class="main-body">
            <view class="inp">
              <view class="inp-text">
                <view class="l">
                  <image class="width-height-atuo" :src="$static_ctx + 'image/system/logo/icon-phone.png'"/>
                </view>
                <text class="r">手机</text>
              </view>
              <view class="inp-main">
                <view class="main-r" :class="inputPhone ? 'bdb-1-ccc' : 'bdb-1-topic-c'">
                  <input
                    type="number"
                    maxlength="11"
                    v-model="phone"
                    placeholder="请输入手机号码"
                    placeholder-class="f-w-medium"
                    @focus="phoneFocus($event, 'inputPhone')"
                    @blur="phoneBlur($event, 'inputPhone')"
                  >
                </view>
              </view>
            </view>
            <view class="inp">
              <view class="inp-text">
                <view class="l">
                  <image class="width-height-atuo" :src="$static_ctx + 'image/system/logo/icon-pwd.png'"/>
                </view>
                <text class="r">验证码</text>
              </view>
              <view class="verification-code">
                <view class="verification-inp" :class="inputCode ? 'bdb-1-ccc' : 'bdb-1-topic-c'">
                  <input
                    type="number"
                    v-model="captcha"
                    placeholder="请输入验证码"
                    maxlength="4"
                    placeholder-class="f-w-medium"
                    @focus="CodeFocus($event, 'inputCode')"
                    @blur="CodeBlur($event, 'inputCode')"
                  >
                  <text class="verification" :class="{'is-get-code': !isAllowGetNum}" @tap="getCode">{{ getVerificationTxt }}</text>
                </view>
              </view>
            </view>
            <view class="m-t-80">
              <view :class="{'opacity-5': phone === '' || captcha === ''}" class="b-btn" @tap="onSubmit">
                {{ isLogining ? "正在登录" : "立即登录" }}
              </view>
            </view>
            <!-- <view class="registered" @tap="navTo('Register')">
              <view class="view-but">注 册</view>
            </view> -->
            <agree-with-protocol ref="checkboxRef" @returnFn = "returnFn"></agree-with-protocol>
          </view>
        </view>
      </view>
    </page>
  </view>
</template>

<script>
import AgreeWithProtocol from '@/modules/system/agreement-gather/components/agree-with-protocol'
import { mapActions } from 'vuex'
export default {
  components: {
    AgreeWithProtocol
  },
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $static_ctx: this.$static_ctx,
      title: '登录',
      num: 0,
      providerList: [],
      hasProvider: false,
      account: '',
      phone: '',
      code: '',
      captcha: '',
      positionTop: 0,
      redirectUrl: '',
      inputPhone: true,
      inputCode: true,
      getVerificationTxt: '获取验证码',
      isAllowGetNum: true,
      isDisabled: false,
      isLogining: false,
      regForm: {
        isCheckbox: true
      },
    }
  },
  computed: {

  },
  onLoad(paramsObj) {
    const query = this.$Route.query
    if (!this.$validate.isNull(query)) {
      this.phone = query.phone
    }
  },
  methods: {
    returnFn(isCheckbox){
      this.regForm.isCheckbox = isCheckbox
    },
    ...mapActions('user', ['GetToken', 'Login', 'GetUnitList', 'ChangeUnit', 'GetWechatInfo', 'GetUserInfo']),
    change(index) {
      this.num = index
    },
    navTo(name, paramObj) {
      this.$navto.push(name, paramObj)
    },
    changePasswordType(type, isShow) {
      this[isShow] = !this[isShow]
      if (this[type] == 'password') {
        this[type] = 'text'
      } else {
        this[type] = 'password'
      }
    },
    phoneFocus(e, type) {
      this[type] = !this[type]
    },
    phoneBlur(e, type) {
      this[type] = !this[type]
    },
    CodeFocus(e, type) {
      this[type] = !this[type]
    },
    CodeBlur(e, type) {
      this[type] = !this[type]
    },
    initProvider() {
      const filters = ['weixin', 'qq', 'sinaweibo']
      uni.getProvider({
        service: 'oauth',
        success: res => {
          if (res.provider && res.provider.length) {
            for (let i = 0; i < res.provider.length; i++) {
              if (~filters.indexOf(res.provider[i])) {
                this.providerList.push({
                  value: res.provider[i],
                  image: '../../static/img/' + res.provider[i] + '.png'
                })
              }
            }
            this.hasProvider = true
          }
        },
        fail: err => {
          // console.error('获取服务供应商失败：' + JSON.stringify(err))
        }
      })
    },
    initPosition() {
      /**
       * 使用 absolute 定位，并且设置 bottom 值进行定位。软键盘弹出时，底部会因为窗口变化而被顶上来。
       * 反向使用 top 进行定位，可以避免此问题。
       */
      this.positionTop = uni.getSystemInfoSync().windowHeight - 100
    },
    getCode() {
      // this.$uniPlugin.toast('敬请期待')
      // return
      if (!this.isAllowGetNum) return
      const isPhone = this.$validate.isMobile(this.phone)
      if (!isPhone.flag) {
        this.$uniPlugin.toast(isPhone.msg)
        return
      }
      const params = {
        phone: this.phone, // 用户填写的手机号
        uuid: this.$common.getTokenUuid(), // 随机编码
      }
      this.$uniPlugin.toast('发送中')
      this.$api.sys.sendSms(params, res => {
        // debugger
        this.resetTime(60)
        this.$uniPlugin.toast('发送成功')
      })
    },
    async onSubmit() {
      const that = this

      await this.$refs.checkboxRef.isCheckboxToastFn()

      if (this.isLogining) return
      if (!that.isDisabled) {
        that.isDisabled = true
        const tipArr = {
          'phone': '请输入手机号',
          code: '请输入4位数的验证码',
        }
        const params = {
          phone: that.phone, // 登陆账户名或手机号
          code: that.captcha.length === 4 ? that.captcha : '', // 密码,
          uuid: this.$common.getTokenUuid(), // 随机编码
        }

        // 表单验证
        if (!that.$common.validationForm(tipArr, params)) {
          return
        }

        const isPhone = that.$validate.isMobile(that.phone)
        if (!isPhone.flag) {
          that.$uniPlugin.toast(isPhone.msg)
          return
        }
        // if(this.captcha&&this.captcha.length<7){
        //   this.$uniPlugin.toast('验证码无效');
        // }
        const parameter = {
          phone: that.phone, // 登陆账户名或手机号
          code: that.captcha, // 密码
          uuid: that.$common.getTokenUuid() // 随机编码
        }
        that.setLoginStatus(true) // 设置登录状态
        that.$uniPlugin.loading('正在登录', true)
        that.$ext.user.codeLogin(parameter).then(codeDate => {
          that.$uniPlugin.hideLoading()
          that.$uniPlugin.toast('登录成功')
          that.$ext.user.getInfoGroupPage(that, () => {
            // #ifdef MP-WEIXIN
              that.$ext.user.bindWeixinAccount({})
              that.$ext.user.usertenantrecordBindFans()
            // #endif
            that.setLoginStatus(false)
            that.$ext.webSocket.webSocketInit()
          })
        }).catch(() => {
          that.isDisabled = false
          that.$uniPlugin.hideLoading()
        })
      }
    },
    setLoginStatus(flag) {
      this.isLogining = flag
    },
    jumpTo(url) {
      uni.navigateTo({
        url: url
      })
    },
    back() {
      this.$navto.back(1)
    },
    /**
     * 倒计时
     * @param time
     */
    resetTime(time) {
      this.isAllowGetNum = false
      const countdownMinute = time || 30 // 1分钟倒计时

      const startTimes = new Date() // 开始时间 new Date('2016-11-16 15:21');
      const endTimes = new Date(startTimes.setSeconds(startTimes.getSeconds() + countdownMinute)) // 结束时间
      const curTimes = new Date() // 当前时间
      let surplusTimes = endTimes.getTime() / 1000 - curTimes.getTime() / 1000 // 结束毫秒-开始毫秒=剩余倒计时间

      // 进入倒计时
      let countdowns = setInterval(() => {
        surplusTimes--
        // eslint-disable-next-line no-unused-vars
        let minu = '' + Math.floor(surplusTimes / 60)
        let secd = '' + Math.round(surplusTimes % 60)
        // console.log(minu+':'+secd);
        minu = minu.length === 1 ? '0' + minu : minu
        secd = secd.length === 1 ? '0' + secd : secd
        this.getVerificationTxt = secd + '秒后重试'
        if (surplusTimes <= 0) {
          // console.log('时间到！');

          this.getVerificationTxt = '获取验证码'
          clearInterval(countdowns)
          countdowns = true
          this.isAllowGetNum = true
        }
      }, 1000)
    }
  },
  onReady() {
  },
  mounted() {
    // 随机码
    this.uuid = this.$common.uuid()
  }
}
</script>

<style lang="scss" scoped>
uni-button:after{
  border: none !important;
}
.opacity-5{
  @include opacity(0.5);
}
.content {
  background: #fff;
  height: 100vh;
  .main{
    .main-top{
      padding: 30upx;
      position: relative;
      height: 30upx;
      .l{
        width: 30upx;
        height: 30upx;
        position: absolute;
        left: 30upx;
        top: 30upx;
      }
      .r{
        height: 30upx;
        line-height: 30upx;
        color: #333;
        font-size: 30upx;
        position: absolute;
        right: 30upx;
        top: 30upx;
      }
    }
    .main-logo{
      //padding: 30upx;
      padding-top: 100upx;
      image{
        width: 340upx;
        height: 150upx;
        margin: 0 auto;
        display: block;
      }
    }
    .main-body{
      width: 100%;
      .inp{
        width: 520upx;
        margin: 20upx auto 0 auto;
        .inp-text{
          height: 48upx;
          margin-bottom: 14upx;
          .l{
            width: 48upx;
            height: 48upx;
            margin-right: 16upx;
            display: inline-block;
            vertical-align: middle;
          }
          .r{
            color: #333;
            font-size: 32upx;
            height: 48upx;
            line-height: 48upx;
            display: inline-block;
            vertical-align: middle;
          }
        }
        .inp-main{
          height: 50upx;
          .main-l{
            display: inline-block;
            vertical-align: middle;
            width: 100upx;
            .l{
              display: inline-block;
              vertical-align: middle;
              height: 50upx;
              line-height: 50upx;
              color: #333;
              font-size: 32upx;
              margin-right: 10upx;
            }
            .r{
              display: inline-block;
              vertical-align: middle;
              height: 8upx;
              width: 15upx;
            }
          }
          .main-r{
            /*width: calc(100% - 100upx);*/
            width: 100%;
            display: inline-block;
            vertical-align: middle;
          }
        }
        .verification-code{
          position: relative;
          .verification-inp{
            height: 70upx;
            padding-right: 200upx;
          }
          .verification{
            position: absolute;
            right: 0;
            top: 0;
            text-align: center;
            color: #fff;
            font-size: 24upx;
            width: 160upx;
            height: 60upx;
            //background: linear-gradient(90deg,rgba(255,185,36,1),rgba(254,219,55,1));
            background: $topicC;
            @include rounded(30upx);
            line-height: 60upx;
          }
        }
      }
      .text-btn{
        width: 520upx;
        margin: 20upx auto 0 auto;
        position: relative;
        height: 30upx;
        .l{
          position: absolute;
          left: 0;
          top: 0;
          color: $topicC;
          font-size: 28upx;
        }
        .r{
          position: absolute;
          right: 0;
          top: 0;
          color: $topicC;
          font-size: 28upx;
        }
      }
    }
  }
}

.width-height-atuo{
  width: 100%;
  height: 100%;
}
.f-w-medium{
  color: #bfbfbf;
  font-weight: Medium;
  font-size: 30upx;
}
.bdb-1-topic-c {
  border-bottom: 2upx solid $topicC;
}
.bdb-1-ccc {
  border-bottom: 2upx solid #ccc;
}
.bg-white{
  background-color: #fff;
}
.m-t-80{
  margin-top: 80upx;
}
.m-t-20{
  margin-top: 20upx;
}
.margin-0-auto{
  margin: 0 auto;
}
.b-btn {
  width: 520upx;
  //background:linear-gradient(90deg,rgba(255,185,36,1),rgba(254,219,55,1));
  background: $topicC;
  color: #fff;
  @include rounded(44upx);
  margin: 0 auto;
  height: 88upx;
  line-height: 88upx;
  font-size: 36upx;
  text-align: center;
}
.is-get-code{
  @include opacity(0.5);
}
.registered{
  width: 120upx;
  margin: 32upx auto 0;
  .view-but{
    font-size: 15px;
    line-height: 27px;
    color: #b1b0af;
    text-align: center;
  }
}
</style>

