
<template>
  <view class="title-input clear-float" :class="{'bdt':defaultConfig.bdt,'bdb':defaultConfig.bdb && !child}">
   <!-- <view class="l-l" :style="{'color': defaultConfig.titleColor}">
      {{defaultConfig.label}}
      <text class="star" v-if="defaultConfig.required">*</text>
    </view> -->
    <view class="l-r">
      <template v-for='(item,index) in rateArr'>
        <view class='rateitem' :key='index'>
          <span class='l-l' :class='defaultConfig.titleClass'>{{defaultConfig.label}}</span>
          <!-- <view class='ratelabel'>{{defaultConfig.ptitle}}</view> -->
          <text class="star" v-if="defaultConfig.required">*</text>
          <uni-rate :margin="8" @change="returnFn"  v-model='form.data.val' :size="initSize"  :max="startNum" :readonly='disabled' />
          <view class="rateinfo">{{item.textObj[form.data.val]}}</view>
        </view>
      </template>
    </view>
  </view>
</template>

<script>
import uniRate from '@/modules/activity/components/uni/uni-rate/uni-rate'
export default {
  components:{
    uniRate
  },
  data() {
    return {
      startSize:22,
      initSize:28,
      startNum:5,
      rateArr:[
        {
          textObj:{
            1:'一般',
            2:'很好'
          }
        }
      ],
      form: {
        data: {
          val: ''
        }
      },
      array: [],
      index: 0,
      defaultConfig: {
        bdt: false,
        bdb: true,
        titleColor: '#333',
        textColor: '#333',
        label: '单行输入框',
        name: 'input',
        required: false,
      }
    }
  },
  watch: {

    cData: {
      handler(val) {
        // this.watchDataMain(val)
      },
      deep: true
    },
    config: {
      handler(val) {
        this.copyConfig()
      },
      deep: true
    }
  },
  props: {
    child:{
      type:Boolean,
      default:false,
    },
    // 是否禁用disable
    disabled: {
      type: Boolean,
      default() {
        return false
      }
    },
    // 初始值传值，用于回显
    cData: {
      type: [String, Number],
      required: false,
      default() {
        return ''
      }
    },
    // 参数设置
    config: {
      type: Object,
      required: false,
      default: () => {
        return {}
      }
    }
  },
  computed: {

  },
  mounted() {
    console.log(this.config)
      this.copyConfig()
      if (!this.$validate.isNull(this.cData)) {
        this.watchDataMain(this.cData)
      }
  },
  methods: {
    /**
       * 初始化拷贝config对象
       */
    copyConfig() {
      const that = this
      const obj = that.config
      Object.keys(obj).forEach(function(key) {
        that.defaultConfig[key] = obj[key]
      })
      console.log('defaultConfig',this.defaultConfig)
      if(this.defaultConfig.config && this.defaultConfig.config.startNum){
        this.startNum = this.defaultConfig.config.startNum
        this.startSize = this.defaultConfig.config.startSize || 22
        let children = this.defaultConfig.config.children;
        let result = []
        for(let i=0;i<children.length;i++){

          let textObj = {}
          for(let j=0;j<children[i].children.length;j++){
            textObj[j + 1] = children[i].children[j].describe
          }
          result.push({
            textObj,
            title:children[i].title
          })
        }
        this.rateArr = result
      }
    },
    /**
       * 监听Data主逻辑方法
       */
    watchDataMain(val) {
      this.form.data.val = val
    },
    /**
       * picker触发选中事件
       * @param e
       */
    returnFn(e) {
     const that = this
     if (that.disabled) return
     that.$emit('updateForm', { key: '' + that.config.name, value: that.form.data.val,extendValue:that.rateArr[0]?.textObj[that.form.data.val] })
    }
  }
}
</script>

<style lang="scss" scoped>

  .rateitem{
    display:flex;
    // margin-bottom:20upx;
    .ratelabel{
      // padding-right:20upx;
      text-align:right
    }
  }
  .color-topicC{
    color: $topicC !important;
  }
  .title-input{
    background-color:#ffffff;
    .l-l{
      // line-height: 88upx;
      color: #333333;
      font-weight: 600;
      // font-size:30upx;
      font-size: 36rpx;
    }
    .l-l.font36{
      font-size: 36upx;
    }

    .l-r{
      margin-bottom: 5px;
      padding-top: 30upx;
      padding-bottom: 15px;
      // display: flex;
      // align-items: center;
          // padding-bottom: 20rpx;
      // input {
      //   height: 80upx;
      //   line-height: 80upx;
      //   color: #333;
      //   font-size: 32upx;
      //   text-align: left;
      //   display: inline-block;
      //   vertical-align: middle;
      //   width: calc(100% - 64upx);
      //   border-bottom: 1upx solid $borderColor;
      //   border-radius: 10upx;
      //   padding: 0 20rpx;

      // }
      // input.disabled{
      //     background-color: #dbdbdb;
      //     color: #fff;
      // }
      // .util{
      //   width: 64upx;
      //   font-size: 28upx;
      //       overflow: hidden;
      //       text-align: center;

      // }
    }
  }
  .star{
    color: #F85E4C;
    padding-top: 6upx;
    font-size: 32upx;
    padding-right: 20upx;
    display: inline-block;
        margin-left: 10rpx;
  }
</style>
