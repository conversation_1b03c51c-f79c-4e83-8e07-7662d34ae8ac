<template>
    <view>
      <canvas @longpress="downloadCode" class="myCanvas" canvas-id="myCanvas" :style='{width:canvasStyle.width,height:canvasStyle.height}'></canvas>
      <image v-if="imgSrc" class="poster" :src="imgSrc"></image>
      <view class="bottomBox">
        <view @click="downloadCode" class="botButton">保存到相册</view>
      </view>
    </view>
</template>
<script>
import serverOptions from '@/config/env/options'
export default {
    data() {
        return {
            poster: '', // 海报地址
            canvasStyle: {
              width: '0', // 初始宽度
              height: '0' // 初始高度`
            }, // 用于存储canvas的宽度和高度
            codeImg:'',
            orderId: '',
            bookName: '',
            serviceName: '',
            payPrice: '',
            providerId: '',
            imgSrc:''
        }
    },
    async onLoad({orderId,bookName,serviceName,payPrice,providerId}) {
        // 获取分享信息
        this.orderId = orderId;
        this.bookName = decodeURIComponent(decodeURIComponent(decodeURIComponent(bookName)));
        this.serviceName = decodeURIComponent(decodeURIComponent(decodeURIComponent(serviceName)));
        this.payPrice = payPrice;
        this.providerId = providerId;
        console.log('分享信息', {orderId,bookName,serviceName,payPrice,providerId});
        uni.showLoading({title:'加载中...',mask:true})
        await this.orderCode()
        await this.loadCodeImg()
        uni.hideLoading();
      },
    methods: {
      // 读取海报 拼接图片
      async loadCodeImg(){
        if(!this.providerOptions) this.providerOptions = (await this.$api.accompanyDoctor.accompanyproviderQueryOne({id:serverOptions.providerId})).data;
        let {poster,posterLogo} = this.providerOptions;
        poster = this.file_ctx + poster;
        posterLogo = this.file_ctx + posterLogo;
        // 获取设备像素比
        const dpr = uni.getSystemInfoSync().pixelRatio; 
        // 使用canvas读取poster这张海报的所有像素信息 罗列出每个像素点的颜色和坐标
        const canvas = uni.createCanvasContext('myCanvas',this);
        console.log('poster',poster)
        // 获取海报背景图片信息
        let posterInfo = await this.getImageInfo({imgSrc:poster})
        // 获取当前屏幕宽度 计算出图片的缩放比例
        const screenWidth = posterInfo.width;
        const scale = screenWidth / posterInfo.width;
        console.log('当前界面缩放比例',posterInfo);
        // 设置canvas尺寸与图片一致
        this.canvasStyle.width = posterInfo.width + 'px';
        this.canvasStyle.height = posterInfo.height + 'px';
        // 绘制背景海报
        await this.drawImageToCanvas({path:posterInfo.path,x:0,y:0,width:posterInfo.width,height:posterInfo.height,canvas})
        let pro = new Promise((res)=>{
          setTimeout(()=>{
            res()
          },50)
        })
        await pro;
        // 获取像素数据
        let bgImageData = await this.getImageData({canvasId:'myCanvas',x:0,y:0,width:posterInfo.width,height:posterInfo.height})
        // 计算获取灰色的像素点用来贴海报
        let {leftPixel,rightPixel,distance} = this.getPixelPosition({bgImageData,currentColor:'203, 42, 182',scale})
        console.log('距离',distance,rightPixel,leftPixel);
        // 获取二维码图片信息
        let codeImgInfo = await this.getImageInfo({imgSrc:this.codeImg})
        // 绘制二维码
        await this.drawImageToCanvas({path:codeImgInfo.path,x:leftPixel.x,y:leftPixel.y - distance / 2,width:distance + 1,height:distance + 4,canvas,isCircle:true})

        // 获取当前服务商logo
        let posterLogoInfo = await this.getImageInfo({imgSrc:posterLogo})
        // 绘制二维码
        await this.drawImageToCanvas({path:posterLogoInfo.path,x:30 * 3,y:30 * 3,width:posterLogoInfo.width / 2 * 3,height:posterLogoInfo.height / 2 * 3,canvas,isCircle:true})
        // 绘制文字
        // 绘制用户名
        await this.drawTextToCanvas({text:this.bookName,x:screenWidth/2,y:(leftPixel.y - distance / 2) -20 * 3,canvas,fontSize:16 * 3,color:'#777777',textAlign:'center',isCircle:true})
        // 绘制服务名字
        await this.drawTextToCanvas({text:this.serviceName,x:screenWidth/2,y:(leftPixel.y - distance / 2) ,canvas,fontSize:16 * 3,color:'#777777',textAlign:'center',isCircle:true})
        // 绘制服务价格 
        await this.drawTextToCanvas({text:'￥' + this.payPrice / 100,x:screenWidth/2,y:(leftPixel.y + distance / 2) + 30 * 3,canvas,fontSize:16 * 3,color:'#fa7c3d',textAlign:'center',isCircle:true})
        // 绘制提示语
        await this.drawTextToCanvas({text:'长按二维码前往支付',x:screenWidth/2,y:(leftPixel.y + distance / 2) + 50 * 3,canvas,fontSize:16 * 3,color:'#777777',textAlign:'center',isCircle:true})
        // 渲染海报
        await this.renderPoster()
      },
      // 获取订单二维码
      async orderCode(){
        // 插入存储订单二维码
        try {
          // 查询该订单是否有二维码
          let {data:codeData} = await this.$api.accompanyDoctor.minichannellinkQueryOne({
            businessId:this.orderId,
            businessType:4
          })
          // 如果有则直接返回
          if(codeData.qrcodePath){
            // 确保二维码图片链接是完整的URL
            let qrCodeUrl = codeData.qrcodePath;
            if (qrCodeUrl && !qrCodeUrl.startsWith('http://') && !qrCodeUrl.startsWith('https://')) {
              const baseUrl = getApp().globalData.baseUrl || '';
              qrCodeUrl = baseUrl ? baseUrl + qrCodeUrl : qrCodeUrl;
            }
            
            // 确保使用https协议
            if (qrCodeUrl.startsWith('http://')) {
              qrCodeUrl = qrCodeUrl.replace('http://', 'https://');
              console.log('已将图片URL从http转换为https:', qrCodeUrl);
            }
            
            this.codeImg = qrCodeUrl;
            console.log('二维码图片地址:', this.codeImg);
            return; 
          }          
          // 如果没有则插入参数 渲染二维码
          let {data} = await this.$api.common.codeInsert({
            path:'modules/accompany-doctor/service-reservation/index?id='+this.orderId,
            appid:serverOptions.getoptions().appId,
            name:'订单二维码',
            businessType:4,
            businessId:this.orderId,
            CustomParameters:`providerId=${this.providerId}`
          });
          
          // 确保二维码图片链接是完整的URL
          let qrCodeUrl = data.qrcodePath;
          if (qrCodeUrl && !qrCodeUrl.startsWith('http://') && !qrCodeUrl.startsWith('https://')) {
            const baseUrl = getApp().globalData.baseUrl || '';
            qrCodeUrl = baseUrl ? baseUrl + qrCodeUrl : qrCodeUrl;
          }
          
          // 修改图片链接
          if (qrCodeUrl.startsWith('http://')) {
            qrCodeUrl = qrCodeUrl.replace('http://', 'https://');
            console.log('已将图片URL从http转换为https:', qrCodeUrl);
          }
          
          this.codeImg = qrCodeUrl;
          console.log('新生成的二维码图片地址:', this.codeImg);
          
        } catch (error) {
          console.error('获取二维码失败:', error);
          uni.showToast({
            title: '获取二维码失败',
            icon: 'none'
          });
        }
      },
      // 询问是否授权下载图片
      downloadImage(url) {
        let pro,resFn;
        pro = new Promise((res)=>resFn = res);
        uni.authorize({
          scope:'scope.writePhotosAlbum',
          success() {
            resFn(true)           
          },
          fail() {
            resFn(false)
          }
        })
        return pro
      },
      async downloadCode(){
        let downLoadFlag = await this.downloadImage()
        if(!downLoadFlag){
          uni.showToast({
            title: '请先授权保存图片',
            icon: 'none'
          });
          return;
        }
        uni.showLoading({title: '保存中...', mask: true});
        // 保存图片到相册
        uni.saveImageToPhotosAlbum({
          filePath: this.imgSrc,
          success: (cer) => {
            uni.hideLoading();
            uni.showToast({
              title: '保存成功',
              icon:'success'
            });
          },
          fail: (err) => {
            console.log('保存失败:', err);
            uni.hideLoading();
          }
        })
      },
      // 生产二维码海报图片路径
      renderPoster(){
        let pro,resFn;
        pro = new Promise((res)=>resFn = res);
        let imageUrl = this.codeImg;
        // 确保URL是完整的
        if (imageUrl && !imageUrl.startsWith('http://') && !imageUrl.startsWith('https://')) {
          const baseUrl = getApp().globalData.baseUrl || '';
          if (baseUrl) {
            imageUrl = baseUrl + imageUrl;
          } else {
            imageUrl = 'https://file.greenboniot.cn/' + imageUrl;
          }
        }
        
        // 确保使用https协议
        if (imageUrl.startsWith('http://')) {
          imageUrl = imageUrl.replace('http://', 'https://');
        }
        // 获取当前canvas中的图片渲染
        uni.canvasToTempFilePath({
          x: 0,
          y: 0,
          quality:1,
          width: this.canvasStyle.width,
          height: this.canvasStyle.height,
          destWidth: this.canvasStyle.width * 2,
          destHeight: this.canvasStyle.height * 2,
          canvasId: 'myCanvas',
          success: (res) => {
            this.imgSrc = res.tempFilePath;
            resFn()
          },
          fail: (err) => {
            console.log('canvasToTempFilePath 失败:', err);
            resFn()
            uni.showToast({
              title: '生产失败',
              icon: 'none'
            });
          }
        },this)
        return pro
      },
      
      // 获取图片信息
      getImageInfo({imgSrc}) {
        let resFn;
        let pro = new Promise((res)=>resFn = res);
        uni.getImageInfo({
          src: imgSrc,
          success: (res) => {
            resFn(res)
          },
          fail: (err) => {
            console.error('获取图片信息失败:', err);
            resFn(err)
          }
        })
        return pro
      },
      // 绘制图片到canvas
      drawImageToCanvas({path,x,y,width,height,canvas,isCircle=false}){
        let resFn;
        let pro = new Promise((res)=>resFn = res);
        // 绘制图片到canvas
        canvas.drawImage(path, x, y, width, height);
        canvas.draw(isCircle, async () => resFn())
        return pro
      },
      // 获取像素数据
      getImageData({canvasId,x,y,width,height}){
        let resFn;
        let pro = new Promise((res)=>resFn = res);
        uni.canvasGetImageData({
          canvasId,x,y,width,height,
          success: (res) => {  resFn(res)},
          fail: (err) => {
              console.error('获取图片信息失败:', err);
              resFn(err)
            }
        },this)
        return pro
      },
      colorDistance(color1, color2) {
        const [r1, g1, b1] = color1;
        const [r2, g2, b2] = color2;
        return Math.sqrt(
          Math.pow(r2 - r1, 2) +
          Math.pow(g2 - g1, 2) +
          Math.pow(b2 - b1, 2)
        );
      },
      // 计算像素点位置
      getPixelPosition({bgImageData,currentColor,threshold = 10,scale}){
        const dpr = uni.getSystemInfoSync().pixelRatio;
        let pixels = [];
        const channels = currentColor.split(',').map(Number);
          // 允许的色差阈值（建议5-15）
        const colorThreshold = threshold; 
        // 遍历所有像素（每4个值表示一个像素的RGBA）
        for (let i = 0; i < bgImageData.data.length; i += 4) {
          const r = bgImageData.data[i];
          const g = bgImageData.data[i+1];
          const b = bgImageData.data[i+2];
          // 计算当前像素与目标颜色的距离
          const distance = this.colorDistance([r, g, b], channels);
          if (distance <= colorThreshold) {
            const x = (i / 4) % bgImageData.width;
            const y = Math.floor((i / 4) / bgImageData.width);
            pixels.push({x, y});
          }

        }
        console.log('像素数据获取完成', pixels);
        // 遍历像素集合 找到最左边和最右边的像素点并计算他们的距离
        let leftPixel = pixels.reduce((acc, pixel) => {
          return pixel.x < acc.x ? pixel : acc;
        })
        let rightPixel = pixels.reduce((acc, pixel) => {
          return pixel.x > acc.x ? pixel : acc;
        })
        let distance = (rightPixel.x - leftPixel.x + 15);
        leftPixel.x -= 10;
        leftPixel.y += 10;
        return {pixels,leftPixel,rightPixel,distance}
      },
      // 绘制文字
      drawTextToCanvas({text,x,y,canvas,fontSize=16,color='black',textAlign='center',isCircle=false}){
        let resFn;
        let pro = new Promise((res)=>resFn = res);  
        canvas.setFillStyle(color);
        canvas.setFontSize(fontSize);
        canvas.setTextAlign(textAlign);
        canvas.fillText(text, x, y);
        canvas.draw(isCircle, () => resFn())
        return pro
      },
    }
}
</script>
<style lang="scss" scoped>
.poster{
  width: 100vw;
  height: 100vh;
  margin-bottom: 180rpx;
}
.myCanvas {
  position: absolute;
  left: -9999px;
  top: 0;
  z-index: -1;
  visibility: hidden;
}
.bottomBox{
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100vw;
  height: 180rpx;
  background: white;
  padding: 24rpx 52rpx 68rpx;
  box-sizing: border-box;
  .botButton{
    width: 646rpx;
    height: 88rpx;
    background: #00B484;
    border-radius: 44rpx 44rpx 44rpx 44rpx;
    font-weight: 500;
    font-size: 32rpx;
    color: #FFFFFF;
    text-align: center;
    line-height: 88rpx;
  }
}
</style>