import md5 from 'js-md5'
import request from '@/common/util/request'
import env from '@/config/env'
import common from '@/common/util/main'
import uniPlugin from '@/common/util/uni-plugin'
import validate from '@/common/util/validate'
import store from '@/store'
import constant from '@/constant'

/**
 * 系统请求工具类，
 * 注意：API接口只做请求数据和格式化数据的事情，其他事务一概不处理
 */

// 新修改密码
const changePwd = function changePwd(param) {
  const url = env.ctx + 'auth/api/v1/user/code/change/password'
  return request.postJson(url, param)
}

// 获取临时token
const getToken = function getToken(params) {
        const url = env.ctx + 'auth/api/v2/app/login/temporary'
        return request.postForm(url, params)
    }
    /**
     * 登陆  18520583470 / gb@2019
     * @param {String} name 账号
     * @param {String} pwd 密码
     * @param {String} loginType 登陆方式 1.手动登陆； 2.自动登陆（默认）
     * 注意：自动登陆成功，返回true； 若不成功，则提示相应问题，并返回false，自动切换为游客模式；
     * 手动登陆成功后，返回true
     */
const login = (params) => {
    const url = env.ctx + 'auth/api/v1/login'
    return request.postForm(url, params)
}

// 验证码登录
const codeLogin = function codeLogin(param) {
  const url = env.ctx + 'auth/api/v1/user/code/login'
  return request.postJson(url, param)
}

/**
 * 获取用户档案信息
 */
const getUserInfo = function getUserInfo() {
    uniPlugin.loading('获取档案中')
    return new Promise((resolve, reject) => {
        const url = env.ctx + 'auth/api/v2/app/sysuser/info'
        request.get(url, {}).then((res) => {
            if (res.code === 0) {
                resolve(res.result)
            }
        }).catch(e => {
            reject(e)
        })
    })
}

/**
 * 获取用户拥有的资源菜单
 * @param type 档案类型
 * @returns {Promise<any>}
 */
const getPermission = function getPermission(params) {
    return new Promise((resolve, reject) => {
        const url = env.ctx + 'auth/api/v1/permission/get/applet/role/all/permission'
        request.get(url, params).then((res) => {
            if (res.code === 0) {
                resolve(res.data)
            } else {
                reject()
            }
        })
    })
}

/**
 * 更换手机号
 * @param {String} phone 要更换的手机号
 */
const changePhone = function changePhone(phone) {
    const params = {
        phone: phone
    }
    const url = env.ctx + 'auth/api/v2/app/user/changephone'
    request.putForm(url, params).then((res) => {
        if (res.code === 0) {

        }
    })
}

/**
 * 修改密码
 * @param {String} oldPwd 旧密码（经过Md5处理过的）
 * @param {String} pwd 新密码（经过Md5处理过的）
 */
const changePWD = function changePWD(oldPwd, pwd) {
    const params = {
        oldPwd: md5(oldPwd),
        pwd: md5(pwd)
    }
    const url = env.ctx + 'auth/api/v2/app/user/changepwd'
    request.putForm(url, params).then((res) => {
        if (res.code === 0) {

        }
    })
}

/**
 * 重置密码
 * @param {String} phone 手机号
 * @param {String} code 随机编码
 * @param {String} captcha 验证码
 * @param {String} pwd 新密码（经过Md5处理过的
 */
const resetPwd = function resetPwd(phone, code, captcha, pwd) {
    const params = {
        phone: phone,
        code: code,
        captcha: captcha,
        pwd: md5(pwd)
    }
    const url = env.ctx + 'auth/api/v2/app/user/restpwd'
    request.putForm(url, params).then((res) => {
        if (res.code === 0) {

        }
    })
}

/**
 * 解绑/绑定微信openId
 * @param {String} openId 微信openId
 * @param {String} unitId 用户所在当前单位id
 * @param {String} type 1：绑定 2：解绑
 */
const bindWechat = function bindWechat(unitId, type) {
    return new Promise((resolve, reject) => {
        const firstUrlParam = common.getKeyVal('user', 'firstUrlParam', 'sessionStorage')
        const params = {
            openId: firstUrlParam.openId,
            unitId: unitId,
            type: type
        }
        const url = env.ctx + 'auth/api/v2/app/user/bindingopenid'
        request.get(url, params).then((res) => {
            // console.log('绑定微信openId', res)
            if (res.code === 0) {
                resolve(res)
            }
        }).catch(e => {
            reject(e)
        })
    })
}

/**
 * 获取 微信信息
 */
const getWechatInfo = function getWechatInfo() {
        return new Promise((resolve, reject) => {
            const firstUrlParam = common.getKeyVal('user', 'firstUrlParam', 'sessionStorage') || {}
            let weChatInfo = {
                    nickName: '',
                    headImgUrl: '',
                    // nickNameTip: '', // 解绑/绑定 弹窗使用
                    // headImgUrlTip: '', // 解绑/绑定 弹窗使用
                    isBind: false,
                    isShow: false
                }
                // 若没有openId 则设置为获取失败
            if (!firstUrlParam.openId) {
                common.setKeyVal('user', 'weChatInfo', weChatInfo)
                reject()
                return
            }

            const params = {
                openId: firstUrlParam.openId
            }
            const url = env.ctx + 'wechat/api/v1/openwechatuser/query/one'
            request.get(url, params).then((res) => {
                // console.log('获取微信信息', res)
                if (res.code === 0) {
                    if (!validate.isNull(res.result)) {
                        weChatInfo = res.result
                    }
                    resolve(weChatInfo)
                }
            }).catch(e => {
                reject(e)
            })
        })
    }
    /**
     * 获取 平台未读信息
     */
const getUnreadPlateNum = function getUnreadPlateNum() {
        return new Promise((resolve, reject) => {
            request.get('basics/api/v1/platformmessageitem/query/unread', {}).then((res) => {
                if (res.code === 0) {
                    resolve(res)
                }
            }).catch(error => {
                reject(error)
            })
        })
    }
    /**
     * 获取租户未读信息
     */
const getUnreadGardentNum = function getUnreadGardentNum() {
        const params = {
            userId: store.state.user.curSelectRole.identityType === '1538' ? store.state.user.curSelectRole.data.studentId : store
                .state.user.userInfo.id,
            userType: this.$userType
        }
        return new Promise((resolve, reject) => {
            request.get(env.ctx + 'manage/api/v1/dynamic/query/unread', params).then((res) => {
                if (res.code === 0) {
                    const str = 'grandNum:' + res.result
                    resolve(str)
                }
            })
        })
    }
    /**
     * 获取 其他未读信息
     */
const getOhtersUnreadNum = function getOhtersUnreadNum() {
    this.getUnreadGardentNum()
    return new Promise((resolve, reject) => {
        return Promise.all([grandNum]).then((res) => {
            resolve(res)
        }).catch(error => {
            reject(error)
        })
    })
}

/**
 * 获取中央用户信息
 */
const getCodeUserInfo = function getCodeUserInfo() {
    return request.get(env.ctx + 'auth/api/v1/sysuser/query/one')
}

export default {
    changePwd,
    getToken,
    login,
    codeLogin,
    getUserInfo,
    getPermission,
    changePhone,
    changePWD,
    resetPwd,
    bindWechat,
    getWechatInfo,
    getUnreadPlateNum,
    getUnreadGardentNum,
    getOhtersUnreadNum,
    getCodeUserInfo,
    /**
     * 获取当前用户下该系统所有档案租户
     */
    getTenantRecord(param) {
        const url = env.ctx + 'auth/api/v1/centeruser/get/tenant/record'
        return request.get(url, param)
    },
    getBusinessIdentity(param) {
        const url = env.ctx + 'auth/api/v1/get/business/identity'
        return request.get(url, param)
    },
    /**
     * 手机专属登陆接口
     * @param param
     * @param resolve
     * @param reject
     */
    phoneLogin(param) {
        const url = env.ctx + 'auth/api/v1/app/phone/login'
        return request.postForm(url, param)
    },

    /**
     * 注册接口
     * @param param
     * @returns {Promise<unknown>}
     */
    register(param) {
      const url = env.ctx + 'auth/api/v1/user/code/register'
      return request.postJson(url, param)
    },
    /**
     * 退出登录
     * @param param
     * @returns {Promise<unknown>}
     */
    loginOut(param) {
        const url = env.ctx + 'auth/api/v1/logout'
        return request.postForm(url, param)
    },
    queryOne(param) {
        const url = env.ctx + '/auth/api/v1/centeruser/query/one'
        return request.get(url, param);
    },
    /**
     * 绑定微信唯一用户
     * @param param
     * @returns {Promise<unknown>}
     * */
    bindWeixinAccount (param) {
      const url = env.ctx + 'auth/api/v1/usertenantrecord/bind/weixin/account'
      return request.postForm(url, param)
    },
    /**
     * 获取小程序用户静默授权信息
     * @param param
     * @returns {Promise<unknown>}
     * */
    getMiniCode2Session (param) {
      const url = env.ctx + 'manage/api/mini/code2Session'
      return request.postForm(url, param)
    },
    /**
     * 小程序用户绑定唯一用户
     * @param param
     * @returns {Promise<unknown>}
     * */
    getWxUserBindAccount (param) {
      const url = env.ctx + 'manage/api/wx/wxUserBindAccount'
      return request.postJson(url, param)
    },
    /**
     * accountId临时登录
     */
    accountLogin (param) {
      const url = env.ctx + 'auth/api/v1/account/login'
      return request.postForm(url, param)
    },
    // 支付宝小程序根据授权码获取userId
    alipayGetUserid(param) {
      const url = env.ctx + 'manage/api/v1/alipay/get/userid'
      return request.get(url, param)
    },
    // 根据accountId获取绑定的中央用户档案
    getFansBindRecord(param) {
      const url = env.ctx + 'auth/api/v1/usertenantrecord/get/fans/bind/record'
      return request.get(url, param)
    }
}
