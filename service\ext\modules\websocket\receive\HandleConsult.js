import MessageDefault from "./MessageDefault"
import $api from '@/service/api'
import $env from '@/config/env'
import validate from '@/common/util/validate'

const defaultAvatar = $env.file_ctx + 'static/image/system/avatar/icon-default-avatar.png' // 默认头像

// 咨询问题登记成功
export default class HandleConsult extends MessageDefault {
    constructor (websocket) {
        super()
        this.websocket = websocket
    }

    match (dataJson) {
        const { CONSULT_CMD } = this.chatCmd
        return dataJson.cmd === CONSULT_CMD
    }

    async processMessage (dataJson) {
        // 用户更新引导语 是否进行下一步引导
        const { ORDER_GUIDE } = this.chatCmd
        const chatItem = this.$common.getKeyVal('chat', 'chatItem', false)
        this.chatItem = chatItem

        // 首次进入页面订单详情未拉取
        let res = null
        // 获取订单详情
        if (dataJson) {
            res = dataJson
        } else {
            res = await $api.chat.orderGetById({ id: this.chatItem.orderId })
            setTimeout(() => {
                uni.pageScrollTo({
                    scrollTop: 99999,
                    duration: 0, //小程序如果有滚动效果 input的焦点也会随着页面滚动...
                })
            }, 0)
        }
        
        // 订单是否结束（问诊前/问诊后）
        let nodereplyconfig = this.$common.getKeyVal('chat', 'nodereplyconfig')
        if (res.data.consultStatus == 3) {
            // 问诊后
            nodereplyconfig = nodereplyconfig.filter(item => item.nodeType === 2)
        } else {
            nodereplyconfig = nodereplyconfig.filter(item => item.nodeType === 1)
        }
        const oldOrderDetail = this.$common.getKeyVal('chat', 'orderDetail')
        this.$common.setKeyVal('chat', 'orderDetail', res.data)
        // 是否是cmd触发该事件（用户更新订单） 是否为结束后引导（不触发）
        if (!this.$validate.isNull(dataJson) && res.data.consultStatus != 3) {
            const fieldList = this.getNodeConfigFillInField().filter(item => ['gfDepartmentId', 'gfIssue', 'gfPatientId', 'gfConsultType'].includes(item))
            // 是否有未填写项
            const isCompleted = fieldList.some(item => !res.data[item])
            if (!isCompleted) {
                // 是否为订阅消息触发
                if (oldOrderDetail.subscribeStatus === 2 && res.data.subscribeStatus === 1) {
                    
                } else {
                    this.sendMsg()
                    this.$common.setKeyVal('chat', 'guideMode', '', false)
                }
            }
        }
        
        // 是否已完结
        if (this.consultNodeIsEnd()) {
            this.$common.setKeyVal('chat', 'guideMode', '', false)
            return
        }
        let currentNodeConfig = {}
        // 首次进入
        if (this.$validate.isNull(res.data.nodeConfigReply)) {
            currentNodeConfig = {}
        } else {
            const nodeConfigReply = this.$validate.isNull(res.data.nodeConfigReply) ? [] : JSON.parse(res.data.nodeConfigReply)
            const currentNode = nodeConfigReply[nodeConfigReply.length - 1]
            currentNodeConfig = nodereplyconfig.find(item => item.id === currentNode.nodeConfigId)
        }
        // 执行下一步
        const nextNodeConfig = this.getNextConsultNodeConfig()
        console.log('nextNodeConfig -----------------',nextNodeConfig)
        switch (nextNodeConfig.pushType) {
            // 选择科室
            case 3:
                this.$common.setKeyVal('chat', 'guideMode', 'department', false)
                break
            // 咨询问题
            case 4:
                this.$common.setKeyVal('chat', 'guideMode', 'issue', false)
                break
            // 咨询人档案
            case 5:
                this.$common.setKeyVal('chat', 'guideMode', 'patient', false)
                break
            // 订阅消息
            case 6:
                let dto =  {
                    cmd: ORDER_GUIDE,
                    data: {
                        orderId: chatItem.orderId,
                        userId: chatItem.userId,
                        chatUserId: chatItem.chatUserId,
                        nodeConfigId: nextNodeConfig.id
                    }
                }
                this.websocket.webSocketSend(ORDER_GUIDE, dto)
                break
            // 咨询类型
            case 7:
                this.$common.setKeyVal('chat', 'guideMode', 'consultType', false)
                break
            // 用户评价
            case 8:
                break
            default:
                let chatDto =  {
                    cmd: ORDER_GUIDE,
                    data: {
                        orderId: chatItem.orderId,
                        userId: chatItem.userId,
                        chatUserId: chatItem.chatUserId,
                        nodeConfigId: nextNodeConfig.id
                    }
                }
                this.websocket.webSocketSend(ORDER_GUIDE, chatDto)
                break
        }

        return
    }

    sendMsg () {
        const messageList = this.$common.getKeyVal('chat', 'messageList', false)
        const { SINGLE_CHAT_CMD, touchType: { defaultChat } } = this.chatCmd
        const orderDetail = this.$common.getKeyVal('chat', 'orderDetail', false)
        const { gfAttachs, gfDepartmentName, gfIssue, patientInfoVo, gfConsultTypeName } = orderDetail
        const desc = {
            patientInfoVo,
            gfIssue,
            gfDepartmentName,
            gfAttachs,
            gfConsultTypeName
        }
        let time = new Date().getTime()
        const dto = {
            cmd: SINGLE_CHAT_CMD,
            data: {
                msgType: 8,
                msgContent: '',
                content: '',
                orderId: this.chatItem.orderId,
                seatUserId: "",
                touchType: defaultChat,
                createTime: time,
                attach: gfAttachs, // 咨询问题图片
                text: '咨询资料', // 卡片标题
                desc: JSON.stringify(desc)
            }
        }
        this.websocket.webSocketSend(SINGLE_CHAT_CMD, dto)
        const listItem = {
            hasBeenSentId: time + '-' + this.chatItem.userId, //已发送过去消息的id
            content: JSON.stringify(orderDetail),
            fromUserHeadImg: defaultAvatar, //用户头像
            fromUserId: this.chatItem.userId,
            isItMe: true,
            createTime: time,
            contentType: 8, // 1文字文本 2语音
            msgType: 8,
            msgCloudStatus: 2, //消息发送结果状态，1是正常，2是发送中，待回调，3是等待回调失败
            attach: gfAttachs, // 咨询问题图片
            text: '咨询资料', // 卡片标题
            desc: desc
        }

        // this.messageList.push(listItem);
        this.$common.setKeyVal('chat', 'messageList', [...messageList, listItem])
    }
    /**
     * 根据订单判断问诊前所有咨询节点是否完结
     * @return {Boolean} 是否已完结
     */
    consultNodeIsEnd () {
        const orderDetail = this.$common.getKeyVal('chat', 'orderDetail')
        const nodeConfigReply = this.$validate.isNull(orderDetail.nodeConfigReply) ? [] : JSON.parse(orderDetail.nodeConfigReply)
        const nodereplyconfig = this.$common.getKeyVal('chat', 'nodereplyconfig').filter(item => item.nodeType === 1)
        // 是否已经完成所有问诊前咨询节点
        const lastNodeConfig = nodereplyconfig.find(item => {
            return (item.nodeType === 1 && item.finishEnabled === 1)
        })
        for (let i = 0; i < nodeConfigReply.length; i++) {
            if (nodeConfigReply[i].nodeConfigId === lastNodeConfig.id) return true
        }
        return false
    }

    /**
     * 获取下一咨询节点配置
     * @return {object} 下一咨询节点配置
     */
    getNextConsultNodeConfig () {
        const orderDetail = this.$common.getKeyVal('chat', 'orderDetail')
        let nodeConfigReply = this.$validate.isNull(orderDetail.nodeConfigReply) ? [] : JSON.parse(orderDetail.nodeConfigReply)
        let nodereplyconfig = this.$common.getKeyVal('chat', 'nodereplyconfig')
        console.log(nodereplyconfig)
        if (orderDetail.consultStatus == 3) {
            nodereplyconfig = nodereplyconfig.filter(item => item.nodeType === 2)
        } else {
            nodereplyconfig = nodereplyconfig.filter(item => item.nodeType === 1)
        }
        console.log('nodereplyconfig---------------', nodereplyconfig)
        // 暂未开始资讯
        if (this.$validate.isNull(nodeConfigReply)) {
            return nodereplyconfig[0]
        }
        nodeConfigReply = nodeConfigReply.map(item => {
            return {
                ...item,
                weight: nodereplyconfig.find(i => i.id === item.nodeConfigId).weight
            }
        })
        nodeConfigReply = nodeConfigReply.sort((a,b) => a.weight - b.weight)
        const nodeConfigId = nodeConfigReply[nodeConfigReply.length - 1].nodeConfigId
        // 最近的咨询节点
        for (let i = 0; i < nodereplyconfig.length; i++) {
            if (nodereplyconfig[i].id === nodeConfigId) {
                return nodereplyconfig[i+1]
            }
        }
    }

    /**
     * 根据资讯节点配置获取需要用户填写字段
     * @return {Array<string>} 用户填写字段
     */
    getNodeConfigFillInField () {
        let fieldList = []
        const nodereplyconfig = this.$common.getKeyVal('chat', 'nodereplyconfig')
        nodereplyconfig.forEach(item => {
            // 问诊前
            if (item.nodeType === 1) {
                switch (item.pushType) {
                    // 选择科室
                    case 3:
                        fieldList.push('gfDepartmentId')
                        break
                    // 资讯问题
                    case 4:
                        fieldList.push('gfIssue')
                        fieldList.push('gfAttachs')
                        break
                    // 咨询人档案
                    case 5:
                        fieldList.push('gfPatientId')
                        break
                    // 订阅消息
                    case 6:
                        fieldList.push('subscribeStatus')
                        fieldList.push('initiatorUserAppOpenId')
                        break
                    // 资讯类型
                    case 7:
                        fieldList.push('gfConsultType')
                        break
                    default:
                }
            }
        })
        return fieldList
    }
}