<template>
  <page>
    <view slot="content" class="body-main">
      <view class="main">
        <scroll-refresh
          style="height: 100%"
          :isShowEmptySwitch="true"
          :fixed="false"
          :isAbsolute="false"
          :up="upOption"
          :down="downOption"
          @returnFn="returnFn"
          @scrollInit="scrollInit"
          @scroll="scroll"
        >
          <view class="scroll-view-main">
            <view class="m-main">
              <view class="my-data">
                <view
                  class="my-bg-img"
                  :style="{
                    backgroundImage:
                      'url(' +
                      file_ctx +
                      'static/image/business/hulu-v2/icon-personal-home-bg.png)',
                  }"
                ></view>
                <uni-nav-bar
                  @clickLeft="back"
                  color="#1d2029"
                  :border="false"
                  left-icon="left"
                  fixed
                  :backgroundColor="scrollTop > 50 ? '#fff' : 'rgba(0,0,0,0)'"
                  statusBar
                >
                <view v-if="scrollTop > 50" class="header-search-box header-title">
                  个人主页
                </view>
                <view slot="right" @tap.stop="searcFn" class="header-search-box-right">
                  <image mode="aspectFit" :src="file_ctx + 'static/image/business/hulu-v2/icon-personal-home-search.png'" class="header-search-img"/>
                </view>
                </uni-nav-bar>
                <view class="my-user" id="user-info-id">
                  <view class="user-info-top">
                    <view
                      class="img-main"
                      :style="
                        infoObj.lightLogoPath ? 'margin-right:32rpx;' : ''
                      "
                    >
                      <view class="user-head-pic">
                        <default-img
                          style="
                            width: 160rpx;
                            height: 160rpx;
                            display: inline-block;
                          "
                          :config="config.avatar"
                          :cData="
                            infoObj.headPath ? infoObj.headPath : defaultAvatar
                          "
                          :cName="infoObj ? infoObj.nickName : ''"
                          class="role-image"
                        />
                        <!-- <view class="user-sex">
                                  <image class="width-height-atuo" :src="codeUserInfo.sex == $constant.noun.gender1428 ? $static_ctx + 'image/business/icon-boy.png' : $static_ctx + 'image/business/icon-girl.png'"/>
                                </view> -->
                        <template v-if="infoObj.isAddV == 1">
                          <image
                            v-if="infoObj.vType == 2"
                            class="head-v-icon"
                            :src="
                              file_ctx +
                              'static/image/system/avatar/icon-v-e.png'
                            "
                            mode="aspectFill"
                          ></image>
                          <image
                            v-else-if="infoObj.vType == 1"
                            class="head-v-icon"
                            :src="
                              file_ctx + 'static/image/system/avatar/icon-v.png'
                            "
                            mode="aspectFill"
                          ></image>
                        </template>
                      </view>
                      <!-- 头饰挂件 -->
                      <image
                        v-if="infoObj.lightLogoPath"
                        class="headimg-wrapper"
                        :src="file_ctx + '0/msg-reply/808801329699454978.png'"
                        mode="aspectFill"
                      ></image>
                    </view>
                    <view class="user-data">
                      <view class="user-data-item">
                        <text class="user-data-num">{{
                          likeNumObj.beLikeNumberText
                        }}</text>
                        <text class="user-data-desc">获赞</text>
                      </view>
                      <view class="user-data-item">
                        <text class="user-data-num">{{
                          likeNumObj.beCollectNumberText
                        }}</text>
                        <text class="user-data-desc">收藏</text>
                      </view>
                    </view>
                  </view>
                  <view class="user-info-bottom">
                    <view class="name">{{
                      infoObj.nickName || "暂无名称"
                    }}</view>
                    <!-- <text class="desc">这个人还没有填写个人简介...</text> -->
                  </view>
                </view>
              </view>
            </view>

            <view class="main-list">
              <view class="list-title">Ta的动态</view>
              <view class="list-content">
                <nui-list
                  class="nui-list"
                  :indexlist="indexlist"
                  @cateClick="cateClick"
                  :posts-params="postsParams"
                  :isShowBtn="isShowBtn"
                ></nui-list>
              </view>
            </view>
          </view>
        </scroll-refresh>
      </view>
    </view>
  </page>
</template>

<script>

import defaultImg from '@/components/basics/default-avatar/index'
import StatusBarHeight from '@/components/business/module/status-bar-height/index'
import uniNavBar from '@/components/uni/uni-nav-bar/uni-nav-bar.vue'
import nuiList from '@/components/community/nui-list/nui-list.vue'
import uniIcons from '@/components/uni/uni-icons/uni-icons.vue'
import search from '@/components/basics/form/search'
export default {
  components: {
    StatusBarHeight,
    defaultImg,
    uniNavBar,
    nuiList,
    uniIcons,
    search
  },
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $accurateConversion: this.$accurateConversion,
      file_ctx: this.file_ctx,
      $timePlugin: this.$timePlugin,
      $static_ctx: this.$static_ctx,
      accountId: '',
      infoObj: {},
      config: {
        avatar: {
          widthHeightAuto: true,
          itemClass: {
            width: '158rpx',
            height: '158rpx',
            display: 'inline-block',
          }
        }
      },
      mescroll: null, // mescroll实例对象
      downOption: {
        auto: false // 不自动加载
      },
      upOption: {
        auto: false, // 不自动加载
        empty: {
          top: 0,
          zIndex: 999,
        }
      },
      isInit: false, // 列表是否已经初始化
      indexlist: [],
      defaultAvatar: this.$static_ctx + 'image/system/avatar/icon-default-avatar.png',
      scrollTop: 0,
      isShowBtn:true,
      likeNumObj:{},//点赞和收藏数据
    }
  },
  computed: {
    postsParams() {
      return {
        mode: 'personal',
        accountId: this.accountId
      }
    }
  },
  onLoad() {
    const that = this
    const query = this.$Route.query
    // console.log(this.$Route.query,'this.$Route.query')
    this.accountId = query.accountId
    if(query?.isShowBtn){
      this.isShowBtn = JSON.parse(query.isShowBtn)
      // console.log(this.isShowBtn,'this.isShowBtn0000')
    }
    this.getFansrecordGetFansrecordByAccountid()
    this.getLikeNumAndCollectNum()
    this.$nextTick(() => {
      that.init()
    })
  },
  methods: {
    scroll(e) {
      this.scrollTop = e.detail.scrollTop
    },
    searcFn() {
      this.$navto.push('CommonSystemSearch', { accountId: this.accountId })
    },
    cateClick(data) {
      this.navtoGo('Circle', { cid: data.circleClassifyId })
    },
    navtoGo(url, obj = {}) {
      this.$navto.push(url, obj)
    },
    back() {
      this.$navto.back(1)
    },
    async getFansrecordGetFansrecordByAccountid() {
      const param = {
        accountId: this.accountId
      }
      const res = await this.$api.community.fansrecordGetFansrecordByAccountid(param)
      
      this.infoObj = {
        ...res.data,
        beLikeNumberText: this.$common.bigNumberTransform(res.data.beLikeNumber || 0),
        beCollectNumberText: this.$common.bigNumberTransform(res.data.beCollectNumber || 0)
      }
    },
    async getLikeNumAndCollectNum(){
      const param = {
        accountId: this.accountId
      }
      const res = await this.$api.community.postmessageStatisticsAccountId(param)
      this.likeNumObj = {
        ...res.data,
        beLikeNumberText: this.$common.bigNumberTransform(res.data.likeNum || 0),
        beCollectNumberText: this.$common.bigNumberTransform(res.data.collectNum || 0)
      }
    },
    init() {
      this.$nextTick(() => {
        this.mescroll.triggerDownScroll()
      })
    },
    scrollInit(scroll) {
      scroll.optUp.page.num = 1
      scroll.optUp.page.size = 10
      this.mescroll = scroll
    },
    returnFn(obj) {
      const that = this
      setTimeout(function () {
        let params = {
          current: obj.pageNum,
          size: obj.pageSize,
          condition: {
            accountId: that.accountId,
            orderByPutawayTime: 1
          }
        }
        that.$ext.community.postmessageQueryAppPage(params).then(res => {
          let data = res.data.records || []
          // data = data.map(item => {
          //     return {
          //       ...item,
          //     }
          // })
          if (obj.pageNum === 1) {
            that.indexlist = []
          }
          that.indexlist = [...that.indexlist, ...data]
          obj.successCallback && obj.successCallback(data)
        })
      }, that.$constant.noun.scrollRefreshTime)

    },
  }
}
</script>

<style lang="scss" scoped>
.main-list {
  padding: 64rpx 40rpx 0;
  background: #fff;
}
.list-content {
  overflow: hidden;
  ::v-deep.nui-list{
    .lists{
      .list-wrapper{
        .list{
          padding: 24rpx 0 0;
        }
      }
    }
  }
}
.list-title {
  font-weight: 500;
  font-size: 36rpx;
  color: #00B484;
  line-height: 50rpx;
  margin-bottom: 4rpx;
}
.body-main {
  height: 100%;
  background: #f4f5f8;
  .main {
    height: 100%;
    .scroll-view-main {
      height: 100%;
    }
  }
}
.my-bg-img {
  position: absolute;
  height: 372rpx;
  width: 100%;
  left: 0;
  top: 0;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}
.m-main {
  .my-data {
    position: relative;
    .my-user {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      position: relative;
      padding: 36rpx 48rpx 0;
      line-height: 0;
      box-sizing: border-box;
      background-color: #fff;
      margin-top: 180rpx;
      @include rounded(24rpx 24rpx 0 0);
      overflow: visible;
      .user-info-top {
        display: flex;
        align-items: center;
        justify-content: space-between;
        line-height: 0;
        .img-main {
          position: absolute;
          display: inline-block;
          left: 48rpx;
          top: -70rpx;
          border-radius: 50%;
          border: 8rpx solid #fff;
          z-index: 1;
          .role-image {
            width: 160rpx;
            height: 160rpx;
            display: inline-block;
          }
          .user-sex {
            position: absolute;
            right: 4rpx;
            bottom: 4rpx;
            width: 30rpx;
            height: 30rpx;
            z-index: 1;
            @include rounded(50%);
            overflow: hidden;
          }
        }
        .role {
          color: #da4d01;
          background: #ffc746;
          @include rounded(30rpx);
        }
        .user-data {
          width: 100%;
          display: flex;
          justify-content: flex-end;
          &-item {
            display: flex;
            align-items: center;
            position: relative;
            & + .user-data-item {
              margin-left: 32rpx;
            }
          }
          &-num {
            font-weight: 500;
            font-size: 32rpx;
            color: #081849;
            line-height: 44rpx;
            padding-right: 4rpx;
          }
          &-desc {
            font-size: 24rpx;
            color: #868c9c;
            line-height: 34rpx;
          }
        }
      }

      .user-info-bottom {
        display: flex;
        flex-direction: column;
        padding-top: 42rpx;
        .name {
          font-weight: 500;
          font-size: 40rpx;
          color: #1D2029;
          line-height: 56rpx;
          padding-bottom: 12rpx;
        }
        .desc {
          font-size: 28rpx;
          color: #4E5569;
          line-height: 40rpx;
        }
      }
    }
  }
}
.width-height-atuo {
  width: 100%;
  height: 100%;
}
.m-t-10 {
  margin-top: 10rpx;
}
.user-head-pic {
  position: relative;
  .head-v-icon {
    position: absolute;
    width: 42rpx;
    height: 42rpx;
    right: 0;
    bottom: 0;
  }
}
.headimg-wrapper {
  position: absolute;
  height: 74px;
  width: 74px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.header-search-box {
  width: 100%;
  display: flex;
  align-items: center;
}
.header-search-box-right {
  // #ifdef MP-WEIXIN
  justify-content: flex-end;
  padding-right: 95px;
  // #endif
}
.header-title {
  font-weight: 500;
  font-size: 36rpx;
  color: #1D2029;
  line-height: 50rpx;
}
.header-search-img {
  width: 32px;
  height: 32px;
}
::v-deep .uni-navbar__header-btns-left {
  width: 48rpx !important;
}
::v-deep .uni-navbar__header-container {
  padding-left: 24rpx;
}
::v-deep .uni-navbar__header-btns-right {
  width: 200px !important;
}
</style>
