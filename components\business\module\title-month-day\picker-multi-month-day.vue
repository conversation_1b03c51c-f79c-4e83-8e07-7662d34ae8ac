<!--
*
*config  [Object] 参数设置
bdt: true,              上划线border-top
bdb: true,              下划线border-bottom
label: '下拉框',         左边name值
name: 'select',         字段名
placeholder: '请选择',   提示
required: false,        是否必填
array: [],              展示数组
dicKey: '',             查询字典字段key
disabled: false         是否禁用
-------------------------------------------

*returnFn    [function]     回调函数
*cData        [String]       (默认选中)key传入方式
*getData     [String]      做数据处理首次默认值watch执行监听
*
*字典选择器(已经放到全局使用) <dictionary-selector  :config="xxx" :cData="xxx"  @returnFn = "xxx"></dictionary-selector>
 -->
<template>
  <picker mode="multiSelector" :disabled="disabled" @change="returnFn" :value="form.data.select" :range="array">
    <text :class="{'color-999': disabled}">
      {{defaultConfig.placeholder}}
    </text>
  </picker>
</template>

<script>

  export default {
    data() {
      return {
        form: {
          data: {
            select: []
          }
        },
        array: [],
        defaultConfig: {
          placeholder: '请选择'
        }
      }
    },
    watch: {
      cData: {
        handler(val) {
          this.watchDataMain(val)
        },
        deep: true
      },
      config: {
        handler(val) {
          this.copyConfig()
        },
        deep: true
      }
    },
    props: {
      // 是否禁用disable
      disabled: {
        type: Boolean,
        default() {
          return false
        }
      },
      // 初始值传值，用于回显
      cData: {
        type: String,
        required: false,
        default() {
          return ''
        }
      },
      // 参数设置
      config: {
        type: Object,
        required: false,
        default: () => {
          return {}
        }
      }
    },
    computed: {

    },
    created() {
      const hourArr = []
      const minuteArr = []
      for(let i = 0; i < 24; i++) {
        const val = i < 10 ? '0' + i : i + ''
        hourArr.push(val)
      }
      for(let i = 0; i < 60; i++) {
        const val = i < 10 ? '0' + i : i + ''
        minuteArr.push(val)
      }
      this.array = [hourArr, minuteArr]
    },
    mounted() {
      this.copyConfig()
      if (!this.$validate.isNull(this.cData)) {
        this.watchDataMain(this.cData)
      }
    },
    methods: {
      /**
       * 初始化拷贝config对象
       */
      copyConfig() {
        const that = this
        const obj = that.config
        Object.keys(obj).forEach(function(key) {
          that.defaultConfig[key] = obj[key]
        })
        that.defaultConfig = Object.assign({}, that.defaultConfig)
        if (!that.$validate.isNull(that.cData)) {
          that.watchDataMain(that.cData)
        }
      },
      /**
       * 监听Data主逻辑方法
       */
      watchDataMain(val) {
        const str = val
        let strArr = []
        if (!this.$validate.isNull(str)) {
          strArr = str.split(":")
          let indexOne = 0
          let indexTwo = 0
          for(let i = 0; i < this.array[0].length; i++) {
            if (this.array[0][i] === strArr[0]) {
              indexOne = i
              break;
            }
          }
          for(let i = 0; i < this.array[1].length; i++) {
            if (this.array[1][i] === strArr[1]) {
              indexTwo = i
              break;
            }
          }
          this.form.data.select = [indexOne, indexTwo]
          this.defaultConfig.placeholder = str
        }
      },
      /**
       * picker触发选中事件
       * @param v
       */
      returnFn(v) {
        const eq = v.detail.value
        const value = this.array[0][eq[0]] + ':' + this.array[1][eq[1]]
        this.form.data.select = eq
        this.defaultConfig.placeholder = value
        this.$emit('returnFn', value)
      }
    }
  }
</script>

<style lang="scss" scoped>
  .color-999{
    color: #999999!important;
  }
</style>
