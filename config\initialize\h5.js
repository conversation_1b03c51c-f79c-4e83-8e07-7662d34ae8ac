/**
 * H5-初始化配置
 */
import Vue from 'vue'

import Vconsole from 'vconsole'
import env from '@/config/env'
import common from '@/common/util/main'
import validate from '@/common/util/validate'

/**
 * 注册打印日志面板
 */
if (env.isDebug) {
  const vConsole = new Vconsole()
  Vue.use(vConsole)
}

/**
 * 获取URL地址的参数值
 * @type {*|{}}
 */
let urlParam = common.getKeyVal('user', 'firstUrlParam', true) || {}
if (validate.isNull(urlParam)) {
  common.cutOutUrl()
  urlParam = common.getKeyVal('user', 'firstUrlParam', true) || {}
}
window.onload = function() {
  // 判断是否是微信坏境
  if (Vue.prototype.$ext.wechat.isWechatEnv()) {
    // 微信注入权限验证配置
    Vue.prototype.$ext.wechat.handlerJSsdkConfig().then((res) => {
      // console.log("handlerJSsdkConfig:初始化微信JSSDK成功")
    }).catch(error => {
      // console.log("handlerJSsdkConfig:初始化微信JSSDK失败")
    })
  }
}
