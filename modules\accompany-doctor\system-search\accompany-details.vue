<template>
  <view class="page">
    <!-- S 背景板 -->
    <view class="tabHeader">
      <image class="tabHeader1" :src="tabHeader1" mode=""></image>
      <image class="tabHeader2" :src="tabHeader2" mode=""></image>
    </view>
    <!-- E 背景板 -->

    <!-- S 头部 -->
    <view :style="'height:' + statusBarHeight + 'px;'"></view>
    <view class="top-nav">
      <view class="top-nav-l" v-if="!showHome" @tap.stop="back"><image mode="aspectFit" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-service-left-arrows.png'" class="header-search-img"/></view>
        <view class="top-nav-l indexPath" v-if="showHome" @tap.stop="back"><image mode="aspectFit" :src="indexPath" class="header-search-img"/></view>
      <view class="top-nav-c">{{ employeeTitle }}</view>
      <view class="top-nav-r">
        <button class="share-btn" open-type="share"></button><image :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-teacher-share.png'" class="share-img"></image>
      </view>
    </view>
    <!-- E 头部 -->

    <!-- S 内容区域 -->
    <view class="content" v-if="userDetails.id">
      <!-- 陪诊师id卡片 -->
      <view class="idCard">
        <view class="idCardHeader">
          <view class="idCardHeader-l">
            <view class="item-r-head">
              <view class="name">{{ userDetails.username }}</view>
              <view class="star" v-if="userDetails.star">
                <image class="starIcon" :src="star" mode=""></image>
                {{ userDetails.star }}
              </view>
            </view>
            <view class="college" v-if="userDetails.college">
              毕业院校:{{userDetails.college}}
            </view>
            <view class="languageMap" v-if="userDetails.language && showUserDetails">
              <view class="languageItem" v-for="(item,index) in sortLanguages(userDetails.language.filter(item => item))" :key="index">{{item}}</view>
            </view>
          </view>
          <image class="idCardHeader-r" mode="aspectFit" :src="file_ctx + userDetails.avatar"></image>
          <!-- <image class="idCardHeader-r" v-if="userDetails.sex == 0" :src="file_ctx + 'static/image/business/hulu-v2/icon-avatar-default-grild.png'" mode=""></image>
          <image class="idCardHeader-r" v-if="userDetails.sex == 1" :src="file_ctx + 'static/image/business/hulu-v2/icon-avatar-default-man.png'" mode=""></image> -->
        </view>
        <view class="line"></view>
        <view class="experience" v-if="userDetails.experience">
          <view class="experienceTitle">经验描述</view>
          <moreLinesDivide :line="3" :dt="userDetails.experience"></moreLinesDivide>
        </view>
      </view>
      <!-- 资质证书 -->
      <view class="cardBox new-cardBox" v-if="userDetails.certificate.length">
        <view class="title">资质证书</view>
        <view class="pzsCertificate">
          <view class="pzsCertificateItem"  v-for="(item,index) in userDetails.certificate" :key="index">
            <view class="pzsCertificateIcon"><image class="img" :src="getPzsCertificate(item,'url')"></image></view>
            <view class="text">{{getPzsCertificate(item,'name')}}</view>
          </view>
        </view>
      </view>
      <!-- 用户评价 -->
      <view class="cardBox" v-if="accompanybookList.length">
        <view class="title">用户评价</view>
        <view class="pzsCertificate-item border-none" v-for="(item,index) in accompanybookList.slice(0, 1)" :key="item.id">
          <view class="pzsCertificate-top">
            <view class="pzsCertificate-l">
              <image v-if="item.avatar" class="name-l-photo" :src="file_ctx + item.avatar"></image>
              <view class="name">匿名用户</view>
              <view class="my-evaluate" v-if="item.commentState == 2">
                <view class="evaluate-img1" v-if="item.star == 1"><image class="img" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-very-evaluate1.png'"></image></view>
                <view class="evaluate-img2" v-if="item.star == 2"><image class="img" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-very-evaluate2.png'"></image></view>
                <view class="evaluate-img3" v-if="item.star == 3"><image class="img" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-very-evaluate3.png'"></image></view>
                <view class="evaluate-img4" v-if="item.star == 4"><image class="img" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-very-evaluate4.png'"></image></view>
                <view class="evaluate-img5" v-if="item.star == 5"><image class="img" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-very-evaluate5.png'"></image></view>
              </view>
            </view>
            <view class="pzsCertificate-r">{{item.startTime}}</view>
          </view>
          <view class="pzsCertificate-bottom">
            <view class="evaluation-content">{{ item.comment }}</view>
          </view>
        </view>
        <view class="look-more" @click="handleOpenPopup(1)">查看更多<image class="head-r-img" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-service-right-arrows.png'"></image></view>
      </view>
      <view class="accompany-service">
        <view class="title">陪诊服务</view>
        <view class="accompany-service-item" v-for="item in serviceList" :key="item" @click="hanldeClickServiceJump(item.id)">
          <view class="project-item-l"><image class="img" :src="file_ctx + item.listImg"></image></view>
          <view class="project-item-r">
            <view class="project-item-r-title">{{ item.serviceName }}</view>
            <view class="project-item-r-info">{{ item.description }}</view>
            <view class="project-item-r-appointment">
              <view class="project-item-r-money">¥<span v-if="item.cityPrice">{{ item.cityPrice / 100 }}</span><span v-else>{{ item.price / 100 }}</span></view>
              <view class="project-item-r-btn" @click.prevent.stop="gotoOrder(item)">预约</view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <!-- E 内容区域 -->
    <uni-popup ref="guardDetailPopup" type="bottom">
      <scroll-view scroll-y="true" class="scroll-y" @scrolltolower="upper">
        <view class="guard-detail-content">
            <view class="title">用户评价</view>
            <view class="error" @tap="handleOpenPopup(2)"><image :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-service-error.png'"></image></view>
            <view class="pzsCertificate-item" v-for="(item,index) in accompanybookList" :key="item.id">
              <view class="pzsCertificate-top">
                <view class="pzsCertificate-l">
                  <image v-if="item.avatar" class="name-l-photo" @error="handleErrorImg(item.avatar)" :src="file_ctx + item.avatar"></image>
                  <view class="name-box">
                    <view class="name">匿名用户</view>
                    <view class="name-time">{{item.startTime}}</view>
                  </view>
                </view>
                <view class="pzsCertificate-r">
                  <view class="my-evaluate" v-if="item.commentState == 2">
                    <view class="evaluate-img1" v-if="item.star == 1"><image class="img" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-very-evaluate1.png'"></image></view>
                    <view class="evaluate-img2" v-if="item.star == 2"><image class="img" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-very-evaluate2.png'"></image></view>
                    <view class="evaluate-img3" v-if="item.star == 3"><image class="img" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-very-evaluate3.png'"></image></view>
                    <view class="evaluate-img4" v-if="item.star == 4"><image class="img" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-very-evaluate4.png'"></image></view>
                    <view class="evaluate-img5" v-if="item.star == 5"><image class="img" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-very-evaluate5.png'"></image></view>
                  </view>
                </view>
              </view>
              <view class="pzsCertificate-bottom">
                <view class="evaluation-content">{{ item.comment }}</view>
              </view>
            </view>
        </view>
      </scroll-view>
    </uni-popup>
  </view>
</template>

<script>
  import moreLinesDivide from '@/components/business/module/more-lines-divide/more-lines-divide'
  import uniPopup from '@/components/uni/uni-popup'
  import serverOptions from '@/config/env/options'

  export default{
    components:{
      moreLinesDivide,
      uniPopup,
    },
    data(){
      return {
        userDetails:{},
        statusBarHeight: 0,
        file_ctx: this.file_ctx,
        employeeTitle: '本地陪诊师', // 动态文案，默认值
        tabHeader1: this.file_ctx + 'static/image/business/hulu-v2/tabHeader1.png',
        tabHeader2: this.file_ctx + 'static/image/business/hulu-v2/tabHeader2.png',
        star: this.file_ctx + 'static/image/business/hulu-v2/star.png',
        indexPath: this.$static_ctx + "image/business/homeIcon.png",
        // pzsCertificate1: this.file_ctx + 'static/image/business/hulu-v2/pzsCertificate1.png',
        // pzsCertificate2: this.file_ctx + 'static/image/business/hulu-v2/pzsCertificate2.png',
        // pzsCertificate3: this.file_ctx + 'static/image/business/hulu-v2/pzsCertificate3.png',
        // pzsCertificate4: this.file_ctx + 'static/image/business/hulu-v2/pzsCertificate4.png',
        pzsCertificateMap:[
          {url:this.file_ctx + 'static/image/business/hulu-v2/pzsCertificate1.png',name:'医疗陪诊顾问',value:1},
          {url:this.file_ctx + 'static/image/business/hulu-v2/pzsCertificate2.png',name:'急救师证',value:2},
          {url:this.file_ctx + 'static/image/business/hulu-v2/pzsCertificate3.png',name:'健康管理顾问证',value:3},
          {url:this.file_ctx + 'static/image/business/hulu-v2/pzsCertificate4.png',name:'医师资格证',value:4},
          {url:this.file_ctx + 'static/image/business/hulu-v2/pzsCertificate5.png',name:'执业药师资格证',value:5},
          {url:this.file_ctx + 'static/image/business/hulu-v2/pzsCertificate6.png',name:'心肺复苏证书',value:6},
          {url:this.file_ctx + 'static/image/business/hulu-v2/pzsCertificate7.png',name:'育婴师证',value:7},
          {url:this.file_ctx + 'static/image/business/hulu-v2/pzsCertificate8.png',name:'月嫂证',value:8},
          {url:this.file_ctx + 'static/image/business/hulu-v2/pzsCertificate9.png',name:'养老护理员证',value:9},
          {url:this.file_ctx + 'static/image/business/hulu-v2/pzsCertificate10.png',name:'社工资格证',value:10},
          {url:this.file_ctx + 'static/image/business/hulu-v2/pzsCertificate11.png',name:'助理人力资源师',value:11},
          {url:this.file_ctx + 'static/image/business/hulu-v2/pzsCertificate12.png',name:'教师资格证',value:12},
          {url:this.file_ctx + 'static/image/business/hulu-v2/pzsCertificate13.png',name:'护士资格证',value:13},
          {url:this.file_ctx + 'static/image/business/hulu-v2/pzsCertificate14.png',name:'小儿推拿证',value:14},
          {url:this.file_ctx + 'static/image/business/hulu-v2/pzsCertificate15.png',name:'健康管理证',value:15},
          {url:this.file_ctx + 'static/image/business/hulu-v2/pzsCertificate16.png',name:'遗传咨询证',value:16},
          {url:this.file_ctx + 'static/image/business/hulu-v2/pzsCertificate17.png',name:'出生缺陷防控咨询证',value:17},
        ],
        serviceList:[],
        accompanybookList:[],
        current:1,
        loadEnd:false,
        imgShow:true,
      }
    },
    computed: {
      showUserDetails(){
        return this.userDetails.language.filter(item => item).length > 0
      },
      showHome(){
        // 检测当前页面栈是否还有上一页如果没有则返回首页
        const pages = getCurrentPages()
        return pages.length <= 1
      },
    },
    onLoad(res) {
      console.log('res',res);
      if(res.id){
        this.id = res.id;
        this.queryDetails(res.id)
        this.accompanybookQuery()
      }
      if(res.employeeTitle){
        this.employeeTitle = decodeURIComponent(res.employeeTitle)
      }
      this.accompanyserviceQueryCityPage()
    },
    // 分享到好友
    onShareAppMessage(res) {
      let path = `modules/accompany-doctor/system-search/accompany-details?id=${encodeURIComponent(this.id)}`;
      path += `&providerId=${serverOptions.providerId}`
      return {
        title: this.employeeTitle || '本地陪诊师',//分享的标题
        path //点击链接后跳转的页面，可以带参数
      }
    },
    mounted () {
      this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight // 获取设备的状态栏高度
    },
    methods:{
      sortLanguages(languages) {
        // 定义语言优先级顺序：1.英语 2.粤语 3.普通话
        const languagePriority = {
          '英语': 1,
          '粤语': 2,
          '普通话': 3
        };

        // 对语言数组进行排序
        return [...languages].sort((a, b) => {
          const priorityA = languagePriority[a] || 999;
          const priorityB = languagePriority[b] || 999;
          return priorityA - priorityB;
        });
      },
      handleErrorImg(img){
      },
      upper(){
        if(this.loadEnd) return
        this.current++;
        this.accompanybookQuery();
      },
      async accompanybookQuery(){
        let {data:{records,total}} = await this.$api.accompanyDoctor.accompanybookQuery({current:this.current,size:10,condition:{employeeId:this.id,starNum:3}})
        let list = records.map(item=>({...item,startTime:this.$common.formatDate(new Date(item.startTime), 'yyyy-MM-dd')}))
        this.accompanybookList.push(...list)
        if(total <= this.accompanybookList.length){
          this.loadEnd = true
        }
      },
      // 服务列表
      accompanyserviceQueryCityPage(){
        const providerId = this.$common.getKeyVal('user','providerId',true)
        const city = this.$common.getKeyVal('user','cityName',true)
        this.$api.accompanyDoctor.accompanyserviceQueryCityPage({current:1,size:4,condition:{providerId,state:1,city}}).then(res=>{
          this.serviceList = res.data.records
          console.log('serviceList',this.serviceList);

        })
      },
      getPzsCertificate(value,key){
        let index = this.pzsCertificateMap.findIndex(item=>item.value == value)
        return this.pzsCertificateMap[index]?.[key]
      },
      async queryDetails(id){
        let {data} = await this.$api.accompanyDoctor.getAccompanyemployeeQueryOne({id})
        this.userDetails = {...data,language:data.language.length && data.language.split(',') || [],certificate:data.certificate.length && data.certificate.split(',') || []};
        console.log('userDetails111111',this.userDetails);
      },
      handleOpenPopup(type){
        if(type == 1){
          this.$refs.guardDetailPopup.open()
        } else {
          this.$refs.guardDetailPopup.close()
        }
      },
      hanldeClickServiceJump(id){
        const city = this.$common.getKeyVal('user','cityName',true)
        this.$navto.push('ServiceDetail',{id,city})
      },
      back(){
        // 检测当前页面栈是否还有上一页如果没有则返回首页
        const pages = getCurrentPages()
        if (pages.length <= 1) {
          uni.switchTab({
            url: 'pages/accompany-home/index'
          })
        } else {
          this.$navto.back(1)
        }
      },
      gotoOrder(item){
        const city = this.$common.getKeyVal('user','cityName',true)
        this.$navto.push('ServiceDetail',{id:item.id,classifyId:item.classifyId,name:item.classifyName,city})
      },
    }
  }
</script>

<style lang="scss" scoped>
    .indexPath{
      display: flex;
      width: 56rpx;
      height: 56rpx;
      .header-search-img{
        width: 100% !important;
        height: 100% !important;
      }
    }
  @mixin contentFlex {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .img{
    width: 100%;
    height: 100%;
  }
  .languageMap{
    display: flex;
    margin-top: 22rpx;
    flex-wrap: wrap;
    .languageItem{
      height: 36rpx;
      padding: 2rpx 8rpx;
      margin-right: 8rpx;
      margin-bottom: 8rpx;
      background: #EBF7F5;
      border-radius: 4rpx;
      font-weight: 400;
      font-size: 22rpx;
      color: #007959;
    }
  }
  .page{
    width: 100vw;
    height: 100vh;
    background: #F4F6FA;
    z-index: 9999;
    position: relative;
    .content{
      width: 100vw;
      padding: 32rpx;
      box-sizing: border-box;
      height: calc(100vh - 174rpx);
      overflow: scroll;
      .idCard{
        width: 686rpx;
        background: #FFFFFF;
        border-radius: 16rpx;
        padding: 28rpx 32rpx;
        box-sizing: border-box;
        .idCardHeader{
          display: flex;
          position: relative;
          justify-content: space-between;
          .idCardHeader-l{
            margin-top: 12rpx;
            .item-r-head{
              display: flex;
              align-items: center;
              .name{
                font-weight: 500;
                font-size: 40rpx;
                color: #1D2029;
              }
              .star{
                margin-left: 16rpx;
                padding: 0 4rpx;
                height: 36rpx;
                background: #00B484;
                border-radius: 4rpx;
                display: flex;
                align-items: center;
                font-weight: 600;
                font-size: 22rpx;
                color: #FFFFFF;
                .starIcon{
                  width: 20rpx;
                  height: 20rpx;
                  margin-right: 2rpx;
                }
              }
            }
            .college{
              font-weight: 400;
              font-size: 24rpx;
              color: #4E5569;
              margin-top: 16rpx;
            }
          }
          .idCardHeader-r{
            width: 184rpx;
            height: 184rpx;
            border-radius: 93rpx;
          }
        }
        .line{
          width: 100%;
          height: 2rpx;
          margin: 44rpx 0 24rpx 0;
          background: #F2F3F7;
        }
        .experience{
          .experienceTitle{
            font-weight: 500;
            font-size: 26rpx;
            color: #1D2029;
            margin-bottom: 8rpx;
          }
        }
      }
      .cardBox,.new-cardBox{
        width: calc(100% - 52rpx);
        padding: 26rpx;
        background: #FFFFFF;
        border-radius: 16rpx;
        margin-top: 20rpx;
      }
      .new-cardBox{
        padding: 26rpx 0;
        width: 100%;
        background: #FFFFFF;
        border-radius: 16rpx;
        margin-top: 20rpx;
        .title{
          padding-left: 26rpx;
          font-weight: 600;
          font-size: 32rpx;
          color: #1D2029;
        }
      }
      .title{
        font-weight: 500;
        font-size: 32rpx;
        color: #1D2029;
      }
      .pzsCertificate{
        display: flex;
        flex-wrap: wrap;
        margin-top: 16rpx;
        gap: 16rpx;
        padding-left: 26rpx;
        .pzsCertificateItem{
          @include contentFlex;
          flex-direction: column;
          margin-top: 16px;
          .pzsCertificateIcon{
            display: flex;
            width: 114rpx;
            height: 96rpx;
          }
          .text{
            font-size: 24rpx;
            color: #1D2029;
            margin-top: 16rpx;
          }
        }
      }
      .accompany-service{
        padding:24rpx;
        background-color: #fff;
        border-radius: 16rpx;
        margin-top: 20rpx;
        .title{
          font-size: 32rpx;
          color: #1D2029;
          line-height: 44rpx;
        }
        .accompany-service-item{
          display: flex;
          padding: 24rpx 0;
          border-bottom: 1rpx solid #EAEBF0;
          .project-item-l{
            width: 144rpx;
            height: 144rpx;
            border-radius: 12rpx;
            margin-right: 20rpx;
            overflow: hidden;
          }
          .project-item-r{
            display: flex;
            flex: 1;
            flex-direction: column;
            .project-item-r-title{
              font-size: 30rpx;
              color: #1D2029;
              line-height: 42rpx;
            }
            .project-item-r-info{
              display: -webkit-box;
              width: 332rpx;
              font-size: 22rpx;
              color: #868C9C;
              line-height: 32rpx;
              margin: 4rpx 0 10rpx;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            .project-item-r-appointment{
              display: flex;
              justify-content: space-between;
              .project-item-r-money{
                color: #FF5500;
                span{
                  font-size: 36rpx;
                  line-height: 50rpx;
                }
              }
              .project-item-r-btn{
                display: flex;
                justify-content: center;
                align-items: center;
                width: 110rpx;
                height: 56rpx;
                background: #fff;
                border-radius: 28rpx;
                border: 1rpx solid #00B484;
                font-size: 26rpx;
                color: #00B484;
                line-height: 36rpx;
              }
            }
          }
          &:last-child{
            border-bottom: none;
          }
        }
      }
    }
  }
  .tabHeader{
    display: flex;
    flex-wrap: wrap;
    position: absolute;
    z-index: -1;
    .tabHeader1{
      width: 100vw;
      height: 176rpx;
    }
    .tabHeader2{
      width: 100vw;
      height: 208rpx;
    }
  }
  .top-nav{
    position: relative;
    width: calc(100% - 56rpx);
    @include contentFlex;
    height: 40px;
    line-height: 40px;
    padding: 0 32rpx 0 24rpx;
    .top-nav-l{
      @include contentFlex;
      position: absolute;
      height: 100%;
      left: 24rpx;
      top: 0;
      .header-search-img{
        display: flex;
        width: 48rpx;
        height: 48rpx;
      }
    }
    .top-nav-c{
      @include contentFlex;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      top: 0;
      height: 100%;
      font-weight: 600;
      font-size: 32rpx;
      color: #1D2029;
      margin-right: 48rpx;
    }
    .top-nav-r{
      @include contentFlex;
      position: absolute;
      right: 30%;
      top: 0;
      height: 100%;
      .share-img{
        display: flex;
        height: 64rpx;
        width: 64rpx;
      }
      .share-btn{
        position: absolute;
        opacity: 0;
        display: flex;
        height: 64rpx;
        width: 64rpx;
      }
    }
  }
  .scroll-y{
    max-height: 1416rpx;
    overflow: hidden;
  }
  .guard-detail-content{
    position: relative;
    background: #fff;
    padding: 32rpx 32rpx 386rpx;
    border-radius: 24rpx 24rpx 0rpx 0rpx;
    overflow-y: auto;
    .title{
      display: flex;
      justify-content: center;
      font-size: 32rpx;
      color: #1D2029;
      line-height: 44rpx;
    }
    .error{
      position: absolute;
      right: 32rpx;
      top: 38rpx;
      width: 32rpx;
      height: 32rpx;
      image{
        width: 100%;
        height: 100%;
      }
    }
  }
  .border-none{
    border-bottom: none !important;
  }
  .pzsCertificate-item{
    margin: 26rpx 0 12rpx;
    border-bottom: 2rpx solid #F2F3F7;
    .pzsCertificate-top{
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20rpx;
      .pzsCertificate-l{
        display: flex;
        .name-box{
          display: flex;
          flex-direction: column;
          margin-left: 4rpx;
          .name{
            font-size: 28rpx;
            color: #2D2F38;
            font-weight: 600;
          }
          .name-time{
            font-size: 24rpx;
            color: #868C9C;
          }
        }
        .name-l-photo{
          display: flex;
          width: 48rpx;
          height: 48rpx;
          border-radius: 50%;
          overflow: hidden;
        }
        .name{
          margin: 0 12rpx;
        }
      }
    }
    .pzsCertificate-bottom{
      .evaluation-content{
        font-size: 26rpx;
        color: #2D2F38;
        line-height: 40rpx;
        margin-bottom: 24rpx;
      }
    }
    &:last-child{
      border-bottom: none;
    }
  }
  .look-more{
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24rpx;
    color: #4E5569;
    .head-r-img{
      display: flex;
      width: 32rpx;
      height: 32rpx;
    }
  }
  .my-evaluate{
    display: flex;
    align-items: center;
    height: 48rpx;
    .evaluate-img1{
      width: 204rpx;
      height: 48rpx;
    }
    .evaluate-img2{
      width: 156rpx;
      height: 48rpx;
    }
    .evaluate-img3{
      width: 132rpx;
      height: 48rpx;
    }
    .evaluate-img4{
      width: 132rpx;
      height: 48rpx;
    }
    .evaluate-img5{
      width: 180rpx;
      height: 48rpx;
    }
  }
</style>
