
<template>
  <view class="title-input clear-float" :class="{'bdt':defaultConfig.bdt,'bdb':defaultConfig.bdb}">
    <view class="l-l" :style="{'color': defaultConfig.titleColor}" v-if="defaultConfig.showLabel">
      <text class="star" v-if="defaultConfig.required">*</text>
      {{defaultConfig.label}}
    </view>
    <view class="l-r">
      <input :disabled="disabled"
        v-model="form.data.val"
        :type="defaultConfig.type"
        :maxlength="defaultConfig.maxlength"
        @input="returnFn"
        :placeholder="placeholder ? placeholder : `请输入${defaultConfig.label}`"
        :style="defaultConfig.style"
        class="input"
      />
      <text class="l-r-input-text" v-if="maxlength != -1 && showWordLimit">{{ `${form.data.val.length}/${maxlength}` }}</text>
    </view>
  </view>
</template>

<script>

export default {
  data() {
    return {
      form: {
        data: {
          val: ''
        }
      },
      array: [],
      index: 0,
      defaultConfig: {
        bdt: true,
        bdb: true,
        titleColor: '#333',
        textColor: '#333',
        label: '单行输入框',
        name: 'input',
        required: false,
        type: 'text',
        showLabel: true,
        style: {}
      }
    }
  },
  watch: {
    cData: {
      handler(val) {
        this.watchDataMain(val)
      },
      deep: true
    },
    config: {
      handler(val) {
        this.copyConfig()
      },
      deep: true
    },
    value: {
      handler () {
        this.form.data.val = this.value
      },
      immediate: true
    },
    'form.data.val': {
      handler () {
        this.$emit('input', this.form.data.val)
      },
      deep: true
    }
  },
  props: {
    // 展示字数统计 只在maxlength不为-1时生效
    showWordLimit: {
      type: Boolean,
      default: false
    },
    // 限制最大值
    maxlength: {
      type: Number,
      default() {
        return -1
      }
    },
    value: [String,Number],
    placeholder: String,
    // 是否禁用disable
    disabled: {
      type: Boolean,
      default() {
        return false
      }
    },
    // 初始值传值，用于回显
    cData: {
      type: [String, Number],
      required: false,
      default() {
        return ''
      }
    },
    // 参数设置
    config: {
      type: Object,
      required: false,
      default: () => {
        return {}
      }
    }
  },
  computed: {

  },
  mounted() {
      this.copyConfig()
      if (!this.$validate.isNull(this.cData)) {
        this.watchDataMain(this.cData)
      }
  },
  methods: {
    /**
       * 初始化拷贝config对象
       */
    copyConfig() {
      const that = this
      const obj = that.config
      Object.keys(obj).forEach(function(key) {
        that.defaultConfig[key] = obj[key]
      })
    },
    /**
       * 监听Data主逻辑方法
       */
    watchDataMain(val) {
      this.form.data.val = val
    },
    /**
       * picker触发选中事件
       * @param e
       */
    returnFn(e) {
      const that = this
      if (that.disabled) return
      that.$emit('updateForm', { key: '' + that.config.name, value: that.form.data.val })
    }
  }
}
</script>

<style lang="scss" scoped>
  .bdb2{
    border-bottom: 2upx dotted #9e9e9e;
  }
  .color-topicC{
    color: $topicC !important;
  }
  .title-input{
    background-color:#ffffff;
    .l-l{
      line-height: 88upx;
      color: #333333;
      // font-weight: 600;
      font-size:30upx;
    }
    .l-r{
      margin-bottom: 5px;
      .input {
        height: 80upx;
        line-height: 80upx;
        color: #333;
        font-size: 32upx;
        text-align: left;
        display: inline-block;
        vertical-align: middle;
        width: calc(100% - 64upx);
        border: 2upx solid #ede9e9;
        border-radius: 10upx;
      }
      &-input-text {
        height: 80upx;
        line-height: 80upx;
        color: #888;
        font-size: 26rpx;
      }
    }
  }
  .star{
    color: #F85E4C;
    padding-top: 6upx;
    font-size: 32upx;
    display: inline-block;
  }
</style>
