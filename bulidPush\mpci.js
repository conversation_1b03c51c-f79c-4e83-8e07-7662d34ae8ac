/**
 * 小程序CI发布脚本 - 使用官方miniprogram-ci包
 * 用法: node mpci.js description=版本描述 idList=1,2,3 version=1.2.3
 */
import { exec } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import optionsMap from "../config/env/optionsMap.js";

// 获取当前模块的文件路径
const __filename = fileURLToPath(import.meta.url);
// 获取当前模块的目录名称
const __dirname = dirname(__filename);
// 定义项目名称
const project = 'gb-hybrid-app-ps';

// 获取指令参数
export function ParsingOptions() {
  // 获取指令传递来的参数
  const args = process.argv.slice(2); // 忽略前两个默认参数
  if (args.length === 0) return {}
  // 解析参数集合
  return args.reduce((accumulator, currentValue) => {
    let [key, value] = currentValue.split('=');
    if (!key || !value) throw '参数格式错误'
    if (key === 'idList') {
      value = value.split(',').map(e => +e);
    }
    accumulator[key] = value;
    return accumulator
  }, {})
}

// 生成编译指令集合
export function createInstructionSet(optionsMap, parameters) {
  if (parameters.idList) {
    optionsMap = optionsMap.filter(e => parameters.idList.indexOf(e.id) >= 0)
    delete parameters.idList
  }
  return optionsMap.map((e) => ({
    id: e.id,
    title: e.title,
    appid: e.appId,
    version: parameters.version,
    description: parameters.description,
    privatekey: path.join(__dirname, 'keys', e.privatekey)
  }));
}

// 覆写文件
export function override(id) {
  return new Promise((resolve) => {
    let filePath = path.join(__dirname, '..', 'config', '/env/options.js');
    // 读取文件内容
    let fileContent = fs.readFileSync(filePath, 'utf8');
    console.log('开始覆写文件', id);
    // 将文件内容按行分割成数组
    let lines = fileContent.split('\n');
    // 覆写
    lines[2] = `let currentId = ${id};\r`;
    let modifiedContent = lines.join('\n');
    fs.writeFileSync(filePath, modifiedContent, 'utf8');
    console.log('覆写成功', id);
    resolve(1);
  });
}

// 运行HBuilderX编译命令
async function runHBuilderCompile() {
  return new Promise((resolve, reject) => {
    console.log('正在编译小程序...');
    
    // 我们需要根据实际情况修改这个命令，这里假设是通过命令行编译
    // 可以根据实际情况调整HBuilderX CLI的路径和参数
    const compileCmd = 'cli publish --platform mp-weixin --project gb-hybrid-app-ps';
    
    try {
      exec(compileCmd, (err, stdout, stderr) => {
        if (err) {
          console.error('编译失败:', stderr);
          reject(err);
          return;
        }
        console.log('编译完成');
        resolve();
      });
    } catch (error) {
      console.error('执行编译命令失败:', error);
      // 这里我们不直接reject，因为可能HBuilderX不支持命令行编译
      // 而是通知用户需要手动编译
      console.log('提示: 请确保已通过HBuilderX编译最新的小程序代码');
      resolve();
    }
  });
}

// 使用miniprogram-ci发布小程序
async function publishMiniProgram(appInfo) {
  console.log(`开始发布 ${appInfo.title} (${appInfo.appid})...`);
  
  try {
    // 检查编译后的目录是否存在
    const mpWeixinPath = path.join(__dirname, '..', 'unpackage', 'dist', 'build', 'mp-weixin');
    if (!fs.existsSync(mpWeixinPath)) {
      console.log('警告: 编译后的小程序目录不存在');
      console.log('提示: 请确保已通过HBuilderX编译最新的小程序代码');
    }
    
    // 尝试编译小程序（注意：这可能需要HBuilderX支持CLI编译）
    try {
      await runHBuilderCompile();
    } catch (compileError) {
      console.log('编译小程序失败，尝试继续上传已有的编译结果');
    }
    
    // 动态导入miniprogram-ci
    const ci = (await import('miniprogram-ci')).default;
    
    // 创建项目
    const project = new ci.Project({
      appid: appInfo.appid,
      type: 'miniProgram',
      projectPath: mpWeixinPath,
      privateKeyPath: appInfo.privatekey,
      ignores: ['node_modules/**/*'],
    });

    // 上传代码
    const uploadResult = await ci.upload({
      project,
      version: appInfo.version,
      desc: appInfo.description,
      setting: {
        es6: true,
        minify: true,
      },
      robot: 1,
      onProgressUpdate: console.log,
    });

    console.log(`发布成功: ${appInfo.title}`, uploadResult);
    return uploadResult;
  } catch (error) {
    console.error(`发布失败: ${appInfo.title}`, error);
    throw error;
  }
}

// 运行主函数
export async function runTimeMain(instructionSet) {
  for (var index = 0; index < instructionSet.length; index++) {
    var element = instructionSet[index];
    let stopLoading = null;
    
    try {
      // 等待覆写
      await override(element.id);
      
      stopLoading = showLoading('等待编译并上传');
      
      // 使用miniprogram-ci发布
      await publishMiniProgram(element);
      
      if (stopLoading) {
        stopLoading();
        stopLoading = null;
      }
      
      if (index === instructionSet.length - 1) return;
      
      // 等待一段时间再继续下一个
      await delay(5000);
    } catch (error) {
      console.error(`处理 ${element.title} 时出错:`, error);
      
      // 确保停止加载动画
      if (stopLoading) {
        stopLoading();
        stopLoading = null;
      }
      
      // 继续处理下一个，不中断整个流程
    }
  }
}

const delay = (ms) => {
  return new Promise(resolve => {
    console.log('间隔' + ms / 1000 + '秒后继续...');
    const stopLoading = showLoading('等待缓冲');
    setTimeout(() => {
      stopLoading();
      resolve();
    }, ms);
  });
}

function showLoading(loadText) {
  const symbols = ['|', '/', '-', '\\'];
  let index = 0;

  const timer = setInterval(() => {
    process.stdout.write(`\r${loadText} ${symbols[index++]}  `);
    index %= symbols.length;
  }, 100);

  // 返回停止函数
  return () => {
    clearInterval(timer);
    process.stdout.write('\r完成!            \n'); // 覆盖并换行
  };
}

// 主函数调用
async function main() {
  try {
    // 获取参数
    let parameters = ParsingOptions();
    console.log('参数:', parameters);
    
    if (!parameters.version || !parameters.description) {
      console.error('缺少必要参数: version 或 description');
      return;
    }
    
    // 生成编译指令集合
    let instructionSet = createInstructionSet(optionsMap, parameters);
    console.log('生成发布任务:', instructionSet);
    
    // 运行主函数
    await runTimeMain(instructionSet);
  } catch (error) {
    console.error('发布过程中出错:', error);
  }
}

// 只有直接运行时才执行主函数，作为模块导入时不执行
if (import.meta.url === `file://${process.argv[1]}`) { 
  main();
}