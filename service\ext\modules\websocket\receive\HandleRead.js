import MessageDefault from './MessageDefault'

export default class HandleRead extends MessageDefault {
    constructor (websocket) {
        super()
        this.websocket = websocket
    }

    match (dataJson) {
        const { READ_CMD } = this.chatCmd
        return dataJson.cmd === READ_CMD
    }

    processMessage (dataJson) {
        let messageList = this.$common.getKeyVal('chat', 'messageList', false)
        for (let i = 0; i < messageList.length; i++) {
            let item = messageList[i];
            let id = item.hasBeenSentId
            let msgIds = dataJson.data.msgIds || dataJson.data.id
            if (msgIds.indexOf(id)!=-1){
                messageList[i].readStatus= 1
                // console.log(messageList[i])
            }
        }
        this.$common.setKeyVal('chat', 'messageList', messageList, false)
    }
}