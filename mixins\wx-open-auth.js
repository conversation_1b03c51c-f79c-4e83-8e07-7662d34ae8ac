// vue中提供了一种混合机制--mixins，用来更高效的实现组件内容的复用
// 混入 (mixins) 是一种分发 Vue 组件中可复用功能的非常灵活的方式。
// 混入对象可以包含任意组件选项。当组件使用混入对象时，
// 所有混入对象的选项将被混入该组件本身的选项
// #ifdef H5
import wx from 'weixin-js-sdk'
// #endif
const mixin = {
  methods: {
    async wxh5ShareInit() {
      // #ifdef H5
      const UA = navigator.userAgent.toLowerCase()
      // 是否是微信浏览器并存在accountId
      if(UA.match(/MicroMessenger/i) == 'micromessenger' && this.$common.getKeyVal('user', 'accountId', true)) {
          this.createJsapiSignature() // mixins wxShare
      } else {
          // 重定向到微信包装链
          if (process.env.NODE_ENV !== 'development' && !['c.xhl.greenboniot.cn'].includes(window.location.host)) {
            this.$api.common.wxGetCommonOauth2WrapUrl({ url: encodeURIComponent(window.location.href) }).then(res => {
              window.location.href = res
            })
          }
      }
      // #endif
    },
    createJsapiSignature () {
      let url = window.location.href.split('#')[0]
      // let url = 'gift.ngrok.greenboniot.cn'
      const that = this

      // const path = encodeURIComponent(window.location.href)
      const path = encodeURIComponent(url)
      this.$api.common.createJsapiSignature({ path }).then(res => {
        const data = res.data
        const appId = data.appId
        const timestamp = data.timestamp
        const signature = data.signature
        const nonceStr = data.nonceStr
        that.appId = appId

          wx.config({
            debug: false,
            appId: appId,
            timestamp: timestamp, // 必填，生成签名的时间戳
            nonceStr: nonceStr, // 必填，生成签名的随机串
            signature: signature, // 必填，签名，见附录1
            jsApiList: ['wx-open-launch-weapp'], // 必填，需要使用的JS接口列表，所有JS接口列表见附录2
            openTagList: ['wx-open-launch-weapp']
          })
          // 通过ready接口处理成功验证
          wx.ready(function () {
            wx.checkJsApi({
              jsApiList: ['wx-open-launch-weapp'],
              success: function (res) {
                console.log('可用')
              },
              fail: (err) => {
                console.log(err, '不可用 checkJsApi')
                // alert("不可用")
              }
            })
          })
          // 通过error接口处理失败验证
          wx.error(function (res) {
          })
        })
    }
  }
}
export default mixin
