<template>
  <view class="searchbox">
    <image :src="ico" class="searchico"></image>
    <input type="text" class="searchinput" v-model="input" confirm-type="search" @confirm="confirm" @input="inputFn" @blur="blurFn" :placeholder="placeholder" />
  </view>
</template>

<script>
const static_ctx = 'http://localhost:3000';
export default {
  name: 'titleSearch',
  props: {
    placeholder: {
      type: String,
      default: '请输入关键字'
    },
    value: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      // ico:'icon-im-search.png',
      ico: this.$static_ctx + 'image/business/case-collect/icon-im-search.png',
      input: ''
      // placeholder:"请输入姓名关键字",
    };
  },
  mounted() {
    // console.log('33',this.debounce)
  },
  methods: {
    confirm(e){
      // console.log('kl')
      this.debounce(function() {
        // console.log('执行力');
        // console.log(e)
        // console.log(this.input)
        this.$emit('update',{
          value:this.input,
          type:"confirm"
        })
      });
    },
    inputFn(e) {
      // console.log(e);
      this.debounce(function() {
        // console.log('执行力');
        // console.log(e)
        // console.log(this.input)
        this.$emit('update',{
          value:this.input,
          type:"input",
        })
      });
    },
    debounce(fn, delay = 500) {
      if (this.timer) {
        clearTimeout(this.timer);
      }
      this.timer = setTimeout(() => {
        fn.apply(this, arguments);
        this.timer = null;
      }, delay);
    },
    blurFn() {}
  }
};
</script>

<style lang="scss" scoped>
.searchbox {
  height: 100upx;
  background-color: #fff;
  width: 100%;
  padding: 0 20upx;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  position: relative;
  .searchinput {
    height: 80upx;
    width: 100%;
    background-color: #f2f2f2;
    border-radius: 10upx;

    padding-left: 70upx;
  }
  .searchico {
    width: 40upx;
    height: 40upx;
    position: absolute;
    left: 35upx;
    top: 50%;
    transform: translateY(-50%);
  }
}
</style>
