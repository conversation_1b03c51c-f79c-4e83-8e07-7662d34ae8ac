// import { JSEncrypt } from '@/common/plugins/jsencrypt'
import md5 from 'js-md5'
let JSEncrypt = ()=>{};
export default {
    /**
     * 格式化MD5
     * @param value
     */
    md5(value) {
        return md5(value)
    },
    /**
     * 公钥加密
     * @param key
     * @param value
     */
    jsEncryptCode(value) {

        const encrypt = new JSEncrypt()
        encrypt.setPublicKey(
            '-----BEGIN PUBLIC KEY-----\n' +
            'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAw5fFGwk4eqgen3IUQs9m\n' +
            'sE7G8rhQvzpVtvt/HQoa/We43rrWC3NfV9cQU18+8kMGBHEOOAbnDVemxCm28goN\n' +
            'Lmkx74H6nYLfIxrrGC+AFffRFaAfPzp2m712clymuZEeSM01tpgp1XgIBuWBRV30\n' +
            '94Cba1XnJw3IXbICdkvIkmvtkUnkbnplKjDUDZ4whG5OG0FsOUODgArc//3xj8I3\n' +
            'MOQytp/0JqEgIV9ejkpRdIqZL3QIZDEFlfsyV6nhsKYutkSVBvzE4NAx6bJUl4cc\n' +
            'BfaxmkysmGzXr6Mpyi+WBT3oYL6chhb+y/Nzuim0WOEAZZfBnWK/VlTuU9NWhWFW\n' +
            '8QIDAQAB\n' +
            '-----END PUBLIC KEY-----'
        )
        return encrypt.encrypt(value)
    },
    /**
     * 公钥加密
     * @param key
     * @param value
     */
    encrypt(key, value) {
        const jse = new JSEncrypt()
        jse.setPublicKey(key)
        value = jse.encrypt(value)
        return value
    },
    /**
     * 私钥解密
     *
     * @param key
     * @param value
     */
    decrypt(key, value) {
        const jse = new JSEncrypt()
        jse.setPrivateKey(key)
        return jse.decrypt(value)
    }
}
