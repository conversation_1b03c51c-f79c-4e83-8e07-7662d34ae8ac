<template>
  <page>
    <view slot="content" class="page-content">
      <m-nav-bar title="我的客户" left-icon="left" @clickLeft="back" />

      <view class="content-main">
        <!-- 搜索区域 -->
        <view class="search-container">
          <view class="search-box">
            <image :src="searchIcon" class="search-icon" mode="aspectFit"></image>
            <input 
              type="text" 
              placeholder="输入用户名称、手机号码" 
              confirm-type="search"
              v-model="searchQuery"
              @confirm="searchCustomers"
              class="search-input"
            />
            <image 
              v-if="searchQuery"
              src="/static/image/distribution/icon-clzhelear.png" 
              class="clear-icon" 
              mode="aspectFit"
              @tap="clearSearch"
            ></image>
          </view>
          <view class="search-btn" @tap="searchCustomers">搜索</view>
        </view>

        <!-- 客户总数 -->
        <view class="customer-count">
          客户：{{ totalCustomers }}人
        </view>

        <!-- 客户列表 -->
        <view class="customer-list">
          <scroll-refresh
            bgColor='#f5f5f5'
            class="scroll-refresh-main"
            :isShowEmptySwitch="false"
            :fixed="false"
            :up="upOption"
            :down="downOption"
            @returnFn="returnFn"
            @scrollInit="scrollInit"
          >
            <view v-if="customerList && customerList.length > 0" class="customers-wrapper">
              <view 
                v-for="(customer, index) in customerList" 
                :key="index"
                class="customer-item"
              >
                <view class="customer-avatar">
                  <image :src="defaultAvatar" class="avatar-image" mode="aspectFill"></image>
                </view>
                <view class="customer-info">
                  <view class="customer-name">{{ customer.name || '未知用户' }}</view>
                  <view class="customer-phone">{{ formatPhone(customer.phone) }}</view>
                  <view class="customer-date" v-if="customer.bindTime">绑定时间：{{ formatDate(customer.bindTime) }}</view>
                </view>
                <view class="customer-action">
                  <view class="action-btn" @tap="viewCustomerDetail(customer)">查看</view>
                </view>
              </view>
            </view>
          </scroll-refresh>

          <!-- 空数据提示 -->
          <view class="empty-tip" v-if="isDataLoaded && (!customerList || customerList.length === 0)">
            <image src="/static/image/distribution/icon-empty.png" class="empty-icon" mode="aspectFit"></image>
            <view class="empty-text">暂无客户数据</view>
          </view>
        </view>
      </view>
    </view>
  </page>
</template>

<script>
import uniNavBar from '@/components/uni/uni-nav-bar/uni-nav-bar.vue'
import scrollRefresh from '@/components/uni/zPaging/index'
import distributionApi from '@/service/api/modules/distribution.js'
import serverOptions from '@/config/env/options'

export default {
  components: {
    uniNavBar,
    scrollRefresh
  },
  data() {
    return {
      searchQuery: '',
      customerList: [],
      totalCustomers: 0,
      currentPage: 1,
      pageSize: 10,
      hasMoreData: true,
      isRefreshing: false,
      isDataLoaded: false,
      isLoading: false,
      defaultAvatar: this.$static_ctx + 'image/business/hulu-v2/icon-list-avatar-default-grild.png',
      searchIcon: this.$static_ctx + 'image/business/teaching-plan/icon-search.png',
      // 下拉刷新配置
      downOption: {
        auto: false
      },
      upOption: {
        auto: false,
        empty: {
          top: 0,
          zIndex: 999,
        },
        onScroll: true
      },
      mescroll: null
    }
  },
  mounted() {
    this.initData();
  },
  methods: {
    back() {
      uni.navigateBack();
    },
    initData() {
      // 初始化客户列表数据
      this.init();
    },
    // 初始化滚动组件
    scrollInit(scroll) {
      scroll.optUp.page.num = 1;
      scroll.optUp.page.size = 10;
      this.mescroll = scroll;

      // 在初始化滚动组件后立即触发下拉刷新
      this.init();
    },
    
    // 下拉刷新回调
    returnFn(obj) {
      const that = this;
      
      // 标记数据尚未加载完成
      this.isDataLoaded = false;
      this.isLoading = true;
      
      setTimeout(() => {
        // 处理必要的参数
        const processedParams = {};
        
        // 如果有搜索关键词，添加到条件中
        if (this.searchQuery) {
          // 根据输入内容判断是搜索手机号还是名称
          if (/^\d+$/.test(this.searchQuery)) {
            processedParams.phone = this.searchQuery; // 搜索手机号
          } else {
            processedParams.name = this.searchQuery; // 搜索名称
          }
        }
        
        // 添加providerId参数
        processedParams.providerId = serverOptions.providerId;
        
        // 获取分销员ID信息
        that.getDistributorId().then(distributorId => {
          if (distributorId) {
            // 使用分销员ID
            processedParams.distributorId = distributorId;
          }
          
          let params = {
            current: obj.pageNum,
            size: obj.pageSize,
            condition: processedParams
          };
          
          // 显示加载状态
          uni.showLoading({
            title: '加载中...'
          });
          
          // 调用API获取客户列表
          distributionApi.accompanycustomerQueryPage(params).then(res => {
            uni.hideLoading();
            that.isLoading = false;
            console.log('获取客户列表', res);
            
            if (res.code === 0) {
              const { records, total } = res.data;
              
              // 如果是第一页，清空列表
              if (obj.pageNum === 1) {
                that.customerList = [];
              }
              
              // 如果有数据
              if (Array.isArray(records) && records.length > 0) {
                that.customerList = [...that.customerList, ...records];
              }
              
              // 更新客户总数
              that.totalCustomers = total || 0;
              
              // 标记数据已加载
              that.isDataLoaded = true;
              
              // 调用回调
              obj.successCallback && obj.successCallback(records, {
                curPageLen: records.length,
                totalPage: Math.ceil(total / obj.pageSize),
                totalSize: total
              });
            } else {
              // 处理API返回错误
              that.isDataLoaded = true;
              
              uni.showToast({
                title: res.msg || '获取客户列表失败',
                icon: 'none'
              });
              
              // 回调空数据
              obj.successCallback && obj.successCallback([], {
                curPageLen: 0,
                totalPage: 0,
                totalSize: 0
              });
            }
          }).catch(err => {
            uni.hideLoading();
            that.isLoading = false;
            that.isDataLoaded = true;
            
            console.error('获取客户列表失败：', err);
            
            uni.showToast({
              title: '网络异常，请稍后重试',
              icon: 'none'
            });
            
            // 回调空数据
            obj.successCallback && obj.successCallback([], {
              curPageLen: 0,
              totalPage: 0,
              totalSize: 0
            });
          });
        }).catch(error => {
          console.error('获取分销员ID失败：', error);
          uni.hideLoading();
          that.isLoading = false;
          that.isDataLoaded = true;
          
          uni.showToast({
            title: '获取分销员信息失败',
            icon: 'none'
          });
        });
      }, 500);
    },
    
    // 初始化页面数据
    init() {
      this.isDataLoaded = false;
      
      this.$nextTick(() => {
        if (this.mescroll) {
          this.mescroll.triggerDownScroll();
        }
      });
    },
    
    searchCustomers() {
      this.init();
    },
    
    clearSearch() {
      this.searchQuery = '';
      
      // 重置mescroll并重新加载数据
      if (this.mescroll) {
        this.mescroll.resetUpScroll();
      } else {
        this.init();
      }
    },
    
    formatPhone(phone) {
      // 格式化手机号码
      if (!phone) return '未设置';
      
      // 如果不是11位数字，直接返回原值
      if (!/^\d{11}$/.test(phone)) return phone;
      
      // 格式化为 187 **** 8742 格式
      return phone.replace(/(\d{3})(\d{4})(\d{4})/, '$1 **** $3');
    },
    
    formatDate(dateStr) {
      // 格式化日期
      if (!dateStr) return '';
      
      try {
        // 尝试将字符串转换为日期对象
        const date = new Date(dateStr);
        
        // 检查是否是有效日期
        if (isNaN(date.getTime())) return dateStr;
        
        // 格式化为 YYYY-MM-DD 格式
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        
        return `${year}-${month}-${day}`;
      } catch (error) {
        console.error('日期格式化错误:', error);
        return dateStr;
      }
    },
    
    viewCustomerDetail(customer) {
      this.$navto.push('MyCustomersDetail', {
        id: customer.id,
        userId: customer.userId, // 添加userId参数
        name: customer.name || '',
        phone: customer.phone,
        bindTime: customer.bindTime
      });
    },
    
    // 获取分销员ID
    async getDistributorId() {
      try {
        // 从本地存储获取当前用户信息
        const codeUserInfo = this.$common.getKeyVal('user', 'codeUserInfo', true);
        if (!codeUserInfo || !codeUserInfo.id) {
          console.error('未获取到用户ID');
          return null;
        }
        
        // 调用接口获取分销员信息
        const res = await distributionApi.accompanydistributorQueryOneByUserId({
          userId: codeUserInfo.id,
          providerId: serverOptions.providerId
        });
        
        // 如果接口返回成功且有分销员信息
        if (res.code === 0 && res.data && res.data.id) {
          console.log('获取到分销员ID:', res.data.id);
          return res.data.id; // 返回分销员ID
        } else {
          console.warn('用户不是分销员或获取分销员信息失败');
          return null;
        }
      } catch (error) {
        console.error('获取分销员信息异常:', error);
        return null;
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.page-content {  min-height: 100vh;  height: 100vh;  background-color: #f5f5f5;  display: flex;  flex-direction: column;}

.content-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* 搜索区域 */
.search-container {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #fff;
}

.search-box {
  flex: 1;
  height: 72rpx;
  background-color: #f5f5f5;
  border-radius: 36rpx;
  border: 2rpx solid #D9DBE0;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  margin-right: 20rpx;
}

.search-icon {
  width: 34rpx;
  height: 34rpx;
  margin-right: 10rpx;
}

.search-input {
  flex: 1;
  height: 72rpx;
  font-size: 28rpx;
}

.clear-icon {
  width: 30rpx;
  height: 30rpx;
}

.search-btn {
  min-width: 100rpx;
  height: 72rpx;
  background-color: #2196f3;
  color: #fff;
  font-size: 28rpx;
  line-height: 72rpx;
  text-align: center;
  border-radius: 36rpx;
  padding: 0 20rpx;
}

/* 客户总数 */
.customer-count {
  padding: 20rpx 30rpx;
  font-size: 28rpx;
  color: #666;
  background-color: #f5f5f5;
}

/* 客户列表区域 */
.customer-list {  flex: 1;  position: relative;  display: flex;  flex-direction: column;  /* 移除固定高度计算 */  background-color: #f5f5f5;}

.customers-wrapper {
  padding: 0 30rpx;
}

.scroll-refresh-main {  flex: 1;  height: 100%;  position: relative;  background-color: #f5f5f5;  /* 确保安卓上显示正确 */  min-height: 500rpx;}

.customer-item {
  display: flex;
  align-items: center;
  background-color: #ffffff;
  border-radius: 10rpx;
  margin-bottom: 15rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.03);
}

.customer-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  background-color: #f0f0f0;
  margin-right: 20rpx;
}

.avatar-image {
  width: 100%;
  height: 100%;
}

.customer-info {
  flex: 1;
  min-width: 0;
}

.customer-name {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 6rpx;
  font-weight: 500;
}

.customer-phone {
  font-size: 26rpx;
  color: #999;
}

.customer-date {
  font-size: 24rpx;
  color: #999;
  margin-top: 6rpx;
}

.customer-action {
  padding-left: 20rpx;
}

.action-btn {
  width: 100rpx;
  height: 52rpx;
  line-height: 52rpx;
  text-align: center;
  background-color: #f0f0f0;
  border-radius: 26rpx;
  font-size: 24rpx;
  color: #666;
}

/* 空数据提示 */
.empty-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #f5f5f5;
  z-index: 1;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}
</style>