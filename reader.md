---------开发规范----------

路由命名：路由名称首字母大写

背景图片、字体图标
本地背景图片、字体图标的引用路径推荐使用以 ~@ 开头的绝对路径。

image图标命名统一用icon-拼音.png

css单位统一使用upx,以IPONE6 375作为基准，设计稿750 ，禁止使用upx以外单位

所有文件夹命名/文件命名/样式命名必须以-(横杆)为准

除了函数、常量和缓存store使用驼峰式


Components 组件区块
features：功能组件（例如日期，字典组件....）
module:页面块组件
page：中间页组件（例如通讯录）

---------------------------------------------------------------------

文件规则
edit命名使用文件（新增、查看、编辑）
list列表页面


staff 员工（教师端）
guardian 监护人（家长端）


小程序严格开发规范
1.父级传入子级组件数据必须在父级做回调处理
2.标签尽量避免过多复杂嵌套数据操作如时间方法格式化、金额乘除100等
3.标签使用对象或者数组内部属性必须加外层判断保护，尽量避免标签变量层级深
4.data 作为属性保留名,不允许在自定义组件 XXXX 中定义为 props			
解决方案：props中的绑定参数data改为cData，以及关联的页面中使用到相关标签的:data值也要跟着改包括组件自己本身涉及到data也要改成cData		
5.id作为属性保留名,不允许在自定义组件 XXXX 中定义为 props
解决方案：props中的绑定参数id改为cId，以及关联的页面中使用到相关标签的:id值也要跟着改包括组件自己本身涉及到id也要改成cId					
6.v-model.trim在小程序里无法识别，所以报错	
7.页面上中尽量加字段保护机制，禁止出现null和undefined				
8.后端字典数据以页面绑定值尽量避免使用下标做绑定，尽量使用字典编码值做绑定    





