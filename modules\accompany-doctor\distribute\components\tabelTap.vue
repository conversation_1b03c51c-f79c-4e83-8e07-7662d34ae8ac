<template>
  <view class="tabelBox">
    <view class="titleMap padding20" >
      <view v-if="index!==0" v-for="(item,index) in tabelMap" :key="index" class="tabIcon" :class="{endTab:index >= tabelMap.length - 1}">
        <image class="tabelselectIcon" v-if="pageIndex === index" :src="tabelselectIcon" mode=""></image>
        <image class="tabNumber" v-else-if="pageIndex > index" :src="finishTab" mode=""></image>
        <view class="tabNumber" v-else>{{index}}</view>
        <view class="tabLine" :class="{currentLine:pageIndex >= index}" v-if="index < tabelMap.length - 1"></view>
      </view>
    </view>
    <view class="titleMap">
      <view v-if="index!==0" v-for="(item,index) in tabelMap" :key="index">
        {{item.title}}
      </view>
    </view>
  </view>
</template>

<script>
  export default{
    props:{
      pageIndex:{
        type:Number,
        default:1
      }
    },
    data(){
      return {
        tabelMap:[
          '',
          {title:'服务预约'},
          {title:'人工导诊'},
          {title:'在线支付'},
          {title:'专人陪诊'},
          {title:'服务完成'},
        ],
        tabelselectIcon: this.$static_ctx + "image/business/hulu-v2/tabelselectIcon.png",
        finishTab: this.$static_ctx + "image/business/hulu-v2/finishTab.png",
      }
    }
  }
</script>

<style lang="scss">
  .tabelBox{
    margin-bottom: 40rpx;
  }
  .tabelselectIcon{
    width: 48rpx;
    height: 48rpx;
  }
  .padding20{
    padding: 0 40rpx;
  }
  .titleMap{
    display: flex;
    justify-content: space-evenly;
    margin-top: 16rpx;
    .tabIcon{
      display: flex;
      align-items: center;
      flex: 1;
      justify-content: space-around;
    }
    .endTab{
      flex: 0.5;
    }
  }
  .tabNumber{
    width: 36rpx;
    height: 36rpx;
    background: #C9CCD4;
    border-radius: 50%;
    text-align: center;
    line-height: 36rpx;
  }
  .tabLine{
    width: 68rpx;
    height: 8rpx;
    background: #C9CCD4;
    border-radius: 4rpx;
  }
  .currentLine{
    background: #CCF0E6;
  }
  .endLine{
    opacity: 0;
  }
</style>
