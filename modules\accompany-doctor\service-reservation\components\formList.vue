<template>
  <view class="server">
    <template v-for="(item,index) in serverMap">
      <view v-if="showItem(item)" :key="index" @click="$emit('emitChange',item.textType)" class="serverItem">
        <view class="serverTitle"><text class="sign" v-if="item.required">*</text>{{item.title}}</view>
        <view class="serverContent">{{item.text || item.noValue}}</view>
        <image class="iconRightArrow" :src="iconRightArrow" mode=""></image>
      </view>

      <view v-if="item.type === 'textarea' && !item.hidden" :key="index" class="serverItem">
        <view class="serverTitle" :style="{width:item.miniTitle ? '100%' : '172rpx'}"><text class="sign" v-if="item.required">*</text>{{item.title}}<text class="miniTitle" v-if="item.miniTitle">({{item.miniTitle}})</text></view>
        <textarea class="serverContent" :placeholder="item.noValue" @input="res=>inputChange(item.textType,res)"></textarea>
      </view>

      <view v-if="item.type === 'input' && !item.hidden" :key="index" class="serverItem">
        <view class="serverTitle"><text class="sign" v-if="item.required">*</text>{{item.title}}</view>
        <input class="serverContent" :value="item.value" :placeholder="item.noValue" @input="res=>inputChange(item.textType,res)"></input>
      </view>

      <view v-if="item.type === 'box' && !item.hidden" :key="index" class="serverItem">
        <view class="serverBox">
          <view v-if="item.text">{{item.text}}</view>
          <slot></slot>
        </view>
      </view>
      <view v-if="item.type === 'noPaddingBox' && !item.hidden" :key="index" class="serverItem noPadding">
        <slot></slot>
      </view>
      <view v-if="item.type === 'radio' && !item.hidden" :key="index" class="serverItem">
        <view class="serverTitle"><text class="sign" v-if="item.required">*</text>{{item.title}}</view>
          <view v-for="(selectItem) in listOptions[item.textType]" @click="changeSelect(selectItem,listOptions[item.textType],item.textType)" :key="selectItem.text" class="selectRadio">
            <image class="Group" :src="selectItem.select ? iconPostSucess : Ellipse" mode=""></image> {{selectItem.text}}
          </view>
      </view>
      <view class="serverItem" v-if="item.type === 'image' && !item.hidden">
        <view class="serverTitle">{{item.title}}</view>
        <view class="additionalContentImageUpMap">
          <view
          class="additionalContentImageUp"
          v-for="(itemImage,index) in item.value"
          :key="index"
          @click="$emit('previewImage',file_ctx + itemImage)"
          >
            <image class="iconPostMenuClose" @click.stop="$emit('closeImgage',index)" :src="iconPostMenuClose" mode=""></image>
            <image class="imageContent" :src="file_ctx + itemImage"></image>
          </view>
          <view class="additionalContentImageUp" @click="$emit('emitChange',item.textType)">
            <image class="imageUpImage" :src="iconPostUpload" mode=""></image>
            <view class="imageUpText">上传图片</view>
          </view>
        </view>

      </view>
      <view class='line' v-if="index < serverMap.length - 1 && !item.hidden"></view>
    </template>
  </view>
</template>

<script>
  import serverOptions from '@/config/env/options'
  import listOptions from './listOptions.js'
  export default{
    props:{
      serverMap:{
        type:Array,
        default:[]
      }
    },
    data(){
      return {
        file_ctx: this.file_ctx,
        iconRightArrow: this.$static_ctx + "image/business/hulu-v2/icon-right-arrow.png",
        iconPostUpload: this.$static_ctx + "image/business/hulu-v2/icon-post-upload.png",
        Ellipse: this.$static_ctx + "image/business/hulu-v2/Ellipse.png",
        iconPostSucess:
        this.$static_ctx + "image/business/hulu-v2/icon-post-sucess.png",
        listOptions
      }
    },
    methods:{
      inputChange(type,res){
        this.$emit('emitInput',{type,res})
      },
      showItem(item){
        if(item.showSource) return !item.type && !item.hidden && item.showSource === serverOptions.source
        return !item.type && !item.hidden
      },
      changeSelect(item,list,type){
        list.map(e=>{
          e.select = false
        })
        item.select = true;
        this.$emit('select',{type,value:item.value})
      },
    }
  }
</script>

<style lang="scss">
  .selectRadio{
    display: flex;
    align-items: center;
    font-weight: 400;
    font-size: 28rpx;
    color: #1D2029;
    margin-right: 20rpx;
    &:last-child{
      margin-right: 0rpx !important;
    }
    .Group{
      width: 28rpx;
      height: 28rpx;
      margin-right: 4rpx;
    }
  }
  .line{
    width:100%;
    height: 2rpx;
    background: #EAEBF0;
  }
  .iconRightArrow{
    width: 32rpx;
    height: 32rpx;
  }

  .server{
    width: 100%;
    background: #FFFFFF;
    border-radius: 16rpx;
    margin-top: 22rpx;
    box-sizing: border-box;
    .noPadding{
      padding: 0 !important;
    }
    .serverItem{
      width: 100%;
      display: flex;
      padding: 32rpx 8rpx;
      box-sizing: border-box;
      align-items: center;
      flex-wrap: wrap;
      .serverBox{
        padding: 20rpx 28rpx;
        width: 686rpx;
        background: #FFFFFF;
        border-radius: 16rpx 16rpx 16rpx 16rpx;
        box-sizing: border-box;
        font-weight: 500;
        font-size: 24rpx;
        color: #777777;
        max-height: 582rpx;
        overflow: scroll;
      }
      .serverTitle{
        width: 172rpx;
        font-weight: 400;
        font-size: 28rpx;
        color: #1D2029;
        .sign{
          font-weight: 500;
          font-size: 28rpx;
          color: #FF5500;
        }
      }
      .miniTitle{
        font-weight: 400;
        font-size: 24rpx;
        color: #4E5569;
      }
      .serverArea{
        width: 638rpx;
        height: 288rpx;
        background: #FFFFFF;
        border-radius: 16rpx;
        border: 1rpx solid #D9DBE0;
        margin-top: 16rpx;
        padding: 24rpx;
        font-weight: 400;
        font-size: 28rpx;
      }
      .serverContent{
        width: 400rpx;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 28rpx;
        color: #A5AAB8;
      }
      .iconRightArrow{
        margin-left: auto;
      }
    }
  }
  .additionalContentImageUpMap{
    display: flex;
    flex-wrap: wrap;
    column-gap: 20rpx;
  }
  .additionalContentImageUp{
    margin-top: 12rpx;
    width: 144rpx;
    height: 144rpx;
    background: #F4F6FA;
    border-radius: 16rpx;
    text-align: center;
    position: relative;
    &:nth-of-type(3n){
      margin-right: 0;
    }
    .imageUpImage{
      width: 64rpx;
      height: 54rpx;
      margin-top: 46rpx;
    }
    .imageUpText{
      font-weight: 400;
      font-size: 24rpx;
      color: #4E5569;
    }
    .imageContent{
      width: 100%;
      height: 100%;
    }
    .iconPostMenuClose{
      width: 30rpx;
      height: 30rpx;
      position: absolute;
      top: 0;
      right: 0;
      background: white;
    }
  }
</style>
