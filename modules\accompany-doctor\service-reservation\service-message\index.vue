<template>
  <view class="main">
    <view :style="'height:' + statusBarHeight + 'px;'"></view>
    <view class="top-nav">
      <view class="top-nav-l" @click.stop="handleBack"><image mode="aspectFit" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-service-left-arrows.png'" class="header-search-img"/></view>
      <view class="top-nav-c">服务信息</view>
    </view>
    <view class="main-head">
      <view class="main-head-l">
        <view class="head-l-t">半天陪诊</view>
        <view class="head-l-b">服务时间：<span>07.28 08:00～12:00</span></view>
      </view>
      <view class="main-head-r">待服务</view>
    </view>
    <view class="main-content">
      <!-- 服务评价 -->
      <view class="service-evaluate" :style="{backgroundImage:'url(' + file_ctx + 'static/image/business/accompany-doctor/icon-accompany-service-evaluate-bg.png' + ')','background-repeat':'no-repeat','background-size': '100%'}">
        <view class="evaluate-head">
          <view class="evaluate-head-l">服务评价</view>
          <view class="evaluate-head-r"></view>
        </view>
        <view class="evaluate-bott">用户相关评价服务内容用户相关评价服务内容</view>
      </view>

      <!-- 预约信息 -->
      <view class="main-appointment-message">
        <view class="title">预约信息</view>
        <view class="person">
          <view class="person-l">就诊人</view>
          <view class="person-r">用户名称</view>
        </view>
        <view class="hospital">
          <view class="person-l">就诊医院</view>
          <view class="person-r">中山大学附属第一医院</view>
        </view>
        <view class="supplement">
          <view class="supplement-l">补充内容</view>
          <view class="supplement-r">
            <view class="supplement-item" v-for="item in 6" :key="item"></view>
          </view>
        </view>
        <view class="remark">
          <view class="person-l">平台备注</view>
          <view class="person-r">文本文本文本文本文本文本文本文本文本文本</view>
        </view>
      </view>

      <!-- 服务记录 -->
      <view class="service-record">
        <view class="title">服务记录</view>
        <view class="clock-start">
          <view class="clock-start-h">
            <view class="clock-start-h-l">
              <view class="time">08.21 8:00</view>
              <view class="title">签到打卡，开始服务</view>
            </view>
            <view class="clock-start-h-r">编辑</view>
          </view>
          <view class="clock-start-b">
            <view class="clock-start-item" v-for="item in 6" :key="item"></view>
          </view>
        </view>
        <view class="clock-end"></view>
      </view>

      <!-- 订单信息 -->
      <view class="order-message">
        <view class="title">订单信息</view>
        <view class="order-num">
          <view class="num-l">订单号</view>
          <view class="num-r">125678754224 <span @click="handleCopyOrder">复制</span></view>
        </view>
        <view class="service-num">
          <view class="num-l">服务项目</view>
          <view class="num-r">半天陪诊</view>
        </view>
        <view class="order-num">
          <view class="num-l">派单时间</view>
          <view class="num-r">2024.08.21 12:00</view>
        </view>
      </view>
    </view>
    <view class="service-detail-bottom" @click="$refs.guardDetailPopup.open()">
      <button>签到打卡</button>
    </view>

    <!-- 签到打卡弹窗 -->
    <uni-popup ref="guardDetailPopup" type="bottom">
      <view class="guard-detail-content">
        <view class="title">签到打卡</view>
        <view class="error" @click="$refs.guardDetailPopup.close()"><image :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-service-error.png'"></image></view>
        <view class="fast-clock"><image :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-fast-clock.png'"></image><view class="text">快捷打卡</view></view>
        <view class="upload-picture">
          <title-img
            ref="my-img"
            :config="{
              padding: 0,
              background: 'none',
              margin: '10rpx',
              count: 6,
              multiSelectCount: 6,
            }"
            :cData="imageList"
            @returnFn="(obj) => {imgReturnFn(obj)}"
          >
          </title-img>
        </view>
        <view class="guard-detail-bottom"><button>确定</button></view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  import uniPopup from '@/components/uni/uni-popup'
  import TitleImg from "@/components/business/module/title-img/index.vue"
  export default {
    components:{
      uniPopup,
      TitleImg
    },
    data(){
      return{
        file_ctx: this.file_ctx,
        statusBarHeight: 0,
        sendImgages:null,
        config: {
          img: {
            count: 6,
            background: 'rgba(0,0,0,0)',
            formData: {
              groupId: "26000",
              relId: ''
            },
            showImg: false
          }
        },
        imageList:[],
      }
    },
    onLoad(){

    },
    mounted(){
      this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight // 获取设备的状态栏高度
    },
    methods:{
      imgReturnFn(list) {
        this.imageList = list
      },
      handleBack(){
        this.$navto.back(1)
      },
      handleCopyOrder(){
        let phoneNumber = '22222222222222'
        uni.setClipboardData({
          data: phoneNumber,
          success: function () {
            uni.showToast({
              title: '复制成功',
              icon: 'success'
            });
          },
          fail: function (error) {
            // console.error('复制失败', error);
            uni.showToast({
              title: '复制失败',
              icon: 'none'
            });
          }
        });
      },
    },
 }
</script>

<style lang='scss' scoped>
  .main{
    height: 100vh;
    // display: flex;
    // flex-direction: column;
    overflow-x: hidden;
    background-color: #F4F6FA;
    overflow-y: auto;
  }
  .top-nav{
    // position: fixed;
    width: calc(100% - 16rpx);
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 40px;
    line-height: 40px;
    padding: 0 16rpx;
    // z-index: 999;
    // padding: 0 32rpx 0 24rpx;
    .top-nav-l{
      display: flex;
      width: 48rpx;
      height: 48rpx;
      image{
        width: 100%;
        height: 100%;
      }
    }
    .top-nav-c{
      flex: 1;
      text-align: center;
      height: 44rpx;
      font-weight: 500;
      font-size: 32rpx;
      color: #2D2F38;
      line-height: 44rpx;
      margin-right: 48rpx;
    }
  }
  .main-head{
    display: flex;
    justify-content: space-between;
    padding: 40rpx 32rpx;
    overflow: hidden;
    .main-head-l{
      .head-l-t{
        font-size: 36rpx;
        color: #1D2029;
        line-height: 50rpx;
      }
      .head-l-b{
        margin-top: 8rpx;
        font-size: 26rpx;
        color: #4E5569;
        line-height: 36rpx;
        span{
          font-size: 26rpx;
          color: #1D2029;
          line-height: 36rpx;
        }
      }
    }
    .main-head-r{
      font-size: 32rpx;
      color: #00B484;
      line-height: 44rpx;
    }
  }
  .main-content{
    margin: 0 32rpx;
    padding-bottom: 288rpx;
    .service-evaluate{
      width: 686rpx;
      height: 242rpx;
      border-radius: 16rpx;
      overflow: hidden;
      .evaluate-head{
        padding:32rpx 24rpx;
        display: flex;
        justify-content: space-between;
        .evaluate-head-l{
          font-size: 28rpx;
          color: #1D2029;
        }
        .evaluate-head-r{
          display: flex;
          width: 166rpx;
          height: 44rpx;
          background-color: orange;
        }
      }
      .evaluate-bott{
        padding:0 24rpx;
        font-size: 26rpx;
        color: #1D2029;
        line-height: 36rpx;
      }
    }
    .main-appointment-message{
      padding:32rpx 24rpx;
      margin: 20rpx 0;
      background-color: #fff;
      border-radius: 16rpx;
      overflow: hidden;
      .title{
        font-size: 32rpx;
        color: #1D2029;
        line-height: 44rpx;
        margin-bottom: 32rpx;
      }
      .person,.hospital,.remark{
        display: flex;
        align-items: center;
        justify-content: space-between;
        .person-l{
          font-size: 26rpx;
          color: #1D2029;
        }
        .person-r{
          font-size: 26rpx;
          color: #4E5569;
        }
      }
      .hospital{
        margin: 16rpx 0;
      }
      .supplement{
        display: flex;
        justify-content: space-between;
        .supplement-l{
          font-size: 26rpx;
          color: #1D2029;
          width: 104rpx;
        }
        .supplement-r{
          display: flex;
          flex: 1;
          flex-wrap: wrap;
          justify-content: flex-end;
          .supplement-item{
            width: 112rpx;
            height: 112rpx;
            border-radius: 12rpx;
            background-color: pink;
            margin-left: 18rpx;
            margin-bottom: 18rpx;
          }
        }
      }
    }
    .service-record{
      padding:32rpx 24rpx;
      background: #FFFFFF;
      border-radius: 16rpx;
      overflow: hidden;
      .title{
        font-size: 28rpx;
        color: #1D2029;
        line-height: 40rpx;
      }
      .clock-start{
        .clock-start-h{
          display: flex;
          justify-content: space-between;
          .clock-start-h-l{
            .time{
              font-size: 28rpx;
              color: #4E5569;
              line-height: 40rpx;
            }
            .title{
              margin-top: 16rpx;
              font-size: 28rpx;
              color: #1D2029;
              line-height: 40rpx;
            }
          }
          .clock-start-h-r{
            display: flex;
            align-items: center;
            justify-content: center;
            width: 184rpx;
            height: 64rpx;
            background: #FFFFFF;
            border-radius: 8rpx;
            border: 1rpx solid #D9DBE0;
            font-size: 28rpx;
            color: #1D2029;
          }
        }
        .clock-start-b{
          display: flex;
          flex-wrap: wrap;
          padding: 16rpx;
          background: #F4F6FA;
          border-radius: 16rpx;
          overflow: hidden;
          .clock-start-item{
            width: 170rpx;
            height: 170rpx;
            border-radius: 16rpx;
            margin-right: 16rpx;
            margin-bottom: 18rpx;
            &:nth-child(3n){
              margin-right: 0;
            }
          }
        }
      }
    }
    .order-message{
      padding:32rpx 24rpx;
      background: #FFFFFF;
      border-radius: 16rpx;
      overflow: hidden;
      .title{
        font-size: 32rpx;
        color: #1D2029;
        line-height: 44rpx;
        margin-bottom: 32rpx;
      }
      .order-num,.service-num{
        display: flex;
        align-items: center;
        justify-content: space-between;
        .num-l{
          font-size: 26rpx;
          color: #1D2029;
        }
        .num-r{
          font-size: 26rpx;
          color: #4E5569;
          span{
            font-size: 26rpx;
            color: #316EAB;
          }
        }
      }
      .service-num{
        margin: 16rpx 0;
      }
    }
  }
  .service-detail-bottom{
    display: flex;
    justify-content: center;
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 180rpx;
    background: #FFFFFF;
    box-sizing: border-box;
    padding: 24rpx 32rpx 0;
    box-shadow: 0rpx -1rpx 0rpx 0rpx #EAEBF0;
    button{
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 88rpx;
      background: #00B484;
      border-radius: 44rpx;
      font-size: 32rpx;
      color: #FFFFFF;
    }
  }
  .guard-detail-content{
    position: relative;
    background: #F4F6FA;
    padding: 32rpx 0rpx 386rpx;
    border-radius: 24rpx 24rpx 0rpx 0rpx;
    .title{
      display: flex;
      justify-content: center;
      font-size: 32rpx;
      color: #1D2029;
      line-height: 44rpx;
    }
    .error{
      position: absolute;
      right: 32rpx;
      top: 38rpx;
      width: 32rpx;
      height: 32rpx;
      image{
        width: 100%;
        height: 100%;
      }
    }
    .fast-clock{
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 48rpx 0 32rpx;
      image{
        display: flex;
        width: 304rpx;
        height: 304rpx;
      }
      .text{
        position: absolute;
        top: 45%;
        left: 50%;
        transform: translate(-50%, -45%);
        font-size: 40rpx;
        color: #FFFFFF;
      }
    }
    .upload-picture{
      background: #FFFFFF;
      border-radius: 16rpx;
      padding: 32rpx;
    }
    .guard-detail-bottom{
      position: absolute;
      height: 180rpx;
      width: 100%;
      bottom: 0;
      padding-top: 24rpx;
      background-color: #fff;
      button{
        display: flex;
        align-items: center;
        justify-content: center;
        height: 88rpx;
        background: #00B484;
        border-radius: 44rpx;
        font-size: 32rpx;
        color: #FFFFFF;
        margin:0 32rpx;
        &::after{
          border: none !important;
        }
      }
    }
  }
</style>
