
<template>

  <view class="title-input clear-float" :class="{'bdt':defaultConfig.bdt,'bdb':defaultConfig.bdb && !child}">
    <view class="l-l" :style="{'color': defaultConfig.titleColor}">
      {{defaultConfig.label}}
      <text class="star" v-if="defaultConfig.required">*</text>
    </view>
    <view class="l-r" @click="showClick()">
      <input :disabled="true"
        v-model="form.data.val"
        placeholder="请选择省市"
      />
    </view>

    <template v-if="provinceVisible">
      <lvCascadeSelect :level='2' :list="cityOptions" :value='selectAddress' :selectedKey='selectAddressValue' useName='label' @change="handleClick"></lvCascadeSelect>
    </template>
  </view>

</template>

<script>
import lvCascadeSelect from './lv-cascade-select/index.vue'
import store from '@/store'

export default {
  components:{
    lvCascadeSelect
  },
  data() {
    return {
      cityOptions:[],
      selectAddress:['all','all','all'],
      hospitalListText:'全国',
      selectAddressValue:"all",
      provinceVisible:false,

      form: {
        data: {
          val: ''
        }
      },
      array: [],
      index: 0,
      defaultConfig: {
        bdt: false,
        bdb: true,
        titleColor: '#333',
        textColor: '#333',
        label: '单行输入框',
        name: 'input',
        required: false,
      }
    }
  },
  watch: {
    cData: {
      handler(val) {
        // this.watchDataMain(val)
      },
      deep: true
    },
    config: {
      handler(val) {
        this.copyConfig()
      },
      deep: true
    }
  },
  props: {
    child:{
      type:Boolean,
      default:false,
    },
    // 是否禁用disable
    disabled: {
      type: Boolean,
      default() {
        return false
      }
    },
    // 初始值传值，用于回显
    cData: {
      type: [String, Number],
      required: false,
      default() {
        return ''
      }
    },
    // 参数设置
    config: {
      type: Object,
      required: false,
      default: () => {
        return {}
      }
    }
  },
  computed: {

  },
  mounted() {
    this.getAddress()
    this.copyConfig()
    if (!this.$validate.isNull(this.cData)) {
      this.watchDataMain(this.cData)
    }
  },
  methods: {
    async getAddress(){
      const data = await store.dispatch(
        "template/getAddressData",
        {}
      );
      // const ar = data;
      this.cityOptions = data;
    },
    showClick(){
      if (this.disabled) return

      this.provinceVisible = !this.provinceVisible
    },
    handleClick(obj){
      console.log('obj',obj)
      const { close,select,selectLabel} = obj;
      if(close){
        this.selectAddress = [
          ...select
        ]
        this.selectAddressLabel = selectLabel;
        this.selectAddressValue = select[1]
        let hospitalListText = selectLabel[0] + '/' + selectLabel[1]
        // this.province = selectLabel[0]
        // this.city = selectLabel[1]
        // this.district = selectLabel[2]

        this.form.data.val = hospitalListText

        this.$emit('updateForm', { key: '' + this.config.name, value: this.form.data.val })

        this.provinceVisible = false
        // this.init()
      }

    },
    /**
       * 初始化拷贝config对象
       */
    copyConfig() {
      const that = this
      const obj = that.config
      Object.keys(obj).forEach(function(key) {
        that.defaultConfig[key] = obj[key]
      })
    },
    /**
       * 监听Data主逻辑方法
       */
    watchDataMain(val) {
      this.form.data.val = val
    },
  }
}
</script>

<style lang="scss" scoped>
  .color-topicC{
    color: $topicC !important;
  }
  .title-input{
    background-color:#ffffff;
    .l-l{
      line-height: 88upx;
      color: #333333;
      font-weight: 600;
      font-size:30upx;
    }
    .l-r{
      margin-bottom: 5px;
      display: flex;
      align-items: center;
          padding-bottom: 20rpx;
      input {
        height: 80upx;
        line-height: 80upx;
        color: #333;
        font-size: 32upx;
        text-align: left;
        display: inline-block;
        vertical-align: middle;
        width: calc(100% - 64upx);
        border-bottom: 1upx solid $borderColor;
        border-radius: 10upx;
        padding: 0 20rpx;

      }
      input.disabled{
          background-color: #dbdbdb;
          color: #fff;
      }
      .util{
        width: 64upx;
        font-size: 28upx;
            overflow: hidden;
            text-align: center;

      }
    }
  }
  .star{
    color: #F85E4C;
    padding-top: 6upx;
    font-size: 32upx;
    display: inline-block;
        margin-left: 10rpx;
  }
</style>
