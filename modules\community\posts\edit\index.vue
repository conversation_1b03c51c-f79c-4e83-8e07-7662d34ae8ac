<template>
  <page>
    <view slot="content" class="body-main">
      <view class="line-bottom">
        <title-selector
          :config="config.materialType"
          v-model="form.materialType"
          @updateForm="updateForm"
        />
      </view>
      <view class="line-bottom">
        <title-input
          v-model="form.title"
          :config="config.title"
          :placeholder="config.title.placeholder"
        ></title-input>
      </view>
      <view class="" style="padding-top: 32upx" v-show="config.content.show">
        <title-textarea
          style="width: 100%"
          v-model="form.content"
          :config="config.content"
          :placeholder="config.content.placeholder"
        />
      </view>
      <title-img
        v-show="config.videosPath.show"
        :config="config.videosPath"
        fileType="video"
        @returnFn="
          (obj) => {
            imgReturnFn(obj, 'videosPath');
          }
        "
        :cData="cDataVideosPath"
      >
        <view slot="upload-after" class="upload-after">
          <text class="upload-tips">上传短视频</text>
          <text class="upload-tips">助您更清晰描述</text>
        </view>
      </title-img>
      <view class="" style="padding-top: 32upx" v-show="config.intro.show">
        <title-textarea
          style="width: 100%"
          v-model="form.intro"
          :config="config.intro"
          :placeholder="config.intro.placeholder"
        />
      </view>
      <title-img
        :config="config.imagesPath"
        @returnFn="
          (obj) => {
            imgReturnFn(obj, 'imagesPath');
          }
        "
        :cData="cDataImagesPath"
      ></title-img>
      <view class="line-bottom">
        <title-selector
          :config="config.circleClassifyId"
          v-model="form.circleClassifyId"
        >
          <view slot="placeholder" class="selector-placeholder">
            选择合适的圈子，吸引同批小伙伴
          </view>
        </title-selector>
      </view>
      <button class="submit-btn" type="primary" @click="submit">
        {{ submitLoading ? "发布中..." : "发布" }}
      </button>
    </view>
  </page>
</template>

<script>
import { mapState } from 'vuex'
import TitleInput from "@/components/business/module/v1/title-input/index"
import TitleTextarea from "@/components/business/module/v1/title-textarea/index"
import TitleSelector from "@/components/business/module/v1/title-selector/index"
import TitleImg from "@/components/business/module/title-img/index"
export default {
  components: {
    TitleInput,
    TitleTextarea,
    TitleImg,
    TitleSelector
  },
  data() {
    return {
      file_ctx: this.file_ctx,
      cDataImagesPath: [],
      cDataVideosPath: [],
      form: {
        title: '',
        content: '',
        circleClassifyId: '',
        isAnonymity: 2,
        imagesPath: '',
        materialType: null,
        videosPath: ''
      },
      config: {
        title: {
          show: true,
          disabled: false,
          required: true,
          maxlength: 25,
          placeholder: '输入标题，获取更多关注与帮助',
          label: '',
          showLabel: false,
          label: '帖子标题',
          style: "border: none;font-size: 31rpx;font-weight: 500;line-height: 42rpx;outline: none;"
        },
        content: {
          show: true,
          style: 'border: none',
          disabled: false,
          required: true,
          placeholder: '求助需求描述：',
          label: '帖子正文',
          showLabel: false,
          inputStyle: 'border: none;font-size: 27rpx;line-height: 42rpx;min-height: 300rpx;padding: 0;',
          maxlength: -1
        },
        imagesPath: {
          show: true,
          padding: '32rpx 0'
        },
        circleClassifyId: {
          show: true,
          label: '发帖圈子',
          required: false, // 这里应该为必选，只是为了兼容样式
          array: [],
          titleWidth: '150rpx',
          showJump: false
        },
        materialType: {
          show: true,
          name: 'materialType',
          label: '帖子类型',
          required: true, // 这里应该为必选，只是为了兼容样式
          array: [{ value: 1, label: '图文' }, { value: 2, label: '短视频' }],
        },
        intro: {
          style: 'border: none',
          disabled: false,
          placeholder: '短视频简介：',
          showLabel: false,
          inputStyle: 'border: none;font-size: 27rpx;line-height: 42rpx;min-height: 150rpx;padding: 0;',
          maxlength: -1
        },
        videosPath: {
          padding: '32rpx 0',
          required: true
        },
      },
      submitLoading: false,
      postmessageId: ''
    }
  },
  onLoad() {
    const query = this.$Route.query
    this.postmessageId = query.id
    this.getCircleclassifyQueryList()
    this.getPostmessageDetail()
  },
  computed: {
    ...mapState('user', {
      accountId: state => state.accountId
    })
  },
  methods: {
    updateForm(obj) {
      if (obj.key === 'materialType') {
        Object.keys(this.config).forEach(key => {
          if (['content'].includes(key)) {
            this.config[key].show = obj.value !== 2
          } else if (['videosPath', 'intro'].includes(key)) {
            this.config[key].show = obj.value === 2
          }
        })
      }
    },
    async getPostmessageDetail() {
      const res = await this.$api.postmessage.postmessageQueryOne({ id: this.postmessageId, accountId: this.accountId })
      const { title, content, circleClassifyId, isAnonymity, imagesPath, videosPath, materialType } = res.data
      this.form = {
        ...res.data,
        title,
        content,
        circleClassifyId,
        isAnonymity,
        imagesPath: imagesPath ? imagesPath.split(',') : [],
        videosPath: videosPath ? videosPath.split(',') : []
      }
      if (imagesPath) {
        this.cDataImagesPath = imagesPath.split(',').map(item => {
          return { url: this.file_ctx + item, filePath: item, dir: item }
        })
      }
      if (videosPath) {
        this.cDataVideosPath = videosPath.split(',').map(item => {
          return { url: this.file_ctx + item, filePath: item, dir: item }
        })
      }
      this.updateForm({
        key: 'materialType',
        value: materialType
      })
    },
    async submit() {
      if (this.submitLoading) return
      await this.$ext.user.authCommunityFansInfo()
      // 表单校验
      const config = JSON.parse(JSON.stringify(this.config))
      config.circleClassifyId.required = true
      for (const key in this.form) {
        if (!this.$validate.isNull(config[key])) {
          if (config[key].show !== false && config[key].required && !this.form[key]) {
            this.$uniPlugin.toast(`${config[key].label}不得为空！`)
            return
          }
        }
      }
      this.submitLoading = true
      const param = {
        ...this.form,
        imagesPath: !this.$validate.isNull(this.form.imagesPath) ? this.form.imagesPath.join(',') : '',
        videosPath: !this.$validate.isNull(this.form.videosPath) ? this.form.videosPath.join(',') : '',
      }
      this.$api.postmessage.postmessageUpdate(param).then(res => {
        this.$uniPlugin.toast(res.msg)
        this.submitLoading = false
        this.clearForm()
        this.$navto.back(1)
      }).catch(() => {
        this.submitLoading = false
      })
    },
    clearForm() {
      this.form = {
        title: '',
        content: '',
        circleClassifyId: '',
        isAnonymity: 2,
        imagesPath: ''
      }
      this.cDataImagesPath = []
      this.cDataVideosPath = []
      this.$forceUpdate()
    },
    // 获取圈子列表
    getCircleclassifyQueryList() {
      const that = this
      that.$api.circleclassify.circleclassifyQueryList().then(res => {
        const data = res.data.map(item => {
          return {
            ...item,
            value: item.id,
            label: item.name
          }
        })
        that.config.circleClassifyId.array = data
      })
    },
    imgReturnFn(obj, key) {
      if (key === 'videosPath') {
        this.cDataVideosPath = obj
      } else if (key === 'imagesPath') {
        this.cDataImagesPath = obj
      }
      this.form[key] = obj.map(item => item.dir)
    }
  }
}
</script>

<style scoped lang="scss">
.body-main {
  height: 100%;
  overflow: auto;
  background-color: #fff;
  padding: 0 34upx;
  .line-bottom {
    border-bottom: 2upx solid #e5e5e5;
  }
  .selector-placeholder {
    font-size: 27upx;
    font-weight: 500;
    color: #7f7f7f;
    line-height: 42upx;
    background-color: #fff;
    background: #eeeeee;
    @include rounded(20upx);
    display: block;
    padding: 0 12upx;
  }
  .flex-box {
    display: flex;
    align-items: center;
  }
  .anonymous-wrapper {
    display: flex;
    align-items: center;
    padding-top: 38upx;
    .anonymous-checkbox {
      padding: 2upx 10upx;
      background: #eeeeee;
      @include rounded(20upx);
    }
    .anonymous-tips {
      font-size: 27upx;
      font-weight: 500;
      color: #7f7f7f;
      line-height: 42upx;
      margin-left: 12upx;
    }
  }
  .submit-btn {
    background: #00d29d;
    margin-top: 100upx;
    @include rounded(40upx);
    width: 680upx;
    height: 80upx;
    font-size: 33upx;
    font-weight: 500;
    color: #ffffff;
    line-height: 80upx;
  }
}
</style>