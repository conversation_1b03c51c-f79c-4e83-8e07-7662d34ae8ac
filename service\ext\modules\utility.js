import wechatPlugin from '@/common/util/wechat-plugin'
import common from '@/common/util/main'
import uniPlugin from '@/common/util/uni-plugin'
/**
 * 针对uni组件中间层代码 获取数据进行二次数据清洗、加工、转化，特别指API
 */
export default {
  /**
   * 获取授权地理位置
   * @param params {}
   * @param resolve
   * @param reject
   */
  getSetting() {
    return new Promise((resolve, reject) => {
      uniPlugin.getSetting((res) => {
        if (res && res.authSetting && res.authSetting.hasOwnProperty('scope.userLocation')) {
          if (res.authSetting['scope.userLocation']) {
            resolve()
          } else { // 拒绝授权，打开授权设置
            uniPlugin.openSetting(() => {
              reject()
            },
            () => {

            })
          }
        } else {
          uniPlugin.openSetting(() => {
            reject()
          },
          () => {

          })
        }
      }
      )
    })
  },
  /**
   * 调起客户端扫码界面，扫码成功后返回对应的结果。
   * @param callback
   */
  scanCode(callback) {
    // #ifdef H5
    wechatPlugin.scanQRCode(callback)
    // #endif
    // #ifndef H5
    uniPlugin.scanCode(callback)
    // #endif
  },
  /**
   * 获取当前地址
   * @param params {}
   * @param resolve
   * @param reject
   */
  getLocation() {
    return new Promise((resolve, reject) => {
      // #ifdef H5
      wechatPlugin.getLocation((res) => {
        console.log('定位成功1:', res)
        resolve(res)
      }, (res) => {
        console.log('定位失败1:', res)
        uniPlugin.toast('定位失败')
        common.setKeyVal('system', 'longitudeAndLatitude', {}, true)
        reject()
      })
      // #endif
      // #ifndef H5
      // that.getSetting().then(() => {
      uniPlugin.authorize('scope.userLocation', () => {
        uniPlugin.getLocation((res) => {
          console.log('定位成功1:', res)
          resolve(res)
        },
        (res) => {
          console.log('定位失败1:', res)
          // uniPlugin.toast('定位失败')
          common.setKeyVal('system', 'longitudeAndLatitude', {}, true)
          reject()
        })
      },
      (res) => {
        uniPlugin.toast('地理位置授权失败')
        reject()
      })
      // }).catch(() => {
      //
      // })
      // #endif
    })
  }
}
