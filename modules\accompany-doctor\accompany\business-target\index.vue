<template>
  <page>
    <view slot="content" class="body-main">
      <!-- 筛选栏 -->
      <!-- <view class="filter-bar-wrapper">
        <view class="filter-bar">
          <view class="filter-item" @click="selectFilter('time')">
            <text class="filter-text">交易时间</text>
            <image class="filter-arrow" :src="iconRightArrow" mode="aspectFit"></image>
          </view>
        </view>
      </view> -->
      
      <view class="business-content">
        <view v-if="employeeRankStatisticList && employeeRankStatisticList.length > 0">
          <view class="business-item" v-for="(item,index) in employeeRankStatisticList" :key="index">
            <view class="business-item-l">
              <view class="my-img" v-if="index == 0"><image :src="file_ctx + oneUrl"></image></view>
              <view class="my-img" v-else-if="index == 1"><image :src="file_ctx + twoUrl"></image></view>
              <view class="my-img" v-else-if="index == 2"><image :src="file_ctx + threeUrl"></image></view>
              <view class="item-img" v-else><text class="num-text">{{index + 1}}</text></view>
              <view class="profile"><image :src="file_ctx + (item.avatar || 'static/image/business/icon-adult-boy.png')"></image></view>
              <view class="name">{{ item.employeeName }}</view>
            </view>
            <view class="business-item-r">{{ item.orderCount }} 单</view>
          </view>
        </view>
        <view v-else class="empty-data">
          <image :src="file_ctx + 'static/image/business/empty-data.png'" class="empty-img"></image>
          <view class="empty-text">暂无数据</view>
        </view>
      </view>
    </view>
    
    <!-- 弹窗选择器 -->
    <view class="picker-container">
      <timePicker ref="timePicker" :value="timeMap" type="daterange" @change="handleTimeChange" :show="timePickerVisible"
        @cancel="timePickerVisible = false"></timePicker>
    </view>
  </page>
</template>

<script>
  import uniNavBar from '@/components/uni/uni-nav-bar/uni-nav-bar.vue'
  import timePicker from '../../components/uni-datetime-picker/uni-datetime-picker.vue'
  
  export default {
    components: {
      uniNavBar,
      timePicker
    },
    data(){
      return{
        file_ctx: this.file_ctx,
        iconRightArrow: this.file_ctx + "image/business/hulu-v2/icon-right-arrow.png",
        oneUrl:'static/image/business/accompany-doctor/icon-accompany-performance-one.png',
        twoUrl:'static/image/business/accompany-doctor/icon-accompany-performance-two.png',
        threeUrl:'static/image/business/accompany-doctor/icon-accompany-performance-three.png',
        employeeRankStatisticList:[],
        timeMap: [],
        timePickerVisible: false,
        isLoading: false
      }
    },
    onLoad(){
      this.timeMap = this.getDatesOfTodayAndLastWeek();
      this.accompanyBookEmployeeRankStatistic()
    },
    mounted(){
      
    },
    methods:{
      // 获取当前日期和上个月的日期
      getDatesOfTodayAndLastWeek() {
        const dates = [];
        const today = new Date();
        const lastMonth = new Date(today);
        lastMonth.setMonth(lastMonth.getMonth() - 1);

        if (lastMonth.getMonth() === 11 && today.getMonth() === 0) {
          lastMonth.setFullYear(lastMonth.getFullYear() - 1);
        }

        lastMonth.setDate(1);
        dates.push(this.$common.formatDate(lastMonth, 'yyyy-MM-dd'))
        dates.push(this.$common.formatDate(today, 'yyyy-MM-dd'))
        return dates;
      },
      // 选择筛选条件
      selectFilter(type) {
        console.log('触发');
        
        if (type === 'time') {
          this.timePickerVisible = true;
          // 使用setTimeout替代直接调用，避免时间选择器的显示问题
          this.$nextTick(() => {
            if (this.$refs.timePicker) {
              this.$refs.timePicker && this.$refs.timePicker.show();
            }
          })
        }
      },
      // 时间选择回调
      handleTimeChange(e) {
        console.log('时间选择变化：', e);
        if (e && e.length === 2) {
          const [start, end] = e;
          this.timeMap = [start, end];
          this.timePickerVisible = false;
          // 重新获取数据
          this.accompanyBookEmployeeRankStatistic();
        } else {
          this.timeMap = this.getDatesOfTodayAndLastWeek();
          this.timePickerVisible = false;
        }
      },
      // 数据统计-陪诊师排名Top10
      accompanyBookEmployeeRankStatistic(){
        if (this.isLoading) return;
        this.isLoading = true;
        
        const providerId = this.$common.getKeyVal('user', 'providerId',true)        
        let params = {
          startTime: this.timeMap[0] + ' 00:00:00',
          endTime: this.timeMap[1] + ' 23:59:59',
          providerId: providerId
        }
        
        uni.showLoading({
          title: '加载中...'
        });
        
        this.$api.accompanyDoctor.accompanyBookEmployeeRankStatistic(params).then(res=>{
          this.employeeRankStatisticList = res.data || [];
          this.isLoading = false;
          uni.hideLoading();
        }).catch(err => {
          console.error('获取陪诊师排名数据失败:', err);
          this.isLoading = false;
          uni.hideLoading();
          uni.showToast({
            title: '获取数据失败',
            icon: 'none'
          });
        })
      }
    },
 }
</script>

<style lang='scss' scoped>
.body-main{
  background: #F4F6FA;
  padding: 32rpx;
}
.img{
  width: 100%;
  height: 100%;
}

/* 筛选栏样式 */
.filter-bar-wrapper {
  position: relative;
  z-index: 1;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.filter-bar {
  display: flex;
  width: 100%;
  height: 88rpx;
}

.filter-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  position: relative;
}

.filter-text {
  font-size: 28rpx;
  color: #333333;
  margin-right: 8rpx;
  font-weight: 500;
}

.filter-arrow {
  width: 24rpx;
  height: 24rpx;
  transform: rotate(90deg);
  position: relative;
  top: 2rpx;
}

/* 弹窗选择器样式 */
.picker-container {
  position: absolute;
  width: 0;
  height: 0;
  overflow: hidden;
}

::v-deep .uni-date-x {
  height: 0 !important;
  width: 0 !important;
  overflow: hidden !important;
}

::v-deep .uni-date {
  height: 0 !important;
  width: 0 !important;
  overflow: hidden !important;
}

.business-content{
  padding: 32rpx 24rpx;
  background: #FFFFFF;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  .business-item{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32rpx;
    .business-item-l{
      display: flex;
      align-items: center;
      .my-img{
        display: flex;
        width: 44rpx;
        height: 44rpx;
        image{
          width: 100%;
          height: 100%;
        }
      }
      .item-img{
        width: 44rpx;
        height: 44rpx;
        background: #D3F8EF;
        border-radius: 6rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        .num-text {
          font-size: 26rpx;
          color: #00B484;
          font-weight: 500;
        }
      }
      .profile{
        width: 64rpx;
        height: 64rpx;
        background-color: skyblue;
        border-radius: 50%;
        margin: 0 12rpx 0 26rpx;
        overflow: hidden;
        image{
          width: 100%;
          height: 100%;
        }
      }
      .name{
        font-size: 28rpx;
        color: #1D2029;
        line-height: 40rpx;
      }
    }
    .business-item-r{
      font-size: 28rpx;
      color: #1D2029;
      line-height: 40rpx;
    }
    &:last-child{
      margin-bottom: 0;
    }
  }
  .empty-data {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60rpx 0;
    .empty-img {
      width: 200rpx;
      height: 200rpx;
      margin-bottom: 20rpx;
    }
    .empty-text {
      font-size: 28rpx;
      color: #A5AAB8;
    }
  }
}
</style>