<template>
  <page>
    <view slot="content" class="page-content">
      <m-nav-bar title="客户详情" left-icon="left" @clickLeft="back" />
      
      <view class="customer-detail">
        <!-- 客户基本信息 -->
        <view class="customer-header">
          <view class="customer-avatar">
            <image :src="defaultAvatar" class="avatar-image" mode="aspectFill"></image>
          </view>
          <view class="customer-info">
            <view class="customer-name">{{customerInfo.name || '用户名称'}}</view>
            <view class="customer-phone">{{customerInfo.phone || '手机号码'}} <text class="copy-btn" @tap="copyPhone">复制</text></view>
          </view>
        </view>
        
        <!-- 服务记录 -->
        <view class="service-record">
          <view class="service-title">服务记录 {{recordCount || 0}} 条</view>
          
          <!-- 使用scroll-refresh组件 -->
          <scroll-refresh
            bgColor='white'
            class="scroll-refresh-main"
            :isShowEmptySwitch="false"
            :fixed="false"
            :up="upOption"
            :down="downOption"
            @returnFn="returnFn"
            @scrollInit="scrollInit"
          >
            <view v-if="recordList && recordList.length > 0" class="record-list">
              <!-- 服务记录列表 -->
              <view 
                v-for="(record, index) in recordList" 
                :key="index"
                class="record-item"
                @click="goToDetail(record)"
              >
                <view class="record-header">
                  <view class="service-name">{{ record.title || '陪诊服务' }}</view>
                  <view class="service-status-wrapper">
                    <view class="service-status" :class="'status-' + record.statusCode">{{ record.status }}</view>
                  </view>
                </view>
                <view class="record-details">
                  <view class="detail-row">
                    <text class="detail-label">服务费用：</text>
                    <text class="detail-value price">{{ record.price }}</text>
                  </view>
                  <view class="detail-row">
                    <text class="detail-label">预约时间：</text>
                    <text class="detail-value">{{ record.date }}</text>
                  </view>
                  <view class="detail-row" v-if="record.hospital">
                    <text class="detail-label">就诊医院：</text>
                    <text class="detail-value">{{ record.hospital || '-' }}</text>
                  </view>
                  <view class="detail-row">
                    <text class="detail-label">订单号：</text>
                    <text class="detail-value">{{ record.id || '-' }}</text>
                  </view>
                </view>
              </view>
            </view>
          </scroll-refresh>
          
          <!-- 空数据提示 -->
          <view class="empty-record" v-if="isDataLoaded && (!recordList || recordList.length === 0)">
            <view class="empty-text">暂无订单</view>
          </view>
        </view>
      </view>
    </view>
  </page>
</template>

<script>
import distributionApi from '@/service/api/modules/distribution.js'
import serverOptions from '@/config/env/options'

export default {
  data() {
    return {
      customerId: '',
      userId: '',
      customerInfo: {},
      recordList: [],
      recordCount: 0,
      defaultAvatar: this.$static_ctx + 'image/business/hulu-v2/icon-list-avatar-default-grild.png',
      isDataLoaded: false,
      mescroll: null,
      // 下拉刷新配置
      downOption: {
        auto: false
      },
      upOption: {
        auto: false,
        empty: {
          top: 0,
          zIndex: 999,
        },
        onScroll: true
      }
    }
  },
  onLoad(options) {
    // 直接使用传递的客户信息
    if (options.id) {
      this.customerId = options.id;
      this.userId = options.userId; // 保存客户的userId
      
      // 设置客户基本信息，对中文进行解码
      this.customerInfo = {
        name: decodeURIComponent(options.name) || '用户名称',
        phone: options.phone || '手机号码',
        bindTime: options.bindTime || ''
      };
      
      // 初始化列表
      this.init();
    } else {
      uni.showToast({
        title: '客户信息不存在',
        icon: 'none'
      });
      setTimeout(() => {
        this.back();
      }, 1500);
    }
  },
  methods: {
    back() {
      uni.navigateBack();
    },
    
    // 初始化滚动组件
    scrollInit(scroll) {
      scroll.optUp.page.num = 1;
      scroll.optUp.page.size = 10;
      this.mescroll = scroll;

      // 在初始化滚动组件后立即触发下拉刷新
      this.init();
    },
    
    // 初始化页面数据
    init() {
      // 标记数据尚未加载完成
      this.isDataLoaded = false;

      this.$nextTick(() => {
        if (this.mescroll) {
          this.mescroll.triggerDownScroll();
        }
      });
    },
    
    // 下拉刷新回调
    returnFn(obj) {
      // 避免重复加载
      setTimeout(() => {
        // 显示加载中提示
        uni.showLoading({
          title: '加载中...'
        });
        
        // 构建请求参数
        const params = {
          current: obj.pageNum,
          size: obj.pageSize,
          condition: {
            userId: this.userId, // 使用传入的用户ID
            providerId: serverOptions.providerId || '' 
          }
        };
        
        console.log('查询参数：', params);
        
        // 调用陪诊订单分页接口获取客户订单
        this.$api.accompanyDoctor.accompanybookQuery(params).then(res => {
          uni.hideLoading();
          
          if (res.code === 0) {
            // 接口调用成功
            const { records, total } = res.data;
            
            // 更新记录总数
            this.recordCount = total || 0;
            
            // 第一页时清空列表
            if (obj.pageNum === 1) {
              this.recordList = [];
            }
            
            // 处理记录数据
            if (Array.isArray(records) && records.length > 0) {
              const newRecords = records.map(item => ({
                id: item.id,
                title: item.serviceName || '未知服务',
                date: this.formatDate(item.createTime),
                price: item.payPrice ? `¥${(item.payPrice / 100).toFixed(2)}` : '¥0.00',
                status: this.getOrderStateText(item.orderState),
                statusCode: item.orderState,
                hospital: item.hospitalName || '',
                startTime: item.startTime,
                endTime: item.endTime
              }));
              
              this.recordList = [...this.recordList, ...newRecords];
            }
            
            // 标记数据已加载
            this.isDataLoaded = true;
            
            // 调用回调函数
            obj.successCallback && obj.successCallback(records, {
              curPageLen: records.length,
              totalPage: Math.ceil(total / obj.pageSize),
              totalSize: total
            });
          } else {
            // 接口调用失败
            console.log('获取服务记录失败:', res.msg);
            uni.showToast({
              title: '获取服务记录失败',
              icon: 'none'
            });
            
            // 标记数据已加载
            this.isDataLoaded = true;
            
            // 回调空数据
            obj.successCallback && obj.successCallback([], {
              curPageLen: 0,
              totalPage: 0,
              totalSize: 0
            });
          }
        }).catch(err => {
          uni.hideLoading();
          console.error('获取服务记录失败:', err);
          uni.showToast({
            title: '网络异常，请稍后重试',
            icon: 'none'
          });
          
          // 标记数据已加载
          this.isDataLoaded = true;
          
          // 回调空数据
          obj.successCallback && obj.successCallback([], {
            curPageLen: 0,
            totalPage: 0,
            totalSize: 0
          });
        });
      }, 500);
    },
    
    // 获取订单状态文字
    getOrderStateText(state) {
      const stateMap = {
        1: '待接入',
        2: '待支付',
        3: '待派单',
        4: '待接单',
        5: '待服务',
        6: '服务中',
        7: '已完成',
        8: '已取消'
      };
      return stateMap[state] || '未知状态';
    },
    
    // 格式化日期
    formatDate(timestamp) {
      if (!timestamp) return '';
      
      try {
        // 尝试将时间戳转换为日期
        const date = new Date(timestamp);
        
        // 检查是否是有效日期
        if (isNaN(date.getTime())) return '';
        
        // 格式化为 YYYY-MM-DD HH:MM 格式
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        
        return `${year}-${month}-${day} ${hours}:${minutes}`;
      } catch (error) {
        console.error('日期格式化错误:', error);
        return '';
      }
    },
    // 添加新的方法 goToDetail
    goToDetail(item) {
      // 跳转到陪诊记录详情页，使用与订单中心页面相同的导航方式
      this.$navto.push('accompanyDetails', {
        id: item.id,
        isServer: true,
        from: 'myCustomer' // 添加来源参数，防止循环跳转
      });
    },
    copyPhone() {
      if (!this.customerInfo.phone) {
        uni.showToast({
          title: '手机号码为空',
          icon: 'none'
        });
        return;
      }
      
      uni.setClipboardData({
        data: this.customerInfo.phone,
        success: () => {
          uni.showToast({
            title: '复制成功',
            icon: 'success'
          });
        }
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.page-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.customer-detail {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 客户基本信息 */
.customer-header {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background-color: #fff;
  margin-bottom: 20rpx;
}

.customer-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  overflow: hidden;
  background-color: #f0f0f0;
  margin-right: 20rpx;
}

.avatar-image {
  width: 100%;
  height: 100%;
}

.customer-info {
  flex: 1;
}

.customer-name {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 10rpx;
  font-weight: 500;
}

.customer-phone {
  font-size: 28rpx;
  color: #666;
}

.copy-btn {
  display: inline-block;
  margin-left: 20rpx;
  color: #2196f3;
  font-size: 26rpx;
}

/* 服务记录 */
.service-record {
  flex: 1;
  background-color: #F4F6FA;
  padding: 30rpx;
  border-radius: 16rpx;
}

.service-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 30rpx;
  border-left: 8rpx solid #2196f3;
  padding-left: 20rpx;
}

.record-list {
  padding-bottom: 30rpx;
}

.empty-record {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300rpx;
  text-align: center;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  padding: 40rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #333;
  line-height: 1.5;
  margin-bottom: 30rpx;
}

.empty-tip {
  font-size: 28rpx;
  color: #999;
}

.record-item {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 20rpx;
  overflow: hidden;
  position: relative;
  z-index: 1;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.record-header {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 10rpx;
  border-bottom: 1rpx solid #F5F5F5;
  margin-bottom: 20rpx;
}

.service-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}

.service-status-wrapper {
  display: flex;
  align-items: center;
  background-color: transparent;
}

.service-status {
  font-size: 28rpx;
  color: #666666;
}

.service-status.status-1 {
  color: #00B484;  /* 待接入 */
}

.service-status.status-2 {
  color: #FF6B00;  /* 待支付 */
}

.service-status.status-3 {
  color: #FF9500;  /* 待派单 */
}

.service-status.status-4 {
  color: #1687F7;  /* 待接单 */
}

.service-status.status-5 {
  color: #1687F7;  /* 待服务 */
}

.service-status.status-6 {
  color: #2196f3;  /* 服务中 */
}

.service-status.status-7 {
  color: #00B484;  /* 已完成 */
}

.service-status.status-8 {
  color: #999999;  /* 已取消 */
}

.record-details {
  padding: 0 10rpx;
}

.detail-row {
  display: flex;
  margin-bottom: 16rpx;
}

.detail-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #999999;
  flex-shrink: 0;
}

.detail-value {
  flex: 1;
  font-size: 28rpx;
  color: #666666;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.price {
  color: #FF5500;
  font-weight: 500;
}
</style>
