import MessageDefault from "./MessageDefault"

export default class HandleGetChatList extends MessageDefault {
    constructor (websocket) {
        super()
        this.websocket = websocket
    }

    match (dataJson) {
        const { GET_CHATLIST_CMD } = this.chatCmd
        return dataJson.cmd === GET_CHATLIST_CMD
    }

    processMessage (dataJson) {
        this.$common.setKeyVal('chat', 'chatlist', dataJson.data, false)
    }
}