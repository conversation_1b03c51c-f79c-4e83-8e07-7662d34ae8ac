<template>
  <view class="guidanceBox">
    <view class="guidanceCard">
      <view class="headerTab">
        <!-- 服务名称 -->
        <view class="serverTitle">
          <image class="serverIcon" :src="file_ctx + accompanybookOne.serviceDetailImg" mode=""></image>
          <view class="serviceName">
            <view class="serverNameT">{{accompanybookOne.serviceName}}</view>
            <view class="serverTime"><text class="tabTitle">陪诊时间:</text>{{timestampToDateTime(accompanybookOne.startTime)}}~{{timestampToDateTime(accompanybookOne.endTime)}}</view>
            <view class="employee">
              <text class="tabTitle">陪诊师</text>:
              <image class="avatar" :src="file_ctx + avatar" mode=""></image>
              {{accompanybookOne.employeeName}}
              </view>
          </view>
          <view class="payNums"><text class="paySign">￥</text>{{accompanybookOne.payPrice / 100}}</view>
        </view>
        <!-- 订单信息 -->
        <view class="orderMap">
          <view class="serverNameT">订单信息</view>
          <view class="orderItem">
            <text class="orderTitle">订单号</text>
            <view class="">
              {{accompanybookOne.id}}
              <text class="copy" @click="copyOrderId(accompanybookOne.id)">复制</text>
            </view>
          </view>
          <view class="orderItem">
            <text class="orderTitle">创建时间</text>
            {{timestampToDateTime(accompanybookOne.createTime)}}
          </view>
          <view class="orderItem">
            <text class="orderTitle">支付时间</text>
            {{timestampToDateTime(accompanybookOne.payTime)}}
          </view>
          <view class="orderItem">
            <text class="orderTitle">支付方式</text>
            {{getOrderType()}}
          </view>
        </view>
        <!-- 服务记录 -->
        <view class="serviceLog" v-if="clockMap.length > 2" @click="openServiceLog">
          医嘱反馈
          <image class="serviceLogIcon" :src="openBox" mode=""></image>
        </view>
      </view>
    </view>
    <view class="serviceEvaluation">
      <view class="serviceEvaluationTitle">
        <view class="serviceEvaluationTc">服务评价</view>
        <view class="serviceEvaluationIconBox" >
          <image class="serviceEvaluationIcon" :src="file_ctx + serviceList[accompanybookOne.star - 1].activeUrl" mode=""></image>
          <view class="serviceEvaluationContent" :style="{background:serviceList[accompanybookOne.star - 1].backColor}">{{serviceList[accompanybookOne.star - 1].name}}</view>
        </view>
      </view>
      <view class="default" v-if="!accompanybookOne.star">
        <image class="defaultImg" :src="defaultImg" mode=""></image>
        <view class="defaultTitle">暂无相关数据~</view>
        <view class="defaultBtn" @click="$refs.guardDetailPopup.open()">立即评价</view>
      </view>
      <view class="serviceEvaluationContent">
        {{accompanybookOne.comment}}
      </view>
    </view>
    <!-- 评价弹窗 -->
    <uni-popup ref="guardDetailPopup" type="bottom">
      <view class="guard-detail-content">
        <view class="title">服务评价</view>
        <view class="error" @click="$refs.guardDetailPopup.close()"><image :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-service-error.png'"></image></view>
        <view class="guard-list">
          <view class="guard-item" v-for="(item,index) in serviceList" :key="index" @click="changeSelecter(index)">
            <view class="item-img" v-if="currentIndex == index"><image class="img" :src="file_ctx + item.activeUrl"></image></view>
            <view class="item-img" v-else><image class="img" :src="file_ctx + item.url"></image></view>
            <view class="text">{{ item.name }}</view>
          </view>
        </view>
        <view class="evaluate-content" :style="{display:isShow?'block':'none'}"><textarea v-model="comment" placeholder-style="color:#A5AAB8" placeholder="请输入评价内容"/></view>
        <view class="guard-detail-bottom" @click="handleSubmit"><button>提交</button></view>
      </view>
    </uni-popup>
    <!-- 服务记录弹窗 -->
    <uni-popup ref="serviceLogPopup" type="center">
      <view class="service-log-popup">
        <view class="service-log-header">
          <text class="service-log-title">医嘱反馈</text>
          <view class="service-log-close" @click="$refs.serviceLogPopup.close()">
            <image :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-service-error.png'"></image>
          </view>
        </view>
        <scroll-view
          class="service-log-content"
          scroll-y="true"
          show-scrollbar="true"
        >
          <!--
          <view class="clock-start" v-for="(item,index) in clockMap" :key="index">
            <view class="clock-start-dot"></view>
            <view class="clock-start-line" v-if="index !== clockMap.length-1"></view>
            <view class="clock-start-content">
              <view class="time">{{timestampToDateTime(item.createTime)}}</view>
              <view class="title">
                {{['签到','接到客户','签出'][index]}}打卡，{{ index === 2 ? '服务结束' : '开始服务' }}
              </view>
              <view class="clock-start-b" v-if="item.imagePath">
                <image :src="file_ctx + imageItem" class="clock-start-item" v-for="(imageItem, imageIndex) in item.imagePath.split(',')" :key="imageIndex" @click="previewImage(item.imagePath.split(','), imageIndex)"></image>
              </view>
            </view>
          </view>
          -->
          <!-- 医嘱反馈 -->
          <view class="clock-start medical-advice">
            <view class="clock-start-dot"></view>
            <view class="clock-start-content">
              <view class="time" v-if="medicalAdvice">{{timestampToDateTime(medicalAdvice.createTime)}}</view>
              <view v-if="medicalAdvice" class="advice-wrapper">
                <view class="advice-content-box" :class="{'expanded': isAdviceExpanded}">
                  <text class="title-prefix">医嘱反馈：</text>
                  <text class="advice-content">{{medicalAdvice.advice}}</text>
                  <view class="expand-btn" v-if="shouldShowExpandBtn && !isAdviceExpanded" @click="isAdviceExpanded = true">
                    <text>展开</text>
                  </view>
                  <view class="collapse-btn" v-if="isAdviceExpanded" @click="isAdviceExpanded = false">
                    <text>收起</text>
                  </view>
                </view>
              </view>
              <view v-else class="no-advice-wrapper">
                <text class="no-advice-text">暂未上传医嘱</text>
              </view>
              <view class="clock-start-b" v-if="medicalAdvice && medicalAdvice.image">
                <image :src="file_ctx + imageItem" class="clock-start-item" v-for="(imageItem, imageIndex) in medicalAdvice.image.split(',')" :key="imageIndex" @click="previewImage(medicalAdvice.image.split(','), imageIndex)"></image>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  import uniPopup from '@/components/uni/uni-popup'
  export default{
    components:{
      uniPopup
    },
    props:{
      accompanybookOne:{
        type:Object,
        default:{}
      },
      showGuardDetail:{
        type:Boolean,
        default:false
      }
    },
    data(){
      return {
        file_ctx:this.file_ctx,
        $static_ctx:this.$static_ctx,
        currentServer:{},
        defaultImg: this.$static_ctx + "image/business/hulu-v2/default.png",
        serviceList:[
          {backColor:'#FFD3D2',name:'非常不满意',url:'static/image/business/accompany-doctor/icon-accompany-be-very-dissatisfied.png',activeUrl:'static/image/business/accompany-doctor/icon-accompany-be-very-dissatisfied-active.png'},
          {backColor:'#FFD4BA',name:'不满意',url:'static/image/business/accompany-doctor/icon-accompany-be-dissatisfied.png',activeUrl:'static/image/business/accompany-doctor/icon-accompany-be-dissatisfied-active.png'},
          {backColor:'#FFE1B1',name:'一般',url:'static/image/business/accompany-doctor/icon-accompany-ordinary.png',activeUrl:'static/image/business/accompany-doctor/icon-accompany-ordinary-active.png'},
          {backColor:'#FFE1B1',name:'满意',url:'static/image/business/accompany-doctor/icon-accompany-satisfaction.png',activeUrl:'static/image/business/accompany-doctor/icon-accompany-satisfaction-active.png'},
          {backColor:'#FFE1B1',name:'非常满意',url:'static/image/business/accompany-doctor/icon-accompany-very-satisfaction.png',activeUrl:'static/image/business/accompany-doctor/icon-accompany-very-satisfaction-active.png'}
        ],
        serverCurrent:{},
        clockMap:[],
        avatar:'',
        currentIndex:null,
        isShow:false,
        comment:'',
        openBox: this.$static_ctx + "image/business/hulu-v2/openBox.png",
        medicalAdvice: null,
        isAdviceExpanded: false
      }
    },
    computed:{
      getShowGuardDetail(){
        if(this.showGuardDetail){
          setTimeout(()=>{
            this.$refs.guardDetailPopup.open();
          },1000)
        }
        return this.showGuardDetail;
      },
      async getAccompanybookOne(){
        let {data:{records:[serverCurrent]}} = await this.$api.accompanyDoctor.getAccompanyservicePage({
          condition:{serviceId:this.accompanybookOne.serviceId},
        })
        this.serverCurrent = serverCurrent;
        let {data:{avatar}} = await this.$api.accompanyDoctor.getAccompanyemployeeQueryOne({
          id:this.accompanybookOne.employeeId
          })
        this.avatar = avatar;
        // 获取打卡记录
        let {data:clockMap} = await this.$api.accompanyDoctor.signinlogGetLogList({
          businessType:4,
          businessId:this.accompanybookOne.id
        })
        console.log('clockMap',clockMap);
        this.clockMap = clockMap;
        // 获取医嘱信息
        await this.getMedicalAdvice();
        return this.accompanybookOne
      },
      shouldShowExpandBtn() {
        // 判断文本是否超过一定长度，决定是否显示展开按钮
        return this.medicalAdvice && this.medicalAdvice.advice && this.medicalAdvice.advice.length > 80;
      }
    },
    async mounted() {

    },
    methods:{
      getOrderType(){
        let {comboPay,orderType,payType} = this.accompanybookOne;
        if(comboPay) return '套餐支付';
        if(orderType) return orderType === 1 ? '线下支付' : '联合订单支付';
        return ['','微信支付','支付宝支付'][payType];
      },
      copyOrderId(data){
        uni.setClipboardData({
        	data,
        	success: function () {
        		console.log('success');
        	}
        });
      },
      // 提交
      async handleSubmit(){
        console.log('currentIndex',this.currentIndex);
        console.log('comment',this.comment);
        let {data} = await this.$api.accompanyDoctor.accompanybookComment({
          id:this.accompanybookOne.id,
          comment:this.comment,
          star:this.currentIndex + 1
        })
        this.$refs.guardDetailPopup.close();
        this.$emit('loadData')
      },
      changeSelecter(index){
        this.isShow = true
        this.currentIndex = index
      },
      async clearOrder(){
        let {data:{records,total}} = await this.$api.accompanyDoctor.accompanybookCancel({
          id:this.accompanybookOne.id,
          cancelReason: "用户主动取消",
          refundAmount: this.accompanybookOne.payPrice || 0 // 全额退款，单位分
        });
        uni.navigateBack()
      },
      timestampToDateTime(timestamp) {
        // 创建一个新的Date对象，传入的时间戳是以毫秒为单位的
        var date = new Date(timestamp);
        // 获取年、月、日、时、分、秒
        var year = date.getFullYear();
        var month = ("0" + (date.getMonth() + 1)).slice(-2); // 月份是从0开始的，所以+1，并补零
        var day = ("0" + date.getDate()).slice(-2); // 获取天数，并补零
        var hour = ("0" + date.getHours()).slice(-2); // 获取小时，并补零
        var minute = ("0" + date.getMinutes()).slice(-2); // 获取分钟，并补零
        var second = ("0" + date.getSeconds()).slice(-2); // 获取秒数，并补零

        // 返回格式化的字符串
        return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
      },
      openServiceLog() {
        this.$refs.serviceLogPopup.open();
      },
      // 添加图片预览功能
      previewImage(urls, current) {
        uni.previewImage({
          urls: urls.map(url => this.file_ctx + url),
          current: current
        });
      },
      // 获取医嘱信息
      async getMedicalAdvice() {
        try {
          let { data } = await this.$api.accompanyDoctor.accompanyadviceQueryAccompany({
            accompanyId: this.accompanybookOne.id
          });
          if (data) {
            this.medicalAdvice = data;
          }
        } catch (error) {
          console.error('获取医嘱失败', error);
        }
      }
    }
  }
</script>

<style lang="scss">
  .default{
    margin-top: 32rpx;
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    .defaultImg{
      width: 204rpx;
      height: 204rpx;
    }
    .defaultTitle{
      width: 100%;
      margin-top: 24rpx;
      font-weight: 400;
      font-size: 24rpx;
      color: #4E5569;
      text-align: center;
    }
  }
  .defaultBtn{
    width: 208rpx;
    height: 64rpx;
    background: #FFFFFF;
    border-radius: 36rpx;
    border: 1rpx solid #00B484;
    font-weight: 500;
    font-size: 26rpx;
    color: #00B484;
    text-align: center;
    line-height: 64rpx;
    margin-top: 24rpx;
  }
  .serviceEvaluation{
    width: 686rpx;
    margin-top: 16rpx;
    padding: 32rpx 24rpx;
    box-sizing: border-box;
    background: #FFFFFF;
    border-radius: 16rpx;
    .serviceEvaluationTitle{
      display: flex;
      justify-content: space-between;
      .serviceEvaluationTc{
        font-weight: 500;
        font-size: 28rpx;
        color: #1D2029;
      }
      .serviceEvaluationIconBox{
        display: flex;
        align-items: center;
        .serviceEvaluationIcon{
          width: 48rpx;
          height: 48rpx;
          z-index: 1;
        }
        .serviceEvaluationContent{
          height: 44rpx;
          border-radius: 28rpx;
          padding-left: 50rpx;
          padding-right: 20rpx;
          margin-left: -34rpx;
        }
      }
    }
    .serviceEvaluationContent{
      font-weight: 400;
      font-size: 26rpx;
      color: #1D2029;
    }
  }
  .service-record{
    padding:32rpx 24rpx;
    padding-top: 0;
    background: #FFFFFF;
    border-radius: 16rpx;
    .title{
      font-size: 28rpx;
      color: #1D2029;
      line-height: 40rpx;
    }
    .clock-start{
      .clock-start-h{
        display: flex;
        justify-content: space-between;
        .clock-start-h-l{
          .time{
            font-size: 28rpx;
            color: #4E5569;
            line-height: 40rpx;
          }
          .title{
            margin-top: 16rpx;
            font-size: 28rpx;
            color: #1D2029;
            line-height: 40rpx;
          }
        }
        .clock-start-h-r{
          display: flex;
          align-items: center;
          justify-content: center;
          width: 184rpx;
          height: 64rpx;
          background: #FFFFFF;
          border-radius: 8rpx;
          border: 1rpx solid #D9DBE0;
          font-size: 28rpx;
          color: #1D2029;
        }
      }
      .clock-start-b{
        display: flex;
        flex-wrap: wrap;
        padding: 16rpx;
        background: #F4F6FA;
        border-radius: 16rpx;
        overflow: hidden;
        .clock-start-item{
          width: 170rpx;
          height: 170rpx;
          border-radius: 16rpx;
          margin-right: 16rpx;
          margin-bottom: 18rpx;
          &:nth-child(3n){
            margin-right: 0;
          }
        }
      }
    }
  }
  .tabTitle{
    font-weight: 400;
    font-size: 24rpx;
    color: #4E5569;
  }
  .guidanceBox{
    width: 100vw;
    padding: 0 32rpx;
    box-sizing: border-box;
  }
  .img{
    width: 100%;
    height: 100%;
  }
  .guidanceCard{
    width: 686rpx;
    background: #FFFFFF;
    border-radius: 16rpx;
    margin-top: 32rpx;
    .headerTab{
      width: 686rpx;
      background: #FFFFFF;
      border-radius: 16rpx;
      padding: 24rpx;
      box-sizing: border-box;
      padding-bottom: 0;
      overflow: visible;
      .changeServer{
        width: 148rpx;
        height: 52rpx;
        background: #FFFFFF;
        border-radius: 36rpx;
        border: 1rpx solid #D9DBE0;
        margin-left: auto;
      }
    }
  }
  .serverNameT{
    font-weight: 600;
    font-size: 32rpx;
    color: #1D2029;
  }
  .orderMap{
    padding-bottom: 32rpx;
    border-bottom: 2rpx solid #EAEBF0;
    .serverNameT{
      margin: 32rpx 0;
    }
  }
  .serviceLog{
    padding: 32rpx 0;
    display: flex;
    justify-content: space-between;
    font-weight: 500;
    font-size: 28rpx;
    color: #1D2029;
    .serviceLogIcon{
      width: 32rpx;
      height: 32rpx;
      background: #FFFFFF;
    }
  }
  .serverTitle{
    display: flex;
    padding-bottom: 32rpx;
    border-bottom: 2rpx solid #EAEBF0;
    .paySign{
      font-weight: 400;
      font-size: 22rpx;
      color: #FF5500;
    }
    .payNums{
      font-weight: 500;
      font-size: 36rpx;
      color: #FF5500;
    }
    .serverIcon{
      width: 144rpx;
      height: 144rpx;
      background: #D8D8D8;
      border-radius: 12rpx;
      border: 1rpx solid #D9DBE0;
      margin-right: 20rpx;
      flex-shrink: 0;
    }
    .serviceName{
      font-weight: 500;
      font-size: 32rpx;
      color: #1D2029;
      display: flex;
      flex-wrap: wrap;
      .serverTime{
        font-weight: 400;
        font-size: 24rpx;
        color: #1D2029;
      }
      .employee{
        font-weight: 400;
        font-size: 22rpx;
        color: #1D2029;
        display: flex;
        align-items: center;
        .avatar{
          width: 32rpx;
          height: 32rpx;
          margin-left: 20rpx;
          border-radius: 50%;
          margin-right: 8rpx;
        }
      }
      .signal{
        font-weight: 400;
        font-size: 22rpx;
        color: #FF5500;
      }
      .serverNum{
        font-weight: 500;
        font-size: 36rpx;
        color: #FF5500;
      }
      .tag{
        font-weight: 400;
        font-size: 20rpx;
        color: #868C9C;
      }
    }
  }
  .orderItem{
    width: 100%;
    font-weight: 400;
    font-size: 26rpx;
    color: #4E5569;
    display: flex;
    justify-content: space-between;
    .orderTitle{
      font-weight: 400;
      font-size: 26rpx;
      color: #1D2029;
    }
    .copy{
      font-weight: 400;
      font-size: 26rpx;
      color: #316EAB;
      margin-left: 8rpx;
    }
  }
  .guidanceTitle{
    font-weight: 500;
    font-size: 36rpx;
    color: #1D2029;
  }
  .guidanceTwoTitle{
    font-weight: 400;
    font-size: 26rpx;
    color: #868C9C;
  }
  .clearBtn{
    width: 684rpx;
    height: 88rpx;
    background: #FFFFFF;
    border-radius: 44rpx;
    border: 1rpx solid #D9DBE0;
    font-weight: 400;
    font-size: 32rpx;
    color: #4E5569;
    text-align: center;
    line-height: 88rpx;
    margin: 32rpx auto;
  }
  .guard-detail-content{
    position: relative;
    background: #F4F6FA;
    padding: 32rpx 0rpx 386rpx;
    border-radius: 24rpx 24rpx 0rpx 0rpx;
    .title{
      display: flex;
      justify-content: center;
      font-size: 32rpx;
      color: #1D2029;
      line-height: 44rpx;
    }
    .error{
      position: absolute;
      right: 32rpx;
      top: 38rpx;
      width: 32rpx;
      height: 32rpx;
      image{
        width: 100%;
        height: 100%;
      }
    }
    .guard-list{
      display: flex;
      justify-content: center;
      margin-top: 80rpx;
      .guard-item{
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        margin-right: 58rpx;
        &:last-child{
          margin-right: 0;
        }
        .item-img{
          width: 64rpx;
          height: 64rpx;
        }
        .text{
          margin-top: 20rpx;
          font-size: 20rpx;
          color: #4E5569;
        }
      }
    }
    .evaluate-content{
      width: 622rpx;
      height: 228rpx;
      background: #FFFFFF;
      border-radius: 16rpx;
      border: 1rpx solid #D9DBE0;
      margin: 32rpx auto 0;
      textarea{
        height: calc(100% - 48rpx);
        width: calc(100% - 48rpx);
        padding: 24rpx;
        font-size: 26rpx;
        color: #1D2029;
      }
    }
    .guard-detail-bottom{
      position: absolute;
      height: 180rpx;
      width: 100%;
      bottom: 0;
      padding-top: 24rpx;
      background-color: #fff;
      button{
        display: flex;
        align-items: center;
        justify-content: center;
        height: 88rpx;
        background: #00B484;
        border-radius: 44rpx;
        font-size: 32rpx;
        color: #FFFFFF;
        margin:0 32rpx;
        &::after{
          border: none !important;
        }
      }
    }
  }
  .service-log-popup{
    width: 686rpx;
    height: 80vh;
    background: #FFFFFF;
    border-radius: 24rpx;
    display: flex;
    flex-direction: column;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
    overflow: hidden;

    .service-log-header{
      padding: 32rpx 24rpx;
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #EAEBF0;
      background: #FAFBFC;

      .service-log-title{
        font-size: 36rpx;
        color: #1D2029;
        font-weight: 600;
      }

      .service-log-close{
        width: 40rpx;
        height: 40rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 50%;
        background: rgba(0,0,0,0.05);
        transition: all 0.3s;

        &:active {
          background: rgba(0,0,0,0.1);
        }

        image{
          width: 24rpx;
          height: 24rpx;
        }
      }
    }

    .service-log-content{
      flex: 1;
      padding: 32rpx 24rpx;
      box-sizing: border-box;
      height: calc(80vh - 100rpx);
      position: relative;

      .clock-start{
        margin-bottom: 60rpx;
        position: relative;
        display: flex;

        &:last-child {
          margin-bottom: 32rpx;
        }

        .clock-start-dot {
          width: 24rpx;
          height: 24rpx;
          border-radius: 50%;
          background: #00B484;
          margin-right: 24rpx;
          margin-top: 10rpx;
          flex-shrink: 0;
          z-index: 2;
          box-shadow: 0 0 0 6rpx rgba(0, 180, 132, 0.1);
        }

        .clock-start-line {
          position: absolute;
          left: 12rpx;
          top: 34rpx;
          width: 2rpx;
          background: #E6E8EB;
          height: calc(100% + 30rpx);
          z-index: 1;
        }

        .clock-start-content {
          flex: 1;

          .time{
            font-size: 28rpx;
            color: #4E5569;
            line-height: 40rpx;
            font-weight: 500;
          }

          .title{
            margin-top: 12rpx;
            font-size: 30rpx;
            color: #1D2029;
            line-height: 42rpx;
            font-weight: 500;
          }

          .clock-start-b{
            display: flex;
            flex-wrap: wrap;
            padding: 24rpx;
            margin-top: 24rpx;
            background: #F8F9FB;
            border-radius: 16rpx;
            overflow: hidden;

            .clock-start-item{
              width: 170rpx;
              height: 170rpx;
              border-radius: 12rpx;
              margin-right: 16rpx;
              margin-bottom: 16rpx;
              box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);
              border: 1rpx solid #EAEBF0;
              transition: all 0.3s;

              &:active {
                transform: scale(0.98);
                opacity: 0.9;
              }

              &:nth-child(3n){
                margin-right: 0;
              }
            }
          }
        }
      }

      /* 医嘱反馈样式 */
      .medical-advice {
        margin-bottom: 20rpx;

        .advice-wrapper {
          margin: 20rpx 0;
        }

        .advice-content-box {
          position: relative;
          max-height: 160rpx;
          overflow: hidden;
          transition: max-height 0.3s ease;
          word-break: break-word;

          &.expanded {
            max-height: none;
          }
        }

        .title-prefix {
          font-weight: bold;
          color: #333;
        }

        .advice-content {
          color: #666;
          word-break: break-all;
        }

        .expand-btn, .collapse-btn {
          position: absolute;
          right: 0;
          bottom: 0;
          background: linear-gradient(to left, #fff 50%, rgba(255, 255, 255, 0) 100%);
          padding-left: 40rpx;
          text {
            color: #26cc91;
          }
        }
        .no-advice-wrapper {
          margin: 20rpx 0;
          padding: 20rpx 0;
        }
        .no-advice-text {
          color: #999;
          font-size: 28rpx;
        }
      }
    }
  }
</style>
