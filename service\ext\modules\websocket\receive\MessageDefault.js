import chatCmd from '@/constant/modules/chat'
import common from '@/common/util/main'
import $env from '@/config/env'
import validate from '@/common/util/validate'

const defaultAvatar = $env.file_ctx + 'static/image/system/avatar/icon-default-avatar.png' // 默认头像
export default class HandleMessage {
    constructor () {
        this.chatCmd = chatCmd
        this.$common = common
        this.$validate = validate
        // Object.keys(util).forEach(key => {
        //     this[key] = util[key]
        // })
    }

    /**
     * 转化msg的数据魔板
     * @param item
     * @param msgCloudStatus
     * @returns {{readStatus: (number|*), msgCloudStatus, createTime: *, fromUserId: (default.methods.chatItem.userId|*), hasBeenSentId, isItMe: boolean, fromUserHeadImg: *, contentType: number, content: default.methods.formData.content}}
     */
    etlMsgItem(item, msgCloudStatus) {
        const chatItem = this.$common.getKeyVal('chat', 'chatItem', false)
        let isItMe = item.fromUserId + '' == chatItem.userId + '' //true此条信息是我发送的 false别人发送的
        let userHeadImg = ''
        if (isItMe) {
            userHeadImg = defaultAvatar
        } else {
            userHeadImg = chatItem.headPath
        }

        let content = item.msgContent
        try {
            content = JSON.parse(content)
        } catch {

        }

        let result = {
            ...item,
            hasBeenSentId: item.id, //已发送过去消息的id
            content: this.$validate.judgeTypeOf(content) === 'Object' ? content.text : content,
            text: this.$validate.judgeTypeOf(content) === 'Object' ? content.text : content,
            attach: (this.$validate.judgeTypeOf(content) === 'Object' && [5,6,8].includes(item.msgType)) ? content.attach : '',
            desc: (this.$validate.judgeTypeOf(content) === 'Object' && item.msgType == 8) ?
                JSON.parse(content.desc) : (this.$validate.judgeTypeOf(content) === 'Object' && [5,6].includes(item.msgType)) ?
                content.desc : '',
            urlPath: (this.$validate.judgeTypeOf(content) === 'Object' && [5,6].includes(item.msgType)) ? content.urlPath : '',
            minAppid: (this.$validate.judgeTypeOf(content) === 'Object' && [5,6].includes(item.msgType)) ? content.minAppid : '',
            fromUserHeadImg: userHeadImg, //用户头像
            fromUserId: item.fromUserId,
            isItMe: isItMe,
            createTime: item.createTime,
            // contentType: 1, // 1文字文本 2语音
            msgCloudStatus: msgCloudStatus, //消息发送结果状态，1是正常，2是发送中，待回调，3是等待回调失败
            readStatus: item.readStatus,
        }
        return result
    }
}