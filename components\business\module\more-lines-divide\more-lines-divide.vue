<template>
  <view>
    <view style="position: relative" v-if="isHide">
      <view class="dt-content" :style="'-webkit-line-clamp:'+line">
        <text class="content">
          <slot>{{ dt ? dt : '' }}</slot>
        </text>
      </view>
      <template v-if="enableButton&&lines>line">
        <view class="button-show" v-if="!displayToggleAsText" @tap="isHide = false" >
          <text style="color:#00C1B0;font-size: 28rpx;">{{ expandText }}</text>
          <uni-icons color="#00C1B0" class="fold-bottom-arrow" type="arrowdown" size="24rpx"></uni-icons>
        </view>
        <image v-else  @tap="isHide = false" class="button-expansionIcon" :src="expansion" mode=""></image>
      </template>
    </view>
    <view v-else>
      <view>
        <text class="content">
          <slot>{{ dt ? dt : '' }}</slot>
        </text>
      </view>
      <view class="fold-hint" v-if="foldHint">
        <view @tap="isHide = true">
          {{ foldHint }}
          <uni-icons class="fold-up-arrow" color="#00C1B0" type="arrowup" size="24rpx"></uni-icons>
        </view>
      </view>
    </view>
    <view>
      <text class="placeholder">
        {{ placeholder }}
      </text>
    </view>
  </view>
</template>

<script>
import uniIcons from '@/components/uni/uni-icons/uni-icons'
export default {
  components: {
    uniIcons
  },
  data() {
    return {
      expansion:this.$static_ctx + 'image/business/dm-v2/expansion.png',
      // 是否隐藏多余行。初始状态不隐藏
      isHide: true,
      // 全量所占文本高度
      textHeight: 0,
      // 单行文本所占高度
      lineHeight: 1,
      // 占位文本
      placeholder: '占位'
    };
  },
  props: {
    // 判断当前展开标识使用文字还是图标
    displayToggleAsText:{
      type:Boolean,
      default:false
    },
    // 展示多少行
    line: {
      type: [Number, String],
      default: 2
    },
    // 文本
    dt: {
      type: [String],
      default: ''
    },
    enableButton: {
      type: Boolean,
      default: true
    },
    // 自定义展开提示
    expandText: {
      type: String,
      default: "展开"
    },
    // 自定义收起提示
    foldHint: {
      type: String,
      default: "收起"
    }
  },

  watch:{
    dt(){
      let that = this
      setTimeout(() => {
        let query = uni.createSelectorQuery().in(that);
        // 获取所有文本在html中的高度
        query.select('.content').boundingClientRect(data => {
          that.textHeight = data.height
        }).exec();
      }, 100)
    }
  },

  mounted() {
    if (this.enableButton) {
      let query = uni.createSelectorQuery().in(this);
      // 获取所有文本在html中的高度
      query.select('.content').boundingClientRect(data => {
        this.textHeight = data.height
      }).exec();

      // 通过占位元素获取单行文本的高度
      query.select('.placeholder').boundingClientRect(data => {
        this.lineHeight = data.height
      }).exec();
    }
    // 获取单行文本高度后，置空占位元素，使其释放占位
    this.placeholder = ''
  },
  computed: {
    // 全文本所占总行数
    lines() {
      if (!this.enableButton) {
        return this.line
      }
      return Math.floor(this.textHeight > 0 && this.lineHeight > 0 ? this.textHeight / this.lineHeight : 0)
    }
  }
}
</script>

<style scoped>
.dt-content {
  overflow: hidden;
  text-overflow: clip;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}

.button-show {
  width: 80rpx;
  position: absolute;
  right: 0;
  bottom: 4rpx;
  z-index: 0;
  text-align: right;
  background-image: linear-gradient(-180deg, rgba(233, 236, 239, 0) 50%, #FFF 80%);
  padding-top: 2rem;
}
.button-expansionIcon{
  width: 24rpx;
  height: 24rpx;
  position: absolute;
  right: -24rpx;
  bottom: 4rpx;
  z-index: 0;
}
.button-show,
.fold-hint {
  font-weight: 400;
  font-size: 24rpx;
  /* color: #316EAB; */
  color:#00C1B0;
  line-height: 34rpx;
  text-align: right;
}
.fold-bottom-arrow,.fold-up-arrow{
  position: absolute;
  right: -3rpx;
  bottom: 12rpx;
}
</style>
