<template>
  <view class="account-statement">
    <!-- 顶部导航栏 -->
    <m-nav-bar title="提现记录" left-icon="left" @clickLeft="back" />

    <!-- 日期选择区域 -->
    <view class="filter-bar-wrapper">
      <view class="filter-bar">
        <view class="filter-item" @click="selectFilter('time')">
          <text class="filter-text">时间</text>
          <image class="filter-arrow" :src="iconRightArrow" mode="aspectFit"></image>
        </view>
      </view>
    </view>

    <!-- 筛选时间范围 -->
    <view class="filter-time">
      <text class="filter-time-label">筛选时间范围：</text>
      <text class="filter-time-value">{{ filterTimeText || '全部' }}</text>
    </view>

    <!-- 提现记录列表容器 -->
    <view class="record-list">
      <scroll-refresh
        bgColor='#f7f7f7'
        class="scroll-refresh-main"
        :isShowEmptySwitch="false"
        :fixed="false"
        :up="upOption"
        :down="downOption"
        @returnFn="returnFn"
        @scrollInit="scrollInit"
      >
        <view v-if="withdrawalList && withdrawalList.length > 0">
          <view
            v-for="(record, index) in withdrawalList"
            :key="index"
            class="withdrawal-item"
            @click="goToDetail(record.id)"
          >
            <view class="withdrawal-info">
              <view class="withdrawal-row">
                <text class="withdrawal-label">提现单号：</text>
                <text class="withdrawal-value">{{ record.orderNo }}</text>
              </view>
              <view class="withdrawal-row">
                <text class="withdrawal-label">提现金额：</text>
                <text class="withdrawal-value amount">¥{{ record.amount }}</text>
              </view>
              <view class="withdrawal-row">
                <text class="withdrawal-label">提现时间：</text>
                <text class="withdrawal-value">{{ record.time }}</text>
              </view>
              <view class="status-badge" :class="{
                'status-success': record.status === '已完成',
                'status-processing': record.status === '处理中',
                'status-auditing': record.status === '审核中',
                'status-failed': record.status === '已失败'
              }">{{ record.status }}</view>
            </view>
          </view>
        </view>
      </scroll-refresh>

      <!-- 空数据提示 -->
      <view class="empty-tip" v-if="isDataLoaded && (!withdrawalList || withdrawalList.length === 0)">
        <image src="/static/image/distribution/icon-empty.png" class="empty-icon" mode="aspectFit"></image>
        <view class="empty-text">暂无提现记录</view>
      </view>
    </view>

    <!-- 日期选择弹窗 -->
    <view class="picker-container">
      <timePicker
        ref="timePicker"
        :value="timeMap"
        type="daterange"
        @change="handleTimeChange"
        :show="timePickerVisible"
        @cancel="timePickerVisible = false"
      ></timePicker>
    </view>
  </view>
</template>

<script>
import uniNavBar from '@/components/uni/uni-nav-bar/uni-nav-bar.vue'
import timePicker from '../components/uni-datetime-picker/uni-datetime-picker.vue'
import serverOptions from '@/config/env/options'

export default {
  components: {
    uniNavBar,
    timePicker
  },
  data() {
    return {
      iconRightArrow: this.$static_ctx + "image/business/hulu-v2/icon-right-arrow.png",
      // 筛选条件
      filterParams: {
        startTime: '', // 开始时间
        endTime: '', // 结束时间
      },
      // 筛选时间文本
      filterTimeText: '',
      // 日期选择
      timePickerVisible: false,
      timeMap: [], // 时间选择器的值
      // 日期选项
      dateOptions: [],
      selectedDateIndex: 0,
      // 分页参数
      currentPage: 1,
      pageSize: 10,
      hasMoreData: true,
      isRefreshing: false,
      isDataLoaded: false,
      // 记录列表
      withdrawalList: [],
      // 下拉刷新配置
      downOption: {
        auto: false
      },
      upOption: {
        auto: false,
        empty: {
          top: 0,
          zIndex: 999,
        },
        onScroll: true
      },
      mescroll: null
    }
  },
  mounted() {
    // 设置默认时间为最近一周
    this.setDefaultWeekRange();
  },
  methods: {
    // 返回上一页
    back() {
      uni.navigateBack();
    },

    // 设置默认时间为最近一周
    setDefaultWeekRange() {
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(endDate.getDate() - 6); // 默认显示7天数据（当天+前6天）

      // 格式化起止时间
      const formattedStartDate = this.formatDate(startDate);
      const formattedEndDate = this.formatDate(endDate);

      // 设置时间选择器的值
      this.timeMap = [formattedStartDate, formattedEndDate];

      // 设置筛选参数
      this.filterParams.startTime = formattedStartDate + ' 00:00:00';
      this.filterParams.endTime = formattedEndDate + ' 23:59:59';

      // 设置筛选时间文本
      this.setFilterTimeText(startDate, endDate);

      console.log('默认时间范围：', {
        start: this.filterParams.startTime,
        end: this.filterParams.endTime
      });

      // 在初始化后通过nextTick确保刷新记录
      this.$nextTick(() => {
        this.refreshRecords();
      });
    },

    // 格式化日期为YYYY-MM-DD格式
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },

    // 格式化日期时间
    formatDateTime(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },

    // 设置筛选时间文本
    setFilterTimeText(startDate, endDate) {
      if (!startDate || !endDate) {
        this.filterTimeText = '';
        return;
      }

      const formatDate = (date) => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
      };

      const startStr = formatDate(startDate);
      const endStr = formatDate(endDate);

      if (startStr === endStr) {
        this.filterTimeText = startStr;
      } else {
        this.filterTimeText = `${startStr} 至 ${endStr}`;
      }
    },

    // 初始化滚动组件
    scrollInit(scroll) {
      scroll.optUp.page.num = 1;
      scroll.optUp.page.size = 10;
      this.mescroll = scroll;

      // 在初始化滚动组件后立即触发下拉刷新
      this.refreshRecords();
    },

    // 下拉刷新回调
    returnFn(obj) {
      // 标记数据尚未加载完成
      this.isDataLoaded = false;

      setTimeout(() => {
          const codeUserInfo = this.$common.getKeyVal('user', 'codeUserInfo', true);
        // 处理筛选参数
        const condition = {
          providerId: serverOptions.providerId || null,
          userId: codeUserInfo.id
        };

        // 添加时间筛选条件
        if (this.filterParams.startTime) {
          condition.startCreateTime = this.filterParams.startTime;
        }
        if (this.filterParams.endTime) {
          condition.endCreateTime = this.filterParams.endTime;
        }

        // 构建请求参数
        const params = {
          current: obj.pageNum,
          size: obj.pageSize,
          condition: condition
        };

        // 调用提现记录分页查询接口
        this.$api.distribution.accompanypayoutQueryPage(params).then(res => {
          // 如果是第一页，清空列表
          if (obj.pageNum === 1) {
            this.withdrawalList = [];
          }

          // 从响应中获取数据
          const data = res.data?.records || [];

          // 处理数据
          if (Array.isArray(data) && data.length > 0) {
            const formattedData = data.map(item => {
              return {
                id: item.id,
                orderNo: item.id, // 使用ID作为订单号
                amount: item.amount ? (item.amount / 100).toFixed(2) : '0.00',
                status: this.getWithdrawStatus(item.state, item.withdraw),
                time: this.formatDateTime(new Date(item.createTime)) || '--'
              };
            });

            this.withdrawalList = [...this.withdrawalList, ...formattedData];
          }

          // 标记数据已加载
          this.isDataLoaded = true;

          // 调用回调
          obj.successCallback && obj.successCallback(data, {
            curPageLen: data.length,
            totalPage: res.data?.pages || 1,
            totalSize: res.data?.total || 0
          });
        }).catch(err => {
          console.error('获取提现记录失败:', err);
          this.isDataLoaded = true;

          // 调用错误回调
          obj.successCallback && obj.successCallback([], {
            curPageLen: 0,
            totalPage: 1,
            totalSize: 0
          });

          uni.showToast({
            title: '获取提现记录失败',
            icon: 'none'
          });
        });
      }, 500);
    },

    // 刷新记录
    refreshRecords() {
      this.$nextTick(() => {
        if (this.mescroll) {
          this.mescroll.triggerDownScroll();
        }
      });
    },

    // 选择筛选条件
    selectFilter(type) {
      if (type === 'time') {
        this.timePickerVisible = true;
        this.$nextTick(() => {
          this.$refs.timePicker && this.$refs.timePicker.show();
        });
      }
    },

    // 时间选择回调
    handleTimeChange(e) {
      console.log('时间选择变化：', e);
      if (e.length === 2) {
        const [start, end] = e;
        this.timeMap = [start, end];

        // 直接使用日期字符串，添加时分秒
        this.filterParams.startTime = start + ' 00:00:00';
        this.filterParams.endTime = end + ' 23:59:59';

        // 设置筛选时间文本
        const startDate = new Date(start);
        const endDate = new Date(end);
        this.setFilterTimeText(startDate, endDate);

        console.log('设置的时间范围：', {
          start: this.filterParams.startTime,
          end: this.filterParams.endTime
        });
      } else {
        this.timeMap = [];
        this.filterParams.startTime = '';
        this.filterParams.endTime = '';
        this.filterTimeText = '';
      }
      this.timePickerVisible = false;
      // 重新加载数据
      this.refreshRecords();
    },

    // 跳转到详情页面
    goToDetail(id) {
      if (!id) {
        uni.showToast({
          title: '无效的记录ID',
          icon: 'none'
        });
        return;
      }

      // 查找对应ID的记录
      const record = this.withdrawalList.find(item => item.id === id);
      if (!record) {
        uni.showToast({
          title: '记录不存在',
          icon: 'none'
        });
        return;
      }

      // 传递完整记录数据
      this.$navto.push('WithdrawalRecordDetail', {
        id: record.id,
        amount: record.amount,
        time: record.time,
        status: record.status
      });
    },

    // 获取提现状态文本
    getWithdrawStatus(state, withdraw) {
      // 先判断审核状态
      if (state === 1) return '审核中';
      if (state === 3) return '已失败';

      // 再判断提现状态
      if (withdraw === 1) return '已完成';
      if (withdraw === 2) return '已失败';
      return '处理中';
    }
  }
}
</script>

<style lang="scss" scoped>
.account-statement {
  min-height: 100vh;
  height: 100vh;
  background-color: #f7f7f7;
  display: flex;
  flex-direction: column;
}

/* 日期选择区域 */
.filter-bar-wrapper {
  background-color: #FFFFFF;
  padding: 0;
  border-bottom: 1rpx solid #F2F2F2;
  width: 100%;
}

.filter-bar {
  display: flex;
  width: 100%;
  height: 88rpx;
}

.filter-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  position: relative;
}

.filter-text {
  font-size: 28rpx;
  color: #333333;
  margin-right: 8rpx;
  font-weight: 500;
}

.filter-arrow {
  width: 24rpx;
  height: 24rpx;
  transform: rotate(90deg);
  position: relative;
  top: 2rpx;
}

/* 筛选时间显示 */
.filter-time {
  padding: 20rpx 30rpx;
  background-color: #fff;
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.filter-time-label {
  font-size: 28rpx;
  color: #666;
  margin-right: 10rpx;
}

.filter-time-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 流水记录列表容器 */
.record-list {
  flex: 1;
  position: relative;
  display: flex;
  flex-direction: column;
  /* 移除固定高度计算 */
  background-color: #f7f7f7;
  padding-top: 15rpx;
}

.scroll-refresh-main {
  flex: 1;
  height: 100%;
  position: relative;
  padding: 0 30rpx;
  box-sizing: border-box;
  background-color: #f7f7f7;
  /* 确保安卓上显示正确 */
  min-height: 500rpx;
}

.withdrawal-item {
  background-color: #ffffff;
  border-radius: 10rpx;
  margin-bottom: 15rpx;
  padding: 30rpx;
  display: flex;
  position: relative;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.03);
  box-sizing: border-box;
  width: 100%;
}

.withdrawal-info {
  flex: 1;
  min-width: 0;
  box-sizing: border-box;
}

.withdrawal-row {
  display: flex;
  margin-bottom: 15rpx;
  width: 100%;
}

.withdrawal-label {
  font-size: 28rpx;
  color: #666;
  width: 150rpx;
}

.withdrawal-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.withdrawal-value.amount {
  font-weight: 500;
  color: #FF5000;
}

.status-badge {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
  padding: 8rpx 24rpx;
  font-size: 28rpx;
  font-weight: 500;
  border-radius: 20rpx;
}

.status-success {
  background-color: rgba(0, 180, 132, 0.1);
  color: #00B484;  /* 已完成 */
}

.status-processing {
  background-color: rgba(22, 135, 247, 0.1);
  color: #1687F7;  /* 处理中 */
}

.status-auditing {
  background-color: rgba(255, 149, 0, 0.1);
  color: #FF9500;  /* 审核中 */
}

.status-failed {
  background-color: rgba(255, 80, 0, 0.1);
  color: #FF5000;  /* 已失败 */
}

/* 空数据提示 */
.empty-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #f7f7f7;
  z-index: 1;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 底部提示线 */
.bottom-line {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30rpx 0 40rpx;
  width: 100%;
}

.line {
  height: 1rpx;
  background-color: #e5e5e5;
  width: 120rpx;
}

.line-text {
  color: #999;
  font-size: 24rpx;
  margin: 0 16rpx;
}

/* 日期选择器容器 */
.picker-container {
  position: absolute;
  width: 0;
  height: 0;
  overflow: hidden;
}
</style>
