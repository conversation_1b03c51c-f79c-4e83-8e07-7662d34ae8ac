
<template>
  <view
    class="title-radio clear-float"
    :class="{
      bdt: defaultConfig.bdt,
      bdb: defaultConfig.bdb && !child,
      horizontal: horizontal,
      'mgb30':isAccuratePromotion
    }"
  >
    <template v-if="isAccuratePromotion">
      <view class="accurate-promotion-input">
        <image
          :src="ico"
          class="accurate-promotion-ico"
        >
        </image>
        <view class="accurate-promotion-label">
          {{ defaultConfig.label }}
        </view>
        <view class="accurate-promotion-right">
            <view class="flex1"></view>
            <view
              class="accurate-promotion-li li"

              v-for="(item, index) in array"
              :key="index"
              @tap="returnFn(index)"
            >
              <em
                :class="[
                  item.checked
                    ? 'icon-yijianfankui-d-ok'
                    : 'icon-yijianfankui-d',
                ]"
              ></em>
              <text :class="{ 'color-topicC': item.checked }">{{
                item.openStatus === 1 ? item.label : item.value
              }}</text>
              <input
                v-if="item.openStatus === 1 && item.checked && !item.noinput"
                :disabled="disabled"
                v-model="item.value"

                @input="returnFn(index)"
              />
            </view>
        </view>
        <view class="accurate-promotion-space"> </view>
      </view>
    </template>
    <template v-else>
      <view class="l-l" :style="{ color: defaultConfig.titleColor }">
        {{ defaultConfig.label }}
        <text class="star" v-if="defaultConfig.required">*</text>
      </view>

      <template v-if="!horizontal">
        <view class="l-r">
          <view
            class="li"
            :style="{
              'min-width': cwidth,
            }"
            v-for="(item, index) in array"
            :key="index"
            @tap="returnFn(index)"
          >
            <em
              :class="[
                item.checked ? 'icon-yijianfankui-d-ok' : 'icon-yijianfankui-d',
              ]"
            ></em>
            <text :class="{ 'color-topicC': item.checked }">{{
              item.openStatus === 1 ? item.label : item.value
            }}</text>
            <input
              v-if="item.openStatus === 1 && item.checked && !item.noinput"
              :disabled="disabled"
              v-model="item.value"
              style="margin-bottom: 24upx"
              @input="returnFn(index)"
            />
          </view>
        </view>
      </template>

      <template v-else>
        <view class="libox1">
          <view class="libox">
            <view
              class="li"
              v-for="(item, index) in array"
              :key="index"
              @tap="returnFn(index)"
            >
              <em
                :class="[
                  item.checked
                    ? 'icon-yijianfankui-d-ok'
                    : 'icon-yijianfankui-d',
                ]"
              ></em>
              <text :class="{ 'color-topicC': item.checked }">{{
                item.openStatus === 1 ? item.label : item.value
              }}</text>
              <input
                v-if="item.openStatus === 1 && item.checked && !item.noinput"
                :disabled="disabled"
                v-model="item.value"
                style="margin-bottom: 24upx"
                @input="returnFn(index)"
              />
            </view>
          </view>
        </view>
      </template>
    </template>
  </view>
</template>

<script>
export default {
  data() {
    return {
      ico: this.$static_ctx + 'image/business/applet/icon-im-business-componet-sex.png',
      form: {
        data: {
          select: "",
          // extendValue: '',
          extendValueChecked: false,
        },
      },
      array: [],
      index: 0,
      defaultConfig: {
        bdt: false,
        bdb: true,
        titleColor: "#333",
        textColor: "#333",
        label: "单选",
        name: "select",
        required: false,
        array: [],
        dicKey: "",
        openStatus: 2,
      },
      cwidth: "50%",
    };
  },
  watch: {
    cData: {
      handler(val) {
        console.log("性别", val);
        this.watchDataMain(val);
      },
      deep: true,
    },
    config: {
      handler(val) {
        this.copyConfig();
      },
      deep: true,
    },
  },
  props: {
    // 精准地推类型样式
    isAccuratePromotion: {
      type: Boolean,
      default: false,
    },
    horizontal: {
      type: Boolean,
      default: false,
    },
    child: {
      type: Boolean,
      default: false,
    },
    // 是否禁用disable
    disabled: {
      type: Boolean,
      default() {
        return false;
      },
    },
    // 初始值传值，用于回显
    cData: {
      type: [String, Number],
      required: false,
      default() {
        return "";
      },
    },
    // 参数设置
    config: {
      type: Object,
      required: false,
      default: () => {
        return {};
      },
    },
    // updatecount:{
    //   type:
    // },
  },
  computed: {},
  mounted() {
    this.getDic(() => {
      this.copyConfig();
      console.log("性别", this.cData);
      if (!this.$validate.isNull(this.cData)) {
        this.watchDataMain(this.cData);
      } else {
        for (let i = 0; i < this.array.length; i++) {
          if (this.array[i].openStatus == 1) {
            if ((this.array[i].label + "").length >= 8) {
              this.cwidth = "100%";
            }
          } else {
            if ((this.array[i].value + "").length >= 8) {
              this.cwidth = "100%";
            }
          }
        }
      }
    });
  },
  methods: {
    /**
     * 初始化拷贝config对象
     */
    copyConfig() {
      const that = this;
      const obj = that.config;
      Object.keys(obj).forEach(function (key) {
        that.defaultConfig[key] = obj[key];
      });
    },
    /**
     * @param {Array<object>}
     * 监听Data主逻辑方法
     */
    watchDataMain(val) {
      // debugger
      this.form.data.select = val;
      let isVal = true;
      let index = "";
      for (let i = 0; i < this.array.length; i++) {
        if (this.array[i].openStatus == 1) {
          if ((this.array[i].label + "").length >= 8) {
            this.cwidth = "100%";
          }
        } else {
          if ((this.array[i].value + "").length >= 8) {
            this.cwidth = "100%";
          }
        }
        this.array[i].checked = false;
        if (isVal && val === this.array[i].value) {
          isVal = false;
          this.array[i].checked = true;
          this.array[i].value = val;
          index = i;
        }
      }

      if (index !== "") {
        this.$set(this.array, index, this.array[index]);
      }
    },
    /**
     * 获取字典数据组
     */
    getDic(callBack) {
      const that = this;
      const ar = [
        {
          label: "男",
          value: "男",
        },
        {
          label: "女",
          value: "女",
        },
      ];
      that.array = ar;
      for (const i in that.array) {
        that.array[i].checked = false;
      }
      callBack();
    },
    /**
     * picker触发选中事件
     * @param e
     */
    returnFn(index) {
      // debugger
      const that = this;
      if (that.disabled) return;
      for (const i in that.array) {
        that.array[i].checked = false;
      }
      that.array[index].checked = true;
      that.$set(that.array, index, that.array[index]);
      // const obj = that.array[index]
      // that.form.data.select = obj.id
      that.form.data.val = that.array[index].value;

      that.$emit("updateForm", {
        key: "" + that.config.name,
        value: that.form.data.val,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.mgb30 {
  margin-bottom: 30upx;
}
// 精准地推
.accurate-promotion-input {
  display: flex;
  align-items: center;
  height: 88upx;
  background-color: #f8f8f8;
  border-radius: 10upx;
  overflow: hidden;
  padding: 0 20upx;
  .accurate-promotion-ico {
    height: 30upx;
    width: 30upx;
  }
  .accurate-promotion-label {
    padding: 0 20upx;
    font-weight: 550;
    min-width: 200upx
  }
  .accurate-promotion-space {
    width: 40upx;
    height: 40upx;
  }
  .accurate-promotion-right {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
  }
  .accurate-promotion-input {
    // width: 100%;
    text-align: right;
  }
  .accurate-promotion-li{
    min-width: initial !important;
    flex: initial !important;
    margin-right: 20upx;
  }
  .flex1{
    flex: 1;
  }
}

.color-topicC {
  color: $topicC !important;
}
.title-radio {
  background-color: #ffffff;
  .l-l {
    line-height: 1.5;
    color: #333333;
    font-weight: 600;
    font-size: 30upx;
  }
  .l-r {
    display: flex;
    flex-wrap: wrap;
    padding-bottom: 20upx;
  }

  .li {
    min-width: 50%;
    flex: 1;
    line-height: 88upx;
    em {
      vertical-align: middle;
      margin-right: 10upx;
      width: 24px !important;
      height: 24px !important;
    }
    .icon-yijianfankui-d {
      @include iconImg(36, 36, "/system/icon-yijianfankui-d.png");
    }
    .icon-yijianfankui-d-ok {
      @include iconImg(36, 36, "/system/icon-yijianfankui-d-ok.png");
    }
    text {
      font-size: 32upx;
      vertical-align: middle;
      color: #000;
      margin-left: 10upx;
    }
    input {
      height: 80upx;
      line-height: 80upx;
      color: #333;
      font-size: 32upx;
      text-align: left;
      display: inline-block;
      vertical-align: middle;
      width: calc(100% - 64upx);
      border: 2upx solid #ede9e9;
      border-radius: 10upx;
    }
  }
  .li:last-child {
    margin-right: 0;
  }
}
.star {
  color: #f85e4c !important;
  padding-top: 6upx;
  font-size: 32upx;
  display: inline-block;
  margin-left: 10rpx;
}

.horizontal {
  display: flex;
  align-items: center;
  .libox1 {
    flex: 1;
    display: flex;
    justify-content: flex-end;
  }
  .libox {
    display: inline-flex;
    align-items: center;
    justify-content: flex-end;
    // flex:1
  }
  .l-l {
    font-weight: 500 !important;
  }
  .l-r {
    flex: 1;
    display: flex;
    justify-content: flex-end;
  }
  .li {
    display: flex;
    align-items: center;
    min-width: auto !important;
  }

  text {
    font-size: 24upx !important;
    vertical-align: middle;
    // color: #999999;
    color: #000;
    margin-left: 10upx;
    margin-right: 20upx;
    white-space: nowrap;
  }
}
</style>
