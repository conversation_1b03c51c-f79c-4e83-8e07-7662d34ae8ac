// 移动项目总入口
import commonIndex from './common/index'
// import activityIndex from './activity/index'
// import MyWalletIndex from './my-wallet/index'
import distribution from './distribution/index'
const objectRouter = {
    commonIndex: commonIndex,
    // activityIndex:activityIndex,
    // MyWalletIndex:MyWalletIndex,
    distribution
}

const routerList = []
for (const a in objectRouter) {
    for (const b in objectRouter[a]) {
        routerList.push(objectRouter[a][b])
    }
}

export default routerList
