import MessageCheckView from './MessageCheckView'

export default class HandleSingleChatPage extends MessageCheckView {
    constructor (websocket) {
        super()
        this.formData = {
            content: '',
            limit: 20,
            index: 1
        }
        this.messageListEl = null // 聊天列表组件this 兼容小程序获取元素信息需访问组件this
        this.messageList = [] // 聊天信息列表
        this.billingKey = [] // 未读消息列表
        this.chatItem = {}
        this.websocket = websocket
    }

    match (dataJson) {
        const { GET_SINGLE_CHAT_CMD } = this.chatCmd
        return dataJson.cmd === GET_SINGLE_CHAT_CMD
    }

    processMessage (dataJson) {
        console.log('列表更新--------------------', dataJson)
        let that = this
        this.chatItem = this.$common.getKeyVal('chat', 'chatItem', false)
        this.messageListEl = this.$common.getKeyVal('chat', 'messageListEl', false)
        that.messageList = this.$common.getKeyVal('chat', 'messageList', false)
        let data = dataJson.data ? dataJson.data : []
        let arr = []
        for (let i = 0; i < data.length; i++) {
            let item = data[i]
            arr.push(that.etlMsgItem(item, 1))
        }
        
        // const messageList = [...arr, ...that.messageList];
        const messageList = this.filterResponse([...arr, ...that.messageList], 'id')
        // that.monitorScroll()
        // console.log("messageList:",messageList)
        // if (messageList.length == 0) {
        //     that.loading = false;
        //     return
        // }

        // Vue.prototype.$nextTick(() => {})

        setTimeout(() => {
            // 更新列表加载状态
            if (data.length < this.formData.limit) {
                this.$common.setKeyVal('chat', 'messageListLoadingStatus', 3, false)
            } else {
                this.$common.setKeyVal('chat', 'messageListLoadingStatus', 2, false)
            }

            this.$common.setKeyVal('chat', 'messageList', messageList, false)
            this.$common.setKeyVal('chat', 'messageInit', true, false)
            //填充数据后，视图会自动滚动到最上面一层然后瞬间再跳回bindScroll的指定位置 ---体验不是很好，后期优化
            setTimeout(() => {
                // that.messageList
                if (that.messageList.length) {
                    that.bindScroll(`#msg-${that.messageList[0].hasBeenSentId}`)
                } else {
                    setTimeout(() => {
                        uni.pageScrollTo({
                            scrollTop: 99999,
                            duration: 0
                        })
                    }, 500)
                }
                that.checkDivShow()
            }, 0);
        }, 0)
    }

    filterResponse(data,key){
        let obj = {}
        data = data.reduce((item,next) => {
            obj[next[key]] ? '' : obj[next[key]] = true && item.push(next)
            return item
        },[])
        return data
    }

    //处理滚动
    bindScroll(sel, duration = 0) {
        const query = uni.createSelectorQuery().in(this.messageListEl);
        query
            .select(sel)
            .boundingClientRect(data => {
                uni.pageScrollTo({
                    scrollTop: data && data.top,
                    duration
                });
            })
            .exec();
    }

      // 检查div 是否曝光
    async checkDivShow() {
        console.log('查看是否曝光')
        const that = this
        that.billingKey = []
        that.messageList = this.$common.getKeyVal('chat', 'messageList', false)
        this.messageListEl = this.$common.getKeyVal('chat', 'messageListEl', false)
        for (let i = 0; i < that.messageList.length; i++) {
            var item = that.messageList[i];
            if (item.readStatus == 2 && !(item.fromUserId + '' == this.chatItem.userId + '')) {
            
                // #ifdef H5
                var getDiv = document.getElementById('msg-' + item.hasBeenSentId)
                // #endif
                // #ifndef H5
                const query = uni.createSelectorQuery().in(this.messageListEl)
                var getDiv = query.select('#msg-' + item.hasBeenSentId)
                // #endif

                if (getDiv!=null){
                    if (getDiv.id !== undefined || getDiv.boundingClientRect !== undefined) {
                        let obj = {id: item.hasBeenSentId, readStatus: item.readStatus}
                        // 曝光上报
                        if (await this.isElementInViewport(getDiv) && !this.isDivDisplay(getDiv)) {
                            var isExist = false;
                            that.billingKey.some(function (item) {
                                if (item.id === obj.id) {
                                    isExist = true;
                                }
                            })
                            if (!isExist) {
                                that.billingKey.push(obj)
                            }
                        }
                    }
                }
            }
        }
        console.log("that.billingKey:",that.billingKey)
        if (that.billingKey.length >0) {
            that.sendReadStatus()
        }
    }

    /**
     * 发送已读到WS
     */
    sendReadStatus(){
        const { READ_CMD } = this.chatCmd
        const that = this
        let msgIds = []
        for (let i = 0; i < that.billingKey.length; i++) {
            let item = that.billingKey[i]
            if (item.readStatus==2 && !item.isItMe) {
                msgIds.push(item.id)
            }
        }
        if (msgIds.length === 0){
            return
        }
        const data =  {
            cmd: READ_CMD,
            data: {
                businessType: 1,
                userId: this.chatItem.userId,
                chatUserId: this.chatItem.chatUserId,
                msgIds:msgIds,
                orderId: this.chatItem.orderId
            }
        }
        this.websocket.webSocketSend(READ_CMD, data)
    }
}