import constant from '@/constant'
import env from '@/config/env'
import validate from '@/common/util/validate'
import storage from '@/common/util/storage'
import store from '@/store'
import uniPlugin from '@/common/util/uni-plugin'



// 上传文件请求头的terminalType参数在不同环境下的配置
let terminalType = ''
    // #ifdef H5
terminalType = constant.system.terminal.h5
    // #endif
    // #ifdef APP-PLUS
terminalType = constant.system.terminal.app
    // #endif
    // #ifdef MP-WEIXIN
terminalType = constant.system.terminal.miniProgram
    // #endif

/**
 * 主要工具类入口
 */
export default {
  // 时间差 格式拓展
  getDifferenceTimeTypeFormat(startTime, endTime, type) {
      const time = this.getDifferenceTime(startTime, endTime)
      if (!time) {
          return
      }
      let text = ''
      let {year,month,day,hour,minute,second} = time
      year = year === '00' ? '' : year + '年'
      month = month === '00' ? '' : month + '月'
      day = day === '00' ? '' : day + '天'
      hour = hour === '00' ? '' : hour + '小时'
      minute = minute === '00' ? '' : minute + '分钟'
      second = second === '00' ? '' : second + '秒'
      switch (type) {
          case 'year':
              text = year
              break
          case 'month':
              text = year + month
              break
          case 'day':
              text = year + month + day
              break
          case 'hour':
              text = year + month + day + hour
              break
          case 'minute':
              text = year + month + day + hour + minute
              break
          case 'second':
              text = year + month + day + hour + minute + second
              break
          default:
              text = year + month + day + hour + minute + second
      }
  
      return text
  },
  /**
   * 数字转换，将大额数字转换为万、亿、万亿等
   * @param value 数字值
   */
   bigNumberTransform (value, decimals = 2) {
    if (typeof value == 'string') {
      value = Number(value);
    }
    let unit = '';
    let k = 10000;
    let sizes = ['', '万+', '亿+', '万亿+'];
    let i;
    if(value < k){
      value =value;
    }else{
      i = Math.floor(Math.log(value) / Math.log(k));
      value = ((value / Math.pow(k, i))).toFixed(decimals);
      unit = sizes[i];
    }
    return value + unit;
  },
  /**
   * html提取文字
   * @param originString
   * @returns {*}
   */
    rexFilter (originString)  {
      // js 处理转义字符
    // http://t.zoukankan.com/keepdoit-p-15306592.html 符号对照表
      var result = originString.replace(/&([^&;]+);/g, function (matchStr, b) {
        var entity = {
          mdash:'－－',
          nbsp: ' ',
          hellip:'...',
          quot: '"',
          lt: '<',
          gt: '>',
          apos: "'",
          amp: '&',
          ldquo: '“',
          rdquo: '”'
        };
        var r = entity[b];
        return typeof r === 'string' ? r : matchStr;
      });
      result = result.replace(/<[^<>]+>/g, "") // js去除标签
      return result;
    },
    /**
     * find 方法封装用于匹配字典数组
     * 默认匹配数组格式
     * array = [{label:'',value:''}]
     * @param {Array} array 目标数组
     * @param {any} target 需要匹配的数据
     * @param {String} key 用于匹配的数据字段
     * @param {String} resultKey 需要返回的字段
     * @param {String} unfindStr 不匹配返回文字
     * **/
    findFn(array, target, key = 'value', resultKey = 'label', unfindStr = '未知') {
        if (array.find(i => i[key] === target)) {
            return array.find(i => i[key] === target)[resultKey]
        } else {
            return unfindStr
        }
    },
    /**
     * 复制工具函数
     * @param {String} value
     */
    copyFn(value, that) {
        /* #ifdef H5 */
        that.$copyText(value).then(res => {
            uniPlugin.successToast('复制成功')
        }, err => {
            uniPlugin.errorToast('复制失败')
        })

        /* #endif */
        /* #ifndef H5 */
        if (!value) return
        uni.setClipboardData({
            data: value.toString(),
            success: function() {
                uniPlugin.successToast('复制成功')
            },
            fail: function(err) {
                console.log(err)
                uniPlugin.errorToast('复制失败')
            }
        });

        /* #endif */

    },
    /**
     * 获取H5、小程序、App端状态栏高度,并设置vue原生属性
     * @param obj VUE
     */
    getHearderBar(obj) {
        uni.getSystemInfo({
            success: function(e) {
                obj.prototype.statusBar = e.statusBarHeight
                    // #ifndef MP
                if (e.platform === 'android') {
                    obj.prototype.customBar = e.statusBarHeight + 50
                } else {
                    obj.prototype.customBar = e.statusBarHeight + 45
                }
                // #endif

                // #ifdef MP-WEIXIN
                const custom = wx.getMenuButtonBoundingClientRect()
                obj.prototype.customBar = custom.bottom + custom.top - e.statusBarHeight
                    // #endif

                // #ifdef MP-ALIPAY
                obj.prototype.customBar = e.statusBarHeight + e.titleBarHeight
                    // #endif
            }
        })
    },
    /**
     * 表单验证
     * tipArr: 要验证的表单字段+名字，json格式
     * data: 要验证的表单值，json格式
     * return true: 验证通过； false: 验证不通过
     * that
     * */
    validationForm(tipArr, data) {
        for (const k in tipArr) {
            if (k && tipArr.hasOwnProperty(k)) {
                let arr = []
                let key
                let val
                let type
                if (k.indexOf('.') > -1) { // 为这类设置 tipArr['noticeSign.endTime'] = '请选择截止时间！'
                    arr = k.split('.')
                    key = arr[0]
                    val = data[arr[0]][arr[1]]
                } else {
                    arr = k.split('-')
                    key = arr[0]
                    type = arr[1]
                    val = data[key]
                }
                if (typeof val === 'string') {
                    val = val.replace(/\s+/g, '')
                }
                if (typeof val === 'undefined' || val === null || val === '' || val.length <= 0) { // 为空
                  // debugger
                    // let text = '请输入'
                    // if (tipArr[k].indexOf('请') > -1) text = ''
                    // uniPlugin.toast(text + tipArr[k])
                    uniPlugin.toast(tipArr[k])
                    return false
                } else { // 不为空
                    if (type) {
                        tipArr[k] = tipArr[k].replace('请输入', '')
                        tipArr[k] = tipArr[k].replace('请选择', '')
                        if (type === 'phone') { // 手机验证
                            const reg = /^1[0-9]{10}$/
                            if (!reg.test(val)) {
                                uniPlugin.toast(tipArr[k] + '有误')
                                return false
                            }
                        } else if (type === 'mail') { // 邮箱验证
                            const reg = /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(.[a-zA-Z0-9_-])+/
                            if (!reg.test(val)) {
                                uniPlugin.toast(tipArr[k] + '有误')
                                return false
                            }
                        } else if (type === 'IDCard') { // 身份证验证
                            const obj = validate.isCardid(val)
                            if (!obj.status) {
                                uniPlugin.toast(obj.text)
                                return obj.status
                            }
                        }
                    }
                }
            }
        }
        return true
    },
    /**
     * 获取链接末尾参数
     * @param {string}  num：1. hash
     */
    getCurPathParam(num) {
        const newObj = {}
        if (num === 1) {
            const url = location.hash // 获取链接 #/...? 后的字符串，并组合成 json 格式
            const reg = /#\/.*?\?/gi

            // if(url.indexOf('?') !== -1){
            const res = url.match(reg)
            if (res && url.match(reg).length > 0) {
                // let str = url.substr(3);
                let str = url.replace(reg, '')
                str = str.replace(/#\/$/, '')
                const strs = str.split('&')
                for (let i = 0; i < strs.length; i++) {
                    newObj[strs[i].split('=')[0]] = (strs[i].split('=')[1])
                }
            }
        } else {
            let url = location.search // 获取链接 #/...? 后的字符串，并组合成 json 格式
            if (url) {
                url = url.substr(1)
                const obj = url.split('&')
                let arr = []
                for (const key of obj) {
                    arr = key.split('=')
                    newObj[arr[0]] = arr[1]
                }
            }
        }
        return newObj
    },
    /**
     * 获取token
     */
    getToken() {
        return this.getKeyVal('user', 'token', true)
    },
    getIsLogin() {
        return this.getKeyVal('user', 'isLogin', true) !== 'false'
    },
    /**
     * 获取上传文件请求头
     */
    getUploadFileHeader() {
        const header = {
            'terminalType': terminalType,
            'accept': 'application/json; charset=utf-8',
            'authorization': this.getToken()
        }
        return header
    },
    /**
     * 截取链接末尾参数
     * url案例：http://localhost:8080/#/?openId=omZ3l1KhrolH2-Mr2FxYYg1uCqQ8&appId=wx2cd3a98f3ec208a7&unitId=6666666666
     */
    cutOutUrl() {
        // 截取链接末尾参数(保证每次打开浏览器只截取一遍)
        const isCutOutUrl = this.getKeyVal('user', 'isCutOutUrl', true)
        if (!isCutOutUrl) {
            // const num = process.env.ENV_CONFIG === 'dev' ? 1 : 2 // 开发环境和生产环境的连接不一样，获取末尾参数方式也不一样
            const firstParam = this.getCurPathParam(1) || {}
            if (firstParam.openId) {
                this.setKeyVal('user', 'isCutOutUrl', true, true)
                this.setKeyVal('user', 'firstUrlParam', firstParam, true)
            }
        }
    },
    /**
     * 存储 store
     * 注意传参格式
     * @param {String} module
     * @param {String} key
     * @param {String} val
     * @param {Boolean} storeType 默认flase(不存本地缓存) 可设置true(存本地缓存)
     */
    setKeyVal(module, key, val, storeType) {
        const fnName = module + '/Update' + key.substring(0, 1).toUpperCase() + key.substring(1)
        store.dispatch(fnName, val)
        if (storeType) {
            this.setCache(key, val)
        }
    },
    /**
     * 获取 store,sessionStorage
     * @param {String} module 模块名字
     * @param {String} key state属性
     * @param {Boolean} storeType 默认flase(不存本地缓存) 可设置true(存本地缓存)
     */
    getKeyVal(module, key, storeType) {
        let val = ''
        if (storeType) {
            val = this.getCache(key)
        } else {
            val = store.state[module][key]
        }
        // let val = store.state[module][key]
        // if (validate.isNull(val)) {
        //   val = this.getCache(key)
        // }
        return val
    },
    /**
     * 对象，深拷贝
     * @param {Object} obj
     */
    deepCloneObj(data) {
        const type = this.getType(data)
        let obj
        if (type === 'array') {
            obj = []
        } else if (type === 'object') {
            obj = {}
        } else {
            return data
        }
        if (type === 'array') {
            for (let i = 0, len = data.length; i < len; i++) {
                obj.push(this.deepCloneObj(data[i]))
            }
        } else if (type === 'object') {
            for (const key in data) {
                obj[key] = this.deepCloneObj(data[key])
            }
        }
        return obj
    },

    /**
     * 获取对象属性类型
     *
     * @param e
     * @returns {string}
     */
    getType(e) {
        if (e == null) {
            return ''
        }
        if (e.constructor === Array) {
            return 'array'
        } else if (e.constructor === Object) {
            return 'object'
        } else if (e.constructor === String) {
            return 'string'
        } else if (e.constructor === Number) {
            return 'number'
        } else if (e.constructor === Boolean) {
            return 'boolean'
        }
        return ''
    },
    /**
     * 对象，浅拷贝
     * @param {Object} obj
     */
    easyCloneObj(obj) {
        const newObj = {}
        for (const i in obj) {
            if (i && obj.hasOwnProperty(i)) {
                newObj[i] = obj[i]
            }
        }
        return newObj
    },
    /**
     * 设置缓存
     * @param key
     * @param value
     *
     * 该方法的key静态名称，都使用@/constant/modules/noun.js下，没有则补
     */
    setCache(key, value) {
        storage.setStorageSync(key, value)
    },
    /**
     * 获取缓存
     * @param key
     *
     * 该方法的key静态名称，都使用@/constant/modules/noun.js下，没有则补
     */
    getCache(key) {
        const item = storage.getStorageSync(key)
        if (validate.isNull(item)) {
            return ''
        }
        return item
    },
    /**
     * 获取缓存
     * @param {String} key
     */
    getTokenUuid() {
        let val = this.getCache('captcha')
        if (!val) {
            val = this.uuid()
            this.setCache('captcha', val)
        }
        return val
    },

	// 直播获取token,使用两次刷新一次
	getCookieUuid(id,callback){
		if(!uni.$cookieCount || uni.$cookieCount >=2){
			uni.$cookieCount = 0;
		}
		
		uni.$cookieCount += 1;
		let val = ''
		if(uni.$cookieCount == 1){
			uni.$cookieCountId = id;
			
			val = this.uuid()
			this.setCache('liveCookie', val)
			// 重新进入小程序
			this.setCache('liveCookieInfo',{
				val,
				id,
			})
		}else{
			if(uni.$cookieCountId == id){
				val = this.getCache('liveCookie')
				this.setCache('liveCookieInfo','')
				uni.$cookieCount = 0;
			}else{
				uni.$cookieCountId = id;
				
				// 刷新前重新请求接口
				if(callback){
					let liveCookieInfo = this.getCache('liveCookieInfo')
					if(liveCookieInfo && liveCookieInfo instanceof Object){
						callback({
							mainId: liveCookieInfo.id,
							source: 2,
							// cookieId: this.$common.getTokenUuid()
							cookieId: liveCookieInfo.val
						})
					}
					
				}
	
				val = this.uuid()
				this.setCache('liveCookie', val)
				// 重新进入小程序
				this.setCache('liveCookieInfo',{
					val,
					id,
				})
				
				uni.$cookieCount = 1
			}
		}
		
		return val;
		
		
	},
	
    /**
     * 获取uuid
     * @returns {string}
     */
    uuid() {
        function S4() {
            return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1)
        }
        return (S4() + S4() + '-' + S4() + '-' + S4() + '-' + S4() + '-' + S4() + S4() + S4())
    },
    /**
     * 拼接头像参数，获取头像地址
     * @param img
     * @returns {*}
     */
    getHeadImage(imgUrl) {
        if (imgUrl && imgUrl.indexOf('http') <= -1 && imgUrl.indexOf('/static/image/') <= -1) {
            imgUrl = env.file_ctx + imgUrl
        }
        return imgUrl
    },

    /**
     * 获取默认头像
     */
    getDefaultHeadImage(obj) {
        if (validate.isNull(obj)) {
            return
        }

        function etlHttpImage(obj) {
            if (obj.avatar && obj.avatar.indexOf('http') <= -1) {
                obj.avatar = env.file_ctx + obj.avatar
            }
        }
        if (obj.type.key === constant.noun.staffCode) {
            if (obj.avatar.length > 0) {
                etlHttpImage(obj)
            } else {
                if (obj.gender.key === constant.noun.gender1428) {
                    obj.avatar = '.png'
                } else if (obj.gender.key === constant.noun.gender1429) {
                    obj.avatar = '.png'
                } else {
                    obj.avatar = '.png'
                }
            }
        } else if (obj.type.key === constant.noun.parentsCode) {
            for (const bady of obj.babyList) {
                if (bady.avatar.length > 0) {
                    etlHttpImage(obj)
                } else {
                    if (bady.gender.key === constant.noun.gender1428) {
                        obj.avatar = '.png'
                    } else if (bady.gender.key === constant.noun.gender1429) {
                        obj.avatar = '.png'
                    } else {
                        obj.avatar = '.png'
                    }
                }
            }
        }
    },
    createQrCode(QRCode, paramsData, fn) {
        const text = paramsData.text || '请重试！'
        QRCode.toCanvas(paramsData.canvas, text, function(res) {
            if (fn) {
                fn(res)
            }
        })
    },
    // new[date]： 时间
    // type[string]： 需要转成的字符串时间格式：yyyy-MM-dd 、yyyy-MM-dd HH:mm:ss，默认是中文年月日
    formatDate(now = new Date(), type, symbol = '-') {
        const year = now.getFullYear() // 年
        let month = now.getMonth() + 1 // 月
        let date = now.getDate() // 日
        let hour = now.getHours() // 时
        let minute = now.getMinutes() // 分
        let second = now.getSeconds() // 秒
        let getDay = now.getDay() // 周几
        let getDayArr = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']

        // 这个月的第一天
        let one = new Date(year, month, 1).getDate()
        one = one <= 9 ? '0' + one : '' + one
            // 这个月的最后一天
        let not = new Date(new Date(year, month, 1).getTime() - 1000 * 60 * 60 * 24).getDate()
        not = not <= 9 ? '0' + not : '' + not
        if (date <= 9) {
            date = '0' + date
        } else {
            date = '' + date
        }
        if (month <= 9) {
            month = '0' + month
        } else {
            month = '' + month
        }
        if (hour <= 9) {
            hour = '0' + hour
        } else {
            hour = '' + hour
        }
        if (minute <= 9) {
            minute = '0' + minute
        } else {
            minute = '' + minute
        }
        if (second <= 9) {
            second = '0' + second
        } else {
            second = '' + second
        }
        if (type === 'yyyy-MM-dd') {
            return year + symbol + month + symbol + date
        } else if (type === 'yyyy') {
            return year
        } else if (type === 'yyyy-text') {
            return year + '年'
        } else if (type === 'MM-dd') {
            return month + symbol + date
        } else if (type === 'dd') {
            return date
        } else if (type === 'yyyy-MM') {
            return year + symbol + month
        } else if (type === 'yyyy-MM-one') {
            return year + symbol + month + symbol + one
        } else if (type === 'yyyy-MM-not') {
            return year + symbol + month + symbol + not
        } else if (type === 'yyyy-MM-dd HH:mm:ss') {
            return year + symbol + month + symbol + date + ' ' + hour + ':' + minute + ':' + second
        } else if (type === 'yyyy-MM-dd HH:mm') {
            return year + symbol + month + symbol + date + ' ' + hour + ':' + minute
        } else if (type === 'HH:mm') {
            return hour + ':' + minute
        } else if (type === 'HH:mm:ss') {
            return hour + ':' + minute + ':' + second
        } else if (type === 'array') {
            return [year, month, date, hour, minute, second]
        } else if (type === 'ymd-hm-text') { // 中文-年月日时分
            return year + '年' + month + '月' + date + '日' + ' ' + hour + ':' + minute
        } else if (type === 'ymd-week-text') { // 中文-年月日周几
            return year + '年' + month + '月' + date + '日' + ' ' + getDayArr[getDay]
        } else if (type === 'MM-dd-text') {
            return month + '月' + date + '日'
        } else {
            return year + '年' + month + '月' + date + '日'
        }
    },
    // 或者月份的第一天和最后一天 date: 时间函数（如：new Date()）
    setOneDayLastDayDate(date = new Date()) {
        let curDate = date
        let y = curDate.getFullYear()
        let m = curDate.getMonth() + 1 //本身就得+1才等于当前月份
        if (m > 12) {
            m = 1
            y++
        }
        let monthLastDay = new Date(y, m, 0).getDate()
        const oneDay = y + '-' + (m < 10 ? '0' + m : m) + '-' + '01'
        const lastDay = y + '-' + (m < 10 ? '0' + m : m) + '-' + (monthLastDay < 10 ? '0' + monthLastDay : monthLastDay)
        return { oneDay, lastDay }
    },

    // 验证短日期，形如 (2008-09-13)
    strDateTime(str) {
        const r = str.match(/^(\d{1,4})(-|\/)(\d{1,2})\2(\d{1,2})$/)
        if (r == null) return false
        const d = new Date(r[1], r[3] - 1, r[4])
        return (d.getFullYear() == r[1] && (d.getMonth() + 1) == r[3] && d.getDate() == r[4])
    },

    // 时间戳转成时间格式 时间戳为10位需*1000，再传进来，时间戳为13位的话不需乘1000
    timestampToTime(timestamp) {
        let mun = timestamp
        const str = timestamp + ''
        if (str.length < 13) {
            mun = mun * 1000
        }
        return new Date(mun)
    },

    // 时间格式转成时间戳(字符串格式‘-’，‘/’)
    timeToTimestamp(date) {
        if (!date) {
            return false
        }
        date = date.substring(0, 19)
        date = date.replace(/-/g, '/')
        const timestamp = new Date(date).getTime()
        return new Date(timestamp)
    },

    // 相隔年月日时分秒（startTime-2019-02-02）
    getRemainderTime(startTime, state) {
        let s1 = new Date(startTime.replace(/-/g, '/')),
            s2 = new Date(),
            runTime = parseInt((s2.getTime() - s1.getTime()) / 1000)
        let year = Math.floor(runTime / 86400 / 365)
        runTime = runTime % (86400 * 365)
        let month = Math.floor(runTime / 86400 / 30)
        runTime = runTime % (86400 * 30)
        let day = Math.floor(runTime / 86400)
        runTime = runTime % 86400
        const hour = Math.floor(runTime / 3600)
        runTime = runTime % 3600
        const minute = Math.floor(runTime / 60)
        runTime = runTime % 60
        const second = runTime
            // console.log(year,month,day,hour,minute,second);
            // return year + '年' + month + '月' + day + '日' + hour + '时' + minute + '分' + second + '秒'

        if (year) {
            year = year + '岁'
        } else {
            year = ''
        }
        if (month) {
            month = month + '月'
        } else {
            month = ''
        }
        if (day) {
            day = day + '天'
        } else {
            day = ''
        }
        if (state) {
            return { year: year, month: month, day: day }
        } else {
            // return year + '年' + month + '月' + day + '日' + hour + '时' + minute + '分' + second + '秒'
            return year + month + day
        }
    },

    // 两个时间相减获取相差年月日时分秒（startTime,endTime-2019-02-02）
    getDifferenceTime(startTime, endTime) {
        let s1 = new Date(startTime.replace(/-/g, '/')),
            s2 = new Date(endTime.replace(/-/g, '/')),
            runTime = parseInt((s2.getTime() - s1.getTime()) / 1000)
        const year = Math.floor(runTime / 86400 / 365)
        runTime = runTime % (86400 * 365)
        const month = Math.floor(runTime / 86400 / 30)
        runTime = runTime % (86400 * 30)
        const day = Math.floor(runTime / 86400)
        runTime = runTime % 86400
        const hour = Math.floor(runTime / 3600)
        runTime = runTime % 3600
        const minute = Math.floor(runTime / 60)
        runTime = runTime % 60
        const second = runTime
        return {
            year: year < 9 ? '0' + year : year,
            month: month < 9 ? '0' + month : month,
            day: day < 9 ? '0' + day : day,
            hour: hour < 9 ? '0' + hour : hour,
            minute: minute < 9 ? '0' + minute : minute,
            second: second < 9 ? '0' + second : second
        }
    },

    // 时间差
    getDifferenceTimeType(startTime, endTime, type) {
        const time = this.getDifferenceTime(startTime, endTime)
        if (!time) {
            return
        }
        let text = ''

        switch (type) {
            case 'year':
                text = time.year + '年'
                break
            case 'month':
                text = time.year + '年' + time.month + '月'
                break
            case 'day':
                text = time.year + '年' + time.month + '月' + time.day + '日'
                break
            case 'hour':
                text = time.year + '年' + time.month + '月' + time.day + '日' + time.hour + '时'
                break
            case 'minute':
                text = time.year + '年' + time.month + '月' + time.day + '日' + time.hour + '时' + time.minute + '分'
                break
            case 'second':
                text = time.year + '年' + time.month + '月' + time.day + '日' + time.hour + '时' + time.minute + '分' + time.second + '秒'
                break
            default:
                text = time.year + '年' + time.month + '月' + time.day + '日' + time.hour + '时' + time.minute + '分' + time.second + '秒'
        }

        return text
    },
    // 保留多少位小数
    // num：为需要保留小数的值（类型：number、string）
    // decimal：为保留多少位小数（类型：number）
    formatDecimal(num, decimal = 2) {
        num = num.toString()
        const index = num.indexOf('.')
        if (index !== -1) {
            num = num.substring(0, decimal + index + 1)
        } else {
            num = num.substring(0)
        }
        return parseFloat(num).toFixed(decimal)
    },

    // 强制转换为正数（多数用于输入价格，如-13转为13，-13.0585转成13.05保留两位小数）
    // val：参入的值(String, Number)；
    // mantissa: 小数保留多少位，默认值为2(String, Number)；
    positiveNumber(val, mantissa = 2) {
        let num = Math.abs(val)
        let digit = Math.abs(mantissa)
        const numStr = num + ''
        if (numStr.indexOf('.') !== -1) {
            return num.toFixed(digit) - 0
        }
        const str = num + ''
        if (str.indexOf('e-') !== -1) {
            num = 1
        }
        return num
    },

    // 强制转换为正数（多数用于输入价格，如-13转为13，-13.0585转成13.05保留两位小数），传入值必须为number类型---不做四舍五入
    /*
    * <input type="number" placeholder="请输金额" v-model="addPrice" maxlength="7" @input="addPriceFun" @blur="addPriceFun">
    *
    *addPriceFun(e) {
        this.addPrice = that.$common.absPositiveNumber(e.target.value)
      }
    *
    * */
    absPositiveNumber(val, decimal = 2) {
        let num = Math.abs(val).toString()
        const index = num.indexOf('.')
        if (index !== -1) {
            num = num.substring(0, decimal + index + 1)
        } else {
            num = num.substring(0)
        }
        return parseFloat(num)
    },

    // 强制转换为正整数（多数用于输入价格，如-13转为13，-13.0585转成13），传入值必须为number类型
    positiveInteger(val) {
        let num = Math.trunc(Math.abs(val))
        const str = num + ''
        if (str.indexOf('e-') !== -1) {
            num = 1
        }
        return num
    },
    // 一维数组转成二维数组
    // arr：数组（类型：Array）
    // num：多少个为一组（类型：number）
    arrayType(arr, num) {
        const imageList = []
        const arrList = JSON.parse(JSON.stringify(arr))
        for (let i = 0; i < arrList.length; i += num) {
            imageList.push(arrList.slice(i, i + num))
        }
        return imageList
    },

    // 判断是否为手机号
    isPoneAvailable(pone) {
        const myreg = /^1[0-9]{10}$/
        if (!myreg.test(pone)) {
            return false
        } else {
            return true
        }
    },

    // 倒计时
    countdown(num, fn) {
        let timeNum = num
        const countdownTime = setInterval(function() {
            time()
        }, 1000)

        function time() {
            timeNum--
            fn(timeNum)
            if (timeNum === 0) {
                clearInterval(countdownTime)
            }
        }
    },
    /**
     * 重置数组每条中同一个字段键值
     * @param arr 数组原数据
     * @param key 键值
     * @param value 值
     */
    clearChooseChecked(arr, key, value) {
        if (!validate.isNull(key) && !validate.isNull(value) && !validate.isNull(arr) && arr.length > 0) {
            for (const i in arr) {
                arr[i][key] = value
            }
        }
        return arr
    },
    /**
     * 数组对象去重并且保留相同的一个
     * @param arr 数组原数据
     * @param key 数组对象里需要去重比较的键值
     */
    noRepeatFn(arr, key) {
        let map = new Map();
        for (let item of arr) {
            if (!map.has(item[key])) {
                map.set(item[key], item)
            }
        }
        return [...map.values()]
    },
    /**
     * 获取枚举label
     * @param {*} value 枚举值
     * @param {*} list 枚举list
     * @returns label
     */
    getEnumText(value, list) {
      const itemType = list.find(item => item.value === value)
      return (itemType && Object.keys(itemType).length) ? itemType.label : ''
    },
    /**
     * 节流函数
     * @param {function} fn
     * @param {Number} interval
     */
    throttle(fn, interval = 300) {
      let canRun = true
      return function () {
        if (!canRun) return
        canRun = false
        setTimeout(() => {
          fn.apply(this, arguments)
          canRun = true
        }, interval)
      }
    },
}
