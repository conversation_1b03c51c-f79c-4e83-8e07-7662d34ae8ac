<template>
  <page>
    <view slot="content" class="main-html">
      <view class="main">
        <view class="main-logo">
          <image :src="$static_ctx + 'image/system/logo/icon-logo.png'"/>
        </view>
        <!-- 手机号 -->
        <view class="main-body">
          <view class="inp">
            <view class="inp-text">
              <view class="l">
                <image class="width-height-atuo" :src="$static_ctx + 'image/system/logo/icon-phone.png'"/>
              </view>
              <text class="r">手机</text>
            </view>
            <view class="inp-main">
              <view class="main-r" :class="inputPhone ? 'bdb-1-ccc' : 'bdb-1-topic-c'">
                <input :maxlength="11" type="number" v-model="phone" placeholder="请输入手机号码" placeholder-class="f-w-medium" @focus="phoneFocus($event, 'inputPhone')" @blur="phoneBlur($event, 'inputPhone')">
              </view>
            </view>
          </view>
        </view>
        <view class="main-body">
          <view class="inp">
            <view class="inp-text">
              <view class="l">
                <image class="width-height-atuo" :src="$static_ctx + 'image/system/logo/code.png'"/>
              </view>
              <text class="r">验证码</text>
            </view>
            <view class="verification-code">
              <view class="verification-inp" :class="inputCode ? 'bdb-1-ccc' : 'bdb-1-topic-c'">
                <input
                  :maxlength="4"
                  type="number"
                  v-model="captcha"
                  placeholder="请输入验证码"
                  placeholder-class="f-w-medium"
                  @focus="CodeFocus($event, 'inputCode')"
                  @blur="CodeBlur($event, 'inputCode')"
                >
                <text class="verification" :class="{'is-get-code': !isAllowGetNum}" @tap="getCode">{{ getVerificationTxt }}</text>
              </view>
            </view>
          </view>
        </view>
        <!-- 密码 -->
        <view class="main-body">
          <view class="inp">
            <view class="inp-text">
              <view class="l">
                <image class="width-height-atuo" :src="$static_ctx + 'image/system/logo/icon-pwd.png'"/>
              </view>
              <text class="r">密码</text>
            </view>
            <view class="password-main">
              <view :class="inputPassword ? 'bdb-1-ccc' : 'bdb-1-topic-c'">
                <input :maxlength="16" type="password" v-model="password" placeholder="请输入密码(6-20位)" placeholder-class="f-w-medium" @focus="passwordFocus($event, 'inputPassword')" @blur="passwordBlur($event, 'inputPassword')" >
                <view class="icon" v-if="password" @click="details">
                  <image style="display: block" class="width-height-atuo" :src="$static_ctx + 'image/system/logo/icon-delete.png'"/>
                </view>
              </view>
            </view>
          </view>
          <view class="m-t-80" @tap="nextgo">
            <button :disabled="!regForm.isCheckbox || phone === '' || captcha === '' || password === ''" :class="{'opacity-5': !regForm.isCheckbox || phone === '' || captcha === '' || password === ''}" type="" class="b-btn" >
              确定
            </button>
          </view>
        </view>
        <agree-with-protocol ref="checkboxRef" @returnFn = "returnFn"></agree-with-protocol>
      </view>
    </view>
  </page>
</template>

<script>
import AgreeWithProtocol from '@/modules/system/agreement-gather/components/agree-with-protocol'
import password from '@/modules/system/components/basics/form/password'
import md5 from 'js-md5'
import api from '@/service/api'
export default {
  components: {
    password,
    AgreeWithProtocol
  },
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      file_ctx: this.file_ctx,
      $static_ctx: this.$static_ctx,
      phone: '',
      code: '',
      code1: '',
      code2: '',
      code3: '',
      password: '',
      inputCode: true,
      inputPassword: true,
      inputPhone: true,
      isLogining: false,
      pwdConfig: {
        name: 'pwd',
        isShowPWD: true,
        placeholder: '请输入密码 (6-20位)'
      },
      regForm: {
        name: '',
        pwd: '',
        isCheckbox: true
      },
      codeAll: '',
      isAllowGetNum: true,
      captcha: '',
      getVerificationTxt: '获取验证码'
    }
  },
  onReady() {
    const redirect = this.$Route.query.redirect
    const redirectParams = this.$Route.query.redirectParams
    if (!this.$validate.isNull(redirect)) {
      this.redirect = redirect
    }
    if (!this.$validate.isNull(redirectParams)) {
      this.redirectParams = redirectParams
    }
  },
  mounted() {

  },
  methods: {
    returnFn(isCheckbox){
      this.regForm.isCheckbox = isCheckbox
    },
    navtoGo(url = '', obj = {}) {
      this.$navto.push(url, obj)
    },
    /**
     * 设置登录状态
     */
    setLoginStatus(flag) {
      this.isLogining = flag
    },
    async nextgo() {
      const that = this
      const tipArr = {
        'phone': '请输入手机号',
        password: '请输入密码(6-16位)',
        code: '请输入4位数的验证码'
      }
      const params = {
        phone: that.phone, // 用户填写的手机号
        password: md5(that.password), // 密码
        code: that.captcha, // 验证码

      }
      await this.$refs.checkboxRef.isCheckboxToastFn()
      // 表单验证
      if (!that.$common.validationForm(tipArr, params)) {
        return
      }
      const parameter = {
        phone: that.phone, // 用户填写的手机号
        // code: that.$common.getTokenUuid(), // 随机编码
        code: that.captcha, // 验证码
        password: (this.$rsa.jsEncryptCode(that.password)), // 密码, // 密码
        // password: md5(that.password), // 密码
        uuid: that.$common.getTokenUuid(), // 随机编码
        // serviceCode: 'nursery_service'
      }
      that.$uniPlugin.loading('正在注册中')
      that.setLoginStatus(true) // 设置登录状态
      that.$ext.user.register(parameter,(res) => {
        that.$uniPlugin.successToast('注册成功...')
        that.$ext.user.getInfoGroupPage(that, () => {
          // #ifdef MP-WEIXIN
            that.$ext.user.bindWeixinAccount({})
            that.$ext.user.usertenantrecordBindFans()
          // #endif
          that.$ext.webSocket.webSocketInit()
          that.$uniPlugin.hideLoading()
          that.setLoginStatus(false)
        })
      },(res)=>{
        that.setLoginStatus(false)
      })
    },
    getCode() {
      // this.$uniPlugin.toast('敬请期待！')
      // return
      if (!this.isAllowGetNum) return
      const isPhone = this.$validate.isMobile(this.phone)
      if (!isPhone.flag) {
        this.$uniPlugin.toast(isPhone.msg)
        return
      }
      const params = {
        phone: this.phone, // 用户填写的手机号
        // code
        uuid: this.$common.getTokenUuid(), // 随机编码, // 随机编码
        // unitId: '99' // 单位id
      }
      this.$uniPlugin.toast('发送中')
      api.sys.sendSms(params, res => {
        this.resetTime(60)
        this.$uniPlugin.toast('发送成功')
      })
    },
    passwordFocus(e, type) {
      this[type] = !this[type]
    },
    passwordBlur(e, type) {
      this[type] = !this[type]
    },
    phoneFocus(e, type) {
      this[type] = !this[type]
    },
    phoneBlur(e, type) {
      this[type] = !this[type]
    },
    CodeFocus(e, type) {
      this[type] = !this[type]
    },
    CodeBlur(e, type) {
      this[type] = !this[type]
    },
    details() {
      this.password = ''
    },
    updatePwd(key, val) {
      this.regForm[key] = val
    },
    isCheckboxFn() {
      this.regForm.isCheckbox = !this.regForm.isCheckbox
    },
    resetTime(time) {
      this.isAllowGetNum = false
      const countdownMinute = time || 30 // 1分钟倒计时

      const startTimes = new Date() // 开始时间 new Date('2016-11-16 15:21');
      const endTimes = new Date(startTimes.setSeconds(startTimes.getSeconds() + countdownMinute)) // 结束时间
      const curTimes = new Date() // 当前时间
      let surplusTimes = endTimes.getTime() / 1000 - curTimes.getTime() / 1000 // 结束毫秒-开始毫秒=剩余倒计时间

      // 进入倒计时
      let countdowns = setInterval(() => {
        surplusTimes--
        // eslint-disable-next-line no-unused-vars
        let minu = '' + Math.floor(surplusTimes / 60)
        let secd = '' + Math.round(surplusTimes % 60)
        // console.log(minu+':'+secd);
        minu = minu.length === 1 ? '0' + minu : minu
        secd = secd.length === 1 ? '0' + secd : secd
        this.getVerificationTxt = secd + '秒后重试'
        if (surplusTimes <= 0) {
          this.getVerificationTxt = '获取验证码'
          clearInterval(countdowns)
          countdowns = true
          this.isAllowGetNum = true
        }
      }, 1000)
    }
  },
  computed: {

  }
}
</script>

<style lang="scss" scoped>
uni-button:after{
  border: none !important;
}
.opacity-5{
  @include opacity(0.5);
}
.main-html{
  background-color: #fff;
  height: 100%;
}
.main{
  height: 100%;
  position: relative;
  background-color: #fff;
  .main-logo{
    //padding: 30upx;
    padding-top: 100upx;
    image{
      width: 340upx;
      height: 150upx;
      margin: 0 auto;
      display: block;
    }
  }
  .main-body{
    width: 100%;
    .inp{
      width: 520upx;
      margin: 20upx auto 0 auto;
      .inp-text{
        height: 48upx;
        margin-bottom: 14upx;
        .l{
          width: 48upx;
          height: 48upx;
          margin-right: 16upx;
          display: inline-block;
          vertical-align: middle;
        }
        .r{
          color: #333;
          font-size: 32upx;
          height: 48upx;
          line-height: 48upx;
          display: inline-block;
          vertical-align: middle;
        }
      }
      .verification-code{
        position: relative;
        .verification-inp{
          height: 70upx;
          padding-right: 200upx;
          input{
            height: 70upx;
            line-height: 70upx;
          }
        }
        .verification{
          position: absolute;
          right: 0;
          top: 0;
          text-align: center;
          color: #fff;
          font-size: 24upx;
          width: 160upx;
          height: 60upx;
          //background: linear-gradient(90deg,rgba(255,185,36,1),rgba(254,219,55,1));
          background: $topicC;
          @include rounded(30upx);
          line-height: 60upx;
        }
      }
      .password-main{
        height: 50upx;
        position: relative;
        .icon{
          position: absolute;
          right: 0;
          top: 6upx;
          height: 36upx;
          width: 36upx;
          z-index: 2;
        }
      }
    }
    .inp-main{
      height: 50upx;
      .main-l{
        display: inline-block;
        vertical-align: middle;
        width: 100upx;
        .l{
          display: inline-block;
          vertical-align: middle;
          height: 50upx;
          line-height: 50upx;
          color: #333;
          font-size: 32upx;
          margin-right: 10upx;
        }
        .r{
          display: inline-block;
          vertical-align: middle;
          height: 8upx;
          width: 15upx;
        }
      }
      .main-r{
        /*width: calc(100% - 100upx);*/
        width: 100%;
        display: inline-block;
        vertical-align: middle;
      }
    }
    .text-btn{
      width: 520upx;
      margin: 20upx auto 0 auto;
      position: relative;
      height: 30upx;
      .l{
        position: absolute;
        left: 0;
        top: 0;
        color: $topicC;
        font-size: 28upx;
      }
      .r{
        position: absolute;
        right: 0;
        top: 0;
        color: $topicC;
        font-size: 28upx;
      }
    }
  }
}
.f-w-medium{
  color: #bfbfbf;
  font-weight: Medium;
  font-size: 30upx;
}
.m-t-80{
  margin-top: 80upx;
}
.m-t-20{
  margin-top: 20upx;
}
.margin-0-auto{
  margin: 0 auto;
}
.width-height-atuo{
  width: 100%;
  height: 100%;
}
.bdb-1-topic-c {
  border-bottom: 2upx solid $topicC;
}
.bdb-1-ccc {
  border-bottom: 2upx solid #ccc;
}

.content {
  width: 200upx;
  height: 200upx;
  background: yellow;
  display: none;
}
.m-t-264 {
  margin-top: 264upx;
}
.c-333 {
  color: #333333;
}
.uni-input-placeholder {
  color: #ccc;
}
.topic-c {
  color: $topicC;
}
.b-btn {
  width: 520upx;
  //background: linear-gradient(90deg,rgba(255,185,36,1),rgba(254,219,55,1));
  background: $topicC;
  color: #fff;
  @include rounded(44upx);
}
.is-get-code{
  @include opacity(0.5);
}
</style>
