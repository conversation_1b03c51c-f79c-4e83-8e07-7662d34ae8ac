<template>
  <view class="wrapper" :style="wrapperStyle">
    <view class="item" v-for="(item, key) in config" :key="key" :style="item.style">
      <!-- selector 下拉框选择 -->
      <view style="display: inline-block;" v-if="item.type === 'selector'">
        <selector-item :config="item" @change="change($event, item, key)" />
      </view>
    </view>
  </view>
</template>

<script>
import selectorItem from './selector-item.vue'
export default {
  components: {
    selectorItem
  },
  props: {
    config: {
      type: Object,
      default: () => {
        return {}
      }
    },
    wrapperStyle: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      params: {}
    }
  },
  methods: {
    change(value, config, key) {
      if (!key) {
        throw 'id不得为空'
      }
      this.params[key] = value
      this.$emit('change', this.params)
    }
  }
}
</script>

<style lang="scss" scoped>
.wrapper {
  display: flex;
  padding: 12rpx 0;
  flex-wrap: wrap;
}
.item + .item {
  margin-left: 32rpx;
}
</style>