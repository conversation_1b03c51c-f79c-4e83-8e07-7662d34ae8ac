<template>
  <view class="tabelBox">
    <view class="titleMap padding20">
      <template v-for="(item,index) in tabelMap">
        <view 
        v-if="index!==0 && item" 
        :key="index" 
        class="tabIcon" 
        :style="{ flex: `0 1 ${100 / getItemLength}%` }"
      >
        <image class="tabelselectIcon" v-if="pageIndex === index" :src="tabelselectIcon" mode=""></image>
        <image class="tabNumber" v-else-if="pageIndex > index" :src="finishTab" mode=""></image>
        <view class="tabNumber" v-else>{{item.index}}</view>
        <view class="tabLine" :class="{currentLine:pageIndex >= index}" v-if="index < tabelMap.length - 1"></view>
      </view>
      </template>
    </view>

    <view class="titleMap" :style="{ padding: '0 40rpx' }">  <!-- 新增统一间距 -->
      <template v-for="(item,index) in tabelMap">
        <view 
          v-if="index!==0 && item" 
          :key="index"
          :style="{ flex: `0 1 ${100 / getItemLength}%`, textAlign: 'center' }" 
        >
          {{item.title}}
        </view>
      </template>
    </view>
  </view>
</template>

<script>
  import serverOptions from '@/config/env/options'
  export default{
    props:{
      pageIndex:{
        type:Number,
        default:1
      },
      accompanybookOne:{
        type:Object,
        default:null 
      }
    },
    computed:{
      timeType(){
        if(!this.provinceValue) return false
        return this.provinceValue.manualButton === 0
      },
      getItemLength(){
        return this.tabelMap.filter(e=>e).length - 1  
      }
    },
    watch:{
      async accompanybookOne(){
        console.log('this.accompanybookOne',this.accompanybookOne);
        if(!this.provinceValue){
          let provinceValue = (await this.$api.accompanyDoctor.accompanyproviderQueryOne({id:serverOptions.providerId})).data;
          this.provinceValue = provinceValue;
        }
        let manualButton = this.provinceValue.manualButton;
        // 如果当前请求不到订单信息时 则默认走当前服务商的设置选择的模式
        if(!this.accompanybookOne) return this.tabelMap = this.tabelMapList[manualButton];
        if(manualButton != 2){
          return this.tabelMap = this.tabelMapList[manualButton];
        }
        // 那么就只剩下用户自主选择的情况了 如果当前订单状态为1 则走人工模式 否则走在线模式
        if(this.accompanybookOne.orderState == 1){
          return this.tabelMap = this.tabelMapList[1];
        }
        this.tabelMap = this.tabelMapList[0];
      }   
    },
    data(){
      return {
        tabelMap:[],
        tabelselectIcon: this.$static_ctx + "image/business/hulu-v2/tabelselectIcon.png",
        finishTab: this.$static_ctx + "image/business/hulu-v2/finishTab.png",
        provinceValue:null,
        tabelMapList:[
          ['',{title:'服务预约',index:1},'',{title:'在线支付',index:2},{title:'专人陪诊',index:3},{title:'服务完成',index:4}],
          ['',{title:'服务预约',index:1},{title:'人工导诊',index:2},{title:'在线支付',index:3},{title:'专人陪诊',index:4},{title:'服务完成',index:5}],
        ]
      }
    },
    async mounted() {
      let provinceValue = (await this.$api.accompanyDoctor.accompanyproviderQueryOne({id:serverOptions.providerId})).data;
      this.provinceValue = provinceValue;
      let manualButton = this.provinceValue.manualButton;
      console.log('manualButton',manualButton);
      manualButton = manualButton == 2 ? 0 : manualButton;
      this.tabelMap = this.tabelMapList[manualButton];
      console.log('this.tabelMap',this.tabelMap);
      
    },
  }
</script>

<style lang="scss">
  .tabelBox{
    margin-bottom: 40rpx;
  }
  .tabelselectIcon{
    width: 48rpx;
    height: 48rpx;
  }
  .padding20{
    padding: 0 40rpx;
  }
  .titleMap{
    display: flex;
    justify-content: space-between;
    margin-top: 16rpx;
    padding: 0 40rpx;
    .tabIcon {
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
    }
    .tabNumber{
    width: 36rpx;
    height: 36rpx;
    background: #C9CCD4;
    border-radius: 50%;
    text-align: center;
    line-height: 36rpx;
  }
  .currentLine{
    background: #CCF0E6;
  }
    .tabLine {
      position: absolute;
      right: -34rpx;
      width: 68rpx;
      height: 8rpx;
      background: #C9CCD4;
      border-radius: 4rpx;
    }
  }
</style>
