<template>
  <view class="guidanceBox">
    <view class="guidanceTitle">
      <image class="clockImg" :src="clock" mode=""></image>
      等待人工导诊
    </view>
    <view class="guidanceTwoTitle">客服将致电联系您，请留意来电。</view>
    <view class="guidanceCard">
      <view class="headerTab">
        <view class="serverInfo">
          <image class="serverIcon" :src="file_ctx + accompanybookOne.serviceDetailImg" mode=""></image>
          <!-- 服务名称 -->
          <view class="serviceName">
            <view class="serverTitle">{{accompanybookOne.serviceName}}</view>
            <view class="tabItem">
              <text class="tabTitle">就诊时间:</text>
                {{timestampToDateTime(accompanybookOne.startTime)}}~{{timestampToDateTime(accompanybookOne.endTime,true)}}
              </view>
            <view class="tabItem" v-if='accompanybookOne.hospitalName'>
              <text class="tabTitle">就诊医院:</text>
              {{accompanybookOne.hospitalName}}
            </view>
          </view>
        </view>
        <view class="orderInfo">
          <view class="orderTitle">订单信息</view>
          <view class="orderValue">
            <text class="orderIdTitle">订单号</text>
            <view class="">
              {{accompanybookOne.id}}
              <text class="copy" @click="handleCopyOrder">复制</text>
            </view>
          </view>
          <view class="orderValue">
            <text class="timeTitle">创建时间</text>
            {{timestampToDateTime(accompanybookOne.createTime)}}
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  export default{
    props:{
      accompanybookOne:{
        type:Object,
      }
    },
    data(){
      return {
        file_ctx:this.file_ctx,
        clock: this.$static_ctx + "image/business/hulu-v2/clock.png",
        currentServer:{},
        serverCurrent:{},
      }
    },
    async mounted() {

    },
    computed:{
      async getAccompanybookOne(){
        let {data:{records:[serverCurrent]}} = await this.$api.accompanyDoctor.getAccompanyservicePage({
          condition:{serviceId:this.accompanybookOne.serviceId},
        })
        this.serverCurrent = serverCurrent;
        return this.accompanybookOne
      },
    },
    methods:{
      handleCopyOrder(){
        uni.setClipboardData({
          data: this.accompanybookOne.id,
          success: function () {
            uni.showToast({title: '复制成功',icon: 'success'});
          }
        });
      },
      timestampToDateTime(timestamp,flag) {
        if(!timestamp) return ''
        // 创建一个新的Date对象，传入的时间戳是以毫秒为单位的
        var date = new Date(timestamp);
        // 获取年、月、日、时、分、秒
        var year = date.getFullYear();
        var month = ("0" + (date.getMonth() + 1)).slice(-2); // 月份是从0开始的，所以+1，并补零
        var day = ("0" + date.getDate()).slice(-2); // 获取天数，并补零
        var hour = ("0" + date.getHours()).slice(-2); // 获取小时，并补零
        var minute = ("0" + date.getMinutes()).slice(-2); // 获取分钟，并补零
        if(flag) return `${month}.${day} ${hour}:${minute}`
        // 返回格式化的字符串
        return `${year}.${month}.${day} ${hour}:${minute}`;
      }

    }
  }
</script>

<style lang="scss">
  .guidanceBox{
    width: 100vw;
    padding: 0 32rpx;
    box-sizing: border-box;
  }
  .guidanceCard{
    width: 686rpx;
    height: 426rpx;
    background: #FFFFFF;
    border-radius: 16rpx;
    margin-top: 24rpx;
    .headerTab{
      width: 686rpx;
      background: #FFFFFF;
      border-radius: 16rpx;
      padding: 24rpx;
      box-sizing: border-box;
      .serverInfo{
        display: flex;
        padding: 8rpx;
        padding-bottom: 32rpx;
        border-bottom: 2rpx solid #EAEBF0;
      }
      .orderInfo{
        .orderTitle{
          margin: 32rpx 0;
          font-weight: 600;
          font-size: 28rpx;
          color: #2D2F38;
        }
        .timeTitle{
          font-weight: 600;
          font-size: 26rpx;
          color: #2D2F38;
        }
        .orderIdTitle{
          font-weight: 400;
          font-size: 26rpx;
          color: #2D2F38;
        }
        .orderValue{
          margin-bottom: 16rpx;
          display: flex;
          justify-content: space-between;
          font-weight: 400;
          font-size: 26rpx;
          color: #6F7281;
          .copy{
            margin-left: 8rpx;
            font-weight: 400;
            font-size: 26rpx;
            color: #316EAB;
          }
        }
      }
      .serverIcon{
        flex-shrink: 0;
        width: 144rpx;
        height: 144rpx;
        background: #D8D8D8;
        border-radius: 12rpx;
        border: 1rpx solid #D9DBE0;
        margin-right: 20rpx;
      }
      .serviceName{
        font-weight: 500;
        font-size: 32rpx;
        color: #1D2029;
        .tabItem{
          font-weight: 400;
          font-size: 24rpx;
          color: #1D2029;
        }
        .tabTitle{
          font-weight: 400;
          font-size: 24rpx;
          color: #4E5569;
          margin-right: 10rpx;
        }
        .serverTitle{
          font-weight: 500;
          font-size: 32rpx;
          color: #1D2029;
        }
        .signal{
          font-weight: 400;
          font-size: 22rpx;
          color: #FF5500;
        }
        .serverNum{
          font-weight: 500;
          font-size: 36rpx;
          color: #FF5500;
        }
        .tag{
          font-weight: 400;
          font-size: 20rpx;
          color: #868C9C;
        }
      }
      .changeServer{
        width: 148rpx;
        height: 52rpx;
        background: #FFFFFF;
        border-radius: 36rpx;
        border: 1rpx solid #D9DBE0;
        margin-left: auto;
      }
    }
  }
  .guidanceTitle{
    font-weight: 500;
    font-size: 36rpx;
    color: #1D2029;
    display: flex;
    align-items: center;
    .clockImg{
      width: 40rpx;
      height: 40rpx;
      margin-right: 8rpx;
    }
  }
  .guidanceTwoTitle{
    width: 686rpx;
    height: 60rpx;
    line-height: 60rpx;
    background: #FFEEE6;
    border-radius: 8rpx;
    padding: 0 24rpx;
    font-weight: 500;
    font-size: 26rpx;
    color: #FF5500;
    box-sizing: border-box;
    margin-top: 12rpx;
    border: 1rpx solid #F7D4C4;
  }

</style>
