// 资源汇总文件
import $storage from '@/common/util/storage'
import $common from '@/common/util/main'
import $wechat from '@/common/util/wechat-plugin'
import $uniPlugin from '@/common/util/uni-plugin'
import $timePlugin from '@/common/util/time-plugin'
import $constant from '@/constant'
import $request from '@/common/util/request'
import $ext from '@/service/ext'
import $api from '@/service/api'
import $validate from '@/common/util/validate'
import $navto from '@/router/config/nav-to'
import $env from '@/config/env'
import $accurateConversion from '@/common/util/accurate-conversion'
import $typeConversion from '@/common/util/type-conversion'
import $md5 from 'js-md5'
import $rsa from '@/common/util/rsa'

const ctx = $env.ctx
const ws_ctx = $env.ws_ctx
const file_ctx = $env.file_ctx
const domain_ctx = $env.domain_ctx
const $static_ctx = $env.static_ctx
const version_ctx = $env.version_ctx
const $appId = $env.appId
const $userType = $env.userType
const $tenantId = $env.tenantId
const $envVersion = $env.envVersion

export default {
  $rsa,
  $ext,
  $api,
  $storage,
  $request,
  $constant,
  $uniPlugin,
  $timePlugin,
  $common,
  $validate,
  $navto,
  // #ifdef H5
  $wechat,
  // #endif
  ctx,
  file_ctx,
  ws_ctx,
  domain_ctx,
  $accurateConversion,
  $md5,
  $static_ctx,
  version_ctx,
  $appId,
  $envVersion,
  $typeConversion,
  $userType,
  $tenantId
}
