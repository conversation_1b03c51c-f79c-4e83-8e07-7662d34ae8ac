import api from '@/service/api'
import store from '../index'
import ext from '@/service/ext'

const user = {
    namespaced: true,
    state: {
        accountId: '', // 唯一用户Id
        isBindWxAccountId: '', // 是否已绑定accountId唯一用户id

        loginMode: 'pwd', // 登录模式---pwd账号密码登录/sms验证吗登录
        isCutOutUrl: false, // isCutOutUrl 是否已截取链接，每次进来，在关闭浏览器之前，只允许截取一次
        isLogin: false, // 是否已登录
        token: '',
        refreshToken: '', // 定时刷新token
        weChatInfo: {}, // 当前用户微信数据
        mpWxUserInfo: {},

        curSelectUserInfo: {}, // 当前选中档案用户信息
        curSelectUserInfoId: '', // 当前选中档案用户信息主键
        userInfoList: [], // 档案用户列表数据
        curSelectStore: {}, // 当前选中的租户
        curSelectStoreId: '', // 当前选中的租户主键
        storeList: [], // 租户列表数据
        curSelectUnitId: {}, // 当前选中的单位主键
        permission: {}, // 权限code,
        isSmallProgram: 0, // 判断是否第一次进入小程序

        firstUrlParam: '', // 首页链接传参
        codeUserInfo: {}, // 中央用户信息
        recordUserInfo: {}, // 档案用户信息，根据产品业务来定义返回处理使用
        fansRecord: {}, // 社区粉丝档案
        fansRecordOld: {}, // 社区粉丝档案 旧的数据
        wxUserInfo: {}, // 绑定accountId接口返回的微信用户信息
        newsUnreadObj: {}, // 消息未读数
        publishInfo:{}, //病友分享发布心得所需参数
        healthInfo:'',//健康自测对象
        providerId:'', //服务商id，商家端必传
        isUserRole:{}, //判断用户是否是陪诊师傅和服务商
        cityName:'',//陪诊首页城市名称
    },

    mutations: {
        UPDATE_CITYNAME(state, cityName) {
          state.cityName = cityName
        },
        UPDATE_ISUSERROLE(state, data) {
          state.isUserRole = data
        },
        UPDATE_PUBLISHINFO(state, publishInfo) {
            state.publishInfo = publishInfo
        },
        UPDATE_PROVIDERID(state, providerId) {
            state.providerId = providerId
        },
        UPDATE_HEALTHINFO(state, healthInfo) {
            state.healthInfo = healthInfo
        },
        UPDATE_NEWSUNREADOBJ(state, newsUnreadObj) {
            state.newsUnreadObj = newsUnreadObj
        },
        UPDATE_WXUSERINFO(state, wxUserInfo) {
            state.wxUserInfo = wxUserInfo
        },
        UPDATE_FANSRECORD(state, fansRecord) {
            state.fansRecord = fansRecord
        },
        UPDATE_FANSRECORDOLD(state, fansRecordOld) {
            state.fansRecordOld = fansRecordOld
        },
        /**
        * isBindWxAccountId 值更新
        * @param {*} state
        * @param {*} isBindWxAccountId
        */
        UPDATE_ISBINDWXACCOUNTID(state, isBindWxAccountId) {
          state.isBindWxAccountId = isBindWxAccountId
        },
        /**
        * accountId 值更新
        * @param {*} state
        * @param {*} accountId
        */
        UPDATE_ACCOUNTID(state, accountId) {
          state.accountId = accountId
        },
        UPDATE_RECORDUSERINFO(state, recordUserInfo) {
          state.recordUserInfo = recordUserInfo
        },
        UPDATE_MPWXUSERINFO(state, mpWxUserInfo) {
            state.mpWxUserInfo = mpWxUserInfo
        },
        /**
         * firstUrlParam 值更新
         * @param {*} state
         * @param {*} firstUrlParam
         */
        UPDATE_LOGINMODE(state, loginMode) {
            state.loginMode = loginMode
        },
        /**
         * firstUrlParam 值更新
         * @param {*} state
         * @param {*} firstUrlParam
         */
        UPDATE_FIRSTURLPARAM(state, firstUrlParam) {
            state.firstUrlParam = firstUrlParam
        },
        /**
         * isLogin 是否已登录
         * @param {*} state
         * @param {*} token
         */
        UPDATE_ISLOGIN(state, isLogin) {
            state.isLogin = isLogin
        },
        /**
         * refreshToken 定时刷新token
         * @param {*} state
         * @param {*} refreshToken
         */
        UPDATE_REFRESHTOKEN(state, refreshToken) {
            state.refreshToken = refreshToken
        },
        /**
         * isCutOutUrl 是否已截取链接，每次进来，在关闭浏览器之前，只允许截取一次
         * @param {*} state
         * @param {*} isCutOutUrl
         */
        UPDATE_ISCUTOUTUTL(state, isCutOutUrl) {
            state.isCutOutUrl = isCutOutUrl
        },
        /**
         * token 值更新
         * @param {*} state
         * @param {*} token
         */
        UPDATE_TOKEN(state, token) {
            state.token = token
        },
        /**
         * weChatInfo 值更新
         * @param {*} state
         * @param {*} weChatInfo
         */
        UPDATE_WECHATINFO(state, weChatInfo) {
            state.weChatInfo = weChatInfo
        },
        /**
         * curSelectUnitId 值更新
         * @param {*} state
         * @param {*} curSelectUnitId
         */
        UPDATE_CURSELECTUNITID(state, curSelectUnitId) {
            state.curSelectUnitId = curSelectUnitId
        },
        /**
         * curSelectStoreId 值更新
         * @param {*} state
         * @param {*} curSelectStoreId
         */
        UPDATE_CURSELECTSTOREID(state, curSelectStoreId) {
            state.curSelectStoreId = curSelectStoreId
        },
        /**
         * curSelectStore 值更新
         * @param {*} state
         * @param {*} curSelectStore
         */
        UPDATE_CURSELECTSTORE(state, curSelectStore) {
            state.curSelectStore = curSelectStore
        },
        /**
         * isSmallProgram 判断是否第一次进入小程序
         * @param {*} state
         * @param {*} isSmallProgram
         */
        UPDATE_ISSMALLPROGRAM(state, isSmallProgram) {
          state.isSmallProgram = isSmallProgram
        },
        /**
         * storeList 值更新
         * @param {*} state
         * @param {*} storeList
         */
        UPDATE_STORELIST(state, storeList) {
            state.storeList = storeList || []
        },
        /**
         * 用户信息 值更新
         * @param {*} state
         * @param {*} userInfo
         */
        UPDATE_USERINFO(state, userInfo) {
            state.userInfo = userInfo
        },
        /**
         * 资源权限数据对象，用于页面显示隐藏资源菜单
         * @param state
         * @param permissions
         * @constructor
         */
        SET_PERMISSIONS: (state, permissions) => {
            const list = {}
            for (let i = 0; i < permissions.length; i++) {
                list[permissions[i]] = true
            }
            state.permission = list
        },
        /**
         * 清除权限
         * @param state
         */
        CLEAR_PERMISSIONS: (state) => {
            const list = state.permission
            const result = {}
            for (const i in list) {
                result[i] = false
            }
            state.permission = result
        },
        /**
         *  修改中央用户信息
         */
        UPDATE_CODECUSERINFO: (state, data) => {
            state.codeUserInfo = data
        },
        /**
         * 当前选中档案用户信息
         * @param state
         * @param curSelectUserInfo
         * @constructor
         */
        UPDATE_CURSELECTUSERINFO: (state, curSelectUserInfo) => {
            state.curSelectUserInfo = curSelectUserInfo
        },
        /**
         * 当前选中档案用户信息主键
         * @param state
         * @param curSelectUserInfoId
         * @constructor
         */
        UPDATE_CURSELECTUSERINFOID: (state, curSelectUserInfoId) => {
            state.curSelectUserInfoId = curSelectUserInfoId
        },
        /**
         * 档案用户列表数据
         * @param state
         * @param userInfoList
         * @constructor
         */
        UPDATE_USERINFOLIST: (state, userInfoList) => {
            state.userInfoList = userInfoList
        }
    },

    actions: {
        UpdateHealthInfo(context, healthInfo) {
            context.commit('UPDATE_HEALTHINFO', healthInfo)
        },
        UpdateProviderId(context, providerId) {
            context.commit('UPDATE_PROVIDERID', providerId)
        },
        UpdateCityName(context, cityName) {
            context.commit('UPDATE_CITYNAME', cityName)
        },
        UpdateIsUserRole(context, data) {
            context.commit('UPDATE_ISUSERROLE', data)
        },
        UpdatePublishInfo(context, publishInfo) {
            context.commit('UPDATE_PUBLISHINFO', publishInfo)
        },
        UpdateNewsUnreadObj(context, newsUnreadObj) {
            context.commit('UPDATE_NEWSUNREADOBJ', newsUnreadObj)
        },
        UpdateWxUserInfo({ commit }, wxUserInfo) {
            commit('UPDATE_WXUSERINFO', wxUserInfo)
        },
        UpdateFansRecord({ commit }, fansRecord) {
            commit('UPDATE_FANSRECORD', fansRecord)
        },
        UpdateFansRecordOld({ commit }, fansRecordOld) {
            commit('UPDATE_FANSRECORDOLD', fansRecordOld)
        },
        UpdateAccountId({ commit }, accountId) {
          commit('UPDATE_ACCOUNTID', accountId)
        },
        UpdateIsBindWxAccountId({ commit }, isBindWxAccountId) {
          commit('UPDATE_ISBINDWXACCOUNTID', isBindWxAccountId)
        },
        /**
         * 案例模板
         * @param commit
         * @param data
         * @constructor
         */
        UpdateRecordUserInfo({ commit }, data) {
          commit('UPDATE_RECORDUSERINFO', data)
        },
        UpdateIsSmallProgram(context, isSmallProgram) {
          context.commit('UPDATE_ISSMALLPROGRAM', isSmallProgram)
        },
        UpdateCodeUserInfo({ commit }, data) {
            commit('UPDATE_CODECUSERINFO', data)
        },
        UpdateDemo({ commit }, data) {
          commit('UPDATE_DEMO', data)
        },
        UpdateMpWxUserInfo({ commit }, data) {
            commit('UPDATE_MPWXUSERINFO', data)
        },
        UpdateLoginMode({ commit }, data) {
            commit('UPDATE_LOGINMODE', data)
        },
        UpdateFirstUrlParam(context, firstUrlParam) {
            context.commit('UPDATE_FIRSTURLPARAM', firstUrlParam)
        },
        UpdateIsLogin(context, isLogin) {
            context.commit('UPDATE_ISLOGIN', isLogin)
        },
        UpdateToken(context, token) {
            context.commit('UPDATE_TOKEN', token)
        },
        UpdateRefreshToken(context, refreshToken) {
            context.commit('UPDATE_REFRESHTOKEN', refreshToken)
        },
        UpdateIsCutOutUrl(context, isCutOutUrl) {
            context.commit('UPDATE_ISCUTOUTUTL', isCutOutUrl)
        },
        UpdateCurSelectStore(context, curSelectStore) {
            context.commit('UPDATE_CURSELECTSTORE', curSelectStore)
        },
        UpdateCurSelectStoreId(context, curSelectStoreId) {
            context.commit('UPDATE_CURSELECTSTOREID', curSelectStoreId)
        },
        UpdateWeChatInfo(context, weChatInfo) {
            context.commit('UPDATE_WECHATINFO', weChatInfo)
        },
        UpdateCurSelectUnitId(context, curSelectUnitId) {
            context.commit('UPDATE_CURSELECTUNITID', curSelectUnitId)
        },
        UpdateStoreList(context, storeList) {
            context.commit('UPDATE_STORELIST', storeList)
        },
        UpdateUserInfo(context, userinfo) {
            context.commit('UPDATE_USERINFO', userinfo)
        },

        UpdateCurSelectUserInfo(context, curSelectUserInfo) {
            context.commit('UPDATE_CURSELECTUSERINFO', curSelectUserInfo)
        },
        UpdateCurSelectUserInfoId(context, curSelectUserInfoId) {
            context.commit('UPDATE_CURSELECTUSERINFOID', curSelectUserInfoId)
        },
        UpdateUserInfoList(context, userInfoList) {
            context.commit('UPDATE_USERINFOLIST', userInfoList)
        },
        /**
         * 清除微信信息
         * @param {Object} context
         */
        CleanWeChatInfo(context) {
            context.commit('UPDATE_WECHATINFO', {})
            uni.removeStorageSync('weChatInfo')
        },
        /**
         * 设置资源全部为false
         * @param context
         * @param data
         * @constructor
         */
        ClearPermissionsAll({ commit, state }) {
            commit('CLEAR_PERMISSIONS')
                // commit('UPDATE_CURSELECTROLE', {})
        },
        ClearPermissions({ commit, state }) {
            commit('CLEAR_PERMISSIONS')
        },
        /**
         * 获取临时token
         * @param commit
         * @param data
         * @returns {Promise<any>}
         * @constructor
         */
        GetToken({ commit }, data) {
            return new Promise((resolve, reject) => {
                ext.user.getToken().then((res) => {
                    resolve(res)
                }).catch(e => {
                    reject(e)
                })
            })
        },
        /**
         * 退出清除相关数据
         * @param commit
         * @param that
         * @returns {Promise<any>}
         * @constructor
         */
        LoginOut({ commit, state }) {
            commit('UPDATE_TOKEN', '')
            commit('UPDATE_REFRESHTOKEN', '')
            commit('UPDATE_STORELIST', []) // unitList 单位数据
            commit('UPDATE_USERINFOLIST', []) // unitList 单位数据
            commit('UPDATE_ISLOGIN', false) // isLogin 登录状态
            commit('UPDATE_CURSELECTSTORE', {}) // curSelectStore 当前选中单位
            commit('UPDATE_CURSELECTSTOREID', null) // curSelectStoreId 当前选中单位id
            commit('UPDATE_USERINFO', {}) // userInfo 用户档案数据
            commit('UPDATE_CURSELECTUSERINFO', {}) // curSelectRole 当前选中角色
            commit('UPDATE_CURSELECTUSERINFOID', null) // curSelectRoleId 当前选中角色id
            commit('UPDATE_CODECUSERINFO', {}) // 中央用户信息
            commit('UPDATE_ACCOUNTID', '') // 唯一用户Id
            commit('UPDATE_ISBINDWXACCOUNTID', false) // 是否存在唯一用户Id
        },

        /**
         * 获取用户信息
         * @param commit
         * @param state
         * @returns {Promise<any>}
         * @constructor
         */
        GetUserInfo({ commit, state }) {
            return new Promise((resolve, reject) => {
                ext.user.getUserInfo().then(res => {
                    resolve(res)
                }).catch(e => {
                    reject(e)
                })
            })
        },
        /**
         * 获取权限菜单信息
         * @param commit
         * @param state
         * @returns {Promise<any>}
         * @constructor
         */
        GetPermission({ commit, state }, data) {
            if (data) {
                return new Promise((resolve, reject) => {
                    api.user.getPermission(data).then(res => {
                        commit('SET_PERMISSIONS', res)
                        resolve(res)
                    })
                })
            } else {
                store.commit('user/CLEAR_PERMISSIONS')
            }
        }
    }
}

export default user
