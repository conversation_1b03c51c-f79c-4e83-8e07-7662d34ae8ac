/** * 使用方法: node publish-interactive.js * 
 *  * 注意：使用此脚本前，请确保已通过HBuilderX对要发布的小程序进行了编译 * 
 * 1. 打开HBuilderX
 * 2. 在修改options.js中的currentId为要发布的小程序ID * 
 * 3. 编译生成对应的微信小程序代码 * 
 * 4. 然后再运行本脚本进行发布 */
import readline from 'readline';
import { createInstructionSet, runTimeMain } from './mpci.js';
import optionsMap from "../config/env/optionsMap.js";

// 创建readline接口
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// 显示所有可用的小程序
function showAvailableApps() {
  console.log('\n可用的小程序列表：');
  optionsMap.forEach(app => {
    console.log(`  [${app.id}] ${app.title}`);
  });
  console.log();
}

// 询问小程序ID
function askAppIds() {
  return new Promise((resolve) => {
    showAvailableApps();
    rl.question('请输入要发布的小程序ID(多个用逗号分隔，如 1,2,3): ', (answer) => {
      const idList = answer.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
      if (idList.length === 0) {
        console.log('错误: 请至少输入一个有效ID');
        return askAppIds().then(resolve);
      }
      resolve(idList);
    });
  });
}

// 询问发布描述
function askDescription() {
  return new Promise((resolve) => {
    rl.question('请输入发布描述: ', (answer) => {
      if (!answer.trim()) {
        console.log('错误: 描述不能为空');
        return askDescription().then(resolve);
      }
      resolve(answer.trim());
    });
  });
}

// 询问版本号
function askVersion() {
  return new Promise((resolve) => {
    rl.question('请输入版本号(如 1.2.5): ', (answer) => {
      const version = answer.trim();
      if (!version || !/^\d+\.\d+\.\d+$/.test(version)) {
        console.log('错误: 请输入有效的版本号，格式为 x.y.z');
        return askVersion().then(resolve);
      }
      resolve(version);
    });
  });
}

// 确认发布
function confirmPublish(idList, description, version) {
  return new Promise((resolve) => {
    const selectedApps = optionsMap.filter(app => idList.includes(app.id));
    
    console.log('\n发布确认：');
    console.log('  版本号:', version);
    console.log('  发布描述:', description);
    console.log('  选择的小程序:');
    
    selectedApps.forEach(app => {
      console.log(`    [${app.id}] ${app.title}`);
    });
    
    rl.question('\n确认发布吗? (y/n): ', (answer) => {
      resolve(answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes');
    });
  });
}

// 主函数
async function main() {
  try {
    console.log('\n===== 小程序交互式发布工具 =====');
    console.log('注意: 请确保已通过HBuilderX编译了要发布的小程序');
    
    // 询问用户输入
    const idList = await askAppIds();
    const description = await askDescription();
    const version = await askVersion();
    
    // 确认发布
    const confirmed = await confirmPublish(idList, description, version);
    
    if (!confirmed) {
      console.log('发布已取消');
      rl.close();
      return;
    }
    
    // 准备参数
    const parameters = {
      idList,
      description,
      version
    };
    
    console.log('准备发布...');
    
    // 生成指令集合
    const instructionSet = createInstructionSet(optionsMap, parameters);
    console.log('生成发布任务:', instructionSet);
    
    // 执行发布
    await runTimeMain(instructionSet);
    
    console.log('所有发布任务已完成!');
  } catch (error) {
    console.error('发布过程中出错:', error);
  } finally {
    rl.close();
  }
}

// 执行主函数
main(); 