import request from '@/common/util/request'
import env from '@/config/env'

/**
 * 药品说明书请求工具类，
 * 注意：API接口只做请求数据和格式化数据的事情，其他事务一概不处理
 */

export default {
  // 根据渠道链code 单一查询
  getChannelQueryOneCode(param){
    const url = env.ctx + 'dm/api/v1/eiproduct/query/one/code'
    return request.get(url, param)
  },
  // 药师问答分页列表
  getQuestionanswerPage(param){
      const url = env.ctx + 'dm/api/v1/crawlersquestionanswer/query/page'
      return request.postJson(url, param)
  },
  // 问答点赞
  addQuestionanswerLike(param){
    const url = env.ctx + 'dm/api/v1/crawlersquestionanswer/add/like'
    return request.postForm(url, param)
  },
  // 问答取消点赞
  cancelQuestionanswerLike(param){
    const url = env.ctx + 'dm/api/v1/crawlersquestionanswer/cancel/like'
    return request.postForm(url, param)
  },
  // 企业列表
  getEnterpriseQueryPage(param){
    const url = env.ctx + 'dm/api/v1/brand/query/page'
    return request.postJson(url, param)
  },
  // 产品列表
  getProductQueryPage(param){
    const url = env.ctx + 'dm/api/v1/eiproduct/query/page'
    return request.postJson(url, param)
  },
  // 企业的单一查询
  getEnterpriseQueryOne(param){
    const url = env.ctx + 'dm/api/v1/brand/query/one'
    return request.get(url, param)
  },
  // 企业的单一查询
  getEiproductQueryOne(param){
    const url = env.ctx + 'dm/api/v1/eiproduct/query/one'
    return request.get(url, param)
  },
  // 附近药店-分页列表
  getPharmacyQueryPage(param){
    // const url = env.ctx + 'dm/api/v1/pharmacy/query/page'
    const url = env.ctx + 'dm/api/v1/pharmacy/page/nearby'
    return request.postJson(url, param)
  },
  // 药店提醒-分页列表
  getPharmacyRemindQueryPage(param){
    const url = env.ctx + 'dm/api/v1/medicineremind/query/page'
    return request.postJson(url, param)
  },
  // 药店提醒-用药管理-分页列表
  getPharmacyRemindQueryDistinctPage(param){
    const url = env.ctx + 'dm/api/v1/medicineremind/query/distinct/page'
    return request.postJson(url, param)
  },
  // 用药提醒-保存数据
  pharmacyRemindInsert(param){
    const url = env.ctx + 'dm/api/v1/medicineremind/insert'
    return request.postJson(url, param)
  },
  // 用药提醒-更新数据
  pharmacyRemindUpdate(param){
    const url = env.ctx + 'dm/api/v1/medicineremind/update'
    return request.putJson(url, param)
  },
  // 用药提醒-主键单一查询
  pharmacyRemindQueryOne(param){
    const url = env.ctx + 'dm/api/v1/medicineremind/query/one'
    return request.get(url, param)
  },
  // 用药提醒-关闭计划
  pharmacyRemindDelete(param){
    const url = env.ctx + 'dm/api/v1/medicineremind/shutdown/plan'
    return request.delete(url, param)
  },
  // 用药提醒-发送消息通知
  pharmacyRemindSendMessage(param){
    const url = env.ctx + 'dm/api/v1/medicineremind/notify/switch'
    return request.postJson(url, param)
  },
  // 用药提醒-查询是否关注
  pharmacyRemindQueryAttention(param){
    const url = env.ctx + 'manage/api/v1/authFans/get/by/unionId'
    return request.postForm(url, param)
  },
  // 用药说明书-观看记录
  pharmacyQueryHistory(param){
    const url = env.ctx + 'dm/api/v1/eiproduct/visit/history'
    return request.postJson(url, param)
  },
  // 完整说明书分页列表
  getfullSpecificationQueryPage(param){
    const url = env.ctx + 'dm/api/v1/fullinstructions/query/page'
    return request.postJson(url, param)
  },
  // 完整说明书主键单一查询
  fullSpecificationQueryOne(param){
    const url = env.ctx + 'dm/api/v1/fullinstructions/query/one'
    return request.get(url, param)
  },
  
  // 完整说明书首页路轮播弹窗
  bannerQueryPage(param){
    const url = env.ctx + 'dm/api/v1/banner/query/page'
    return request.postJson(url, param)
  },

  // 用药指南配置入口-查询单一
  productmedicationguideQueryOne (param) {
    const url = env.ctx + 'dm/api/v1/productmedicationguide/query/one/productId'
    return request.get(url, param)
  },

  // 辟谣类型-辟谣分页列表
  refuterumortypeQueryPage (param) {
    const url = env.ctx + 'dm/api/v1/refuterumortype/query/page'
    return request.postJson(url, param)
  },
  // 每日辟谣分页列表
  refuterumordetailQueryPage (param) {
    const url = env.ctx + 'dm/api/v1/refuterumordetail/query/page'
    return request.postJson(url, param)
  },
  // 每日辟谣-获取每日辟谣题目
  refuterumordetailDayDetail (param) {
    const url = env.ctx + 'dm/api/v1/refuterumordetail/dayDetail'
    return request.get(url, param)
  },
  // 每日辟谣-辟谣订阅
  refuterumorsubscribeInsert (param) {
    const url = env.ctx + 'dm/api/v1/refuterumorsubscribe/insert'
    return request.postJson(url, param)
  },
  // 每日辟谣-新增答题记录
  refuterumorrecordInsert (param) {
    const url = env.ctx + 'dm/api/v1/refuterumorrecord/insert'
    return request.postJson(url, param)
  },
}