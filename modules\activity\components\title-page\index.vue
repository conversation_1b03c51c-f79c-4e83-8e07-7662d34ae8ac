<template>
  <scroll-view :scroll-y="true" class="frame-page" :style="{height: screenHeight + 'px'}">
    <view class="uni-flex uni-column">
      <!-- <view :class="isShow?'nav-top-set':''"> -->
      <main class="content" v-if="pageNetworkStatus">
        <slot name="content"></slot>
      </main>
      <main class="content" v-if="!pageNetworkStatus">
        <div class="network-error-panel">
          <div>糟糕，网络已断开了</div>
          <button class="btn" type="default" @tap="submitBtn()">重新刷新</button>
        </div>

      </main>
      <!-- </view> -->
    </view>
  </scroll-view>
</template>
<!--
* 案例模版
<page>
  <view slot="content">
  </view>
</page>
-->
<!--页面框架组件-->
<script>
import { mapState } from 'vuex'
export default {
  name: 'pageView',
  components: {

  },
  computed: {
    isShow() {
      if (this.headerObj) {
        return this.headerObj.isShow
      }
      return false
    },
    ...mapState('navigation', {
      headerObj: state => state.headerObj
    }),
    ...mapState('system', {
      networkStatus: state => state.networkStatus
    })
  },
  data() {
    return {
      pageNetworkStatus: true,
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $accurateConversion: this.$accurateConversion,
      file_ctx: this.file_ctx,
      $static_ctx: this.$static_ctx,
      $timePlugin: this.$timePlugin,
      $validate: this.$validate,
      screenHeight: 0,
      pathParams: {
        redirect: '',
        redirectParams: ''
      },
      timer: undefined
    }
  },
  mounted() {
    this.screenHeight = uni.getSystemInfoSync().windowHeight
    this.pageNetworkStatus = this.networkStatus
  },
  methods: {
    submitBtn() {
      if (!this.networkStatus) {
        this.$uniPlugin.toast('您的网络好像掉线了，请稍后再试')
      } else {
        this.$uniPlugin.toast('网络已连接，请稍等')
      }
      this.timer && clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.pageNetworkStatus = this.networkStatus
        this.$navto.replace(this.pathParams.redirect, this.pathParams.redirectParams)
      }, this.$constant.noun.delayedOperationTime1000)
      // this.$navto.replace(this.pathParams.redirect, this.pathParams.redirectParams)
    }
  },
  props: {
    type: {
      type: String,
      default: ''
    },
    content: {
      type: String,
      default: ''
    }
  },
  onReady() {
    const query = this.$Route.query
    const path = this.$Route.path
    this.pathParams.redirect = path
    this.pathParams.redirectParams = query
  }
}
</script>

<style lang="scss" scoped>
  .frame-page {
    /*flex-direction: row;*/
    /*display: flex;*/
    /*top: 0;*/
    /*left: 0;*/
    height: 100%;
    width: 100%;
    /*height: calc(100vh - 88upx);*/
    /* #ifdef MP-WEIXIN */
    /*height: 100vh;*/
    /* #endif */
    background: $bgColor;
    .uni-flex{
      width: 100%;
      height: 100%;
      .nav-top-set{
        margin-top:88upx !important;
      }
      .content{
        background-color: #f7f7f7;
        height: 100%;
      }
    }
    .network-error-panel{
      text-align: center;
      padding-top: 40%;
      .img{
        width: 340upx;
        height: 310upx;
      }
      .btn{
        width: 55%;
        margin-top: 60upx;
        background: #8BC34A;
        color: white;
        border: none;
      }
    }
  }
</style>
