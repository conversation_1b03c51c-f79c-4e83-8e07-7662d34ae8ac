<template>
  <view class="accompany-teacher">
    <!-- <view :style="'height:' + statusBarHeight + 'px;'"></view>
    <view class="top-nav">
      <view class="top-nav-l" @click.stop="handleBack"><image mode="aspectFit" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-service-left-arrows.png'" class="header-search-img"/></view>
      <view class="top-nav-c">社区交流</view>
    </view> -->
    <scroll-refresh
      style="height: 100%"
      :isShowEmptySwitch="true"
      :fixed="false"
      :isAbsolute="false"
      :up="upOption"
      :down="downOption"
      @returnFn="returnFn"
      @scrollInit="scrollInit"
    >
      <view>
        <view class="main-search">
          <search :fixed="true" top="88" :isShow="false" placeholder="搜索帖子、服务、医院" v-model="search">
            <template #cityName>{{cityName}}</template>
          </search>
        </view>
        <view class="main-list">
          <tabs-sticky
            class="main-tabs-sticky"
            :fontBigger="true"
            :bdb="false"
            :fixed="false"
            :overflowX="true"
            v-model="curIndex"
            :tabs="tabs"
            @change="changeTab"
          ></tabs-sticky>
          <view
            class="main-tips"
          >
            <template v-for="(item, index) in tabs">
              <view class="main-list-content" :key="item.id">
                <!-- 帖子 -->
                <nui-list 
                  v-if="
                    (curIndex == 0 && index === 0) ||
                    (curIndex == 1 && index === 1) ||
                    (curIndex == index && item.contentType === 1)
                  "
                  :indexlist="indexlist"
                  :isShowBtn="false"
                  :posts-params="postsParams"
                  @cateClick="cateClick"
                ></nui-list>
              </view>
            </template>
        </view>
        </view>
        <!-- <view class="img-wrapper" @click="handlePublish">
          <view class="img-item">
            <image :src="file_ctx + 'static/image/business/hulu-v2/icon-publish-experience.png'"></image>
          </view>
          <view class="text">发布入口</view>
        </view> -->
      </view>
    </scroll-refresh>
  </view>
</template>

<script>
import { mapState } from 'vuex'
import StatusBarHeight from '@/components/business/module/status-bar-height/index'
import search from '../components/search'
import nuiList from '@/components/community/nui-list/nui-list.vue'
import TabsSticky from '@/components/basics/tabs-sticky-v3'
import { isDomainUrl } from '@/utils/index.js'
import uniNavBar from '@/components/uni/uni-nav-bar/uni-nav-bar.vue'
export default {
  name: 'CircleHome',
  components: {
    StatusBarHeight,
    search,
    nuiList,
    uniNavBar,
    TabsSticky
  },
  data() {
    return {
      disclaimersUpdateCount:0,
      file_ctx: this.file_ctx,
      $validate: this.$validate,
      defaultAvatar: this.$static_ctx + 'image/system/avatar/icon-default-avatar.png',
      regForm: {
        name: ''
      },
      circleTabs: [],
      circleCurIndex: 0,
      curIndex: 0,
      tabs: [
        { name: '推荐', id: 1 },
        // {name: '名医热点', id: 2},
        { name: '精华', id: 3 },
      ],
      indexlist: [],
      mescroll: null, // mescroll实例对象
      downOption: {
        auto: false // 不自动加载
      },
      upOption: {
        auto: false, // 不自动加载
        empty: {
          top: 0,
          zIndex: 999,
        }
      },
      isInit: false, // 列表是否已经初始化
      $constant: this.$constant,
      videolist: [],
      hotSearchList:[], // 热搜列表
      timer:null,
      statusBarHeight: 0,
    }
  },
  computed: {
    ...mapState('user', {
      codeUserInfo: state => state.codeUserInfo, // 当前登录用户信息
      curSelectUserInfo: state => state.curSelectUserInfo, // 当前选中的用户信息
      curSelectUserInfoId: state => state.curSelectUserInfoId, // 当前选中的档案的ID
      recordUserInfo: state => state.recordUserInfo,
      accountId: state => state.accountId
    }),
    circleInfo() {
      return this.circleTabs[this.circleCurIndex]
    },
    postsParams() {
      return {
        circleClassifyId: (this.$validate.isNull(this.circleTabs) || !this.circleTabs[this.circleCurIndex]) ? '' : this.circleTabs[this.circleCurIndex].id,
        medicineClassifyId: (this.curIndex == 0 || !this.tabs[+this.curIndex]) ? null : this.tabs[+this.curIndex].id
      }
    }
  },
  onLoad() {
    this.init()
    this.searchbuzzwordQueryList()
    this.timer = setInterval(this.searchbuzzwordQueryList, 600000);  //每十分钟执行一次 轮询获取搜索词

  },
  mounted () {
    this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight // 获取设备的状态栏高度
  },
  onShow() {
    // #ifdef MP-WEIXIN
    this.setTabBarIndex(1);
    // #endif
    const that = this
    // Promise.all([this.getCircleclassifyQuerySubscribeList(), this.getCircleclassifyQueryUnsubscribeList()]).then(() => {
    //   this.getApplicationserviceGetCircleApplication()
    //   // this.$nextTick(() => {
    //   //   that.init()
    //   // })
    // })
    this.disclaimersUpdateCount += 1;
  },
  watch: {
    curIndex: {
      handler() {
        this.indexlist = []
        this.init()
      }
    },

  },
  methods: {
    handlePublish(){
      this.$navto.push('AccompanyPublishPost',{shareImg:''})
    },
    handleBack(){
      this.$navto.back(1)
    },
    // 搜索栏热度词
    async searchbuzzwordQueryList(){
      const res = await this.$api.postmessage.searchbuzzwordQueryList({
        putawayStatus:1
      })
      this.hotSearchList = [{word:'搜问题'},...res.data]
    },
    async getMedicineclassifyQueryList() {
      if (this.$validate.isNull(this.circleTabs)) return
      const res = await this.$api.medicineclassify.medicineclassifyQueryList({
        classifyAreaType: 2,
        internalClassifyId: this.circleTabs[this.circleCurIndex].id,
        classifyPutawayStatus: 1
      })
      this.tabs = [
        { name: '推荐', id: 1 },
        // {name: '名医热点', id: 2},
        { name: '精华', id: 3 },
      ].concat(res.data)
    },
    clickApp(data) {
      const { type, path, appId, name } = data
      switch (type) {
        // 小程序
        case 1:
          this.$uniPlugin.navigateToMiniProgram({
            appId,
            path,
            envVersion: 'release',
            extraData: {}
          }, (res) => {
            console.log(res)
          }, (err) => {
            console.log(err)
          })
          break
        // H5
        case 2:
          this.$navto.push('WebHtmlView', { src: path, title: name })
          break
        default:
      }
    },
    filterTabs() {
      // 是否开启名医热点
      if (this.circleInfo && this.circleInfo.isOpenHotspot === 2) {
        this.tabs.filter(item => item.id !== 2)
      }
    },
    cateClick(data) {
      this.navtoGo('Circle', { cid: data.circleClassifyId })
    },
    init() {
      this.$nextTick(() => {
        this.mescroll.triggerDownScroll()
      })
    },
    scrollInit(scroll) {
      scroll.optUp.page.num = 1
      scroll.optUp.page.size = 10
      this.mescroll = scroll
    },
    postReturnFn(obj) {
      const that = this
      const providerId = this.$common.getKeyVal('user','providerId',true)
      setTimeout(async function () {
        if (that.accountId) {
          let params = {
            current: obj.pageNum,
            size: obj.pageSize,
            condition: {
              circleClassifyId: that.$validate.isNull(that.circleTabs) ? '' : that.circleTabs[that.circleCurIndex].id,
              accountId: that.accountId,
              medicineClassifyId: that.curIndex == 0 ? null : that.tabs[+that.curIndex].id,
              // entryType: 5,
              providerId:providerId,
              ...that.regForm
            }
          }
          let res = null
          if (that.tabs[that.curIndex].id === 2) {
            // 名医热点
            res = await that.$ext.community.postmessageQueryPhysicianHotPage(params)
          } else if (that.tabs[that.curIndex].id === 3) {
            // 精华
            res = await that.$ext.community.postmessageQueryEssencePage(params)
          } else {
            // 推荐
            res = await that.$ext.community.postmessageQueryRecommendPage(params)
          }
          let data = res.data.records || []
          if (obj.pageNum === 1) {
            that.indexlist = []
          }
          that.indexlist = [...that.indexlist, ...data]
          obj.successCallback && obj.successCallback(data)
        }

      }, that.$constant.noun.scrollRefreshTime)
    },
    videoReturnFn(obj) {
      const that = this
      function queryPage(pageNum, pageSize, fn) {
        const param = {
          current: pageNum,
          size: pageSize,
          condition: {
            // terminalType: 1
            // 活动状态：1-草稿，2-预告，3-直播中，4-直播结束，5-回放，6-下架
            // activityStatus: '2,3,5',
            activityStatusList:[2,3,5],
            showType:1,
            businessType: that.tabs[+that.curIndex].liveBusinessType || 7,// 直播活动
            medicineClassifyId: that.curIndex == 0 ? null : that.tabs[+that.curIndex].id,
            // orderByActivityStatus:3
            orderByActivityStatus:'5,2,3'
          },
          descs:"createTime"
        }

        that.$api.cloudClassroom.getMeetingQueryPage(param).then(res => {
          if (res && res.data.records) {
            let list = res.data.records
            if (list.length > 0) {
              for (const a in list) {
                const data = list[a]
                data.startTimeText = that.$common.formatDate(new Date(data.startTime), 'yyyy-MM-dd HH:mm:ss');
                data.endTimeText = that.$common.formatDate(new Date(data.endTime), 'yyyy-MM-dd HH:mm:ss');
                data.coverPathsUrl = isDomainUrl(data.coverPaths)
              }
            }
            fn(res.data.records.length > 0 ? res.data.records : [])
          }
        })
      }
      queryPage(obj.pageNum, obj.pageSize, (data) => {
        if (obj.pageNum === 1) {
          that.videolist = []
        }
        that.videolist = that.videolist.concat(data)
        obj.successCallback && obj.successCallback(data)
      })
    },
    returnFn(obj) {
      if (this.$validate.isNull(this.circleTabs) || this.tabs[+this.curIndex].contentType !== 2) {
        this.postReturnFn(obj)
      } else if (this.tabs[+this.curIndex].contentType === 2) {
        this.videoReturnFn(obj)
      }

    },
    changeSearch(obj) {
      this.mescroll.optUp.page.num = 1
      this.mescroll.optUp.page.size = 10
      this.regForm.name = obj.name
      this.mescroll.triggerDownScroll()
    },
    changeTab(index) {
      this.curIndex = index
    },
    navtoGo(url, obj = {}) {
      this.$navto.push(url, obj)
    }
  }
}
</script>

<style lang="scss" scoped>
.accompany-teacher{
  background: #F4F6FA;
  position: relative;
  height: 100vh;
  .top-nav{
    // position: fixed;
    width: calc(100% - 56rpx);
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 40px;
    line-height: 40px;
    // z-index: 999;
    padding: 0 32rpx 0 24rpx;
    .top-nav-l{
      display: flex;
      width: 48rpx;
      height: 48rpx;
      image{
        width: 100%;
        height: 100%;
      }
    }
    .top-nav-c{
      flex: 1;
      text-align: center;
      height: 44rpx;
      font-weight: 500;
      font-size: 32rpx;
      color: #2D2F38;
      line-height: 44rpx;
      margin-right: 48rpx;
    }
  }
  .main-search{
    // padding:0 32rpx;c
    margin:0 32rpx;
    background: #F4F6FA;
  }
  .main-list {
    position: relative;
    border-radius: 20rpx;
    background-color: #ffffff;
    ::v-deep.main-tabs-sticky{
      .tabs-sticky{
        padding:32rpx 32rpx;
        position: sticky;
        top: 0;
        z-index:999;
      }
    }
    ::v-deep.tabs-sticky-body{
      padding: 0;
    }

  }
  .img-wrapper{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: fixed;
    right: 20rpx;
    bottom: 304rpx;
    width: 112rpx;
    height: 112rpx;
    background: #00B484;
    border-radius: 50%;
    box-shadow: 0rpx 8rpx 16rpx 0rpx rgba(0,180,132,0.2);
    .img-item{
      display: flex;
      width: 32rpx;
      height: 32rpx;
      image{
        width: 100%;
        height: 100%;
      }
    }
    .text{
      margin-top: 4rpx; 
      height: 28rpx;
      line-height: 28rpx;
      font-size: 20rpx;
      color:#fff;
    }
  }
}
</style>
