<template>
  <view class="service-detail">
    <!-- <view class="my-data" :style="{'background-image':'url('+ file_ctx +'static/image/business/accompany-doctor/icon-accompany-service-detail-bg.png)','background-size': '100%'}"> -->
    <view class="my-data" :style="{'background-image':'url('+ file_ctx + `${detailObj.detailImg})`,'background-size': '100%'}">
      <view :style="'height:' + statusBarHeight + 'px;'"></view>
      <view class="top-nav">
        <view class="top-nav-l" @click.stop="handleBack"><image mode="aspectFit" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-service-left-arrows.png'" class="header-search-img"/></view>
        <view class="top-nav-c"></view>
      </view>
    </view>
    <view class="service-detail-content">
      <view class="service-detail-head">
        <view class="service-detail-head-t">
          <view class="money">
            <view class="money-l">
              ￥<span class="price"><span v-if="detailObj.cityPrice">{{ detailObj.cityPrice / 100 }}</span><span v-else>{{ detailObj.price / 100 }}</span></span>
              <!-- <span class="num">次/起</span> -->
            </view>
            <view class="money-r" v-if="detailObj.tag && detailObj.tag.split(',').length > 1">
              <span class="special-project" v-for="(item,index) in detailObj.tag.split(',')" :key="index">{{ item }}</span>
            </view>
            <span v-else-if="detailObj.tag && detailObj.tag.split(',').length == 1" class="special-project">{{ detailObj.tag }}</span>
          </view>
          <view class="special-project-accompany">{{ detailObj.serviceName }}</view>
        </view>
        <view class="service-detail-head-b">{{ detailObj.description }}</view>
      </view>
      <view class="service-detail-nav">
        <view class="nav-item" v-for="(item,index) in navList" :key="index" @click="handleClickNav(index)">
          <view :class="currentIndex == index ? 'text active' : 'text'">{{item.name}}</view>
          <view class="item-img" v-if="currentIndex == index"><image :src="file_ctx + 'static/image/business/hulu-v2/border-bottom.png'"></image></view>
        </view>
      </view>
      <view class="service-detail-item" v-if="currentIndex == 0">
        <rich-text class="text-rich" :nodes="formattedContent" preview></rich-text>
      </view>
      <view class="service-detail-item" v-else>
        <rich-text class="text-rich" :nodes="formattedNotice" preview></rich-text>
        <!-- <view class="item-info">
          <view class="title">有效期</view>
          <view class="text">预约后90天内有效</view>
        </view>
        <view class="item-info">
          <view class="title">预约信息</view>
          <view class="text">如遇高峰时段您可能需要排队</view>
        </view>
        <view class="item-info">
          <view class="title">规则提醒</view>
          <view class="text">不再与其他优惠同享</view>
        </view>
        <view class="item-info">
          <view class="title">温馨提示</view>
          <view class="text">若涉及到上门服务，工作人员上门时主动出示</view>
          <view class="text">若涉及到上门服务，工作人员上门时主动出示哈哈哈哈哈哈哈</view>
        </view> -->
      </view>
    </view>
    <view class="service-detail-bottom" @click="handleBooking">
      <button>立即预约</button>
    </view>
  </view>
</template>

<script>
  import serverOptions from '@/config/env/options'
  import { mapState } from 'vuex'
  export default {
    data(){
      return{
        $navto:this.$navto,
        file_ctx:this.file_ctx,
        statusBarHeight: 0,
        navList:[{name:'服务内容'},{name:'预约须知'}],
        currentIndex:0,
        classifyIndex:0,
        detailObj:{},
        itemHeight:null,
        query:{}
        // city:null,
      }
    },
    computed: {
      ...mapState('user', {
        isLogin: state => state.isLogin
      }),
      formattedContent() {
        return this.formatContentWithImages(this.detailObj.content);
      },
      formattedNotice() {
        return this.formatContentWithImages(this.detailObj.notice);
      }
    },
    onLoad(option){
      this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight // 获取设备的状态栏高度
      const query = this.$validate.isNull(this.$Route.query) ? option : this.$Route.query
      // if(query?.city)this.city = query.city
      if(query?.id){
        this.query = query;
        this.accompanyserviceQueryOneByCity({id:query.id,city:query?.city})
      }
    },
    mounted(){
      // this.getElementHeight()
    },
    onShareAppMessage(){
      let path = `/modules/accompany-doctor/service-detail/index?id=${this.query.id}&city=${this.query.city}`;
      path += `&providerId=${serverOptions.providerId}`
      return {
        title: serverOptions.title,
        path
      }
    },
    onShareTimeline(){
      let path = `/modules/accompany-doctor/service-detail/index?id=${this.query.id}&city=${this.query.city}`;
      path += `&providerId=${serverOptions.providerId}`
      return {
        title: serverOptions.title,
        path
      }
    },
    methods:{
      handleBooking(){
        this.$navto.push('serviceReservation',{id:this.detailObj.id, classifyId:this.detailObj.classifyId, name:this.detailObj.classifyName, serviceName:this.detailObj.serviceName});
      },
      formatContentWithImages(content) {
        if (!content) return '';

        const lines = content.split('\n');
        let html = '';

        for (let line of lines) {
          if (line.includes('static/image') && (line.includes('.jpg') || line.includes('.png') || line.includes('.jpeg') || line.includes('.gif'))) {
            // 这是一个图片路径，转换为<img>标签
            html += `<img src="${this.file_ctx + line}" style="width:100%;" />`;
          } else {
            // 这是普通文本，保持原样
            html += `<div>${line}</div>`;
          }
        }

        return html;
      },
      handlClickClassify(index){
        this.classifyIndex = index
      },
      handleClickBuy(){
        this.$refs.guardDetailPopup.open()
      },
      handleClickNav(index){
        this.currentIndex = index
      },
      handleBack(){
        let pages = getCurrentPages() // 获取栈实例
        if (pages.length > 1) {
          this.$navto.back()
        } else {
          this.$navto.replaceAll('AccompanyHome')
        }
      },
      // 处理登录跳转，保存当前页面信息
      gotoLogin() {
        // 跳转到登录页面，并传递当前页面标识参数及服务ID
        this.$navto.push('Login', {
          formPage: 'ServiceDetail',
          id: this.detailObj.id,
          city: this.query.city
        });
      },
      // getElementHeight() {
      //   let query = uni.createSelectorQuery().in(this);
      //   query.select('.service-detail-content').boundingClientRect(data => {
      //     if (data) {
      //       console.log(data,'data.height')
      //       this.itemHeight = data.height
      //     }
      //   }).exec();
      // },

      // 根据id查询服务
      accompanyserviceQueryOneByCity(params){
        this.$api.accompanyDoctor.accompanyserviceQueryOneByCity(params).then(res=>{
          this.detailObj = res.data
        })
      },
    },
 }
</script>

<style lang='scss' scoped>
.service-detail{
  background: #F4F6FA;
  position: relative;
  height: 100vh;
  overflow-y: auto;
}
.my-data{
  height: 520rpx;
  width: 100%;
  .top-nav{
    // position: fixed;
    width: calc(100% - 56rpx);
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 40px;
    line-height: 40px;
    // z-index: 999;
    padding: 0 32rpx 0 24rpx;
    .top-nav-l{
      display: flex;
      width: 48rpx;
      height: 48rpx;
      image{
        width: 100%;
        height: 100%;
      }
    }
    .top-nav-c{
      flex: 1;
      text-align: center;
      height: 44rpx;
      font-weight: 500;
      font-size: 32rpx;
      color: #2D2F38;
      line-height: 44rpx;
      margin-right: 48rpx;
    }
  }
}
.service-detail-content{
  position: absolute;
  top: 490rpx;
  left: 0;
  width: calc(100% - 64rpx);
  // height: calc(100% - 700rpx);
  background: #F4F6FA;
  border-radius: 32rpx 32rpx 0rpx 0rpx;
  padding: 32rpx 32rpx 276rpx;
  overflow: hidden;
  .service-detail-head{
    background-color: #fff;
    border-radius: 16rpx;
    padding: 0 32rpx 32rpx;
    .service-detail-head-t{
      padding: 20rpx 0 24rpx;
      border-bottom: 1rpx solid #EAEBF0;
      .money{
        display: flex;
        align-items: center;
        font-size: 30rpx;
        color: #FF5500;
        .money-l{
          .price{
            font-size: 56rpx;
            line-height: 80rpx;
          }
          .num{
            font-size: 28rpx;
            color: #868C9C;
            line-height: 40rpx;
            margin:0 16rpx 0 4rpx;
          }
        }
        .money-r{
          display: flex;
          .special-project{
            padding: 6rpx 16rpx;
            background: #DEF2ED;
            border-radius: 8rpx;
            font-size: 20rpx;
            color: #00664B;
            margin-right: 8rpx;
            &:last-child{
              margin-right: 0;
            }
          }
        }
        .special-project{
          padding: 6rpx 16rpx;
          background: #DEF2ED;
          border-radius: 8rpx;
          font-size: 20rpx;
          color: #00664B;
          margin-left: 16rpx;
        }
      }
      .special-project-accompany{
        font-size: 40rpx;
        color: #1D2029;
        line-height: 56rpx;
        margin-top: 24rpx;
      }
    }
    .service-detail-head-b{
      margin-top: 24rpx;
    }
  }
  .service-detail-nav{
    display: flex;
    justify-content: space-around;
    padding:24rpx 0;
    .nav-item{
      display: flex;
      flex-direction: column;
      align-items: center;
      .text{
        font-size: 32rpx;
        color: #1D2029;
        line-height: 44rpx;
      }
      .active{
        color: #00B484;
      }
      .item-img{
        display: flex;
        width: 38rpx;
        height: 10rpx;
        image{
          width: 100%;
          height: 100%;
        }
      }
    }
  }
  .service-detail-item{
    width: calc(100% - 64rpx);
    padding:32rpx;
    background: #FFFFFF;
    border-radius: 16rpx;
    .item-info{
      margin-bottom: 32rpx;
      .title{
        font-size: 28rpx;
        color: #1D2029;
        line-height: 40rpx;
      }
      .text{
        margin-top: 12rpx;
        font-size: 28rpx;
        color: #4E5569;
        line-height: 40rpx;
      }
      &:last-child{
        margin-bottom: 0;
      }
    }
  }
}
.service-detail-bottom{
  display: flex;
  justify-content: center;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 180rpx;
  background: #FFFFFF;
  box-sizing: border-box;
  padding: 24rpx 32rpx 0;
  box-shadow: 0rpx -1rpx 0rpx 0rpx #EAEBF0;
  button{
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 88rpx;
    background: #00B484;
    border-radius: 44rpx;
    font-size: 32rpx;
    color: #FFFFFF;
  }
}
.guard-detail-content{
  position: relative;
  background: #fff;
  padding: 48rpx 0rpx 68rpx;
  border-radius: 24rpx 24rpx 0rpx 0rpx;
  .guard-detail-head{
    display: flex;
    padding:0 32rpx;
    .guard-detail-head-l{
      width: 160rpx;
      height: 160rpx;
      background-color: skyblue;
    }
    .guard-detail-head-r{
      margin-left: 24rpx;
      .money{
        color:#FF4100;
        span{
          font-size: 56rpx;
          color: #FF5500;
          line-height: 80rpx;
        }
      }
      .text{
        margin-top: 16rpx;
        font-size: 28rpx;
        color: #1D2029;
        line-height: 40rpx;
      }
    }
  }
  .error{
    position: absolute;
    right: 32rpx;
    top: 38rpx;
    width: 32rpx;
    height: 32rpx;
    image{
      width: 100%;
      height: 100%;
    }
  }
  .classify,.explain{
    margin: 48rpx 0;
    padding:0 32rpx;
    .title{
      font-size: 28rpx;
      color: #1D2029;
      line-height: 40rpx;
      margin-bottom: 24rpx;
    }
    .classify-list{
      display: flex;
      .classify-item{
        font-size: 26rpx;
        color: #1D2029;
        line-height: 36rpx;
        padding:18rpx 32rpx;
        background: #F4F6FA;
        border-radius: 12rpx;
        margin-right: 20rpx;
        &:last-child{
          margin-right: 0;
        }
      }
      .active{
        color: #00B484;
        border: 1rpx solid #00B484;
        background: #D7FAF1;
      }
    }
  }
  .explain{
    .info{
      font-size: 24rpx;
      color: #1D2029;
      line-height: 34rpx;
    }
  }
  .guard-detail-bottom{
    padding-top: 24rpx;
    border-top: 1rpx solid #EAEBF0;
    button{
      display: flex;
      align-items: center;
      justify-content: center;
      height: 88rpx;
      background: #00B484;
      border-radius: 44rpx;
      font-size: 32rpx;
      color: #FFFFFF;
      margin:0 32rpx;
      &::after{
        border: none !important;
      }
    }
  }
}
</style>
