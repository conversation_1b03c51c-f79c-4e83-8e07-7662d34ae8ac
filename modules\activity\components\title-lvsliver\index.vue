<!-- <title-lvsliver  @click="bindClick" :config='item' :cdata="regForm[item.name]" :onlykey='item.name' @update='updateValue'></title-lvsliver>
               -->
<template>
  <view class="title-input clear-float" :class="{ bdt: defaultConfig.bdt, bdb: defaultConfig.bdb && !child}">
    <view class="l-l" :style="{ color: defaultConfig.titleColor }" :class="defaultConfig.titleClass">
      {{ defaultConfig.label }}
      <text class="star" v-if="defaultConfig.required">*</text>
    </view>
    <view class="slider-msg">{{ lvdescrection }}</view>
    <view class="slider">
      <slider style="margin:0" :step="step" :max="max" class="slideritem" :value="cvalue" @change="sliderChange" activeColor="#22daad" backgroundColor="#f5f5f5" block-size="20" />
      <view class="slider-space">
        <template v-for="(item, index) in spaceTemp">
          <view
            class="slider-space-item"
            :key="index"
            :style="{
              left: item.left
            }"
          >
            <view class="tip">{{ item.txt }}</view>
          </view>
        </template>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'titleLvsliver',
  props: {
    child:{
      type:Boolean,
      default:false,
    },
    // 初始值传值，用于回显
    cdata: {
      type: [String, Number],
      required: false,
      default() {
        return '';
      }
    },
    // 参数设置
    config: {
      type: Object,
      required: false,
      default: () => {
        return {};
      }
    },
    onlykey: {
      type: [String, Number, Object]
    }
  },
  data() {
    return {
      spaceTemp: [],
      lvdescrection: '',
      cvalue: '',
      // form: {
      //   data: {
      //     val: ''
      //   }
      // },
      // array: [],
      index: 0,
      defaultConfig: {
        bdt: false,
        bdb: true,
        titleColor: '#333',
        textColor: '#333',
        label: '单行输入框',
        name: 'input',
        required: false
      },
      step: 1,
      max: 100
    };
  },
  mounted() {
    // console.log('kknnn')
    // console.log(this.$refs)
    this.getsliderW();
    this.copyConfig();

    console.log('我是初始化值', this.cdata);
  },
  watch: {
    config: {
      handler(val) {
        this.copyConfig();
      },
      deep: true
    },
    cdata(n) {
      // this.cvalue = cdata;
      console.log('我是初始化值', this.cdata);
    }
  },
  methods: {
    // 初始化默认值
    initvalue() {
      if (this.cdata && this.cdata != '') {
        let idx = this.spaceTemp.findIndex(item => item.value == this.cdata);
        if (this.spaceTemp[idx]) {
          this.cvalue = this.spaceTemp[idx].id;
        }
      }
    },
    sliderChange(e) {
      console.log('value 发生变化：' + e.detail.value);
      console.log(this.spaceTemp);
      let idx = this.spaceTemp.findIndex(item => item.id == e.detail.value);
      let val = '';
      if (this.spaceTemp[idx]) {
        val = this.spaceTemp[idx].value;
      }
      console.log('val', val);
      this.$emit('update', {
        value: val,
        onlykey: this.onlykey
      });
    },
    /**
     * 初始化拷贝config对象
     */
    copyConfig() {
      const that = this;
      const obj = that.config;
      Object.keys(obj).forEach(function(key) {
        that.defaultConfig[key] = obj[key];
      });
      console.log('that.defaultConfig');
      console.log(that.defaultConfig);
    },
    getsliderW() {
      // console.log(this.$refs.sliderRef.clientWidth)
      var obj = uni.createSelectorQuery().in(this);
      let that = this;
      console.log(obj);
      obj.select('.slideritem').boundingClientRect(function(rect) {
        console.log('rect');
        console.log(rect);
        console.log(rect.height);
        console.log(rect.width);
        that.init(rect.width);
      });
      obj.exec();
    },

    // 初始模块
    init(width) {
      // let space = this.config.w || 10;
      let obj = this.config.config;
      console.log(this.config);
      let len = 0;
      let lenobj = {};
      for (let key in obj) {
        // if(obj)
        if (key != 'lvdescrection') {
          // lenobj[len] = key;
          let keyarr = key.split('{{lv');
          let idx = keyarr[1] ? (keyarr[1].split('.')[1] ? keyarr[1].split('.')[1] : keyarr[1].split('.')[0]) : 'in';
          if (idx != 'in') {
            lenobj[idx] = key;
          }
          len += 1;
        } else {
          this.lvdescrection = obj[key];
        }
      }

      // let len = numberScope.length;
      console.log('len', len);

      let step = (width / len).toFixed(2);

      // console.log("step", step);
      let mask = [];

      for (let i = 0; i < len; i++) {
        // if(i == len - 1){
        //   mask.push({
        //     left:width + 'px',
        //     txt:lenobj[i].split("{{lv")[0]
        //   })
        // }else{
        if(lenobj[i]){
        mask.push({
          left: (i + 1) * step + 'px',
          txt: lenobj[i].split('{{lv')[0],
          value: obj[lenobj[i]],
          id: (i + 1) * step
        });
        }

        // }
      }
      this.step = step;
      this.max = len * step;

      this.spaceTemp = mask;
      console.log(mask);
      // this
      // let step = width / space;

      this.initvalue();
    }
  }
};
</script>

<style scoped lang="scss">
.slider-msg {
  font-size: 24upx;
  line-height: 1.5;
  margin-bottom: 10upx;
}
.slider {
  position: relative;
  padding: 60upx 20upx 10upx;
}
.slider-space {
  // width: 100%;
  // width: 10upx;
  pointer-events: none;
  // height: 100%;
  height: 60upx;
  // height: 10upx;
  position: absolute;
  left: 20upx;
  right: 20upx;
  // left: 30px;
  top: 60upx;

  .slider-space-item {
    position: absolute;
    width: 10upx;
    height: 100%;
    background: #fff;
  }
  .tip {
    white-space: nowrap;
    position: absolute;
    left: 5upx;
    top: -40upx;
    transform: translateX(-50%);
    font-size: 24upx;
  }
}

.color-topicC {
  color: $topicC !important;
}
.title-input {
  background-color: #ffffff;
  .l-l {
    line-height: 88upx;
    color: #333333;
    font-weight: 600;
    font-size: 30upx;
  }
  .l-l.font36{
    font-size: 36upx;
  }
  .l-r {
    margin-bottom: 5px;
    input {
      height: 80upx;
      line-height: 80upx;
      color: #333;
      font-size: 32upx;
      text-align: left;
      display: inline-block;
      vertical-align: middle;
      width: calc(100% - 64upx);
      border: 2upx solid #ede9e9;
      border-radius: 10upx;
    }
  }
}
.star {
  color: #f85e4c;
  padding-top: 6upx;
  font-size: 32upx;
  display: inline-block;
  margin-left: 10rpx;
}
</style>
