const nodesMixin = {
  methods: {
    getElInfo(id) {
      return this.getEl(this.getDiv(id))
    },
    getDiv(id) {
      // #ifdef H5
      var getDiv = document.getElementById(id)
      // #endif

      // #ifndef H5
      const query = uni.createSelectorQuery().in(this)
      var getDiv = query.select('#'+id)
      // #endif
      return getDiv
    },
    // 异步获取元素信息
    getEl (getDiv) {
      return new Promise((resolve, reject) => {
        // #ifdef H5
        resolve(getDiv.getBoundingClientRect())
        // #endif
        
        // #ifndef H5
        if (getDiv.boundingClientRect) {
          getDiv.boundingClientRect(data => {
            resolve(data)
          }).exec()
        }
        // #endif
      })
    },
  }
}

export default nodesMixin