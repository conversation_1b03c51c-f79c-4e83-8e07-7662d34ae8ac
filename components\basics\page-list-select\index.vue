<template>
  <uni-popup type="bottom" :show="show" @change="changeShow">
    <view class="wrapper" @touchmove.stop.prevent>
      <view class="header">
        <view class="header-left" @tap="changeShow({ show: false })">取消</view>
        <text class="header-title">{{ title }}</text>
        <view class="header-right" @tap="confirm">确定</view>
      </view>
      <uni-search-bar
        v-model="searchVal"
        placeholder="请输入关键词进行搜索"
        cancelButton="none"
        @confirm="refresh"
      />
      <view class="content">
        <scroll-refresh
          v-if="show"
          :isShowOptUpSwitch="false"
          style="height: 100%"
          :fixed="false"
          :up="upOption"
          :down="downOption"
          @returnFn="returnFn"
          @scrollInit="scrollInit"
        >
          <view>
            <checkbox-group @change="checkboxChange" v-if="multiple">
              <label v-for="(item, index) in list" :key="index">
                <view class="list-item">
                  <checkbox :value="item.id" :checked="value.includes(item.id)" />
                  <text class="item-label">{{ item[labelKey] }}</text>
                </view>
              </label>
            </checkbox-group>
            <radio-group @change="radioChange" v-else>
              <label v-for="(item, index) in list" :key="index">
                <view class="list-item">
                  <radio :value="item.id" :checked="value === item.id" />
                  <text class="item-label">{{ item[labelKey] }}</text>
                </view>
              </label>
            </radio-group>
          </view>
        </scroll-refresh>
      </view>
    </view>
  </uni-popup>
</template>

<script>
import uniPopup from '@/components/uni/uni-popup/index.vue'
import uniSearchBar from '@/components/uni/uni-search-bar/uni-search-bar.vue'
import { filterResponse } from '@/utils/index'
export default {
  components: {
    uniPopup,
    uniSearchBar
  },
  props: {
    value: {
      type: [String, Array],
      default: ''
    },
    show: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    multiple: {
      type: Boolean,
      default: false
    },
    defaultList: {
      type: Array,
      default: () => []
    },
    // 请求方法
    queryApi: {
      type: Function,
      default: () => { }
    },
    // 名称模糊搜索key
    name: {
      type: String,
      default: 'name'
    },
    // 默认传递参数
    defaultParams: {
      type: Object,
      default: () => {
        return {}
      }
    },
    // 接口返回的字段指定value的key
    valueKey: {
      type: String,
      default: 'id'
    },
    // 接口返回的字段指定label的key
    labelKey: {
      type: String,
      default: 'name'
    },
  },
  data() {
    return {
      searchVal: '',
      list: [],
      mescroll: null, // mescroll实例对象
      downOption: {
        auto: false // 不自动加载
      },
      upOption: {
        auto: false, // 不自动加载
      }
    }
  },
  watch: {
    show(val) {
      if (val) {
        this.$nextTick(() => {
          this.init()
        })
      }
    }
  },
  methods: {
    confirm() {
      let selectList = null
      if (this.multiple) {
        selectList = this.list.filter(item => this.value.includes(item[this.valueKey]))
      } else {
        selectList = this.list.find(item => this.value === item[this.valueKey])
      }
      this.$emit('confirm', selectList)
      this.changeShow({ show: false })
    },
    radioChange(e) {
      this.$emit('change', e.detail.value)
      this.$emit('input', e.detail.value)
    },
    checkboxChange(e) {
      this.$emit('change', e.detail.value)
      this.$emit('input', e.detail.value)
    },
    init() {
      this.mescroll.triggerDownScroll()
    },
    refresh() {
      this.mescroll.triggerDownScroll()
    },
    changeShow({ show }) {
      this.$emit('update:show', show)
    },
    scrollInit(scroll) {
      scroll.optUp.page.num = 1
      scroll.optUp.page.size = 10
      this.mescroll = scroll
    },
    async returnFn(obj) {
      const that = this
      let params = {
        current: obj.pageNum,
        size: obj.pageSize,
        condition: {
          [that.name]: that.searchVal,
          ...that.defaultParams
        }
      }
      let res = await that.queryApi(params)
      let data = res.data.records || []
      if (obj.pageNum === 1) {
        that.list = that.defaultList
      }
      that.list = [...that.list, ...data]
      if (obj.pageNum === 1) that.list = filterResponse(that.list, that.valueKey)
      obj.successCallback && obj.successCallback(data)

    },
  }
}
</script>

<style lang="scss" scoped>
.wrapper {
  display: flex;
  flex-direction: column;
  width: 100vw;
  height: 60vh;
  background-color: #fff;
  .header {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 70rpx;
    .header-left {
      width: 100rpx;
      text-align: center;
      line-height: 70rpx;
      font-size: 28rpx;
      color: #999;
    }
    .header-right {
      width: 100rpx;
      text-align: center;
      line-height: 70rpx;
      font-size: 28rpx;
      color: $topicC;
    }
    .header-title {
      flex: 1;
      text-align: center;
      color: #333;
      font-size: 30rpx;
      line-height: 36rpx;
    }
  }
  .content {
    flex: 1;
  }
  .list-item {
    display: flex;
    align-items: center;
    padding: 12rpx 32rpx;
    .item-label {
      padding-left: 12rpx;
      font-size: 26rpx;
      color: #606266;
    }
  }
}
</style>