<template>
  <scroll-view :scroll-x="true">
    <view class="navlist">
      <template v-for="(item, index) in list">
        <view :key="index" class="navitem" @click="toggle(index)" :class="[currentindex == index ? 'active' : '']">
          <view class="txt" :style="{
            '--dcolor':item.dcolor ? item.dcolor :　'#000'
          }">{{ item.label }}</view>
          <view class="number" v-if='item.number === 0 || item.number' :class="[item.activecolor ? 'usercolor' : '']"  :style="{
            '--color':item.activecolor
          }" >{{item.number}}</view>
        </view>
      </template>
    </view>
  </scroll-view>
</template>

<script>
export default {
  name: 'navlist',
  props: {
    list: {
      type: Array,
      default: function() {
        return [];
      }
    },
    current: {
      type: Number,
      default: 0
    },
    updatecount:{
      type:Number,
      default:0,
    }
  },
  data() {
    return {
      currentindex: 0
    };
  },
  mounted() {
    console.log('this.current',this.current)
    this.currentindex = this.current;
  },
  watch: {
    updatecount(n){
      this.currentindex = this.current;
    }
    // current(n) {
    //   // this.currentindex = n;
    // }
  },
  methods: {
    toggle(index) {
      this.currentindex = index;
      this.$emit('change', index);
    }
  }
};
</script>

<style lang="scss" scoped>
.navlist {
  display: flex;
  height: 100upx;
  padding-bottom: 1upx;
  background-color: #fff;
  // width: 100vw;
  .navitem {
    height: 100%;
    // display: flex;
    // align-items: center;
    // justify-content: center;

    min-width: 20vw;
    border-bottom: 1upx solid #dbdbdb;
    padding-bottom: 2upx;
    box-sizing: border-box;
    position: relative;

    flex: 1;

    .txt {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      text-overflow: ellipsis;
      overflow: hidden;
      word-break: break-all;
      white-space: nowrap;
    }
    .number {
      position: absolute;
      top: 0;
      right: 0;
      background: $topicC;
      // background: var(--color);
      color: #fff;
      padding: 0 10upx;
      border-radius: 10upx;
      font-size: 24upx;
      max-width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .number.usercolor{
      background-color: var(--color);
    }
  }
  .navitem.active {
    .txt {
      border-bottom: 3upx solid $topicC;
      color:var(--dcolor);
    }
  }
}
</style>
