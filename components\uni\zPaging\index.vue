<!-- 自定义下拉刷新与上拉加载演示(vue) -->
<template>
	<!-- <view class="content"> -->
	<z-paging ref="paging" class='mescroll-uni' v-model="dataList" @query="queryList" @scroll='scroll'
		:bg-color='bgColor' :scroll-into-view='scrollIntoView2' :default-page-no='defaultPageNo'
		:default-page-size='defaultPageSize' :fixed='fixed' :safe-area-inset-bottom='PageDefault.safeAreaInsetBottom'
		:use-safe-area-placeholder='PageDefault.useSafeAreaPlaceholder' :use-page-scroll='PageDefault.usePageScroll'
		:auto-full-height='PageDefault.autoFullHeight' :default-theme-style='PageDefault.defaultThemeStyle'
		:paging-style='pagingStyle' :delay='PageDefault.delay' :min-delay='PageDefault.minDelay'
		:refresher-enabled='PageDefault.refresherEnabled' :refresher-only='PageDefault.refresherOnly'
		:use-custom-refresher='PageDefault.useCustomRefresher'
		:show-refresher-when-reload='PageDefault.showRefresherWhenReload'
		:reload-when-refresh='PageDefault.reloadWhenRefresh' :refresher-theme-style='PageDefault.refresherThemeStyle'
		:refresher-img-style='PageDefault.refresherImgStyle' :refresher-title-style='PageDefault.refresherTitleStyle'
		:refresher-update-time-style='PageDefault.refresherUpdateTimeStyle'
		:watch-refresher-touchmove='PageDefault.watchRefresherTouchmove'
		:show-refresher-update-time='PageDefault.showRefresherUpdateTime'
		:refresher-default-text='PageDefault.refresherDefaultText'
		:refresher-pulling-text='PageDefault.refresherPullingText'
		:refresher-refreshing-text='PageDefault.refresherRefreshingText'
		:refresher-complete-text='PageDefault.refresherCompleteText'
		:refresher-refreshing-animated='PageDefault.refresherRefreshingAnimated'
		:refresher-end-bounce-enabled='PageDefault.refresherEndBounceEnabled'
		:refresher-default-style='PageDefault.refresherDefaultStyle'
		:refresher-background='PageDefault.refresherBackground'
		:refresher-fixed-background='PageDefault.refresherFixedBackground'
		:refresher-fixed-bac-height='PageDefault.refresherFixedBacHeight'
		:refresher-default-duration='PageDefault.refresherDefaultDuration'
		:refresher-complete-delay='PageDefault.refresherCompleteDelay'
		:refresher-complete-duration='PageDefault.refresherCompleteDuration'
		:refresher-vibrate='PageDefault.refresherVibrate'
		:refresher-complete-scrollable='PageDefault.refresherCompleteDcrollable'
		:refresher-out-rate='PageDefault.refresherOutRate' :refresher-pull-rate='PageDefault.refresherPullRate'
		:refresher-fps='PageDefault.refresherFps' :refresher-max-angle='PageDefault.refresherMaxAngle'
		:refresher-angle-enable-change-continued='PageDefault.refresherAngleEnableChangeContinued'
		:refresher-no-transform='PageDefault.refresherNoTransform' :auto='PageDefault.auto'
		:mounted-auto-call-reload='PageDefault.auto'
		:auto-scroll-to-top-when-reload='PageDefault.autoScrollToTopWhenReload'
		:auto-clean-list-when-reload='PageDefault.autoCleanListWhenReload' :created-reload='PageDefault.createdReload'
		:loading-more-enabled='PageDefault.loadingMoreEnabled'
		:to-bottom-loading-more-enabled='PageDefault.toBottomLoadingMoreEnabled'
		:show-loading-more-when-reload='PageDefault.showLoadingMoreWhenReload'
		:loading-more-theme-style='PageDefault.loadingMoreThemeStyle'
		:loading-more-loading-icon-type='PageDefault.loadingMoreLoadingIconType'
		:loading-more-loading-animated='PageDefault.loadingMoreLoadingAnimated'
		:loading-more-default-text='PageDefault.loadingMoreDefaultText'
		:loading-more-loading-text='PageDefault.loadingMoreLoadingText'
		:loading-more-no-more-text='PageDefault.loadingMoreNoMoreText'
		:loading-more-fail-text='PageDefault.loadingMoreFailText' :hide-no-more-inside='PageDefault.hideNoMoreInside'
		:hide-no-more-by-limit='PageDefault.hideNoMoreByLimit' :inside-more='PageDefault.insideMore'
		:loading-more-default-as-loading='PageDefault.loadingMoreDefaultAsLoading'
		:show-loading-more-no-more-view='PageDefault.showLoadingMoreNoMoreView'
		:show-default-loading-more-text='PageDefault.showDefaultLoadingMoreText'
		:show-loading-more-no-more-line='PageDefault.showLoadingMoreNoMoreLine'
		:hide-empty-view='PageDefault.hideEmptyView' :empty-view-fixed='PageDefault.emptyViewFixed'
		:empty-view-center='PageDefault.emptyViewCenter' :empty-view-text='PageDefault.emptyViewText'
		:empty-view-reload-text='PageDefault.emptyViewReloadText'
		:empty-view-error-text='PageDefault.emptyViewErrorText' :empty-view-style='PageDefault.emptyViewStyle'
		:empty-view-img-style='PageDefault.emptyViewImgStyle' :empty-view-title-style='PageDefault.emptyViewTitleStyle'
		:empty-view-reload-style='PageDefault.emptyViewReloadStyle'
		:show-empty-view-reload='PageDefault.showEmptyViewReload'
		:show-empty-view-reload-when-error='PageDefault.showEmptyViewReloadWhenError'
		:auto-hide-empty-view-when-loading='PageDefault.autoHideEmptyViewWhenLoading'
		:auto-hide-empty-view-when-pull='PageDefault.autoHideEmptyViewWhenPull'
		:auto-hide-loading-after-first-loaded='PageDefault.autoHideLoadingAfterFirstLoaded'
		:loading-full-fixed='PageDefault.loadingFullFixed' :auto-show-system-loading='PageDefault.autoShowSystemLoading'
		:system-loading-text='PageDefault.systemLoadingText' :system-loading-mask='PageDefault.systemLoadingMask'
		:use-virtual-list='PageDefault.useVirtualList' :use-compatibility-mode='PageDefault.useCompatibilityMode'
		:extra-data='PageDefault.extraData' :cell-height-mode='PageDefault.cellHeightMode'
		:preload-page='PageDefault.preloadPage' :virtual-list-col='PageDefault.virtualListCol'
		:virtual-scroll-fps='PageDefault.virtualScrollFps' :use-inner-list='PageDefault.useInnerList'
		:force-close-inner-list='PageDefault.forceCloseInnerList' :inner-list-style='PageDefault.innerListStyle'
		:inner-cell-style='PageDefault.innerCellStyle' :local-paging-loading-time='PageDefault.localPagingLoadingTime'
		:use-chat-record-mode='PageDefault.useChatRecordMode'
		:auto-hide-keyboard-when-chat='PageDefault.autoHideKeyboardWhenChat' :use-cache='PageDefault.useCache'
		:cache-key='PageDefault.cacheKey' :cache-mode='PageDefault.cacheMode'
		:show-scrollbar='PageDefault.showScrollbar' :scrollable='PageDefault.scrollable' :scroll-x='PageDefault.scrollX'
		:scroll-to-top-bounce-enabled='PageDefault.scrollToTopBounceEnabled'
		:scroll-to-bottom-bounce-enabled='PageDefault.scrollToBottomBounceEnabled'
		:scroll-with-animation='PageDefault.scrollWithAnimation' :enable-back-to-top='PageDefault.enableBackToTop'>

		<!-- 需要固定在顶部不滚动的view放在slot="top"的view中，如果需要跟着滚动，则不要设置slot="top" -->
		<!-- 注意！此处的z-tabs为独立的组件，可替换为第三方的tabs，若需要使用z-tabs，请在插件市场搜索z-tabs并引入，否则会报插件找不到的错误 -->
		<template #top>
			<slot name="top"></slot>
			<view class="h0">

			</view>
		</template>

		<!-- 自定义下拉刷新view(如果use-custom-refresher为true且不设置下面的slot="refresher"，此时不用获取refresherStatus，会自动使用z-paging自带的下拉刷新view) -->

		<!-- 注意注意注意！！字节跳动小程序中自定义下拉刷新不支持slot-scope，将导致custom-refresher无法显示 -->
		<!-- 如果是字节跳动小程序，请参照sticky-demo.vue中的写法，此处使用slot-scope是为了减少data中无关变量声明，降低依赖 -->
		<template #refresher="{refresherStatus}">
			<!-- 此处的custom-refresh为demo中自定义的组件，非z-paging的内置组件，请在实际项目中自行创建。这里插入什么view，下拉刷新就显示什么view -->
			<custom-refresher :status="refresherStatus" />
		</template>
		<!-- 自定义没有更多数据view -->
    <template #loadingMoreNoMore>
      <!-- 此处的custom-nomore为demo中自定义的组件，非z-paging的内置组件，请在实际项目中自行创建。这里插入什么view，没有更多数据就显示什么view -->
      <custom-nomore v-if="!$slots.loadingMoreNoMore"></custom-nomore>
      <slot v-else name="loadingMoreNoMore"></slot>
    </template>







		<!-- 顶部 -->
		<view class="h0" :id='uuid'>

		</view>
		<!--  内容区域 -->
		<slot></slot>


		<!-- 空布局 -->
		<template #empty>
			<!-- 空 -->
			<!-- empty -->
			<!-- {{isShowEmptySwitch}} -->
			<slot name="empty"></slot>
			<view class="h0">

			</view>
		</template>


		<template #loadingMoreFail>
			<slot name="loadingMoreFail"></slot>
			<view class="h0">

			</view>
		</template>

		<template #loading>
			<slot name="loading"></slot>
			<view class="h0">

			</view>
		</template>

		<template #header>
			<slot name="top"></slot>
			<view class="h0">

			</view>
		</template>


		<template #cell>
			<slot name="cell"></slot>
			<view class="h0">

			</view>
		</template>

		<template #refresherComplete>
			<slot name="refresherComplete"></slot>
			<view class="h0">

			</view>
		</template>

		<view class="mescroll-empty-box" v-if="isShowEmptySwitch && isShowEmpty">
			<view class="mescroll-empty" :class="{'empty-fixed':optEmpty.fixed, 'mescroll-empty-absolute': isAbsolute}"
				:style="{'z-index':optEmpty.zIndex,'top':optEmpty.top}">
				<image v-if="optEmpty.icon" class="empty-icon" :src="optEmpty.icon" />
				<view v-if="optEmpty.tip" class="empty-tip">{{optEmpty.tip}}</view>
				<view v-if="optEmpty.btnText" class="empty-btn" @click="emptyClick">{{optEmpty.btnText}}</view>
			</view>
		</view>
		<!-- 如果希望其他view跟着页面滚动，可以放在z-paging标签内 -->
		<!-- <view class="item" v-for="(item,index) in dataList" :key="index" @click="itemClick(item)">
				<view class="item-title">{{item.title}}</view>
				<view class="item-detail">{{item.detail}}</view>
				<view class="item-line"></view>
			</view> -->


		<!-- 回到顶部按钮 (fixed元素,需写在scroll-view外面,防止滚动的时候抖动)-->
		<template v-if="optUp.toTop">
			<image v-if="optUp.toTop.src" class="mescroll-totop" :class="{'mescroll-fade-in':isShowToTop}"
				:src="optUp.toTop.src" mode="widthFix" @click="toTopClick" />
		</template>
	</z-paging>
	<!-- </view> -->
</template>

<script>
	import zPaging from '@/components/uni/zPaging/components/z-paging/z-paging.vue';
	import customNomore from '@/components/uni/zPaging/components/custom-nomore/index.vue';
	import customRefresher from '@/components/uni/zPaging/components/custom-refresher/index.vue';
	import GlobalOption from '@/components/uni/zPaging/z-paging-config.js';
	export default {
		components: {
			zPaging,
			customNomore,
			customRefresher
		},
		props: {
			// 不是分页查询，一次请求获取所有数据
			noPage: {
				type: Boolean,
				default: false,
			},
			isAbsolute: { // 空数据提示是否开启垂直水平居中定位
				type: [Boolean, String],
				default () {
					return true
				}
			},
			bgColor: {
				type: String,
				default: "#f7f7f7",
			},
			optUp: {
				type: Object,
				default: function() {
					return {
						page: {
							num: 1,
							size: 10,
						}
					}
				}
			},

			top: [String, Number], // 下拉布局往下偏移的数值, 已默认单位为upx.
			// top:{
			//   type:String,

			// },
			isShowEmptySwitch: { // 是否开启空数据显示
				type: Boolean,
				default () {
					return true
				}
			},
			isShowOptUpSwitch: { // 是否开启上拉加载区域显示
				type: Boolean,
				default () {
					return true
				}
			},
			// 数据&布局配置基本配置属性
			zPageDefault: {
				type: Object,
				default: function() {
					return {}
				}
			},
			// 列表请求方法，必须是promise 或者是async 函数
			// requestApi:{
			// 	type:Function,
			// 	required: true,
			// },
			// 请求除了分页以外的参数
			defaultParam: {
				type: Object,
				default: function() {
					return {}
				}
			},
			fixed: {
				type: Boolean,
				default: true,
			},

			// 滚动到指定未知
			scrollIntoView: {
				type: String,
				default: "",
			}

		},
		data() {
			return {
				scrollIntoView2: "",
				// 唯一id
				uuid: 'z-paging' + Math.random(700),
				windowTop: 0, // 可使用窗口的顶部位置
				windowBottom: 0, // 可使用窗口的底部位置
				optUp2: {
					page: {
						num: 1,
						size: 10,
					}
				},
				defaultPageNo: 1,
				defaultPageSize: 10,

				// empty: {
				//     use: true, // 是否显示空布局
				//     icon: null, // 图标路径
				//     tip: '~ 暂无相关数据 ~', // 提示
				//     btnText: '', // 按钮
				//     btnClick: null, // 点击按钮的回调
				//     onShow: null // 是否显示的回调
				// },
				empty: GlobalOption.up.empty,
				isShowEmpty: false,
				// 可初始化属性
				initAttr: {
					// 全局 & 布局配置
					// defaultPageNo:true,
					// defaultPageSize:true,
					fixed: true,
					safeAreaInsetBottom: true,
					useSafeAreaPlaceholder: true,
					usePageScroll: true,
					autoFullHeight: true,
					defaultThemeStyle: true,
					pagingStyle: true,
					height: true,
					width: true,
					// bgColor:true,
					delay: true,
					minDelay: true,


					// 下拉刷新配置
					refresherEnabled: true,
					// refresher-threshold:'80rp'
					refresherOnly: true,
					useCustomRefresher: true,
					// showRefresherWhenReload:true,
					reloadWhenRefresh: true,
					refresherThemeStyle: true,
					refresherImgStyle: true,
					refresherTitleStyle: true,
					refresherUpdateTimeStyle: true,
					watchRefresherTouchmove: true,
					showRefresherUpdateTime: true,
					// refresherUpdateTimeKey:
					refresherDefaultText: true,
					refresherPullingText: true,
					refresherRefreshingText: true,
					refresherCompleteText: true,
					refresherRefreshingAnimated: true,
					refresherEndBounceEnabled: true,
					refresherDefaultStyle: true,
					refresherBackground: true,
					refresherFixedBackground: true,
					refresherFixedBacHeight: true,
					refresherDefaultDuration: true,
					refresherCompleteDelay: true,
					refresherCompleteDuration: true,
					refresherVibrate: true,
					refresherCompleteScrollable: true,
					refresherOutRate: true,
					refresherPullRate: true,
					refresherFps: true,
					refresherMaxAngle: true,
					refresherAngleEnableChangeContinued: true,
					// refresherNoTransform:true,
					refresherNoTransform: false,

					// reload相关配置
					auto: true,
					autoScrollToTopWhenReload: true,
					// autoCleanListWhenReload:true,
					autoCleanListWhenReload: false,
					showRefresherWhenReload: true,
					showLoadingMoreWhenReload: true,
					createdReload: true,


					// 底部加载更多配置
					loadingMoreEnabled: true,
					toBottomLoadingMoreEnabled: true,
					// showLoadingMoreWhenReload:true,
					loadingMoreThemeStyle: true,
					loadingMoreLoadingIconType: true,
					loadingMoreLoadingAnimated: true,
					loadingMoreDefaultText: true,
					loadingMoreLoadingText: true,
					loadingMoreNoMoreText: true,
					loadingMoreFailText: true,
					hideNoMoreInside: true,
					hideNoMoreByLimit: true,
					insideMore: true,
					loadingMoreDefaultAsLoading: true,
					showLoadingMoreNoMoreView: true,
					showDefaultLoadingMoreText: true,
					showLoadingMoreNoMoreLine: true,


					// 空数据与加载失败配置
					hideEmptyView: true,
					emptyViewFixed: true,
					emptyViewCenter: true,
					emptyViewText: true,
					emptyViewReloadText: true,
					emptyViewErrorText: true,
					emptyViewStyle: true,
					emptyViewImgStyle: true,
					emptyViewTitleStyle: true,
					emptyViewReloadStyle: true,
					showEmptyViewReload: true,
					showEmptyViewReloadWhenError: true,
					autoHideEmptyViewWhenLoading: true,
					autoHideEmptyViewWhenPull: true,

					// 全屏Loading配置
					autoHideLoadingAfterFirstLoaded: true,
					loadingFullFixed: true,
					autoShowSystemLoading: true,
					systemLoadingText: true,
					systemLoadingMask: true,


					// 返回顶部按钮配置
					autoShowBackToTop: true,
					backToTopWithAnimate: true,
					backToTopStyle: true,

					// 虚拟列表配置
					useVirtualList: true,
					useCompatibilityMode: true,
					extraData: true,
					cellHeightMode: true,
					preloadPage: true,
					virtualListCol: true,
					virtualScrollFps: true,
					useInnerList: true,
					forceCloseInnerList: true,
					innerListStyle: true,
					innerCellStyle: true,

					// 本地分页配置
					localPagingLoadingTime: true,

					// 聊天记录模式配置
					useChatRecordMode: true,
					autoHideKeyboardWhenChat: true,


					// scroll-view相关配置
					showScrollbar: true,
					scrollable: true,
					scrollX: true,
					scrollToTopBounceEnabled: true,
					scrollToBottomBounceEnabled: true,
					scrollWithAnimation: true,
					// scroll-into-view:"",
					enableBackToTop: true,


					// 缓存配置
					useCache: false,
					cacheKey: null,
					cacheMode: "default",


					// z-index配置
					topZIndex: true,
					superContentZIndex: true,
					contentZIndex: true,
					emptyViewZIndex: true,







				},
				PageDefault: {
					// 全局 & 布局配置
					// defaultPageNo:1,
					// defaultPageSize:10,
					fixed: true,
					safeAreaInsetBottom: false,
					useSafeAreaPlaceholder: false,
					usePageScroll: false,
					autoFullHeight: true,
					defaultThemeStyle: 'black',
					pagingStyle: {},
					height: null,
					width: null,
					// bgColor:'',
					delay: 0,
					minDelay: 0,

					// 下拉刷新配置
					refresherEnabled: true,
					// refresher-threshold:'80rp'
					refresherOnly: false,
					useCustomRefresher: true,
					showRefresherWhenReload: false,
					reloadWhenRefresh: true,
					refresherThemeStyle: "black",
					refresherImgStyle: {},
					refresherTitleStyle: {},
					refresherUpdateTimeStyle: {},
					watchRefresherTouchmove: false,
					showRefresherUpdateTime: false,
					// refresherUpdateTimeKey:
					refresherDefaultText: '继续下拉刷新',
					refresherPullingText: '松开立即刷新',
					refresherRefreshingText: '正在刷新...',
					refresherCompleteText: "刷新成功",
					refresherRefreshingAnimated: true,
					refresherEndBounceEnabled: true,
					refresherDefaultStyle: 'black',
					refresherBackground: '#FFFFFF00',
					refresherFixedBackground: "#FFFFFF00",
					refresherFixedBacHeight: 0,
					// refresherDefaultDuration:100,
					refresherDefaultDuration: 0,
					refresherCompleteDelay: 0,
					refresherCompleteDuration: 300,
					refresherVibrate: false,
					refresherCompleteScrollable: false,
					refresherOutRate: 0.65,
					refresherPullRate: 0.75,
					refresherFps: 40,
					refresherMaxAngle: 40,
					refresherAngleEnableChangeContinued: false,
					refresherNoTransform: false,


					// reload相关配置
					// auto:true,
					auto: false,
					autoScrollToTopWhenReload: true,
					autoCleanListWhenReload: true,
					showRefresherWhenReload: false,
					showLoadingMoreWhenReload: false,
					createdReload: false,


					// 底部加载更多配置
					loadingMoreEnabled: true,
					toBottomLoadingMoreEnabled: true,
					// showLoadingMoreWhenReload:false,
					loadingMoreThemeStyle: 'black',
					loadingMoreLoadingIconType: "flower",
					loadingMoreLoadingAnimated: true,
					loadingMoreDefaultText: "点击加载更多",
					loadingMoreLoadingText: "正在加载...",
					loadingMoreNoMoreText: "没有更多了",
					loadingMoreFailText: "加载失败，点击重新加载",
					hideNoMoreInside: false,
					hideNoMoreByLimit: 0,
					insideMore: false,
					loadingMoreDefaultAsLoading: false,
					showLoadingMoreNoMoreView: true,
					showDefaultLoadingMoreText: true,
					showLoadingMoreNoMoreLine: true,


					// 空数据与加载失败配置
					hideEmptyView: false,
					emptyViewFixed: false,
					emptyViewCenter: true,
					emptyViewText: '没有数据哦~',
					emptyViewReloadText: "重新加载",
					emptyViewErrorText: '很抱歉，加载失败',
					emptyViewStyle: {},
					emptyViewImgStyle: {},
					emptyViewTitleStyle: {},
					emptyViewReloadStyle: {},
					showEmptyViewReload: false,
					showEmptyViewReloadWhenError: true,
					autoHideEmptyViewWhenLoading: true,
					autoHideEmptyViewWhenPull: true,


					// 全屏Loading配置
					autoHideLoadingAfterFirstLoaded: true,
					loadingFullFixed: false,
					autoShowSystemLoading: false,
					systemLoadingText: '加载中...',
					systemLoadingMask: true,

					// 返回顶部按钮配置
					autoShowBackToTop: false,
					backToTopWithAnimate: true,
					backToTopStyle: {},


					// 虚拟列表配置
					useVirtualList: false,
					useCompatibilityMode: false,
					extraData: null,
					cellHeightMode: "fixed",
					preloadPage: 7,
					virtualListCol: 1,
					virtualScrollFps: 80,
					useInnerList: false,
					forceCloseInnerList: false,
					innerListStyle: {},
					innerCellStyle: {},


					// 本地分页配置
					localPagingLoadingTime: 200,

					// 聊天记录模式配置
					useChatRecordMode: false,
					autoHideKeyboardWhenChat: true,


					// scroll-view相关配置
					showScrollbar: true,
					scrollable: true,
					scrollX: false,
					scrollToTopBounceEnabled: false,
					scrollToBottomBounceEnabled: true,
					scrollWithAnimation: false,
					// scroll-into-view:"",
					enableBackToTop: true,


					// 缓存配置
					useCache: false,
					cacheKey: null,
					cacheMode: "default",


					// z-index配置
					topZIndex: 99,
					superContentZIndex: 1,
					contentZIndex: 10,
					emptyViewZIndex: 9,





				},
				//v-model绑定的这个变量不要在分页请求结束中自己赋值！！！
				dataList: [],
				tabList: ['测试1', '测试2', '测试3', '测试4'],
				tabIndex: 0
			}
		},
		created() {
			this.initAttribute();

			// console.log('---',this.optUp)
			if (this.optUp.page) {
				this.optUp2.page.num = this.optUp.page.num;
				this.optUp2.page.size = this.optUp.page.size;
			}
			this.$emit('scrollInit', {
				optUp: this.optUp2,
				triggerDownScroll: this.triggerDownScroll
			})


			// setInterval(() => {
			//   console.log('this.optUp',this.optUp)
			// },1000)
		},
		methods: {
			// 注册列表滚动事件,用于下拉刷新
			scroll(e) {
				// this.mescroll.scroll(e.detail, () => {
				this.$emit('scroll', e) // 此时可直接通过 this.mescroll.scrollTop获取滚动条位置; this.mescroll.isScrollUp获取是否向上滑动
				// })
			},
			// 点击回到顶部的按钮回调
			toTopClick() {
				this.isShowToTop = false // 回到顶部按钮需要先隐藏,再执行回到顶部,避免闪动
				// this.mescroll.scrollTo(0, this.mescroll.optUp.toTop.duration) // 执行回到顶部
				// this.$emit('topclick', this.mescroll) // 派发点击回到顶部按钮的回调
				this.scrollIntoView2 = this.uuid;

				setTimeout(() => {
					this.scrollIntoView2 = ''
				}, 500)
			},
			triggerDownScroll() {
				this.reload(true)
			},
			/* 联网回调成功,结束下拉刷新和上拉加载
			 * dataSize: 当前页的数据个数(不是所有页的数据总和),用于上拉加载判断是否还有下一页.如果不传,则会判断还有下一页
			 * hasNext: 是否还有下一页,布尔类型;用来解决这个小问题:比如列表共有20条数据,每页加载10条,共2页.如果只根据dataSize判断,则需翻到第三页才会知道无更多数据,如果传了hasNext,则翻到第二页即可显示无更多数据.
			 * systime: 服务器时间(可空);用来解决这个小问题:当准备翻下一页时,数据库新增了几条记录,此时翻下一页,前面的几条数据会和上一页的重复;这里传入了systime,那么upCallback的page.time就会有值,把page.time传给服务器,让后台过滤新加入的那几条记录
			 */
			endSuccess(curPageData, hasNext = false, pageNo) {
				const dataSize = curPageData.length;
				// const pageNum = this.PageDefault.
				// defaultPageNo:1,
				// defaultPageSize:10,
				const pageNum = pageNo;
				const pageSize = this.defaultPageSize;
				let isShowNoMore = false;
				const me = this;
				console.log('pageNum', pageNum)
				console.log('dataSize', dataSize)

				// console.log('dataSize < pageSize || hasNext === false',dataSize < pageSize || hasNext === false)
				if (dataSize < pageSize || hasNext === false) {
					// 返回的数据不满一页时,则说明已无更多数据
					// me.optUp.hasNext = false
					// console.log('dataSize',dataSize)
					// console.log('dataSize === 0 && pageNum === 1',dataSize === 0 && pageNum === 1)
					if (dataSize === 0 && pageNum === 1) {
						// 如果第一页无任何数据且配置了空布局
						isShowNoMore = false
						me.showEmpty()
					} else {
						// 总列表数少于配置的数量,则不显示无更多数据
						const allDataSize = (pageNum - 1) * pageSize + dataSize
						// if (allDataSize < me.optUp.noMoreSize) {
						//     isShowNoMore = false
						// } else {
						//     isShowNoMore = true
						// }
						me.removeEmpty() // 移除空布局
					}
				} else {
					// 还有下一页
					isShowNoMore = false
					// me.optUp.hasNext = true
					me.removeEmpty() // 移除空布局
				}


				this.$refs.paging.complete(curPageData)
			},

			//
			showEmpty() {
				this.isShowEmpty = true;
			},
			removeEmpty() {
				this.isShowEmpty = false;
			},


			// 初始化基本属性
			initAttribute() {
				if (this.zPageDefault instanceof Object) {
					for (let key in this.zPageDefault) {
						if (this.initAttr[key]) {
							this.PageDefault[key] = this.zPageDefault[key];
						}
					}
				}

			},
			// 初始化

			// 组件方法
			complete(list) {
				//如果请求失败写this.$refs.paging.complete(false);
				//注意，每次都需要在catch中写这句话很麻烦，z-paging提供了方案可以全局统一处理
				//在底层的网络请求抛出异常时，写uni.$emit('z-paging-error-emit');即可
				this.$refs.paging.complete(list);
			},
			reload(visible) {
				//当切换tab或搜索时请调用组件的reload方法，请勿直接调用：queryList方法！！
				//调用reload时参数传true则代表reload时触发下拉刷新效果，不传或false则代表取消此效果
				this.$refs.paging.reload(visible);
			},
			// 请求列表
			async queryList(pageNo, pageSize) {
				// console.log('999')
				// console.log('pageSize',pageSize)
				//组件加载时会自动触发此方法，因此默认页面加载时会自动触发，无需手动调用
				//这里的pageNo和pageSize会自动计算好，直接传给服务器即可
				//模拟请求服务器获取分页数据，请替换成自己的网络请求
				// const params = {
				// 	current:pageNo,
				// 	size:pageSize,
				// }

				// if(this.defaultParam instanceof Object) {
				// 	Object.assign(params, this.defaultParam);
				// }
				// console.log('params',params)



				// 联网加载数据
				this.getListDataFromNet(pageNo, pageSize, (curPageData) => {
					// console.log('pageNo',pageNo)
					// 单页数据
					if (this.noPage && pageNo != 1) {
						this.$refs.paging.complete([]);
					} else {
						this.endSuccess(curPageData, false, pageNo);
					}
					// this.$refs.paging.complete(curPageData);
				}, () => {
					// 联网失败的回调,隐藏下拉刷新的状态
					this.$refs.paging.complete(false);
				})
				// const result = await this.requestApi(params)
				// this.$refs.paging.complete(result);
			},
			getListDataFromNet(pageNum, pageSize, successCallback, errorCallback) {
				const obj = {
					pageNum: pageNum,
					pageSize: pageSize,
					successCallback: successCallback,
					errorCallback: errorCallback
				}
				this.$emit('returnFn', obj)
			}

		},
		mounted() {

			// console.log('mounted')
			// console.log(this.requestApi)
			// console.log(this.requestApi())


		},
		watch: {
			scrollIntoView(n) {
				this.scrollIntoView2 = n;

				setTimeout(() => {
					this.scrollIntoView2 = ''
				}, 500)
			},
			optUp2: {
				handler(val) {
					if (!val.page) {
						val.page = {
							num: 1,
							size: 10,
						}
					}
					this.defaultPageNo = val.page.num;
					this.defaultPageSize = val.page.size;

					console.log('变了', val)
					console.log('this.defaultPageSize', this.defaultPageSize)
					// this.watchDataMain(val)
				},
				deep: true
			}
		},
		computed: {
			// top数值,单位upx,需转成px. 目的是使下拉布局往下偏移
			numTop() {
				return uni.upx2px(Number(this.top || 0))
			},
			fixedTop() {
				return this.fixed ? (this.numTop + this.windowTop) + 'px' : 0
			},
			padTop() {
				return !this.fixed ? this.numTop + 'px' : 0
			},
			pagingStyle() {
				return {
					top: this.fixedTop,
				}
			},
			// 空布局的配置
			optEmpty() {
				return this.empty
			},

		}


	}
</script>

<style lang='scss' scoped>
	/* 回到顶部的按钮 */
	.mescroll-totop {
		z-index: 9990;
		position: fixed !important;
		/* 避免编译到H5,在多mescroll中定位失效 */
		right: 20upx;
		bottom: 120upx;
		width: 72upx;
		height: 72upx;
		-webkit-border-radius: 50%;
		-moz-border-radius: 50%;
		border-radius: 50%;
		opacity: 0;
		display: none;
	}

	/* 显示动画--淡入 */
	.mescroll-lazy-in,
	.mescroll-fade-in {
		-webkit-animation: mescrollFadeIn .3s linear forwards;
		animation: mescrollFadeIn .3s linear forwards;
	}

	@-webkit-keyframes mescrollFadeIn {
		0% {
			opacity: 0;
			display: inline-block;
		}

		100% {
			opacity: 1;
		}
	}

	@keyframes mescrollFadeIn {
		0% {
			opacity: 0;
			display: inline-block;
		}

		100% {
			opacity: 1;
		}
	}

	/* 隐藏动画--淡出 */
	.mescroll-fade-out {
		pointer-events: none;
		-webkit-animation: mescrollFadeOut .5s linear forwards;
		animation: mescrollFadeOut .5s linear forwards;
	}

	@-webkit-keyframes mescrollFadeOut {
		0% {
			opacity: 1;
		}

		100% {
			opacity: 0;
		}
	}

	@keyframes mescrollFadeOut {
		0% {
			opacity: 1;
		}

		100% {
			opacity: 0;
		}
	}

	.h0 {
		height: 0;
		overflow: hidden;
	}

	.mescroll-empty-box {
		width: 100%;
		min-height: 100%;
		position: relative;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	/* 定位的方式固定高度 */
	.mescroll-uni.mescroll-uni-fixed {
		/* z-index: 1; */
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		width: auto;
		/* 使right生效 */
		height: auto;
		/* 使bottom生效 */
	}

	.empty-tip {
		margin-top: 20upx;
		font-size: 24upx;
		color: gray;
	}

	/* 无任何数据的空布局 */
	.mescroll-empty {
		box-sizing: border-box;
		width: 100%;
		padding: 40upx;
		text-align: center;
	}

	.mescroll-empty .empty-tip {
		margin-top: 20upx;
		font-size: 24upx;
		color: gray;
	}

	.mescroll-empty .empty-btn {
		display: inline-block;
		margin-top: 40upx;
		min-width: 200upx;
		padding: 18upx;
		font-size: 28upx;
		border: 1upx solid #E04B28;
		border-radius: 60upx;
		color: #E04B28;
	}

	.mescroll-empty .empty-btn:active {
		opacity: .75;
	}

	.mescroll-empty .empty-icon {
		height: 200upx;
		width: 300upx;
	}
</style>