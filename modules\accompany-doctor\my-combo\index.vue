<template>
  <view class="main">
    <view :style="'height:' + statusBarHeight + 'px;'"></view>
    <view class="top-nav">
      <view class="top-nav-l" @click.stop="handleBack"><image mode="aspectFit" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-service-left-arrows.png'" class="header-search-img"/></view>
      <view class="top-nav-c">订单</view>
    </view>
    <view class="l-main">
      <tabs-sticky :fontBigger="true" :bdb="false" :overflowX="true" :overflowY="false" v-model="curIndex" :tabs="tabs" @change="changeTab"></tabs-sticky>
    </view>
    <scroll-refresh class="scroll-refresh-main" :isShowEmptySwitch="true" :fixed="false" :up="upOption" :down="downOption" @returnFn="returnFn" @scrollInit="scrollInit">
      <view class="order-list">
        <view class="order-item" v-for="item in contentList" :key="item.id" @click="handleClickJump(item.id)">
          <view class="order-item-t">
            <view class="order-item-t-l">{{ item.comboName }}</view>
            <view 
              class="order-item-t-r" 
              :style="{color:item.comboState == 0 ? '#FF5500' : item.comboState == 1 ? '#00B484' : item.comboState == 2 ? '#1D2029' : item.comboState == 3 ? '#868C9C' : item.comboState == 4 ? '#868C9C' : '#868C9C'}">
              {{ handleFilterOrderState(item.comboState) }}
            </view>
          </view>
          <view class="order-item-b">
            <view class="cost" v-if="item.comboPrice">套餐费用：<span>￥{{ item.comboPrice / 100 }}</span></view>
            <view class="content">套餐内容：{{ item.comboContent }}</view>
            <view class="time">有效时间：<span>{{ item.startTime }}~{{ item.endTime }}</span></view>
          </view>
        </view>
      </view>
    </scroll-refresh>
  </view>
</template>

<script>
  import TabsSticky from '@/components/basics/tabs-sticky-v3'
  export default {
    components:{
      TabsSticky,
    },
    data(){
      return{
        file_ctx: this.file_ctx,
        curIndex:0,
        statusBarHeight: 0,
        tabs:[{name:'生效中',id:1},{name:'已完成',id:2},{name:'已失效',id:4},{name:'待支付',id:0},{name:'已取消',id:3}],
        mescroll: null, // mescroll实例对象
        downOption: {
          auto: false // 不自动加载
        },
        upOption: {
          auto: false, // 不自动加载
          empty: {
            top: 0,
            zIndex: 999,
          }
        },
        contentList:[],
        comboState:1
      }
    },
    onLoad(){},
    mounted(){
      this.$nextTick(() => {
        this.init()
      })
      this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight // 获取设备的状态栏高度
    },
    methods:{
      handleClickJump(id){
        this.$navto.push('comboOrderIndex',{id})
      },
      handleFilterOrderState(type){
        switch(type){
          case 0:
            return '待支付'
          case 1:
            return '生效中'
          case 2:
            return '已完成'
          case 3:
            return '已取消'
          case 4:
            return '已失效'
        }
      },
      handleBack(){
        this.$navto.back(1)
      },
      changeTab(index) {
        this.comboState = this.tabs[index].id
        this.init()
      },

      returnFn(obj) {
        const that = this
        setTimeout(function () {
          let params = {
            current: obj.pageNum,
            size: obj.pageSize,
            condition: {
              comboState:that.comboState
            }
          }
          that.$api.accompanyDoctor.accompanycombouserQueryMiniAppPage(params).then(res => {
            // let data = res.data.records.map(item=>({...item,listCover:isDomainUrl(item.listCover)}))
            let data = res.data.records.map(item=>(
              {
                ...item,
                startTime:that.$common.formatDate(new Date(item.startTime), 'yyyy-MM-dd').replace(/-/g, '.'),
                endTime:that.$common.formatDate(new Date(item.endTime), 'yyyy-MM-dd').replace(/-/g, '.'),
              }))
            if (obj.pageNum === 1) {
              that.contentList = []
            }
            that.contentList = [...that.contentList, ...data]
            obj.successCallback && obj.successCallback(data)
          })
        }, that.$constant.noun.scrollRefreshTime)
      },  

      scrollInit(scroll) {
        scroll.optUp.page.num = 1
        scroll.optUp.page.size = 10
        this.mescroll = scroll
      },
      init() {
        this.$nextTick(() => {
          this.mescroll.triggerDownScroll()
        })
      },
    },
 }
</script>

<style lang='scss' scoped>
  .main{
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow-x: hidden;
    background-color: #fff;
  }
  .top-nav{
    // position: fixed;
    width: calc(100% - 16rpx);
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 40px;
    line-height: 40px;
    padding: 0 16rpx;
    // z-index: 999;
    // padding: 0 32rpx 0 24rpx;
    .top-nav-l{
      display: flex;
      width: 48rpx;
      height: 48rpx;
      image{
        width: 100%;
        height: 100%;
      }
    }
    .top-nav-c{
      flex: 1;
      text-align: center;
      height: 44rpx;
      font-weight: 500;
      font-size: 32rpx;
      color: #2D2F38;
      line-height: 44rpx;
      margin-right: 48rpx;
    }
  }
  .l-main{
    position: sticky;
    top: 0;
    z-index: 999;
    padding: 22rpx 32rpx 22rpx 12rpx;
    ::v-deep.tabs-sticky{
      overflow-y: hidden;
      .tabs-sticky-body{
        padding: 0;
        .tab{
          text{
            padding: 0;
          }
          &:last-child{
            padding-right: 50rpx;
          }
        }
      }
    }
  }
  .scroll-refresh-main{
    flex: 1;
    // height: calc(100% - 60rpx);
    overflow-x: hidden;
    background-color: #F4F6FA;
    ::v-deep .mescroll-empty-box{
      // min-height: 0%;
      position: absolute !important;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
    .order-list{
      overflow-x: hidden;
      padding: 0 32rpx;
      .order-item{
        padding:32rpx 24rpx;
        margin-top: 20rpx;
        background-color: #fff;
        border-radius: 16rpx;
        .order-item-t{
          display: flex;
          justify-content: space-between;
          .order-item-t-l{
            font-size: 32rpx;
            color: #1D2029;
            line-height: 44rpx;
          }
          .order-item-t-r{
            font-size: 28rpx;
            color: #1687F7;
            line-height: 40rpx;
          }
        }
        .order-item-b{
          margin-top: 16rpx;
          .cost,.time,.content{
            font-size: 24rpx;
            color: #4E5569;
            line-height: 34rpx;
            span{
              color: #1D2029;
            }
          }
          .content{
            margin: 8rpx 0;
          }
          .hospital{}
          .accompany-teacher{
            display: flex;
            .teacher-box{
              display: flex;
              align-items: center;
              .img{
                width: 32rpx;
                height: 32rpx;
                background-color: pink;
                border-radius: 50%;
              }
              span{
                margin-left: 8rpx;
                font-size: 22rpx;
                color: #1D2029;
                line-height: 32rpx;
              }
            }
          }
          .evaluate{
            display: flex;
            align-items: center;
            justify-content: center;
            height: 72rpx;
            width: 100%;
            margin-top: 24rpx;
            font-size: 26rpx;
            color: #00B484;
            background: #FFFFFF;
            border-radius: 36rpx;
            border: 1rpx solid #00B484;
            &::after{
              border: none !important;
            }
          }
          .my-evaluate{
            display: flex;
            align-items: center;
            height: 72rpx;
            background: #F4F6FA;
            border-radius: 8rpx;
            padding: 18rpx 16rpx 20rpx;
            box-sizing: border-box;
            margin-top: 24rpx;
            .evaluate-img{
              display: flex;
              width: 132rpx;
              height: 32rpx;
              background-color: yellow;
            }
          }
        }
      }
    }
  }
</style>