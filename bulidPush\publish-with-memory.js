/**
 * 交互式小程序发布脚本（带内存增强）
 * 用法: node --max-old-space-size=4096 publish-with-memory.js
 */
import readline from 'readline';
import { exec } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import optionsMap from "../config/env/optionsMap.js";

// 获取当前模块的文件路径
const __filename = fileURLToPath(import.meta.url);
// 获取当前模块的目录名称
const __dirname = dirname(__filename);
// 定义项目名称
const project = 'gb-hybrid-app-ps';

// 创建readline接口
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// 显示所有可用的小程序
function showAvailableApps() {
  console.log('\n可用的小程序列表：');
  optionsMap.forEach(app => {
    console.log(`  [${app.id}] ${app.title || `小程序${app.id}`}`);
  });
  console.log();
}

// 询问小程序ID
function askAppIds() {
  return new Promise((resolve) => {
    showAvailableApps();
    rl.question('请输入要发布的小程序ID(多个用逗号分隔，如 1,2,3，回车发布全部): ', (answer) => {
      if (!answer.trim()) {
        // 如果用户直接回车，返回所有小程序的ID
        console.log('未输入ID，将发布所有小程序');
        const allIds = optionsMap.map(app => app.id);
        resolve(allIds);
        return;
      }

      const idList = answer.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
      if (idList.length === 0) {
        console.log('错误: 请至少输入一个有效ID');
        return askAppIds().then(resolve);
      }
      resolve(idList);
    });
  });
}

// 询问发布描述
function askDescription() {
  return new Promise((resolve) => {
    rl.question('请输入发布描述: ', (answer) => {
      if (!answer.trim()) {
        console.log('错误: 描述不能为空');
        return askDescription().then(resolve);
      }
      resolve(answer.trim());
    });
  });
}

// 询问版本号
function askVersion() {
  return new Promise((resolve) => {
    rl.question('请输入版本号(如 1.2.5): ', (answer) => {
      const version = answer.trim();
      if (!version || !/^\d+\.\d+\.\d+$/.test(version)) {
        console.log('错误: 请输入有效的版本号，格式为 x.y.z');
        return askVersion().then(resolve);
      }
      resolve(version);
    });
  });
}

// 确认发布
function confirmPublish(idList, description, version) {
  return new Promise((resolve) => {
    const selectedApps = optionsMap.filter(app => idList.includes(app.id));

    console.log('\n发布确认：');
    console.log('  版本号:', version);
    console.log('  发布描述:', description);
    console.log('  选择的小程序:');

    selectedApps.forEach(app => {
      console.log(`    [${app.id}] ${app.title || `小程序${app.id}`}`);
    });

    rl.question('\n确认发布吗? (y/n): ', (answer) => {
      resolve(answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes');
    });
  });
}

// 覆写文件
function override(id) {
  return new Promise((resolve) => {
    let filePath = path.join(__dirname, '..', 'config', '/env/options.js');
    // 读取文件内容
    let fileContent = fs.readFileSync(filePath, 'utf8');
    console.log('开始覆写文件', id);
    // 将文件内容按行分割成数组
    let lines = fileContent.split('\n');
    // 覆写
    lines[2] = `let currentId = ${id};\r`;
    let modifiedContent = lines.join('\n');
    fs.writeFileSync(filePath, modifiedContent, 'utf8');
    console.log('覆写成功', id);
    resolve(1);
  });
}

// 执行CLI命令编译小程序（不上传）
function compileApp(appId) {
  return new Promise((resolve, reject) => {
    console.log(`编译小程序，ID: ${appId}`);
    // 使用较简单的命令，避免路径问题
    const cmd = `cli publish --platform mp-weixin --project ${project} --upload false`;

    exec(cmd, (err, stdout, stderr) => {
      // 记录完整输出以便调试
      console.log('--- 命令输出开始 ---');
      if (stdout) console.log('标准输出:', stdout);
      if (stderr) console.log('错误输出:', stderr);
      console.log('--- 命令输出结束 ---');

      // 检查是否有错误
      if (err) {
        console.error('编译命令执行失败:', err);
        resolve(false);
        return;
      }

      // 检查输出中是否包含错误信息
      if ((stderr && (stderr.includes('error:') || stderr.includes('错误'))) ||
          (stdout && (stdout.includes('error:') || stdout.includes('错误')))) {
        console.error('编译输出包含错误信息');
        resolve(false);
        return;
      }

      // 需要立即检查编译结果
      const mpWeixinPath = path.join(__dirname, '..', 'unpackage', 'dist', 'build', 'mp-weixin');
      if (!fs.existsSync(mpWeixinPath)) {
        console.error('编译后目录不存在，编译可能失败');
        resolve(false);
        return;
      }

      console.log('编译命令执行成功');
      resolve(true);
    });
  });
}

// 检查编译结果是否有效
function checkCompileResult() {
  const mpWeixinPath = path.join(__dirname, '..', 'unpackage', 'dist', 'build', 'mp-weixin');
  const appJsonPath = path.join(mpWeixinPath, 'app.json');

  console.log('检查编译结果有效性...');

  // 详细记录检查过程
  if (!fs.existsSync(mpWeixinPath)) {
    console.error('编译目录不存在:', mpWeixinPath);
    return false;
  } else {
    console.log('编译目录存在:', mpWeixinPath);
  }

  if (!fs.existsSync(appJsonPath)) {
    console.error('app.json不存在:', appJsonPath);
    return false;
  } else {
    console.log('app.json文件存在');
  }

  // 检查app.json内容
  try {
    const appJson = JSON.parse(fs.readFileSync(appJsonPath, 'utf8'));
    if (!appJson.pages || !appJson.pages.length) {
      console.error('app.json 不包含页面信息');
      return false;
    }
    console.log('app.json内容有效，包含页面数:', appJson.pages.length);

    // 检查其他关键文件
    const mainJsPath = path.join(mpWeixinPath, 'app.js');
    if (!fs.existsSync(mainJsPath)) {
      console.error('app.js不存在');
      return false;
    }

    console.log('编译结果验证通过');
    return true;
  } catch (e) {
    console.error('app.json 解析失败:', e);
    return false;
  }
}

// 使用miniprogram-ci上传小程序
async function uploadApp(appInfo) {
  const mpWeixinPath = path.join(__dirname, '..', 'unpackage', 'dist', 'build', 'mp-weixin');

  if (!fs.existsSync(mpWeixinPath)) {
    console.error('编译目录不存在');
    return Promise.reject(new Error('编译目录不存在'));
  }

  try {
    // 引入miniprogram-ci
    const ci = (await import('miniprogram-ci')).default;

    console.log(`上传小程序: ${appInfo.title} (${appInfo.appid})`);

    // 创建项目
    const project = new ci.Project({
      appid: appInfo.appid,
      type: 'miniProgram',
      projectPath: mpWeixinPath,
      privateKeyPath: appInfo.privateKeyPath,
      ignores: ['node_modules/**/*'],
      setting: {
        es6: true,
        minify: true
      },
      // 显式设置babelSetting避免错误
      babelSetting: {
        ignore: [],
        disablePlugins: [],
        outputPath: ""
      }
    });

    // 上传代码
    const result = await ci.upload({
      project,
      version: appInfo.version,
      desc: appInfo.description,
      setting: {
        es6: true,
        minify: true
      },
      robot: 1,
      onProgressUpdate: console.log
    });

    console.log('上传成功:', result);
    return result;
  } catch (error) {
    console.error('上传失败:', error);
    throw error;
  }
}

// 延迟函数
function delay(ms) {
  return new Promise(resolve => {
    console.log(`等待${ms/1000}秒...`);
    setTimeout(resolve, ms);
  });
}

// 显示加载动画
function showLoading(text) {
  const symbols = ['|', '/', '-', '\\'];
  let i = 0;
  const timer = setInterval(() => {
    process.stdout.write(`\r${text} ${symbols[i++%4]} `);
  }, 100);

  return () => {
    clearInterval(timer);
    process.stdout.write('\r完成!            \n');
  };
}

// 主流程
async function main() {
  try {
    console.log('\n===== 小程序交互式发布工具（内存增强版）=====');

    // 询问用户输入
    const idList = await askAppIds();
    const description = await askDescription();
    const version = await askVersion();

    // 确认发布
    const confirmed = await confirmPublish(idList, description, version);

    if (!confirmed) {
      console.log('发布已取消');
      rl.close();
      return;
    }

    console.log('准备发布...');

    // 创建小程序列表
    const appList = optionsMap.filter(app => idList.includes(app.id))
      .map(app => ({
        id: app.id,
        title: app.title || `小程序${app.id}`,
        appid: app.appId,
        version: version,
        description: description,
        privateKeyPath: path.join(__dirname, 'keys', app.privatekey)
      }));

    console.log('待发布小程序:', appList.map(app => `${app.title} (${app.id})`).join(', '));

    // 依次处理每个小程序
    for (let i = 0; i < appList.length; i++) {
      const app = appList[i];
      console.log(`\n========== 处理 [${i+1}/${appList.length}] ${app.title} (ID: ${app.id}) ==========`);

      // 1. 覆写配置文件
      await override(app.id);

      // 2. 编译小程序（不上传）
      console.log('开始编译小程序...');
      const loadingCompile = showLoading('编译中');
      const compiled = await compileApp(app.id);
      loadingCompile();

      if (!compiled) {
        console.error(`编译命令执行失败，跳过上传: ${app.title}`);
        continue;
      }

      console.log('编译命令执行完成，等待5秒确保文件生成...');
      await delay(5000);

      // 检查编译结果是否有效
      if (!checkCompileResult()) {
        console.error(`编译结果无效，跳过上传: ${app.title}`);
        continue;
      }

      console.log('编译结果有效，准备上传...');

      // 3. 上传小程序
      console.log('开始上传小程序代码...');
      const loadingUpload = showLoading('上传中');

      // 最多尝试3次
      let success = false;
      for (let attempt = 1; attempt <= 3 && !success; attempt++) {
        try {
          if (attempt > 1) {
            console.log(`第${attempt}次尝试上传...`);
          }

          await uploadApp(app);
          success = true;
          console.log(`成功发布小程序: ${app.title}`);
        } catch (error) {
          console.error(`上传失败(${attempt}/3):`, error.message);

          if (attempt < 3) {
            console.log('等待10秒后重试...');
            await delay(10000);
          }
        }
      }

      loadingUpload();

      if (!success) {
        console.error(`无法上传小程序: ${app.title}，3次尝试均失败`);
      }

      // 最后一个小程序不需要等待
      if (i < appList.length - 1) {
        console.log('等待15秒后处理下一个小程序...');
        await delay(15000);
      }
    }

    console.log('\n所有小程序处理完成!');
  } catch (error) {
    console.error('执行过程中出错:', error);
  } finally {
    rl.close();
  }
}

// 执行主流程
main().catch(console.error);
