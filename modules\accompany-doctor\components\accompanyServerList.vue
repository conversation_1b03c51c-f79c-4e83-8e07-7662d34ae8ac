<template>
      <view class="confirm">
        <view class="tabTop">
          <view class="tabItem" @click="changeTab(index)" :class="{selectTabItem:tabIndex === index}" v-for="(item,index) in tabMap" :key="index">
            {{item.title}}
          </view>
        </view>
        <scroll-view :scroll-top="scrollTop" scroll-y="true" class="scroll-Y" @scrolltolower="upper">
          <view v-for="(item,index) in orderMap" :key="item.id">
            <view class="headerTab" @click="gotoDetails(item)">
              <view class="orderTab">
                <view class="orderTitle">{{item.serviceName}}</view>
                <view class="orderTap">{{orderStateMap[item.orderState]}}</view>
              </view>
              <view class="orderTime">
                <view class="orderBoxTitle">服务时间：</view>
                {{timestampToDateTime(item.startTime)}}~{{timestampToDateTime(item.endTime)}}
              </view>
              <view class="orderTime">
                <view class="orderBoxTitle">就诊人：</view>
                {{item.bookName}}
              </view>
              <view class="orderTime">
                <view class="orderBoxTitle">就诊医院：</view>
                {{item.hospitalName}}
              </view>
              <view class="orderTime">
                <view class="orderBoxTitle">订单id：</view>
                {{item.id}}
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
</template>

<script>
  import { mapState } from "vuex";
  import uniPopup from '@/components/uni/uni-popup/uni-popup.vue'
  import common from '@/common/util/main'
  import serverOptions from '@/config/env/options'

  export default{
    components: {
        uniPopup
    },
    props:{

    },
    watch:{
    },
    data(){
      return {
        orderMap:[],
        current:0,
        loadEnd:false,
        receivedOrder: this.$static_ctx + "image/business/hulu-v2/receivedOrder.png",
        userInfo:{},
        tabIndex:0,
        tabMap:[
          {title:'全部',options:{orderStateList:[5,6,7]}},
          {title:'待服务',options:{orderState:5}},
          {title:'服务中',options:{orderState:6}},
          {title:'已完成',options:{orderState:7}},
          ],
        orderStateMap:['','待接入','待支付','待派单','待接单','待服务','服务中','已完成','已取消']
      }
    },
    computed: {
      ...mapState("user", {
        accountId: (state) => state.accountId,
        fansRecord: (state) => state.fansRecord,
      }),
    },
     async mounted() {
       const userId = serverOptions.getUserId(this);
       if(!userId){
         uni.showToast({title:'请先登录',icon:'none'})
         return;
       }
       let {data} = await this.$api.accompanyDoctor.getAccompanyemployeeOneByUserId({userId})
       this.userInfo = data;
       this.getServerData();
    },
    methods:{
      gotoDetails(item){
        this.$navto.push('accompanyDetails',{id:item.id, fromProvider: true})
      },
      timestampToDateTime(timestamp) {
        // 创建一个新的Date对象，传入的时间戳是以毫秒为单位的
        var date = new Date(timestamp);
        // 获取年、月、日、时、分、秒
        var year = date.getFullYear();
        var month = ("0" + (date.getMonth() + 1)).slice(-2); // 月份是从0开始的，所以+1，并补零
        var day = ("0" + date.getDate()).slice(-2); // 获取天数，并补零
        var hour = ("0" + date.getHours()).slice(-2); // 获取小时，并补零
        var minute = ("0" + date.getMinutes()).slice(-2); // 获取分钟，并补零
        var second = ("0" + date.getSeconds()).slice(-2); // 获取秒数，并补零

        // 返回格式化的字符串
        return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
      },
      upper(){
        console.log('触底');
        if(this.loadEnd) return
        this.current++;
        this.getServerData();
      },
      changeTab(index){
        this.tabIndex = index;
        this.initData();
      },
      initData(){
        this.current = 0;
        this.orderMap.length = 0;
        this.getServerData();
      },
      async getServerData(key){
        let queryOptions = {
          current:this.current,
          size:10,
          condition:{employeeId:this.userInfo.id,...this.tabMap[this.tabIndex].options},
        }
        let {data:{records,total}} = await this.$api.accompanyDoctor.accompanybookQuery(queryOptions);
        this.orderMap.push(...records);
        if(total <= this.orderMap.length){
          this.loadEnd = true
        }
      }
    }
  }
</script>

<style lang="scss">
.confirm{
  width: 100%;
  background: #F4F6FA;
  border-radius: 24rpx 24rpx 0rpx 0rpx;
  box-sizing: border-box;
  .tabTop{
    width: 100vw;
    height: 80rpx;
    line-height: 80rpx;
    background: #00B484;
    display: flex;
    justify-content: space-evenly;
    .tabItem{
      font-weight: 300;
      font-size: 30rpx;
      color: #FFFFFF;
    }
    .selectTabItem{
      font-weight: 600;
      font-size: 30rpx;
      color: #FFFFFF;
    }
  }
  .headerTab{
    position: relative;
    width: 686rpx;
    background: #FFFFFF;
    border-radius: 16rpx;
    padding: 32rpx 24rpx;
    box-sizing: border-box;
    margin-bottom: 20rpx;

    .orderTime{
      font-weight: 400;
      font-size: 24rpx;
      color: #1D2029;
      display: flex;
      .orderBoxTitle{
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #4E5569;
        line-height: 34rpx;
        text-align: right;
        font-style: normal;
      }
    }
    .orderTab{
      width: 100%;
      display: flex;
      .orderTitle{
        font-weight: 600;
        font-size: 32rpx;
        color: #1D2029;
        margin: 8rpx 0 20rpx 0;
      }
      .orderTap{
        font-weight: 500;
        font-size: 28rpx;
        color: #00B484;
        margin: 8rpx 0 20rpx 0;
        margin-left: auto;
      }
    }
    .assignOrder{
      width: 158rpx;
      height: 48rpx;
      background: #FFEEE6;
      border-radius: 0rpx 16rpx 0rpx 16rpx;
      text-align: center;
      line-height: 48rpx;
      font-weight: 500;
      font-size: 26rpx;
      color: #FF7029;
      position: absolute;
      top: 0;
      right: 0;
    }
    .clearOrder{
      width: 128rpx;
      height: 48rpx;
      background: #EAEBF0;
      border-radius: 0rpx 16rpx 0rpx 16rpx;
      position: absolute;
      top: 0;
      right: 0;
    }
    .receivedOrder{
      width: 144rpx;
      height: 144rpx;
      border: 4rpx solid #C9CCD4;
      position: absolute;
      top: 0;
      right: 0;
    }
    .serverIcon{
      width: 144rpx;
      height: 144rpx;
      background: #D8D8D8;
      border-radius: 12rpx;
      border: 1rpx solid #D9DBE0;
      margin-right: 20rpx;
    }
    .changeServer{
      width: 148rpx;
      height: 52rpx;
      background: #FFFFFF;
      border-radius: 36rpx;
      border: 1rpx solid #D9DBE0;
      margin-left: auto;
    }
    .buttonMap{
      display: flex;
      justify-content: space-evenly;
      margin-top: 24rpx;
      gap: 30rpx;
      .clearBtn{
        height: 72rpx;
        background: #FFFFFF;
        border-radius: 36rpx;
        border: 1rpx solid #D9DBE0;
        font-weight: 400;
        font-size: 26rpx;
        color: #1D2029;
        text-align: center;
        line-height: 72rpx;
        flex-grow: 1;
      }
      .orderBtn{
        height: 72rpx;
        background: #00B484;
        border-radius: 36rpx;
        font-weight: 500;
        font-size: 26rpx;
        color: #FFFFFF;
        text-align: center;
        line-height: 72rpx;
        flex-grow: 1;
      }
    }
  }
}
.scroll-Y{
  height: calc(100vh - 176rpx - 254rpx);
  box-sizing: border-box;
  margin-top: 20rpx;
  padding: 0rpx 32rpx 88rpx 32rpx;
}
</style>
