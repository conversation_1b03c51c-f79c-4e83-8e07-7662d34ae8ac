<template>
  <view class="main-container" :style="{
    paddingTop:headerTop + 'px'
  }">
    <title-header :headerobj='headerobj' @init='initHeader'></title-header>
    <view class="hospital-search" v-if="hospitalObj.hospitalId">
      <view class="title-head">医院名称：{{ hospitalObj.hospitalName }}</view>
    </view>
    <view class="main-search" v-if="showType != 2 && !hospitalObj.hospitalId">
      <!-- top="88" -->
      <title-selector v-model="regForm.search" :config="searchitem" @updateForm='changeSearch'  />
      <!-- <search placeholder="搜索" :fixed="true" top='0'  v-model="regForm.search" @changeSearch="changeSearch"></search> -->
    </view>
    <!-- top="92" -->

    <scroll-refresh :fixed='false' :up="upOption" :down="downOption" @returnFn="returnFn" @scrollInit="scrollInit">
      <view class="main-content">
        <diagnosis-item :type='2' :list="pdList" :hospitalObj="hospitalObj"></diagnosis-item>
      </view>
    </scroll-refresh>
  </view>
</template>

<script>
// import MescrollUni from '@/components/uni/mescroll-uni'
// import search from '@/components/basics/form/search'
import TitleSelector from '@/components/business/module/v1/title-selector/index.vue';
import diagnosisItem from '@/modules/activity/pages/diagnosis/components/diagnosis-item/item.vue'
import titleHeader from '@/modules/activity/components/title-header/index.vue'
export default {
  components: {
    // MescrollUni,
    // search,
    TitleSelector,
    diagnosisItem,
    titleHeader
  },
  data() {
    return {
      headerobj:{
       headBgColor:'#0bdaa6',
       titleType:"txt",
       titleTxt:"医院服务评价",
       currentIndex:0,
       contentColor:"#fff",
      },
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $accurateConversion: this.$accurateConversion,
      file_ctx: this.file_ctx,
      $static_ctx: this.$static_ctx,
      $timePlugin: this.$timePlugin,
      $validate: this.$validate,
      mescroll: null, // mescroll实例对象
      downOption: {
        auto: false // 不自动加载
      },
      upOption: {
        auto: false // 不自动加载
      },
      pdList: [], // 列表数据
      isInit: false, // 列表是否已经初始化
      scrollY: 0,
      regForm: {
        search: ''
      },
      typeofAll: '',
      field: [],
      activityId: '',
      headerTop:55,
      searchitem:{
        label: '医院名称',type:'select', nextRequest: true, required: true, array: [],name:"installId"
      },
      hospitalObj:{
        hospitalId:null, //医院id
        hospitalName:'', //医院名称
      },
      showType:null,
    }
  },
  watch: {
    // 监听下标的变化

  },
  onLoad(paramsObj) {
    const query = this.$Route.query
    this.hospitalObj.hospitalId = query.id 
    this.hospitalObj.hospitalName = query.hospitalName
    this.showType = query.type
    if(query.type == 2){
      this.headerobj.titleTxt = '用药评价'
      this.hospitalObj = {...this.hospitalObj,type:query.type}
    }
    if (!this.$validate.isNull(query)) {
      this.activityId = query.activityId
    }
  },
  mounted() {
    this.init()
    this.gethospitallistAllHospital()
  },
  methods: {
    initHeader(height){
      this.headerTop = height
    },
    // 获取医院
    gethospitallistAllHospital(){
      // console.log('gethospitallistAllHospital')
      this.$api.activity.hospitallistAllHospital({
        installType:[1] // 安装单位类型(1医院,2药店,3超市)
      }).then(res => {

          res.data.forEach(item => {
            item.label = item.name
            item.value = item.id
          })
          res.data.unshift({
            label:'全部',
            value:'all',
          })
          // console.log('res.data',res.data)
          this.searchitem.array = res.data
      })
    },
    navtoGo(url = '', obj = {}) {
      this.$navto.push(url, obj)
    },
    init(val) {
      this.$nextTick(() => {
        this.isInit = true // 标记为true
        this.regForm.search = ''
        this.mescroll.triggerDownScroll()
      })
    },
    changeSearch(obj) {
      this.mescroll.optUp.page.num = 1
      this.mescroll.optUp.page.size = 7
      // this.regForm.search = obj.name
      this.mescroll.triggerDownScroll()
    },
    scrollInit(scroll) {
      scroll.optUp.page.num = 1
      scroll.optUp.page.size = 7
      this.mescroll = scroll
    },
    returnFn(obj) {
      const that = this
      function queryPage(pageNum, pageSize, fn) {
        const param = {
          current: pageNum,
          size: pageSize,
          condition: {
            // openStatus:2,
            // runStatus:2
            accountId:that.$common.getKeyVal('user', 'accountId', true),
            installId:that.hospitalObj.hospitalId,
            // type:1,
            type:that.showType,
          },
        }
        if (that.regForm.search && that.regForm.search != 'all') {
          param.condition.installId = that.regForm.search
        }
        that.$api.activity.hospitalserviceevaluatequerypage(param).then(res => {
          if (res && res.data.records) {
            for (const a in res.data.records) {
              const data = res.data.records[a]
              data.createText = that.$common.formatDate(new Date(data.updateTime), 'yyyy-MM-dd HH:mm')
            }
            // debugger
            fn(res.data.records)
          }
        })
      }
      setTimeout(function() {
        queryPage(obj.pageNum, obj.pageSize, (data) => {
          if (obj.pageNum === 1) {
            that.pdList = []
          }
          that.pdList = that.pdList.concat(data)
          obj.successCallback && obj.successCallback(data || [])
        })
      }, that.$constant.noun.scrollRefreshTime)
    }
  }
}
</script>
<style lang="scss" scoped>
  .main-container{
    background-color:#f7f7f7;
    height: 100vh;
  }
  .hospital-search{
    background-color:#FFFFFF;
    padding: 20upx;
    .title-head{
      font-size: 30upx;
    }
  }
  .main-search{
    background-color:#FFFFFF;
    padding: 0 20upx;
  }
  .main-content{
    padding:10upx 30upx 30upx 30upx;
    background: #f7f7f7;
    .portrait-list{
      /*margin-top: 10upx;*/
      .li{
        @include rounded(20upx);
        background-color:#FFFFFF;
        padding:16upx;
        margin-bottom: 24upx;
        .img{
          width: 240upx;
          height: 180upx;
          display: inline-block;
          vertical-align: middle;
          @include rounded(20upx);
          .role-image{
            width: 100%;
            height: 100%;
          }
        }
        .content{
          height: 180upx;
          position: relative;
          width: calc(100% - 266upx);
          padding-left: 24upx;
          display: inline-block;
          vertical-align: middle;
          .title{
            font-size: 32upx;
            color: #333333;
            display: block;
            font-weight: 600;
            /*line-height: 40upx;*/
            @include ellipsis(2);
            /*margin-bottom: 16upx;*/
          }
          .title-t{
            font-size: 28upx;
            color: #999999;
            display: block;
          }
          .footer{
            position: absolute;
            width: 100%;
            bottom: 0;
            .icon-time{
              display: inline-block;
              margin-right: 10upx;
              @include iconImg(26, 26, '/system/icon-time.png');
            }
            .time{
              color: #999999;
              font-size: 24upx;
              display: inline-block;
            }
            .btn{
              display: inline-block;
              right: 0;
              top: 0;
              width: 108upx;
              height: 40upx;
              line-height: 40upx;
              font-size: 24upx;
            }
          }
        }
      }
    }
  }
</style>
