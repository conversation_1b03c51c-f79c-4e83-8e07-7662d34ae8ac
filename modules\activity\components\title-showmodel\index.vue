<template>
	<view v-if="visible" >
		<!-- <view class="ly-dialog"> -->
		<!-- 阴影背景 -->

		<!-- 内容盒子 -->
		<view class="ly-dialog-box" :class="animation_box">
			<view class="ly-shadow" :class="ly_shadow_animate" :style="{background:' rgba(0,0,0,' + ly_bg_opacity + ')'
				}" @click="closeDialog('close')">
			</view>
			<!-- 下出 -->
			<block v-if="type == 'bottom'">
				<view class="ly-dialog-bottom" :class="rootClass" :style="{
          '--top':top,
          'z-index':zindex
        }">
					<slot></slot>
				</view>
			</block>

			<!-- 上出 -->
			<block v-else-if="type == 'top'">
				<view class="ly-dialog-top" :class="rootClass">
					<slot></slot>
				</view>
			</block>

			<!-- 右出 -->
			<block v-else-if="type == 'right'">
				<view class="ly-dialog-right" :class="rootClass">
					<slot></slot>
				</view>
			</block>
			<!-- 中出 -->
			<block v-else-if="type == 'center'">
				<view class="ly-dialog-center" :class="rootClass">
					<slot></slot>
				</view>
			</block>
			<!-- 中出提示框 -->
			<block v-else-if="type == 'tips'">
				<view class="ly-dialog-center" :class="rootClass">
					<view class="ly-dialog-t">{{subtitle}}</view>
					<view class="ly-dialog-c">{{subcontent}}</view>
					<view class="ly-dialog-b">
						<view class="ly-dialog-btn" @click='closeDialog("query")'>确定</view>
					</view>
				</view>
			</block>

			<!-- 左出 -->
			<block v-else>
				<view class="ly-dialog-left" :class="rootClass">
					<slot></slot>
				</view>
			</block>


		</view>
	</view>
</template>

<script>
	export default {
		name: "ly-showmodel",
		// 1. 左弹窗 设置滑块宽度
		// right:calc(100vw - 600rpx)!important
		// 2. 上弹窗 设置滑块高度
		// bottom:calc(100vh - 400rpx)!important
		// 3. 下弹框 设置滑块高度
		// top:calc(100vh - 400rpx)!important
		// 4. 右弹框 设置滑块宽度
		// left:calc(100vw - 400rpx)!important
		props: {
			// 弹框显示属性
			show: {
				type: Boolean,
			},
			// 使用哪种弹框
			type: {
				type: String,
				default: "bottom" // 默认使用下弹框  bottom left top rifht
			},
			// 仅当提示框时生效
			subtitle: {
				type: String,
				default: '标题'
			},
			subcontent: {
				type: String,
				default: '代码是写出来给人看的，附带能在机器上运行'
			},
			rootClass: {
				type: String,
				default: ""
			},

      top:{
        type:String,
        default:'20%'
      },
      zindex:{
        type:[Number,String],
        default:1,
      }

		},
		data() {
			return {
				// 显示弹框
				visible: false,
				// 弹框黑色阴影深度
				ly_bg_opacity: 0.3,

				// 弹框背景过渡
				ly_shadow_animate: "ly_shadow_animate",

				// 内容弹出动画过渡 之下
				animation_box: 'ly_bottom_animation',
				// 之上
				// animation_box:'ly_top_animation',
				// 之右
				// animation_box:'ly_right_animation',
				// 之左
				// animation_box: 'ly_left_animation'
				// 弹框值简写
				typeArr: {
					'left': "ly_left_animation",

					'bottom': 'ly_bottom_animation',
					'top': "ly_top_animation",
					'right': "ly_right_animation",
					'center': 'ly_center_animation',
					"tips": 'ly_center_animation'
				}
			};
		},
		methods: {
			// 打开弹窗
			openDialog(e) {
				this.visible = true;
				this.ly_shadow_animate = 'ly_shadow_animate';
				this.animation_box = this.typeArr[this.type]

			},
			// 关闭弹框
			closeDialog(type) {
				console.log('jjj4')
				if (type) {
					// var type = e.currentTarget.dataset.type;
					if (this.type == 'tips' && type != 'query') {
						return;
					}
				}

				if (this.show) {
					console.log('33')
					this.$emit('cancel')
				}
				var that = this;
				this.ly_shadow_animate = 'ly_shadow_animate_out';
				this.animation_box = this.typeArr[this.type] + '_out';

				setTimeout(() => {
					this.visible = false
				}, 400)

			},
		},
		watch: {
			show(params) {
				if (params) {
					this.openDialog()
				} else {
					this.closeDialog()
				}
			}
		}
	}
</script>

<style scoped>
/* miniprogram/pages/ceshi/index.wxss */
page {
  /* background-color: #fff; */
}

/* 阴影 */
.ly-shadow {
  position: fixed;
  /* z-index: 1; */
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

/* 阴影弹框弹出 */
.ly_shadow_animate {
  animation: wrap_animate 0.5s ease-in-out forwards
}

@keyframes wrap_animate {
  0% {}

  100% {
    background: rgba(0, 0, 0, 0.35);
  }
}
.ly_shadow_animate_out {
  animation: wrap_animate_out 0.4s ease-in-out forwards
}

@keyframes wrap_animate_out {
  0% {
    background: rgba(0, 0, 0, 0.35);
  }

  100% {
    background: rgba(0, 0, 0, 0);
  }
}


.ly-dialog-box {
  position: fixed;
  height: 100vh;
  width: 100vw;
  z-index: 9999;
  /* left: 0;
  right: 0; */
}
/* 下弹出弹框 */
.ly-dialog-bottom{
  background-color: #fff;
  position: absolute;
  bottom: 0;
  width: 100%;
  /* top: 20%; */
  top:var(--top,20%);
  left: 0;
  right: 0;
  will-change: transform;
  box-shadow: 0 -2rpx 80rpx rgb(0 0 0 / 30%);
}
/* 底部弹框样式过渡 */
.ly_bottom_animation{
  top: 0;
  right:0;
  /* right:0;
  left: 0; */

  animation: popup_animate 0.5s ease forwards;
}
@keyframes popup_animate {
  0% {
    top: 99vh;
  }

  100% {
    opacity: 1;
    top: 0vh;
  }
}
/* 谈出 */
.ly_bottom_animation_out {
  top: 99vh;
  right:0;
  left: 0;
  animation: popup_animate_outp 0.8s ease forwards;
}

@keyframes popup_animate_outp {
  0% {
    opacity: 1;
    top: 0vh;
  }

  100% {
    opacity: 0;
    top: 99vh;
  }
}
/* 上部弹框样式过渡 */

.ly_top_animation{
  bottom: 100vh;
  /* top: 0;
  right:0; */
  left: 0;
  right: 0;
  animation: popup_animate2 0.5s ease forwards;
}
@keyframes popup_animate2{
  0% {
   bottom:100vh
  }

  100% {
    opacity: 1;
    bottom: 0vh
  }
}
/* 谈出 */
.ly_top_animation_out {
  left: 0;
  right: 0;
  animation: popup_animate_out2 0.4s ease forwards;
}

@keyframes popup_animate_out2 {
  0% {
    opacity: 1;
    bottom: 0vh
  }

  100% {
    opacity: 0;
    bottom:100vh;
  }
}
/* 上弹出弹窗 */
.ly-dialog-top{
  background-color: #fff;
  position: absolute;
  bottom: 20%;
  width: 100%;
  top: 0%;
  left: 0;
  right: 0;
  will-change: transform;
  box-shadow: 0 -2rpx 80rpx rgb(0 0 0 / 30%);
}

/* 右弹框 */
.ly-dialog-right{
  background-color: #fff;
  position: absolute;
  bottom: 0%;
  /* width: 100%; */
  top: 0%;
  left: 20%;
  right: 0;
  will-change: transform;
  box-shadow: 0 -2rpx 80rpx rgb(0 0 0 / 30%);
}
/* 右弹框动画过渡 */
.ly_right_animation{
  top:0;
  left: 100vw;
  animation: popup_animate3 0.5s ease forwards;
}
@keyframes popup_animate3{
  0% {
   left:100vw
  }

  100% {
    opacity: 1;
    left: 0vw
  }
}
/* 谈出 */
.ly_right_animation_out {
  top: 0;
  animation: popup_animate_out3 0.4s ease forwards;
}

@keyframes popup_animate_out3 {
  0% {
    opacity: 1;
    left: 0vw
  }

  100% {
   left: 100vw;
  }
}

/* 左弹框 */
.ly-dialog-left{
  background-color: #fff;
  position: absolute;
  bottom: 0;
  width: 100%;
  top: 0;
  /* left: 0; */
  right: 20%;
  will-change: transform;
  box-shadow: 0 -2rpx 80rpx rgb(0 0 0 / 30%);
}
/* 左弹框动画过渡 */
.ly_left_animation{
  left: -100vw;
  top: 0;
  animation: popup_animate4 0.5s ease forwards;
}
@keyframes popup_animate4{
  0% {
   left:-100vw
  }

  100% {
    opacity: 1;
    left: 0vw
  }
}
/* 谈出 */
.ly_left_animation_out {
  top: 0;
  animation: popup_animate_out4 0.4s ease forwards;
}

@keyframes popup_animate_out4 {
  0% {
    opacity: 1;
    left: 0vw
  }

  100% {
    left:-100vw
  }
}

/* 居中弹出 */
.ly-dialog-center{
  background-color: #fff;
  position: absolute;
  /* 注意：使用center 时，如果自定义弹窗高度及宽度，请按规则设置left 及 top 值 */
  top: calc(50% - 200rpx);
  left: calc(50% - 40vw);
  height: 400rpx;
  width: 80vw;
  border-radius: 30rpx;

  will-change: transform;
  box-shadow: 0 -2rpx 80rpx rgb(0 0 0 / 30%);
  -webkit-animation: zoom 0.6s;
  animation: zoom 0.6s;
  transform-origin: center;
  resize: both;
  overflow: auto;
  animation-fill-mode:forwards
}
.ly_center_animation,.ly_center_animation_out{
  top: 0;
  left: 0;

}
.ly_center_animation_out{
  opacity: 0;
}
.ly-dialog-t{
  height: 80rpx;
  line-height: 80rpx;
  font-size: 36rpx;
  text-align: center;
  margin-top: 30rpx;
  padding: 0 20rpx;

}
.ly-dialog-c{
  font-size: 32rpx;
  line-height: 50rpx;
  padding: 0 20rpx;
  /* text-align: center; */
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;

}
.ly-dialog-b{
  height: 90rpx;
  width: 100%;
  padding: 0 20rpx;

}
.ly-dialog-btn{
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, #ff6034, #ee0a24);
  border-radius: 40rpx;
  color: #fff;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 40rpx;
  font-size: 36rpx;
}

@-webkit-keyframes zoom {
  from {
      -webkit-transform: scale(0)
  }
  to {
      -webkit-transform: scale(1) translate(-50%,-50%);
  }
}



@keyframes zoom {
  from {
      transform: scale(0)
  }
  to {
      transform: scale(1)
  }
}





</style>
