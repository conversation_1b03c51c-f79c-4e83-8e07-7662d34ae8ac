<template>
  <view>
    <view class="template-content">
      <view class="ul" v-for="(item, index) in arr">
          <view class="li">
            <view class="l">
              {{ item.name }}
            </view>
            <view class="r" @tap="openPreview(item.name,item.attachId)">
              <em class="icon-preview"></em>
            </view>
          </view>
        </view>
      <view class="upload-panel">
        <view class="head">
          <view class="l">
          </view>
          <view class="r">
            上传附件
          </view>
        </view>
        <title-file :disabled="fileDisabled" :cData="attachmentList" :config="{multiSelectCount: 50}" @returnFn="imgReturnFn"></title-file>
        <view class="file-panel">
        </view>
      </view>
    <!--  <view class="audit-panel" v-if="auditLog.desc.length > 0">
        <view class="head">
          <view class="l">
          </view>
          <view class="r">
            审核回复
          </view>
        </view>
        <view class="text">
          {{ auditLog.desc }}
        </view>
      </view> -->
    </view>
    <!-- <view class="template-footer">
      <view class="btn-bg m-tb-20-auto"
            :class="btnObject.color"
            @tap="submit()" >{{ btnObject.text }}</view>
    </view> -->
  </view>
</template>

<script>
import TitleFile from '@/modules/activity/components/title-file/index'
// import FilePicker from '@/components/uni/uni-file-picker/upload-file'
export default {
  name: "docTemplate",
  components: {
    TitleFile,
    // FilePicker
  },
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $accurateConversion: this.$accurateConversion,
      file_ctx: this.file_ctx,
      $timePlugin: this.$timePlugin,
      // regForm:{
      //   attachmentList:[]
      // },
      attachmentList:[],
      btnObject:{
        color:'',
        text:'提交'
      },
      fileDisabled: false
    }
  },
  props: {
    // 参数设置
    arr: {
      type: [Array,Object],
      required: true,
      default: () => {
        return []
      }
    },
    // 默认值
    value: {
      type: [Array],
      required: true,
      default: () => {
        return []
      }
    },
    // 参数设置
    // auditLog: {
    //   type: [Object],
    //   required: true,
    //   default: () => {
    //     return {}
    //   }
    // },
    // 业务主键
    businessId: {
      type: [String, Number],
      required: true,
      default() {
        return ''
      }
    },
  },
  watch: {
    value: {
      handler(val) {
        // debugger
        if (val == ""){
          val = []
        }
        this.attachmentList = val
      },
      deep: true
    },
    // auditLog: {
    //   handler(val) {
    //     if (val.auditStatus === 1){
    //       this.btnObject.color = 'bg-color-1'
    //       this.btnObject.text = '审核中'
    //       this.fileDisabled = true
    //     } else if (val.auditStatus === 2){
    //       this.btnObject.color = 'bg-color-2'
    //       this.btnObject.text = '审核通过'
    //       this.fileDisabled = true
    //     } else if (val.auditStatus === 3){
    //       this.btnObject.color = 'bg-color-3'
    //       this.btnObject.text = '退回，可重新提交'
    //       this.fileDisabled = false
    //     }
    //   },
    //   deep: true
    // },
  },
  mounted(){
    this.attachmentList = this.value;
  },
  methods: {
    // submit() {
    //   if (this.auditLog.auditStatus === 1 || this.auditLog.auditStatus === 2){
    //     return
    //   }
    //   this.$emit('returnFn', this.attachmentList)
    // },
    /**
     * 上传图片
     * @param v
     */
    imgReturnFn(v) {
      this.attachmentList = v;
      this.$emit('returnFn',this.attachmentList)
    },
    openPreview(name,url){
      const that =this
      this.$navto.push('WebHtmlView', { src: this.file_ctx+ url, title: name })


      // uni.downloadFile({
      //   url: this.file_ctx+ url,//在线地址
      //   success: function (res) {
      //     var filePath = res.tempFilePath;
      //     uni.openDocument({
      //       filePath: filePath,
      //       showMenu: true,
      //       success: function (res) {
      //         console.log("打开文档成功");
      //       },
      //     })
      //   },
      //   fail:function (){
      //     that.$uniPlugin.toast('下载文件失败！')
      //   }
      // })
    }
  }
}
</script>

<style lang="scss" scoped>
.template-footer{
  margin-top: 30upx;
}
.bg-color-1{
  background: #e6a23c;
}
.bg-color-2{
  background: #67c23a;
}
.bg-color-3{
  background: #f56c6c;
}
.head {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  display: -webkit-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
  -webkit-box-align: center;
  -ms-flex-align: center;
  -webkit-align-items: center;
  align-items: center;
  margin-top: 30upx;
  .l{
    width: 10upx;
    height: 32upx;
    background: #00D29D;
    border-radius: 1px;
  }
  .r{
    margin-left: 10upx;
  }
}
  .template-content{
    margin-top: 10upx;
    .ul{
      .li{
        display: -webkit-box;
        display: -moz-box;
        display: -ms-flexbox;
        display: flex;
        display: -webkit-flex;
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        -webkit-justify-content: flex-start;
        justify-content: flex-start;
        -webkit-box-align: center;
        -ms-flex-align: center;
        -webkit-align-items: center;
        align-items: center;
        border-bottom: 1px dashed #efecec;
        height: 70upx;
        line-height: 70upx;
        .l{
          text-align: left;
          width: 100%;
          color: #2196f3;
        }
        .r{

          .icon-preview{
            margin: 10upx auto;
            display: block !important;
            @include iconImg(30, 30,'/business/icon-eye-opening.png');
          }
        }
      }
    }
  }
</style>
