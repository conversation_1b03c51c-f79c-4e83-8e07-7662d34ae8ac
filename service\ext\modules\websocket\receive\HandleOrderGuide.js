import MessageDefault from "./MessageDefault"

// 推送咨询订单引导语
export default class HandleOrderGuide extends MessageDefault {
    constructor (websocket) {
        super()
        this.websocket = websocket
    }

    match (dataJson) {
        const { ORDER_GUIDE } = this.chatCmd
        return dataJson.cmd === ORDER_GUIDE
    }

    processMessage (dataJson) {
        const { ORDER_GUIDE } = this.chatCmd
        const orderDetail = dataJson.data
        if (this.$common.getKeyVal('chat', 'chatItem').orderId !== orderDetail.id) return
        let nodereplyconfig = this.$common.getKeyVal('chat', 'nodereplyconfig')
        if (orderDetail.consultStatus != 3) {
            nodereplyconfig = nodereplyconfig.filter(item => item.nodeType === 1)
        } else {
            nodereplyconfig = nodereplyconfig.filter(item => item.nodeType === 2)
        }
        let nodeConfigReply = this.$validate.isNull(orderDetail.nodeConfigReply) ? [] : JSON.parse(orderDetail.nodeConfigReply)
        const currentNodeConfig = nodereplyconfig.find(item => item.id === nodeConfigReply[nodeConfigReply.length - 1].nodeConfigId)
        const chatItem = this.$common.getKeyVal('chat', 'chatItem')
        this.$common.setKeyVal('chat', 'orderDetail', orderDetail)
        
        // 是否是完结节点
        if (currentNodeConfig.finishEnabled === 1) {
            this.$common.setKeyVal('chat', 'guideMode', '', false)
            return
        }
        
        // 如下一节点不为文案类型则不推送
        const nextNodeConfig = this.getNextConsultNodeConfig()
        switch (nextNodeConfig.pushType) {
            // 选择科室
            case 3:
                this.$common.setKeyVal('chat', 'guideMode', 'department', false)
                break
            // 咨询问题
            case 4:
                this.$common.setKeyVal('chat', 'guideMode', 'issue', false)
                break
            // 咨询人档案
            case 5:
                this.$common.setKeyVal('chat', 'guideMode', 'patient', false)
                break
            // 订阅消息
            case 6:
                let dto =  {
                    cmd: ORDER_GUIDE,
                    data: {
                        orderId: chatItem.orderId,
                        userId: chatItem.userId,
                        chatUserId: chatItem.chatUserId,
                        nodeConfigId: nextNodeConfig.id
                    }
                }
                this.websocket.webSocketSend(ORDER_GUIDE, dto)
                break
            // 咨询类型
            case 7:
                this.$common.setKeyVal('chat', 'guideMode', 'consultType', false)
                break
            default:
                if (currentNodeConfig.nextEnabled === 2) {
                    let dto =  {
                        cmd: ORDER_GUIDE,
                        data: {
                            orderId: chatItem.orderId,
                            userId: chatItem.userId,
                            chatUserId: chatItem.chatUserId,
                            nodeConfigId: nextNodeConfig.id
                        }
                    }
                    this.websocket.webSocketSend(ORDER_GUIDE, dto)
                }
                break
        }
    }

    /**
     * 根据订单详情获取下一咨询节点配置
     * @return {object} 下一咨询节点配置
     */
    getNextConsultNodeConfig () {
        const orderDetail = this.$common.getKeyVal('chat', 'orderDetail')
        let nodeConfigReply = this.$validate.isNull(orderDetail.nodeConfigReply) ? [] : JSON.parse(orderDetail.nodeConfigReply)
        let nodereplyconfig = this.$common.getKeyVal('chat', 'nodereplyconfig')
        // if (orderDetail.consultStatus != 3) {
        //     nodereplyconfig = nodereplyconfig.filter(item => item.nodeType === 1)
        // } else {
        //     nodereplyconfig = nodereplyconfig.filter(item => item.nodeType === 2)
        // }
        // 暂未开始资讯
        if (this.$validate.isNull(nodeConfigReply)) {
            return nodereplyconfig[0]
        }
        nodeConfigReply = nodeConfigReply.map(item => {
            return {
                ...item,
                weight: nodereplyconfig.find(i => i.id === item.nodeConfigId).weight
            }
        })
        nodeConfigReply = nodeConfigReply.sort((a,b) => a.weight - b.weight)
        const nodeConfigId = nodeConfigReply[nodeConfigReply.length - 1].nodeConfigId
        // 最近的咨询节点
        for (let i = 0; i < nodereplyconfig.length; i++) {
            if (nodereplyconfig[i].id === nodeConfigId) {
                return nodereplyconfig[i+1]
            }
        }
    }
}