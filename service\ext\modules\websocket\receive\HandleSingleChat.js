// import MessageDefault from './MessageDefault'
import MessageCheckView from './MessageCheckView'

export default class HandleSingleChat extends MessageCheckView {
    constructor (websocket) {
        super()
        this.websocket = websocket
    }

    match (dataJson) {
        const { SINGLE_CHAT_CMD } = this.chatCmd
        return dataJson.cmd === SINGLE_CHAT_CMD
    }

    async processMessage (dataJson) {
        this.inputHeight = 0
        this.chatItem = this.$common.getKeyVal('chat', 'chatItem', false)
        let messageList = this.$common.getKeyVal('chat', 'messageList', false)
        messageList = JSON.parse(JSON.stringify(messageList))

        let d = dataJson.data
        // 是否是当前聊天对象
        if (this.chatItem.orderId !== d.orderId) return
        if ((this.chatItem.userId + '' !== d.fromUserId + '') && (d.fromUserId + '' !== this.chatItem.chatUserId + '')) return
        if (d.fromUserId + '' === this.chatItem.userId + '') { //发送回调
            messageList = messageList.map(item => {
                if (item.hasBeenSentId + '' == d.createTime + '-' + this.chatItem.userId) {
                    return this.etlMsgItem(d, 1)
                } else {
                    return item
                }
            })
        } else { //接收别人消息
            messageList.push(this.etlMsgItem(d, 1))
        }

        this.messageListEl = this.$common.getKeyVal('chat', 'messageListEl', false)
        this.inputHeight = this.$common.getKeyVal('chat', 'bottomBoxHeight', false)
        // 这里查询是否曝光只是为了获取滚动条是否在最底部
        this.checkDivShow()
        this.$common.setKeyVal('chat', 'messageList', messageList, false)
    }

    // 检查最后最新的一条数据 是否曝光 曝光则滚动到最底部 没有则提示有最新的消息
    async checkDivShow () {
        let messageList = this.$common.getKeyVal('chat', 'messageList', false)

        const item = messageList[messageList.length - 1]

        if (!item) {
            setTimeout(() => {
                uni.pageScrollTo({
                    scrollTop: 99999,
                    duration: 0
                })
            }, 0)
            return
        }

        // #ifdef H5
        var getDiv = document.getElementById('msg-' + item.hasBeenSentId)
        // #endif
        
        // #ifndef H5
        const query = uni.createSelectorQuery().in(this.messageListEl)
        var getDiv = query.select('#msg-' + item.hasBeenSentId)
        // #endif

        if (getDiv!=null){
          if (getDiv.id !== undefined || getDiv.boundingClientRect !== undefined) {
            // 曝光上报
            if (await this.isElementInViewport(getDiv) && !this.isDivDisplay(getDiv)) {
                console.log('滚动到底部-------------')
              setTimeout(() => {
                  uni.pageScrollTo({
                      scrollTop: 99999,
                      duration: 0
                  })
              }, 0)
            }
          }
        }
    }
}