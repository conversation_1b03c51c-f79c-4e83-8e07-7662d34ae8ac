<template>
  <view class="container">
    <!-- 内容区域 -->
    <view class="content-wrapper">
      <!-- 富文本内容 -->
      <view class="content-body">
        <rich-text class="text-rich" :nodes="content" preview></rich-text>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="footer">
      <view class="btn-apply" @click="handleApply">立即报名</view>
    </view>
  </view>
</template>

<script>
import MNavBar from '@/components/basics/m-nav-bar/index.vue';
import serverOptions from '@/config/env/options';

export default {
  components: {
    MNavBar
  },
  data() {
    return {
      content: '', // 富文本内容
      employeeFeeButton: 0, // 报名费开关状态
      employeeFee: 0, // 费用（分）
      employeeFeeContent: '' // 报名费内容
    };
  },
  onLoad() {
    // 初始化数据
    this.initData();
  },
  methods: {
    // 初始化数据
    async initData() {
      try {
        // 设置默认介绍内容
        this.setDefaultContent();

        // 获取服务商信息
        await this.getProviderInfo();
      } catch (error) {
        console.error('初始化数据失败', error);
      }
    },

    // 设置默认介绍内容
    setDefaultContent() {
      // 这里可以设置一个默认的介绍内容
      this.content = `
      <div>
        <h3 style="color: #333; margin-bottom: 15px;">陪诊师职责</h3>
        <p style="color: #666; line-height: 1.6;">陪诊师是专业医疗陪诊服务人员，主要负责陪伴患者看诊，协助患者办理就诊流程，提供专业陪诊服务。</p>

        <h3 style="color: #333; margin: 15px 0;">工作内容</h3>
        <ul style="color: #666; line-height: 1.6; padding-left: 20px;">
          <li>协助患者进行预约挂号</li>
          <li>陪伴患者前往医院就诊</li>
          <li>协助患者检查、取药等流程</li>
          <li>为患者提供医院导诊服务</li>
          <li>解答患者关于医院流程的问题</li>
        </ul>

        <h3 style="color: #333; margin: 15px 0;">申请条件</h3>
        <ul style="color: #666; line-height: 1.6; padding-left: 20px;">
          <li>年龄18-65周岁，身体健康</li>
          <li>普通话标准，沟通能力良好</li>
          <li>有责任心，能够耐心服务</li>
          <li>熟悉医院就诊流程</li>
          <li>有相关医疗、护理或服务经验优先</li>
        </ul>
      </div>
      `;
    },

    // 获取服务商信息
    async getProviderInfo() {
      try {
        const result = await this.$api.accompanyDoctor.accompanyproviderQueryOne({id: serverOptions.providerId});
        if (result && result.code === 0 && result.data) {
          const providerData = result.data;

          // 获取报名费相关字段
          this.employeeFeeButton = providerData.employeeFeeButton || 0;
          this.employeeFee = providerData.employeeFee || 0;
          this.employeeFeeContent = providerData.employeeFeeContent || '';

          console.log('服务商信息获取成功', {
            employeeFeeButton: this.employeeFeeButton,
            employeeFee: this.employeeFee,
            employeeFeeContent: this.employeeFeeContent ? '内容已获取' : '无内容'
          });

          // 使用employeeFeeContent作为页面显示内容
          if (providerData.employeeFeeContent) {
            this.content = providerData.employeeFeeContent;
          }
        }
      } catch (error) {
        console.error('获取服务商信息失败', error);
      }
    },

    // 立即报名
    handleApply() {
      // 跳转到陪诊师申请页面
      this.$navto.push('Application', {
        employeeFeeButton: this.employeeFeeButton,
        employeeFee: this.employeeFee,
        employeeFeeContent: encodeURIComponent(this.employeeFeeContent)
      });
    }
  }
};
</script>

<style lang="scss">
.container {
  background-color: #F4F6FA;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.content-wrapper {
  flex: 1;
  padding: 32rpx;
  margin-bottom: 120rpx; // 为底部按钮留出空间
}

.content-body {
  line-height: 1.6;
  padding: 32rpx;
  background: #FFFFFF;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.text-rich {
  width: 100%;
  font-size: 28rpx;
  color: #4E5569;
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background: #FFFFFF;
  padding: 20rpx 32rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
}

.btn-apply {
  width: 100%;
  height: 88rpx;
  background: #00B484;
  border-radius: 44rpx;
  text-align: center;
  line-height: 88rpx;
  font-weight: 500;
  font-size: 32rpx;
  color: #FFFFFF;
}
</style>
