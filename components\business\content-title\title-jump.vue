<!-- 内容模块标题配件组件 <title-jump :title="xx" :text="xx" @returnFn = "returnFn"></title-jump> -->
<template>
  <view class="title-jump" :class="{'bdt':bdt,'bdb':bdb}" @tap="returnFn">
    <view class="l-t" :class="{'font-size-28': isFS28}">{{title}}<text class="xingxing" v-if="isRequired">*</text></view>
    <view :class="{'l-r': true, 'width-calc-300 color-999': !iconState}">
      <text class="l-r-text" :style="{'color':color}" :class="{'l-r': true, 'width-calc-80': dNum, 'font-size-28': isFS28}">{{thisText}}</text>
      <view class="l-r-num" v-if="dNum">{{dNum}}</view>
    </view>
    <em v-if="iconState" class="jump" :class="[isDelete ? 'icon-close-black-circle' : 'icon-gengduo']" @tap.stop="delReturnFn"></em>
  </view>
</template>

<script>
export default {
  props: {
    // 数组下标
    dIndex: {
      type: [String, Number],
      default() {
        return ''
      }
    },
    // 显示提醒
    dNum: {
      type: [String, Number],
      default() {
        return ''
      }
    },
    // 是否开启font-size-28的字体大小
    isFS28: {
      type: Boolean,
      default() {
        return false
      }
    },
    // 禁止事件回调
    banReturnFn: {
      type: Boolean,
      default() {
        return true
      }
    },
    // 控制是否出现删除功能
    isDelete: {
      type: Boolean,
      default() {
        return false
      }
    },
    // 控制icon是否显示
    iconState: {
      type: Boolean,
      default() {
        return true
      }
    },
    // 标题名称
    title: {
      type: String,
      default() {
        return ''
      }
    },
    // 返回content
    text: {
      type: String,
      default() {
        return ''
      }
    },
    // border-top
    bdt: {
      type: Boolean,
      default() {
        return false
      }
    },
    bdb: {
      type: Boolean,
      default() {
        return true
      }
    },
    color: {
      type: String,
      default() {
        return '#666666'
      }
    },
    isRequired: {
      type: Boolean,
      default() {
        return false
      }
    }
  },
  data() {
    return {
      thisText: ''
    }
  },
  watch: {
    text: {
      handler(val) {
        this.watchDataMain(val)
      },
      deep: true
    }
  },
  mounted() {
    this.watchDataMain(this.text)
  },
  methods: {
    watchDataMain(val) {
      this.thisText = val
    },
    returnFn() {
      if (this.banReturnFn) {
        if(!this.$validate.isNull(this.dIndex)){
          const obj = {
            text: this.text,
            index: this.dIndex
          }
          this.$emit('returnFn', obj)
        }else{
          this.$emit('returnFn', this.text)
        }
      }
    },
    delReturnFn() {
      if (this.banReturnFn) {
        if(!this.$validate.isNull(this.dIndex)){
          const obj = {
            text: this.text,
            index: this.dIndex
          }
          if (this.isDelete) {
            this.$emit('delReturnFn', obj)
          } else {
            this.returnFn()
          }
        }else{
          if (this.isDelete) {
            this.$emit('delReturnFn', this.text)
          } else {
            this.returnFn()
          }
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.font-size-28 {
  font-size: 28upx !important;
}
.m-r-30{
  margin-right: 30upx;
}
.title-jump{
  height: 88upx;
  overflow: hidden;
  background-color:#ffffff;
  padding: 0 30upx;
  .l-t{
    height: 88upx;
    line-height: 88upx;
    color:#333333;
    font-size:32upx;
    width: 280upx;
    display: inline-block;
    vertical-align: middle;
    margin-right: 20upx;
    @include ellipsis(1);
  }
  .l-r{
    height: 88upx;
    width: calc(100% - 334upx);
    display: inline-block;
    vertical-align: middle;
    .l-r-text {
      width: 100%;
      font-size: 32upx;
      line-height: 88upx;
      color: #333;
      text-align: right;
      @include ellipsis(1);
    }
    .l-r-num {
      display: inline-block;
      vertical-align: middle;
      margin-left: 16upx;
      height: 32upx;
      line-height: 32upx;
      max-width: 64upx;
      min-width: 32upx;
      font-size: 24upx;
      text-align: center;
      @include rounded(16upx);
      background: #FF4A4A;
      color: #fff;
      padding: 0 8upx;
      overflow: hidden;
      box-sizing: border-box;
    }
    .width-calc-80{
      width: calc(100% - 80upx);
    }
  }
  .width-calc-300{
    width: calc(100% - 300upx);
  }
  .jump{
    width:34upx;
    height: 34upx;
    display: inline-block;
    vertical-align: middle;
  }
  .icon-close-black-circle{
    @include iconImg(34,34,'/business/icon-close-black-circle.png');
  }
  .xingxing{
    color: #F85E4C;
    padding-top: 6upx;
    font-size: 32upx;
    display: inline-block;
  }
}
</style>
