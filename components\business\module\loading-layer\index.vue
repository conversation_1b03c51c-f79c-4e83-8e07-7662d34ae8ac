
<template>
  <view class="loading-layer" v-if="isLoadingLayer" :style="{'position': position, 'z-index': defaultConfig.zIndex}" :class="[isLoadingLayerClass ? 'display-block' : 'loading-layer-animation']">
    <view class="mask" :style="{'z-index': (defaultConfig.zIndex + 1)}" :class="[isMask ? 'bg-000-view' : 'bg-fff-view']"></view>
    <view class="loading-layer-img" :style="{'z-index': (defaultConfig.zIndex + 2)}"></view>
  </view>
</template>

<script>

  export default {
    components: {

    },
    data() {
      return {
        // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
        $constant: this.$constant,
        $common: this.$common,
        $accurateConversion: this.$accurateConversion,
        file_ctx: this.file_ctx,
        $static_ctx: this.$static_ctx,
        $timePlugin: this.$timePlugin,
        $validate: this.$validate,
        isLoadingLayer: false,
        isLoadingLayerClass: false,
        timer: undefined,
        defaultConfig: {
          zIndex: 99999999 // z-index层级
        }
      }
    },
    watch: {
      cData: {
        handler(val) {
          this.watchDataMain(val)
        },
        immediate: true,
        deep: true
      },
      config: {
        handler(val) {
          this.copyConfig()
        },
        deep: true
      }
    },
    props: {
      // 初始值传值，用于回显
      cData: {
        type: Boolean,
        required: false,
        default() {
          return false
        }
      },
      // 参数设置
      config: {
        type: Object,
        required: false,
        default: () => {
          return {}
        }
      },
      // 定位css属性
      position: {
        type: String,
        default() {
          return 'fixed'
        }
      },
      // 是否开启遮罩层
      isMask: {
        type: Boolean,
        required: false,
        default() {
          return false
        }
      }
    },
    computed: {

    },
    mounted() {
      this.copyConfig()
      this.init()
    },
    methods: {
      // 初始化拷贝config对象
      copyConfig() {
        const that = this
        const obj = that.config
        Object.keys(obj).forEach(function(key) {
          that.defaultConfig[key] = obj[key]
        })
      },
      // 初始化数据
      init() {
        this.watchDataMain(this.cData)
      },
      /**
       * 监听cData主逻辑方法
       */
      watchDataMain(val) {
        const that = this
        that.isLoadingLayerClass = val
        if (!val) {
          if (that.timer) {
            clearTimeout(that.timer)
          }
          that.timer = setTimeout(() => {
            that.isLoadingLayer = val
          },1000)
        } else {
          that.isLoadingLayer = val
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
  .color-999 {
    color: #999 !important;
  }
  .bg-fff-view {
    background: #fff !important;
  }
  .bg-000-view {
    background: #000 !important;
    @include opacity(.5);
  }
  .loading-layer {
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    display: block;
    .mask {
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
    }
    .loading-layer-img {
      width: 200upx;
      height: 200upx;
      background-image: url($static_ctx + 'image/business/hulu-v2/icon-hulu-loading.gif');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      position: absolute;
      left: 50%;
      top: 50%;
      -webkit-transform: translate(-50%,-50%);
      -moz-transform: translate(-50%,-50%);
      transform:translate(-50%,-50%);
    }
  }
  .loading-layer-animation {
    animation: myfirst .5s;
    -moz-animation: myfirst .5s;
    -webkit-animation: myfirst .5s;
    -o-animation: myfirst .5s;
    -moz-animation-fill-mode: forwards;
    -webkit-animation-fill-mode: forwards;
    -o-animation-fill-mode: forwards;
    animation-fill-mode: forwards
  }

  @keyframes myfirst {
    0% {
      @include opacity(1);
    }
    20% {
      @include opacity(.8);
    }
    40% {
      @include opacity(.6);
    }
    60% {
      @include opacity(.4);
    }
    80% {
      @include opacity(.2);
    }
    100% {
      @include opacity(0);
      display: none;
    }
  }

  @-moz-keyframes myfirst {
    0% {
      @include opacity(1);
    }
    20% {
      @include opacity(.8);
    }
    40% {
      @include opacity(.6);
    }
    60% {
      @include opacity(.4);
    }
    80% {
      @include opacity(.2);
    }
    100% {
      @include opacity(0);
      display: none;
    }
  }

  @-webkit-keyframes myfirst {
    0% {
      @include opacity(1);
    }
    20% {
      @include opacity(.8);
    }
    40% {
      @include opacity(.6);
    }
    60% {
      @include opacity(.4);
    }
    80% {
      @include opacity(.2);
    }
    100% {
      @include opacity(0);
      display: none;
    }
  }

  @-o-keyframes myfirst {
    0% {
      @include opacity(1);
    }
    20% {
      @include opacity(.8);
    }
    40% {
      @include opacity(.6);
    }
    60% {
      @include opacity(.4);
    }
    80% {
      @include opacity(.2);
    }
    100% {
      @include opacity(0);
      display: none;
    }
  }
</style>
