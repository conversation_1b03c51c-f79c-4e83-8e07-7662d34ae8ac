<template>
  <page>
    <view slot="content" class="content">
      <!-- tab菜单-->
      <tabs-sticky
        v-model="curIndex"
        :fixed="false"
        :tabs="tabs"
        @change="changeTab"
      ></tabs-sticky>
      <view class="main">
        <swiper class="swiper" :current="curIndex" @change="swiperChange">
          <swiper-item v-for="(item, index) in tabs" :key="index">
            <reply-list
              ref="replyListRef"
              :params="item"
            />
          </swiper-item>
        </swiper>
      </view>
    </view>
  </page>
</template>

<script>
import TabsSticky from '@/components/basics/tabs-sticky'
import replyList from './components/reply-list.vue'
// #ifdef H5
import wxOpenAuth from '@/mixins/wx-open-auth'
// #endif
export default {
  // #ifdef H5
  mixins: [wxOpenAuth],
  // #endif
  components: {
    TabsSticky,
    replyList
  },
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $accurateConversion: this.$accurateConversion,
      file_ctx: this.file_ctx,
      $static_ctx: this.$static_ctx,
      $timePlugin: this.$timePlugin,
      $validate: this.$validate,
      mescroll: null, // mescroll实例对象
      downOption: {
        auto: false // 不自动加载
      },
      upOption: {
        auto: false // 不自动加载
      },
      curIndex: 0, // 当前菜单下标,
      tabs: [{ name: '未回复', replyStatus: 2 }, { name: '已回复', replyStatus: 1 }]
    }
  },
  onLoad() {
    // #ifdef H5
    this.wxSDKInit() // mixins
    // #endif
  },
  onShow() {
    this.init()
  },
  methods: {
    // 轮播菜单
    swiperChange(e) {
      this.changeTab(e.detail.current)
    },
    changeTab(index) {
      this.curIndex = index
    },
    navtoGo(url = '', obj = {}) {
      this.$navto.push(url, obj)
    },
    init(val) {
      this.$nextTick(() => {
        const curIndex = this.curIndex
        this.changeTab(curIndex)
        this.tabs.forEach((item, index) => {
          this.$refs.replyListRef[index].init()
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
}
.main {
  flex: 1;
  .swiper {
    height: 100%;
  }
}
</style>
