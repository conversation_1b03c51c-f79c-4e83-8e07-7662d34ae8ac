<template>
  <view v-if="showAgreement" class="agreement-row" @click="handleToggleAgreement">
    <view class="custom-checkbox">
      <view
        class="checkbox-icon"
        :class="{ checked: value }"
      ></view>
    </view>
    <text class="agreement-text">我已阅读并同意</text>
    <view class="agreement-link" v-for="(item, index) in tempFilePathList" :key="index" @tap.stop="handleNavToOrderAgreement(item.filePath,item.name)">
      【<view class="agreement-name">{{item.name}}</view>】
    </view>
  </view>
</template>

<script>
import { mapState } from 'vuex' // 导入 mapState

// 页面类型映射常量
const PAGE_TYPES = {
  ORDER_PAGE: '1',         // 下单页
  PAYMENT_PAGE: '2',       // 单个订单支付页
  JOIN_ORDER_PAGE: '3',    // 联合订单支付页
  DISTRIBUTION_PAGE: '4',  // 申请分销页
}

// URL路径与页面类型的映射关系
const URL_PAGE_TYPE_MAP = {
  '/modules/accompany-doctor/service-reservation/index': {
    default: PAGE_TYPES.ORDER_PAGE,
    params: {
      'id': PAGE_TYPES.ORDER_PAGE,
      'classifyId': PAGE_TYPES.ORDER_PAGE,
      'name': PAGE_TYPES.ORDER_PAGE,
      'orderId': PAGE_TYPES.PAYMENT_PAGE
    }
  },
  '/modules/accompany-doctor/service-reservation/joinOrder/joinOrder': {
    default: PAGE_TYPES.JOIN_ORDER_PAGE
  },
  '/modules/accompany-doctor/distribute/index': {
    default: PAGE_TYPES.DISTRIBUTION_PAGE
  }
}

let pageMap = {
  // 下单页
  '1':{url:'/modules/accompany-doctor/service-reservation/index?id'},
  // 单个订单支付页
  '2':{url:'/modules/accompany-doctor/service-reservation/index?orderId'},
  // 联合订单支付页
  '3':{url:'/modules/accompany-doctor/service-reservation/joinOrder/joinOrder?orderId'},
  // 申请分销页
  '4':{url:'/modules/accompany-doctor/distribute/index'},
}
  export default{
    name: '',
    props:{
      provinceValue:{
        type:Object,
        default:null
      },
      value:{
        type:Boolean,
        default:false
      },
      fromQRCode: {
        type: Boolean,
        default: false
      },
      fromOrderQRCode: {
        type: Boolean,
        default: false
      }
    },
    mounted() {
    },
    watch:{
      // 判断是否传递了服务商信息
      provinceValue(newVal,oldVal){
        console.log('newVal',newVal);

        if(newVal){
          let{agreementList,agreementButton} = newVal
          if(!agreementButton) return
          // 获取当前的entryType
          let currentEntryType = this.getCurrentEntryType()
          // console.log('当前页面类型:', currentEntryType);

          // 扫码场景可能需要特殊处理
          if (this.fromQRCode) {
            if (this.fromOrderQRCode) {
              // console.log('确认二维码扫描进入订单支付页');
              currentEntryType = PAGE_TYPES.PAYMENT_PAGE;
            } else {
              // console.log('确认二维码扫描进入下单页');
              currentEntryType = PAGE_TYPES.ORDER_PAGE;
            }
          }

          // 过滤出当前entryType的协议
          let agreementUrlList = agreementList.map(e=>({
            url:this.file_ctx + e.url,
            entryType:e.entryType,
            name:e.name
          })).filter(e=>e.entryType.includes(currentEntryType))

          // console.log('筛选后的协议列表:', agreementUrlList);
          if(agreementUrlList.length === 0) {
            // console.log('当前页面类型无协议需要显示');
            return;
          }

          this.showAgreement = true
          // 加载文档
          this.loadDocument(agreementUrlList);
          this.$emit('input',!this.value);
          this.$emit('onLoadDocument',agreementUrlList);
        }
      }
    },
    data() {
      return {
        file_ctx: this.file_ctx,
        isAgreed: false, // 是否同意协议
        showAgreement: false, // 是否显示协议
        tempFilePathList:[]
      }
    },
    computed:{
      ...mapState('user', { //映射 isLogin 状态
        isLogin: state => state.isLogin
      }),
    },
    created() {},
    methods: {
      // 协议检查方法
      checkAgreement() {
        return new Promise((resolve) => {
          if (this.value) return resolve(true);

          // 获取协议名称列表用于提示
          let agreementNames = this.tempFilePathList.map(item => item.name).join('、');
          let agreementContent = agreementNames ? `我已阅读并同意《${agreementNames}》` : '我已阅读并同意相关协议';

          uni.showModal({
            title: '协议政策及免责条款',
            content: agreementContent,
            confirmColor: '#00B578',
            cancelColor: '#666',
            success: (res) => {
            if (res.confirm) {
              this.$emit('input',true);
              resolve(true);
            } else {
              uni.showToast({ title: '请先同意协议', icon: 'none' });
              resolve(false);
            }
          }
          });
        });
      },
      getCurrentEntryType(){
        // 扫码场景处理
        if (this.fromQRCode) {
          // 如果是订单支付页的二维码
          if (this.fromOrderQRCode) {
            // console.log('检测到扫码进入订单支付页');
            return PAGE_TYPES.PAYMENT_PAGE;
          }
          // 默认为下单页二维码
          // console.log('检测到扫码进入下单页');
          return PAGE_TYPES.ORDER_PAGE;
        }

        // 2. 通过页面实例检测订单页
        let pages = getCurrentPages();
        if (!pages || pages.length === 0) {
          // console.log('无法获取当前页面，默认为下单页');
          return PAGE_TYPES.ORDER_PAGE;
        }

        let currentPageInstance = pages[pages.length - 1];
        // 如果有订单ID，说明是支付页
        if (currentPageInstance && currentPageInstance.$vm &&
            currentPageInstance.$vm.$data && currentPageInstance.$vm.$data.orderId) {
          // console.log('检测到订单ID，判定为订单支付页');
          return PAGE_TYPES.PAYMENT_PAGE;
        }

        // 3. 通过URL路径检测页面类型
        let currentPage = currentPageInstance.$page.fullPath;
        // console.log('当前页面路径:', currentPage);

        // 遍历URL映射表查找匹配
        for (const baseUrl in URL_PAGE_TYPE_MAP) {
          if (currentPage.includes(baseUrl)) {
            const pageConfig = URL_PAGE_TYPE_MAP[baseUrl];

            // 检查URL参数
            if (pageConfig.params) {
              for (const param in pageConfig.params) {
                if (currentPage.includes(`?${param}=`) || currentPage.includes(`&${param}=`)) {
                  // console.log(`检测到参数: ${param}, 页面类型:`, pageConfig.params[param]);
                  return pageConfig.params[param];
                }
              }
            }

            // 没找到特定参数，返回默认值
            // console.log(`使用默认页面类型:`, pageConfig.default);
            return pageConfig.default;
          }
        }

        // 默认为下单页
        // console.log('未匹配到页面类型，默认为下单页');
        return PAGE_TYPES.ORDER_PAGE;
      },
      loadDocument(urlList){
        urlList.forEach(item => {
          uni.downloadFile({
            url:item.url,
            success: (res) => {
              if (res.statusCode === 200) {
                this.tempFilePathList.push({
                  filePath:res.tempFilePath,
                  ...item
                })
                console.log('下载文档成功',res.tempFilePath)
              } else {
                console.error('下载文件失败:', res);
              }
            },
            fail: (err) => {
              console.error('下载文件失败:', err);
            }
          });
        })
      },
      handleNavToOrderAgreement(filePath){
        if (!this.isLogin) return this.gotoLogin()
        uni.openDocument({
          filePath,
          success: (res) => {
            console.log('打开文档成功', res);
          },
          fail: (err) => {
            console.error('打开文档失败:', err);
          }
        });
      },
      // handleNavToOrderAgreement(){
      //   this.$navto.push('OrderAgreement')
      // },
      // 处理登录跳转，保存当前页面信息
      gotoLogin() {
        // 跳转到登录页面，并传递当前页面标识参数
        this.$navto.push('Login', {formPage: 'serviceReservation'});
      },
      toggleAgreement() {
        this.$emit('input',!this.value);
      },
      handleToggleAgreement(){
        if (!this.isLogin) { this.gotoLogin(); return; }
        this.toggleAgreement();
      }
    },
  }
</script>

<style scoped lang="scss">
    .agreement-row {
      width: 100vw;
      height: 50rpx;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      padding: 10px 0;
      border-bottom: 2rpx solid #E6E6E6;
      .custom-checkbox {
        display: inline-block;
        vertical-align: middle;
        margin-right: 8px;
        margin-left: 32rpx;
        .checkbox-icon {
          width: 28rpx;
          height: 28rpx;
          border: 2rpx solid #ccc;
          border-radius: 50%;
          position: relative;
          transition: all 0.3s;
        }
        .checkbox-icon.checked {
          background: #00B578;
          border-color: #00B578;
        }
        .checkbox-icon.checked::after {
          content: '';
          position: absolute;
          left: 50%;
          top: 50%;
          width: 16rpx;
          height: 8rpx;
          border: 4rpx solid #fff;
          border-top: none;
          border-right: none;
          transform: translate(-50%, -60%) rotate(-45deg);
        }
      }
      .agreement-text {
        font-size: 14px;
        color: #666;
      }
      .agreement-link {
        font-size: 14px;
        color: #1687F7;
        display: flex;
        .agreement-name{
          // max-width: 88rpx;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          vertical-align: middle;
        }
      }
    }
</style>
