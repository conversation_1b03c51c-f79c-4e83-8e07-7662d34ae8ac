!function(n,e){if("object"==typeof exports&&"object"==typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var t=e();for(var r in t)("object"==typeof exports?exports:n)[r]=t[r]}}(self,(function(){return function(){var n={867:function(n){n.exports={wx:{name:"微信",version:"7.5.0"}}}},e={};function t(r){var o=e[r];if(void 0!==o)return o.exports;var i=e[r]={exports:{}};return n[r](i,i.exports,t),i.exports}t.d=function(n,e){for(var r in e)t.o(e,r)&&!t.o(n,r)&&Object.defineProperty(n,r,{enumerable:!0,get:e[r]})},t.o=function(n,e){return Object.prototype.hasOwnProperty.call(n,e)},t.r=function(n){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(n,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(n,"__esModule",{value:!0})};var r={};return function(){"use strict";t.r(r),t.d(r,{App:function(){return lt},Component:function(){return Et},Page:function(){return pt}});var n=["request","getSystemInfoSync","getNetworkType","getStorageSync","setStorageSync"],e=["login"],o=["scanCode","chooseAddress","chooseImage","previewImage","chooseInvoiceTitle","chooseInvoice"],i=28,a=255,u=32,c=3e4,s="MiniApp-Stat",f="uuid_getstoragesync",_="ai",l="br",p="pm",d="pr",v="ww",h="wh",R="lang",g="wv",E="wvv",S="wsdk",A="sv",y="nt",m="ufo",O="gid",I="ev",N="dr",b="at",w="ls",P="uu",D="v",T="ak",G="wsr",L="ifo",W="rq_c",j="te",x="et",C="st",M="ge",V="life",q="ec",U="uo",k="qr",K="usr",Y="pp",F="pc",B="fp",J="ifp",$="so",H="ag",z="ps",Q="pdr",X="tp",Z="ct",nn="http://doc.aldwx.com",en="https://log.aldwx.com",tn={SDK_VERSION:en+"/config/app.json",SDK_DOWNLOAD:"https://tj.aldwx.com/downSDK",GET_OPEN_ID:en+"/authorize/mini_program_openid",SEND_LOG:en+"/d.html"},rn="请参考接入文档 "+nn+" 小程序统计平台-快速接入指南-自定义事件！",on="请参考接入文档 "+nn+" 小程序统计平台-快速接入指南-智慧零售分析！",an={ERR_MISS_APPKEY:"请在 ald-stat-conf.js 文件中填写小程序统计/广告监测平台创建小程序后生成的 app_key，请参考接入文档 "+nn+" 小程序统计平台-快速接入指南！",WARN_SDK_NEED_UPDATE:"您的 SDK 不是最新版本，部分功能不可用，请尽快前往 "+tn.SDK_DOWNLOAD+" 升级",ERR_WRONG_OPENID:"OpenID 不符合规则，请参考接入文档 "+nn+" 小程序统计/广告监测平台-快速接入指南！",ERR_MISS_SESSION_KEY:"请传入从后台获取的 session_key",ERR_API_WRONG_EVENT_NAME:"事件名称必须为 String 类型且不能超过 255 个字符，"+rn,ERR_API_EVENT_ARGS_TOO_LONG:"自定义事件参数不能超过 255 个字符，"+rn,ERR_API_WRONG_EVENT_ARGS:"事件参数必须为 String、Object 类型，且参数长度不能超过 255 个字符，"+rn,ERR_API_EVENT_ARGS_INNER_TYPE:"事件参数内部只支持 Number、String 等类型，"+rn,ERR_API_SALE_WRONG_VISIT:"wx.aldVisit() 传参不符合规则，"+on,ERR_API_SALE_WRONG_VISIT_ARGS:"category、id、name 为必传字段且数据类型必须符合规则，"+on,ERR_API_SALE_WRONG_VISIT_CATEGORY:"category 字段（商品类别）只支持 String类 型，且长度小于 32 个字符，"+on,ERR_API_SALE_WRONG_VISIT_ID:"id 字段（商品唯一 id）只支持 Number 类型和 String 类型，"+on,ERR_API_SALE_WRONG_VISIT_NAME:"name 字段（商品名称）只支持 String 类型，且长度小于 32 个字符，"+on,ERR_API_SALE_WRONG_ORDER:"wx.aldPayOrder() 传参不符合规则，"+on,ERR_API_SALE_WRONG_ORDER_ARGS:"price、details 为必传字段且数据类型必须符合规则，"+on,ERR_API_SALE_WRONG_ORDER_PRICE:"price 字段（付费金额）只支持 Number 类型和数字字符串，且不能小于 0，"+on,ERR_API_SALE_WRONG_ORDER_DETAILS:"details 字段（订单详细信息）为 Array 类型，且长度不能小于 1，"+on,ERR_API_SALE_WRONG_ORDER_DETAIL_ARGS:"amount、category、id、name 为必传字段且数据类型必须符合规则，"+on,ERR_API_SALE_WRONG_ORDER_DETAIL_AMOUNT:"details 参数下 amount 字段值（商品数量）只支持 Number 类型和数字字符串，且不能小于或等于 0，"+on,ERR_API_SALE_WRONG_ORDER_DETAIL_ID:"id 字段（商品唯一 id）只支持 Number 类型和 String 类型，"+on,ERR_API_SALE_WRONG_ORDER_DETAIL_CATEGORY:"details 参数下 category 字段值（商品类别）只支持 String 类型，且长度小于 32 个字符，"+on,ERR_API_SALE_WRONG_ORDER_DETAIL_NAME:"details 参数下 name 字段值（商品类别）只支持 String 类型，且长度小于 32 个字符，"+on},un="aldstat_op",cn="aldstat_uuid",sn=["aldVisit","aldPayOrder"],fn={UUID:"ald_share_src",OPEN_ID:"ald_share_op"};function _n(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}for(var ln,pn={},dn=function(){var n=ln.value;pn[n]=function(){throw new Error("Do not use '"+n+"' api without implementation!")}},vn=function(n,e){var t="undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(t)return(t=t.call(n)).next.bind(t);if(Array.isArray(n)||(t=function(n,e){if(n){if("string"==typeof n)return _n(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);return"Object"===t&&n.constructor&&(t=n.constructor.name),"Map"===t||"Set"===t?Array.from(n):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?_n(n,e):void 0}}(n))||e&&n&&"number"==typeof n.length){t&&(n=t);var r=0;return function(){return r>=n.length?{done:!0}:{done:!1,value:n[r++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(n);!(ln=vn()).done;)dn();var hn=pn;function Rn(n,e){var t="undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(t)return(t=t.call(n)).next.bind(t);if(Array.isArray(n)||(t=function(n,e){if(!n)return;if("string"==typeof n)return gn(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(n);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return gn(n,e)}(n))||e&&n&&"number"==typeof n.length){t&&(n=t);var r=0;return function(){return r>=n.length?{done:!0}:{done:!1,value:n[r++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function gn(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function En(){return wx.getAccountInfoSync?wx.getAccountInfoSync().miniProgram.appId:""}var Sn={};function An(n){return Sn[n]}function yn(n,e){Sn[n]=e}function mn(n,e){Sn[n]=Object.assign(Sn[n],e)}function On(n,e){void 0===e&&(e=1),Sn[n]+=e}var In="app_show_time",Nn="app_hide_time",bn="app_error_count",wn="page_show_time",Pn="qrcode_query",Dn="share_source",Tn="share_query",Gn="current_page_path",Ln="last_visit_page_path",Wn="page_options",jn="is_first_onshow",xn="is_first_open_mini_app",Cn="is_first_access_page",Mn="page_duration",Vn="is_under_30s_session",qn="is_onshow_by_share",Un="is_onshow_by_api",kn="session_key",Kn="user_info",Yn="share_info",Fn="openid",Bn="gender",Jn="user_avatar",$n="ald_launch_session",Hn="ald_access_token",zn="ald_uuid",Qn="appid",Xn="show_options",Zn="show_scene",ne="request_common_data",ee="request_count",te="queue_ins",re="is_compliance_enabled";var oe={type:"",name:"",version:"",appKey:"",isPlugin:!1,useCompliance:!1};var ie=oe;function ae(n,e){return function(n,e){for(var t=n.split("."),r=e.split("."),o=Math.max(t.length,r.length);t.length<o;)t.push("0");for(;r.length<o;)r.push("0");for(var i=0;i<o;i++){var a=parseInt(t[i],10),u=parseInt(r[i],10);if(a>u)return 1;if(a<u)return-1}return 0}(n,e)>0}var ue=cn;for(var ce=Object.prototype.toString,se={},fe=function(){var n=le[_e];se["is"+n]=function(e){return ce.call(e).slice(8,-1)===n}},_e=0,le=["String","Array","Function","Number","Date","Boolean","RegExp","Symbol"];_e<le.length;_e++)fe();function pe(n){var e=typeof n;return"function"===e||"object"===e&&!!e}function de(n){var e=Number(n);return(se.isNumber(n)||se.isString(n))&&e>=0&&!isNaN(e)}var ve=se.isString,he=se.isArray,Re=se.isFunction,ge=se.isNumber;se.isDate,se.isBoolean,se.isRegExp,se.isSymbol;function Ee(){return""+Date.now()+Math.floor(1e7*Math.random())}function Se(n,e,t){var r=n[e];n[e]=function(n){t.call(this,n,e),r&&Re(r)&&r.call(this,n)}}function Ae(n,e){var t="undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(t)return(t=t.call(n)).next.bind(t);if(Array.isArray(n)||(t=function(n,e){if(!n)return;if("string"==typeof n)return ye(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(n);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return ye(n,e)}(n))||e&&n&&"number"==typeof n.length){t&&(n=t);var r=0;return function(){return r>=n.length?{done:!0}:{done:!1,value:n[r++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function ye(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}function me(n){return Object.assign({},An(ne),n)}function Oe(n){On(ee),n[b]=An(Hn),n[P]=An(zn),n[D]=ie.version,n[T]=ie.appKey.replace(/(\t)|(\s)/g,""),n[G]=An(Xn),n[L]=An(xn),n[W]=An(ee),n[w]=An($n),n[j]=ie.type,n[x]=Date.now(),n[C]=Date.now(),n[M]=An(Bn);var e=function(n){return function(){return new Promise((function(e){var t,r=((t={}).AldStat=s,t.se=An(kn)||"",t.op=An(Fn)||"",t.img=An(Jn)||"",t),o=An(Qn);o&&(r[_]=o),hn.request({url:tn.SEND_LOG,data:n,header:r,method:"GET",success:function(n){n&&200===n.statusCode?e(""):e("status error")},fail:function(){e("fail")}})}))}}(n);An(te).push(e)}function Ie(n,e,t){var r,o=me(((r={})[I]=n,r[X]=e,r[N]=Date.now()-An(In),r));t&&(o[Z]=t),Oe(o)}function Ne(n,e){var t,r=me(((t={})[I]=n,t[V]=e,t[q]=An(bn),t[N]=Date.now()-An(In),t));"show"==e&&(r[U]=!1);var o=An(Pn);o&&(r[k]=r.sr=o);var i=An(Dn);i&&(r[K]=i),Oe(r)}function be(n,e){var t="undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(t)return(t=t.call(n)).next.bind(t);if(Array.isArray(n)||(t=function(n,e){if(!n)return;if("string"==typeof n)return we(n,e);var t=Object.prototype.toString.call(n).slice(8,-1);"Object"===t&&n.constructor&&(t=n.constructor.name);if("Map"===t||"Set"===t)return Array.from(n);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return we(n,e)}(n))||e&&n&&"number"==typeof n.length){t&&(n=t);var r=0;return function(){return r>=n.length?{done:!0}:{done:!1,value:n[r++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function we(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=new Array(e);t<e;t++)r[t]=n[t];return r}var Pe=function(){function n(){this.events=new Map}var e=n.prototype;return e.on=function(n,e){var t=this.events;return t.has(n)||t.set(n,[]),t.get(n).push(e),this},e.once=function(n,e){var t=this;return this.on(n,(function r(){t.off(n,r);for(var o=arguments.length,i=new Array(o),a=0;a<o;a++)i[a]=arguments[a];e.apply(t,i)}))},e.emit=function(n){for(var e=this.listeners(n),t=arguments.length,r=new Array(t>1?t-1:0),o=1;o<t;o++)r[o-1]=arguments[o];for(var i,a=be(e);!(i=a()).done;){var u=i.value;u.apply(this,r)}},e.off=function(n,e){var t=this.events;if(!n)return t.clear(),this;if(!e)return t.delete(n),this;for(var r=this.listeners(n),o=0,i=r.length;o<i;o++)r[o]===e&&(r.splice(o,1),i--,o--);return 0===r.length&&t.delete(n),this},e.listeners=function(n){return this.events.get(n)||[]},n}(),De="app_on_show",Te="use_compliance",Ge=new Pe,Le=a;function We(n,e){return!n||!ve(n)||n.length>Le?(console.error(an.ERR_API_WRONG_EVENT_NAME),!1):!!(ve(e)&&e.length<=Le)||(pe(e)?JSON.stringify(e).length>=Le?(console.error(an.ERR_API_EVENT_ARGS_TOO_LONG),!1):!function(n){for(var e in n)if(n.hasOwnProperty(e)&&n[e]&&pe(n[e]))return!0;return!1}(e)||(console.error(an.ERR_API_EVENT_ARGS_INNER_TYPE),!1):void 0===e||(console.error(an.ERR_API_WRONG_EVENT_ARGS),!1))}var je={sendEvent:function(n,e){return He((function(){We(n,e)&&Ie("event",n,pe(e)?JSON.stringify(e):e)}))},sendSession:function(n){return He((function(){var e;if(n){yn(kn,n);var t=me(((e={})[I]="event",e[X]="session",e[Z]="session",e)),r=An(Kn);if(r){t[m]=r;var o=An(Yn);""!==o&&(t[O]=o)}Oe(t)}else console.error(an.ERR_MISS_SESSION_KEY)}))},sendOpenid:function(n){return He((function(){var e;n&&n.length===i?(yn(Fn,n),hn.setStorageSync(un,n),Oe(me(((e={})[I]="event",e[X]="openid",e[Z]="openid",e)))):console.error(an.ERR_WRONG_OPENID)}))},setOpenid:function(n){Re(n)&&""===An(Fn)&&n().then((function(n){n.length===i&&(yn(Fn,n),hn.setStorageSync(un,n))}))},sendUser:function(n,e){return He((function(){var n,t;!An(Kn)&&e&&(yn(Kn,e),yn(Jn,(null==(n=e.avatarUrl)?void 0:n.split("/")).reduce((function(n,e){return e.length>n.length?e:n}))),yn(Bn,e.gender),Oe(me(((t={})[m]=e,t))))}))},sendCompliance:function(){ie.useCompliance&&Ge.emit(Te,!0)}},xe=u;var Ce={aldVisit:function(n){return He((function(){(function(n){if(!pe(n))return console.error(an.ERR_API_SALE_WRONG_VISIT),!1;var e=n.category,t=n.id,r=n.name;return e&&(0===t||t)&&r?!ve(e)||e.length>xe?(console.error(an.ERR_API_SALE_WRONG_VISIT_CATEGORY),!1):ge(t)||ve(t)?!(!ve(r)||r.length>xe)||(console.error(an.ERR_API_SALE_WRONG_VISIT_NAME),!1):(console.error(an.ERR_API_SALE_WRONG_VISIT_ID),!1):(console.error(an.ERR_API_SALE_WRONG_VISIT_ARGS),!1)})(n)&&Ie("visit",null,{category:n.category,id:n.id,name:n.name})}))},aldPayOrder:function(n){return He((function(){(function(n){if(!pe(n))return console.error(an.ERR_API_SALE_WRONG_ORDER),!1;var e,t,r,o,i,a=n.price,u=n.details;if(0!==a&&!a||!u)return console.error(an.ERR_API_SALE_WRONG_ORDER_ARGS),!1;if(!de(a))return console.error(an.ERR_API_SALE_WRONG_ORDER_PRICE),!1;if(!he(u)||u.length<1)return console.error(an.ERR_API_SALE_WRONG_ORDER_DETAILS),!1;for(var c=0,s=u.length;c<s;c++)if(e=u[c],t=void 0,r=void 0,o=void 0,i=void 0,t=e.amount,r=e.category,o=e.id,i=e.name,!(t&&r&&(0===o||o)&&i?de(t)?ge(o)||ve(o)?!ve(r)||r.length>xe?(console.error(an.ERR_API_SALE_WRONG_ORDER_DETAIL_CATEGORY),0):ve(i)&&!(i.length>xe)||(console.error(an.ERR_API_SALE_WRONG_ORDER_DETAIL_NAME),0):(console.error(an.ERR_API_SALE_WRONG_ORDER_DETAIL_ID),0):(console.error(an.ERR_API_SALE_WRONG_ORDER_DETAIL_AMOUNT),0):(console.error(an.ERR_API_SALE_WRONG_ORDER_DETAIL_ARGS),0)))return!1;return!0})(n)&&Ie("pay",null,{price:n.price,details:n.details})}))}},Me=function(){function n(n){void 0===n&&(n={}),this.concurrency=void 0,this.queue=[],this.activeCount=0,this.concurrency=n.concurrency||4}var e=n.prototype;return e.push=function(n){var e=this,t=function(){e.activeCount++,n().then((function(){e.next()}))};this.activeCount<this.concurrency?t():this.queue.push(t)},e.next=function(){this.activeCount--,this.queue.length>0&&this.queue.shift()()},n}();function Ve(){return Ve=Object.assign||function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(n[r]=t[r])}return n},Ve.apply(this,arguments)}var qe=ne,Ue=t(867),ke=require("./ald-stat-conf"),Ke=ke.app_key,Ye=ke.plugin,Fe=ke.useCompliance;function Be(){yn(Hn,Ee()),yn(Fn,function(){var n="";try{n=hn.getStorageSync(un)}catch(n){}return n}());var n=function(){var n="";try{n=hn.getStorageSync(ue)}catch(e){n=f}return n}();yn(xn,!n),n||function(n){try{hn.setStorageSync(ue,n)}catch(n){hn.setStorageSync(ue,f)}}(n=function(){function n(){return Math.floor(65536*(1+Math.random())).toString(16).substring(1)}return n()+n()+n()+n()+n()+n()+n()+n()}()),yn(zn,n),yn(Qn,hn.getAppid().split("").map((function(n){return n.charCodeAt(0)+9})).join("-"))}function Je(){var n,e=function(){try{return hn.getSystemInfoSync()}catch(n){}}();if(e){var t,r=e.brand,o=e.model,i=e.pixelRatio,a=e.windowWidth,u=e.windowHeight,c=e.language,s=e.version,f=e.platform,_=e.SDKVersion,m=e.system;mn(qe,((t={})[l]=r,t[p]=o,t[d]=i,t[v]=a,t[h]=u,t[R]=c,t[g]=s,t[E]=f,t[S]=_,t[A]=m,t))}n=function(n){var e;mn(qe,((e={})[y]=n,e))},hn.getNetworkType({success:function(e){n&&n(e.networkType)}})}function $e(){return Fe&&!An(re)}function He(n){$e()||n&&n()}var ze=c;function Qe(n){yn($n,Ee()),this.aldstat=je;return He((function(){yn(Xn,n),yn(Zn,n.scene)}))}function Xe(n){return He((function(){var e=n.scene,t=n.query,r=n.shareTicket,o=t.ald_share_src,i=e===An(Zn);yn(Zn,e),yn(ee,0),yn(Xn,n),yn(Dn,o),yn(Pn,t.aldsrc||""),yn(Tn,o),An(jn)||An(qn)||An(Un)||yn(xn,!1),yn(jn,!1);var a=An(Nn);(0!==a&&Date.now()-a>ze||!i)&&(An(qn)||(yn(Hn,Ee()),yn(In,Date.now()),yn(Mn,0))),0!==a&&Date.now()-a<ze&&yn(Vn,!0),Ne("app","show"),Ge.emit(De,{aldShareSrc:o,shareTicket:r,scene:e})}))}function Ze(){yn(Nn,Date.now());return He((function(){Ne("app","hide")}))}function nt(n){return He((function(){On(bn),Ie("event","ald_error_message",n)}))}function et(n){return He((function(){yn(Wn,n)}))}function tt(){yn(wn,Date.now());return He((function(){var n;yn(Gn,(n=hn.getCurrentPages())[n.length-1].route);var e=Boolean(An(qn)||An(Un));yn(qn,!1),yn(Un,!1);var t=An(Cn);yn(Cn,!1),function(n,e,t){var r,o=void 0===t?{}:t,i=o.isFirstPage,a=void 0!==i&&i,u=o.isFromShareOrApi,c=void 0!==u&&u,s=An(Gn),f=me(((r={})[I]=n,r[V]=e,r[Y]=s,r[F]=An(Ln)||"",r[N]=Date.now()-An(In),r));c&&(f[$]=1);var _=An(Wn);_&&"{}"!==JSON.stringify(_)&&(f[H]=_);var l=An(Pn);l&&(f[k]=f.sr=l);var p=An(Dn);p&&(f[K]=p),An(Vn)&&(f[z]=1),a?(f[J]=!0,f[B]=s,f[Q]=0):f[Q]=An(Mn),Oe(f)}("page","show",{isFromShareOrApi:e,isFirstPage:t}),yn(Vn,!1)}))}function rt(){yn(Mn,Date.now()-An(wn));return He((function(){yn(Ln,An(Gn))}))}var ot=rt;function it(){return He((function(){Ie("event","ald_pulldownrefresh",1)}))}function at(){return He((function(){Ie("event","ald_reachbottom",1)}))}function ut(n,e,t){var r=n[e];r?~r.indexOf(t)||r.length<200&&(n[e]=r+","+t):n[e]=t}function ct(n){if($e())return n;yn(qn,!0);var e=n.path,t=function(n){if(~n.indexOf("?")){var e,t,r={};return n.split("?")[1].split("&").forEach((function(n){if(n){var o=n.split("=");e=o[0],t=o.length>=2?decodeURIComponent(o[1]):null,r[e]=t}})),r}}(e)||{},r={},o=(An(Xn)||{}).query;for(var i in o)o.hasOwnProperty(i)&&Object.values(fn).indexOf(i)>-1&&(r[i]=o[i]);var a=e.split("?")[0]+"?";for(var u in Object.assign(r,t),ut(r,fn.UUID,An(zn)),An(Fn)&&ut(r,fn.OPEN_ID,An(Fn)),r)r.hasOwnProperty(u)&&!~u.indexOf("ald")&&(a+=u+"="+r[u]+"&");return n.path=a+(r.ald_share_op?fn.OPEN_ID+"="+r.ald_share_op+"&":"")+fn.UUID+"="+r.ald_share_src,Ie("event","ald_share_status",n),n}function st(n){return Se(n,"onLaunch",Qe),Se(n,"onShow",Xe),Se(n,"onHide",Ze),Se(n,"onError",nt),n}function ft(n){return Se(n,"onShow",tt),Se(n,"onHide",rt),Se(n,"onLoad",et),Se(n,"onUnload",ot),Se(n,"onReachBottom",at),Se(n,"onPullDownRefresh",it),function(n){var e=n.onShareAppMessage;e&&(n.onShareAppMessage=function(n){var t=e.call(this,n)||{};return t.path=t.path||An(Gn),ct(t)})}(n),n}var _t,lt,pt,dt,vt,ht;function Rt(){var n;Be(),yn(te,new Me),hn.request({url:tn.SDK_VERSION,header:(n={},n.AldStat=s,n),method:"GET",success:function(n){if(200===n.statusCode){var e=n.data;ae(e.version,ie.version)&&console.warn(an.WARN_SDK_NEED_UPDATE),e.warn&&console.warn(e.warn),e.error&&console.error(e.error)}}}),Je(),Ge.on(De,(function(n){var e=n.aldShareSrc,t=n.shareTicket,r=n.scene;if(e&&1044===r&&t?wx.getShareInfo({shareTicket:t,success:function(n){yn(Yn,n),Ie("event","ald_share_click",JSON.stringify(n))}}):e&&Ie("event","ald_share_click",1),!An(Fn)){var o=hn.getAppid();hn.login({success:function(n){hn.request({url:tn.GET_OPEN_ID,data:{ai:o,uuid:An(zn),jc:n.code,reqid:"1"},success:function(n){var e=n.data;if(!e.code){var t,r=null==e||null==(t=e.data)?void 0:t.openid;yn(Fn,r),hn.setStorageSync(un,r)}}})},fail:function(n){console.warn("sdk-login-err",n)}})}}))}if(_t="wx",function(n){n||console.error(an.ERR_MISS_APPKEY)}(Ke),function(n){Object.assign(oe,n)}(Ve({type:_t},Ue[_t],{appKey:Ke,isPlugin:Ye,useCompliance:Fe})),yn(qe,{}),yn(jn,!0),yn(Cn,!0),yn(In,Date.now()),yn(Nn,0),yn(bn,0),yn(Bn,""),yn(ee,0),function(){for(var t,r,o={},i=function(){var n=t.value;o[n]=function(){return wx[n].apply(null,arguments)}},a=Rn(n.concat(e));!(t=a()).done;)i();o.getAppid=En,o.getCurrentPages=getCurrentPages,r=o,Object.assign(pn,r)}(),function(n){n.aldstat=je;for(var e=0,t=sn.length;e<t;e++)r=wx,i=sn[e],a=Ce[sn[e]],Object.defineProperty(r,i,{value:a,writable:!1,enumerable:!0,configurable:!0});var r,i,a;!function(n){for(var e,t=function(){var t=e.value,r=n[t];try{Object.defineProperty(n,t,{get:function(){return yn(Un,!0),r}})}catch(n){}},r=Ae(o);!(e=r()).done;)t()}(n)}(wx),ie.useCompliance?Ge.on(Te,(function(n){yn(re,n),Rt()})):Rt(),ie.isPlugin){var gt={App:function(n){function e(e){return n.apply(this,arguments)}return e.toString=function(){return n.toString()},e}((function(n){return App(st(n))})),Page:function(n){function e(e){return n.apply(this,arguments)}return e.toString=function(){return n.toString()},e}((function(n){return Page(ft(n))}))};lt=gt.App,pt=gt.Page}else dt=App,App=function(n){st(n),dt(n)},vt=Page,Page=function(n){ft(n),vt(n)},ht=Component,Component=function(n){var e=n.methods;e&&ft(e),ht(n)},lt=App,pt=Page;var Et=Component}(),r}()}));