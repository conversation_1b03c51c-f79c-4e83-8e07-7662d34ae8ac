
<template>
  <view class="title-textarea clear-float" :class="{'bdt':defaultConfig.bdt,'bdb':defaultConfig.bdb && !child}">
    <view class="l-l" :style="{'color': defaultConfig.titleColor}" :class="defaultConfig.titleClass">
      {{defaultConfig.label}}
      <text class="star" v-if="defaultConfig.required">*</text>
    </view>
    <view class="l-r">
      <textarea :disabled="disabled" :placeholder="defaultConfig.placeholder" v-model="form.data.val" @blur="returnFn" auto-height />
    </view>
  </view>
</template>

<script>

export default {
  data() {
    return {
      form: {
        data: {
          val: ''
        }
      },
      array: [],
      index: 0,
      defaultConfig: {
        bdt: false,
        bdb: true,
        titleColor: '#333',
        textColor: '#333',
        label: '多行输入框',
        name: 'input',
        required: false,
      }
    }
  },
  watch: {
    cData: {
      handler(val) {
        // this.watchDataMain(val)
      },
      deep: true
    },
    config: {
      handler(val) {
        this.copyConfig()
      },
      deep: true
    }
  },
  props: {
    child:{
      type:Boolean,
      default:false,
    },
    // 是否禁用disable
    disabled: {
      type: Boolean,
      default() {
        return false
      }
    },
    // 初始值传值，用于回显
    cData: {
      type: [String, Number],
      required: false,
      default() {
        return ''
      }
    },
    // 参数设置
    config: {
      type: Object,
      required: false,
      default: () => {
        return {}
      }
    }
  },
  computed: {

  },
  mounted() {
      this.copyConfig()
      if (!this.$validate.isNull(this.cData)) {
        this.watchDataMain(this.cData)
      }
  },
  methods: {
    /**
       * 初始化拷贝config对象
       */
    copyConfig() {
      const that = this
      const obj = that.config
      Object.keys(obj).forEach(function(key) {
        that.defaultConfig[key] = obj[key]
      })
    },
    /**
       * 监听Data主逻辑方法
       */
    watchDataMain(val) {
      this.form.data.val = val
    },
    /**
       * picker触发选中事件
       * @param e
       */
    returnFn(e) {
      const that = this
      if (that.disabled) return
      that.$emit('updateForm', { key: '' + that.config.name, value: that.form.data.val })
    }
  }
}
</script>

<style lang="scss" scoped>
  .color-topicC{
    color: $topicC !important;
  }
  .title-textarea{
    background-color:#ffffff;
    .l-l{
      line-height: 88upx;
      color: #333333;
      font-weight: 600;
      font-size:30upx;
    }
    .l-l.font36{
      font-size: 36upx;
    }
    .l-r{
      margin-bottom: 5px;
      textarea {
        min-height: 150upx;
        //line-height: 80upx;
        color: #333;
        font-size: 32upx;
        text-align: left;
        display: inline-block;
        vertical-align: middle;
        width: calc(100% - 64upx);
        border: 2upx solid #ede9e9;
        border-radius: 10upx;
        padding: 20rpx;
      }
    }
  }
  .star{
    color: #F85E4C;
    padding-top: 6upx;
    font-size: 32upx;
    display: inline-block;
    margin-left: 10rpx;
  }
</style>
