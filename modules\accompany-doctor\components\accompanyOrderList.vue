<template>
      <view class="confirm">
        <view class="" v-if="orderMap.length > 0">请选择服务</view>
        <scroll-view :scroll-top="scrollTop" scroll-y="true" class="scroll-Y" @scrolltolower="upper">
          <view class="default" v-if="orderMap.length <= 0">
            <image class="defaultImg" :src="defaultImg" mode=""></image>
            <view class="defaultTitle">暂无相关数据~</view>
          </view>
          <view v-for="(item,index) in orderMap" :key="item.id">
            <view class="headerTab" @click="gotoDetails(item)">
              <view class="clearOrder" v-if="item.orderState === 8">已取消</view>
              <view class="assignOrder" v-else-if="item.mode === 1 && item.receiveState !== 3 && item.receiveState !== 2">指派订单</view>
              <view class="clearOrder" v-else-if="item.receiveState === 3">已拒绝</view>
              <view class="catchOrder" v-else-if="item.employeeId === userInfo.id && item.receiveState === 2">我已接单</view>
              <image class="receivedOrder" v-else-if="item.receiveState === 2" :src="receivedOrder" mode=""></image>
              <view class="orderTime">
                <view class="orderBoxTitle">派单时间：</view>
                {{timestampToDateTime(item.dispatchTime)}}
              </view>
              <view class="orderTitle">{{item.serviceName}}</view>
              <view class="orderTime">
                <view class="orderBoxTitle">陪诊时间：</view>
                {{timestampToDateTime(item.startTime)}}~{{timestampToDateTime(item.endTime)}}
              </view>
              <view class="orderTime">
                <view class="orderBoxTitle">就诊医院：</view>
                {{item.hospitalName}}
              </view>
              <view class="orderTime">
                <view class="orderBoxTitle">备注：</view>
                {{item.remark}}
              </view>
              <view class="buttonMap" v-if="item.receiveState !== 3 && item.receiveState !== 2 && item.orderState !== 8">
                <view class="clearBtn" v-if="item.mode === 1" @click.stop="rejectOrder(item.id)">拒绝</view>
                <view class="orderBtn" v-if="item.mode === 1" @click.stop="meetOrder(item.id)">接单</view>
                <view class="orderBtn" v-else @click.stop="robOrder(item.id)">抢单</view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
</template>

<script>
  import { mapState } from "vuex";
  import uniPopup from '@/components/uni/uni-popup/uni-popup.vue'
  import common from '@/common/util/main'
  import serverOptions from '@/config/env/options'

  export default{
    components: {
        uniPopup
    },
    props:{

    },
    watch:{
    },
    data(){
      return {
        orderMap:[],
        current:0,
        loadEnd:false,
        receivedOrder: this.$static_ctx + "image/business/hulu-v2/receivedOrder.png",
        userInfo:{},
        defaultImg: this.$static_ctx + "image/business/hulu-v2/default.png",
      }
    },
    computed: {
      ...mapState("user", {
        accountId: (state) => state.accountId,
        fansRecord: (state) => state.fansRecord,
      }),
    },
     mounted() {
      this.getServerData();
    },
    methods:{
      gotoDetails(item){
        this.$navto.push('accompanyDetails',{id:item.id, fromProvider: true})
      },
      initData(){
        this.current = 0;
        this.orderMap.length = 0;
        this.getServerData();
      },
      // 拒绝接单
      async rejectOrder(id){
        let queryOptions = {
          id,
          employeeId:this.userInfo.id
        }
        let {data:{records,total}} = await this.$api.accompanyDoctor.accompanybookReject(queryOptions);
        uni.showToast({
          title:'拒绝成功',
          icon:'none'
        })
        this.initData()
      },
      async meetOrder(id){
        let queryOptions = {
          id,
          employeeId:this.userInfo.id
        }
        let {data:{records,total}} = await this.$api.accompanyDoctor.accompanybookAccept(queryOptions);
        uni.showToast({
          title:'接单成功',
          icon:'none'
        })
        this.initData()
      },
      async robOrder(id){
        let queryOptions = {
          id,
          employeeId:this.userInfo.id
        }
        let {data:{records,total}} = await this.$api.accompanyDoctor.accompanybookCompete(queryOptions);
        uni.showToast({
          title:'抢单成功',
          icon:'none'
        })
        this.initData()
      },
      timestampToDateTime(timestamp) {
        // 创建一个新的Date对象，传入的时间戳是以毫秒为单位的
        var date = new Date(timestamp);
        // 获取年、月、日、时、分、秒
        var year = date.getFullYear();
        var month = ("0" + (date.getMonth() + 1)).slice(-2); // 月份是从0开始的，所以+1，并补零
        var day = ("0" + date.getDate()).slice(-2); // 获取天数，并补零
        var hour = ("0" + date.getHours()).slice(-2); // 获取小时，并补零
        var minute = ("0" + date.getMinutes()).slice(-2); // 获取分钟，并补零
        var second = ("0" + date.getSeconds()).slice(-2); // 获取秒数，并补零

        // 返回格式化的字符串
        return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
      },
      upper(){
        console.log('触底');
        if(this.loadEnd) return
        this.current++;
        this.getServerData();
      },
      async getServerData(){
        const userId = serverOptions.getUserId(this);
        if(!userId){
          uni.showToast({title:'请先登录',icon:'none'})
          return;
        }
        let {data} = await this.$api.accompanyDoctor.getAccompanyemployeeOneByUserId({userId})
        this.userInfo = data;
        let queryOptions = {current:this.current,size:10}
        let {data:{records,total}} = await this.$api.accompanyDoctor.accompanybookEmployeePage(queryOptions);
        this.orderMap.push(...records);
        if(total <= this.orderMap.length){
          this.loadEnd = true
        }
      }
    }
  }
</script>

<style lang="scss">
  .default{
    margin-top: 200rpx;
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    .defaultImg{
      width: 204rpx;
      height: 204rpx;
    }
    .defaultTitle{
      width: 100%;
      margin-top: 24rpx;
      font-weight: 400;
      font-size: 24rpx;
      color: #4E5569;
      text-align: center;
    }
  }
.confirm{
  width: 100%;
  background: #F4F6FA;
  border-radius: 24rpx 24rpx 0rpx 0rpx;
  padding: 32rpx 32rpx 88rpx 32rpx;
  box-sizing: border-box;
  .headerTab{
    position: relative;
    width: 686rpx;
    background: #FFFFFF;
    border-radius: 16rpx;
    padding: 32rpx 24rpx;
    box-sizing: border-box;
    margin-bottom: 20rpx;

    .orderTime{
      font-weight: 400;
      font-size: 24rpx;
      color: #1D2029;
      display: flex;
      .orderBoxTitle{
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #4E5569;
        line-height: 34rpx;
        text-align: right;
        font-style: normal;
      }
    }
    .orderTitle{
      font-weight: 600;
      font-size: 32rpx;
      color: #1D2029;
      margin: 8rpx 0 20rpx 0;
    }
    .assignOrder{
      width: 158rpx;
      height: 48rpx;
      background: #FFEEE6;
      border-radius: 0rpx 16rpx 0rpx 16rpx;
      text-align: center;
      line-height: 48rpx;
      font-weight: 500;
      font-size: 26rpx;
      color: #FF7029;
      position: absolute;
      top: 0;
      right: 0;
    }
    .clearOrder{
      width: 128rpx;
      height: 48rpx;
      background: #EAEBF0;
      border-radius: 0rpx 16rpx 0rpx 16rpx;
      position: absolute;
      top: 0;
      right: 0;
      text-align: center;
    }
    .catchOrder{
      font-weight: 500;
      font-size: 26rpx;
      color: #00664B;
      width: 158rpx;
      height: 48rpx;
      background: #DEF2ED;
      border-radius: 0rpx 16rpx 0rpx 16rpx;
      text-align: center;
      position: absolute;
      top: 0;
      right: 0;
      text-align: center;
    }
    .receivedOrder{
      width: 144rpx;
      height: 144rpx;
      position: absolute;
      top: 0;
      right: 0;
    }
    .serverIcon{
      width: 144rpx;
      height: 144rpx;
      background: #D8D8D8;
      border-radius: 12rpx;
      border: 1rpx solid #D9DBE0;
      margin-right: 20rpx;
    }
    .changeServer{
      width: 148rpx;
      height: 52rpx;
      background: #FFFFFF;
      border-radius: 36rpx;
      border: 1rpx solid #D9DBE0;
      margin-left: auto;
    }
    .buttonMap{
      display: flex;
      justify-content: space-evenly;
      margin-top: 24rpx;
      gap: 30rpx;
      .clearBtn{
        height: 72rpx;
        background: #FFFFFF;
        border-radius: 36rpx;
        border: 1rpx solid #D9DBE0;
        font-weight: 400;
        font-size: 26rpx;
        color: #1D2029;
        text-align: center;
        line-height: 72rpx;
        flex-grow: 1;
      }
      .orderBtn{
        height: 72rpx;
        background: #00B484;
        border-radius: 36rpx;
        font-weight: 500;
        font-size: 26rpx;
        color: #FFFFFF;
        text-align: center;
        line-height: 72rpx;
        flex-grow: 1;
      }
    }
  }
}
.scroll-Y{
  height: calc(100vh - 176rpx - 254rpx);
  margin-top: 20rpx;
}
</style>
