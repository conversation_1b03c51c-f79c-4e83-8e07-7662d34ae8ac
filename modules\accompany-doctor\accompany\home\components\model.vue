<template>
  <view class="model-container">
    <uniPopup :zIndex='99' type="bottom" ref='uniPopup' @change="changeShow">
      <view :class="classMap[modelType]" class="confirm">
        <!-- 标题 -->
        <view class="confirmTitle" :style="{ color: titleMap[modelType] === '订单二维码' ? '#fff' : '' }">
          {{titleMap[modelType]}}
          <image class="iconClose" @click="close" :src="iconClose" mode=""></image>
        </view>
        <!-- 模块分区 -->
        <formList v-if="modelType === 'server'" @emitChange='emit' :serverMap='currentOptions' @emitInput="handleFormInput"></formList>

        <!-- 修改取消订单弹窗部分 -->
        <view v-if="modelType === 'clear'" class="clear">
          <view class="clearTitle">
            <text class="sign">*</text>
            取消原因
          </view>
          <textarea
            class="clearValue"
            v-model="cancelReason"
            placeholder="请输入取消原因（必填）"
          ></textarea>
          <view v-if="orderDetails.orderState >= 3">
            <view class="clearTitle">
              退款金额
              <text class="miniTitle">(单位：元，最大退款金额：{{(orderDetails.payPrice || 0) / 100}}元)</text>
            </view>
            <input
              class="clearInput"
              type="digit"
              v-model.number="refundAmount"
              placeholder="请输入退款金额"
              min="0"
              :max="(orderDetails.payPrice || 0) / 100"
              step="0.01"
            />
          </view>
        </view>

        <view v-if="modelType === 'code'" class="code">
          <view class="codeTitle">长按二维码付款</view>
          <view class="payCode">
            <image class="codeImg" :src="codeImg" mode="aspectFit" show-menu-by-longpress></image>
          </view>
        </view>

        <formList v-if="modelType === 'refresh'" @previewImage='previewImage' @closeImgage='closeImgage' @emitChange='emit' :serverMap='currentOptions'></formList>

        <formList v-if="modelType === 'dispatcher'" @emitChange='emit' @emitInput="handleFormInput" :serverMap='currentOptions' ></formList>

        <formList v-if="modelType === 'refreshOrder'" @emitChange='emit'  :serverMap='currentOptions'></formList>

        <formList v-if="modelType === 'Vector'" @emitChange='emit' :serverMap='currentOptions'></formList>

        <!-- 确认按钮 -->
        <view :class="btnClassMap[modelType]" class="actionsConfirm" @click="trigger">{{buttonTitleMap[modelType]}}</view>
      </view>
    </uniPopup>

    <!-- 弹窗模块 -->
    <changeServer ref="selectChangeServer" @change='changeExchange' @selectServer='selectServer' :openFlag='openServerFlag' :isFromCategory="true" :classifyId="getForm('classifyId').value" :isISP="true"></changeServer>
    <view class="lineHide">
      <selectData ref="selectHospital" placeholder="输入医院名称，模糊匹配搜索"  :localdata="hospitalQuery" popup-title="请选择就诊医院" @change="onchangeHospital"></selectData>
    </view>
    <view class="lineHide">
      <selectData ref="selectDept" placeholder="输入科室名称，模糊匹配搜索" :localdata="deptNameQuery" popup-title="请选择就诊科室" @change="onchangeDept"></selectData>
    </view>
    <view class="lineHide">
      <selectData ref="selectDoctorName" placeholder="输入医生名称，模糊匹配搜索" :localdata="doctorNameQuery" popup-title="请选择就诊医生" @change="onchangeDoctorName"></selectData>
    </view>
    <view class="lineHide">
      <timePicker ref="selectTime" type="datetimerange" @change="onchangeTime" />
    </view>
    <view class="lineHide">
      <dataPicker ref="selectCity" :localdata="provinceMap" popup-title="请选择就诊城市" @change="onchangeCity"></dataPicker>
    </view>
    <view class="lineHide">
      <dataPicker ref="selectmode" :localdata="modeMap" popup-title="请选择派单模式" @change="onchangeMode"></dataPicker>
    </view>
    <view class="lineHide">
      <dataPicker ref="selectAccompanyemployee" :localdata="accompanyemployeeMap" popup-title="请选择陪诊师" @change="onchangeEmployee"></dataPicker>
    </view>
    <view class="lineHide">
      <dataPicker ref="selectProviderId" :localdata="ProviderIdMap" popup-title="请选择接单服务商" @change="onchangeProviderId"></dataPicker>
    </view>
    <view class="lineHide">
      <title-img :config="{}" ref="upDataImage" @returnFn="imgReturnFn" :cData="queryOptions.imageObj"></title-img>
    </view>

    <!-- 就诊人选择弹窗 -->
    <view v-if="showPatientSelectModal" class="patient-select-modal">
      <view class="modal-mask" @click="closePatientSelectModal"></view>
      <view class="patient-select-container">
        <view class="patient-select-header">
          <text class="patient-select-title">请选择就诊人</text>
          <view class="close-icon" @click="closePatientSelectModal">
            <image :src="patientCloseIcon" mode="aspectFit" class="close-icon-image"></image>
          </view>
        </view>

        <view class="patient-list">
          <view
            class="patient-item"
            v-for="(item, index) in patientRecords"
            :key="index"
            @click="selectPatientItem(item)"
          >
            <view class="patient-info">
              <text class="patient-name">姓名：{{item.name}}</text>
              <text class="patient-phone" v-if="item.patientPhone">{{formatPhone(item.patientPhone)}}</text>
            </view>
            <view class="edit-icon" @click.stop="goToEditPatient(item)">
              <image :src="patientEditIcon" mode="aspectFit" class="edit-icon-image"></image>
            </view>
          </view>
        </view>

        <view class="add-btn-wrapper">
          <view class="add-patient-btn" @click="goToAddPatient">
            <text class="add-patient-btn-text">新增就诊人</text>
            <image :src="patientAddIcon" mode="aspectFit" class="add-icon-image"></image>
          </view>
        </view>

        <view class="indicator-bar"></view>
      </view>
    </view>
  </view>
</template>

<script>
  import common from '@/common/util/main'
  import { mapState } from "vuex";
  import uniPopup from '@/components/uni/uni-popup/uni-popup.vue'
  import timePicker from '../../../components/uni-datetime-picker/uni-datetime-picker'
  import dataPicker from '../../../components/uni-data-picker/uni-data-picker.vue'
  import changeServer from '../../../components/changeServer'
  import selectData from '../../../components/select-data.vue'
  import formList from './formList.vue'
  import TitleImg from "@/components/business/module/title-img/index.vue"
  import serverOptions from '@/config/env/options'
  export default{
    components: {
        uniPopup,
        changeServer,
        timePicker,
        dataPicker,
        formList,
        TitleImg,
        selectData
    },
    props:{
      openFlag:{
        type:Boolean,
        default:false
      },
      modelType:{
        type:String,
        default:''
      },
      orderDetails:{
        type:Object,
        default:{}
      }
    },
    watch: {
      async openFlag(n) {
        if (n && this.modelType === 'refreshOrder') {
          // 1. 初始化医院数据
          this.setForm('hospitalName', 'text', this.orderDetails.hospitalName);
          this.setForm('hospitalName', 'value', this.orderDetails.hospitalName);
          this.setForm('deptName', 'text', this.orderDetails.deptName);
          this.setForm('deptName', 'value', this.orderDetails.deptName);
          this.setForm('doctorName', 'text', this.orderDetails.doctorName);
          this.setForm('doctorName', 'value', this.orderDetails.doctorName);
          await this.getHospital({
            province: this.orderDetails.province?.replace('省', ''),
            city: this.orderDetails.city?.replace('市', '')
          });
        }
        if (n && this.modelType === 'refresh') {
          // 自动填充当前城市
          this.setForm('provinceCity', 'value', [
            this.orderDetails.province,
            this.orderDetails.city
          ]);
          this.getHospital({
            province: this.orderDetails.province,
            city: this.orderDetails.city.replace('市', '') // 去除可能存在的后缀
          });
          // 在平台小程序下请求服务商数据
          if(serverOptions.source === 1){
            await this.getProviderId(this.orderDetails.city);
            // 如果有服务商信息，设置服务商
            if (this.orderDetails.providerId) {
              this.setForm('providerName', 'text', this.orderDetails.providerName || '');
              this.setForm('providerName', 'value', this.orderDetails.providerId);
            }
          }
        }
        if (n && this.modelType === 'server') {
          console.log('this.orderDetails', this.orderDetails);
          // 初始化服务信息
          if (this.orderDetails.serviceId) {
            const payPrice = this.orderDetails.payPrice || 0;

            // 金额转换（分转元）
            const yuanPrice = Number(payPrice / 100);

            this.setForm('serviceName', 'text', this.orderDetails.serviceName);
            this.setForm('serviceName', 'value', this.orderDetails.serviceId);
            this.setForm('price', 'text', `￥${yuanPrice}`);
            this.setForm('price', 'value', yuanPrice);

            // 如果有分类信息，也设置分类
            if (this.orderDetails.classifyId && this.orderDetails.classifyName) {
              this.setForm('classifyId', 'text', this.orderDetails.classifyName);
              this.setForm('classifyId', 'value', this.orderDetails.classifyId);
            }
          }
          // 获取医院数据
          await this.getHospital({
            province: this.orderDetails.province?.replace('省', ''),
            city: this.orderDetails.city?.replace('市', '')
          });

          // 如果是平台端且有城市信息，初始化服务商列表
          if (serverOptions.source === 1 && this.orderDetails.city) {
            await this.getProviderId(this.orderDetails.city);

            // 如果有服务商信息，设置服务商并打开服务选择器
            if (this.orderDetails.providerId) {
              this.setForm('providerName', 'text', this.orderDetails.providerName || '');
              this.setForm('providerName', 'value', this.orderDetails.providerId);

              // 先重置changeServer组件的状态
              if (this.$refs.selectChangeServer) {
                this.$refs.selectChangeServer.lastProviderId = this.orderDetails.providerId;
                this.$refs.selectChangeServer.categoryList = [];
                this.$refs.selectChangeServer.currentCategoryId = this.orderDetails.classifyId || '';
                this.$refs.selectChangeServer.currentServices = [];
              }

              // 使用$nextTick确保组件状态已更新
              this.$nextTick(() => {
                // 如果有分类ID，设置到changeServer组件
                if (this.orderDetails.classifyId) {
                  this.$refs.selectChangeServer.getServerData(true, {
                    providerId: this.orderDetails.providerId,
                    classifyId: this.orderDetails.classifyId
                  });
                } else {
                  this.$refs.selectChangeServer.getServerData(true, {
                    providerId: this.orderDetails.providerId
                  });
                }
              });
            }
          }
        }

        if (n && this.modelType === 'dispatcher' && this.orderDetails.patientFileSee === 1) {
          // 当patientFileSee为1时，加载可见陪诊记录
          try {
            const { data } = await this.$api.accompanyDoctor.accompanypatientrecordQuerySeeList({
              accompanyId: this.orderDetails.id
            });
            if (data && data.length > 0) {
              // 保存记录ID集合到orderDetails中
              this.orderDetails.accompanyOrderIdList = data;
            }
          } catch (error) {
            console.error('加载可见陪诊记录失败:', error);
          }
        }

       if (n) {
         this.$refs.uniPopup.open();
         if (!this.currentOptions) return;
         let currentMode; // 保存派单模式值供后续使用
         const phase1Options = this.currentOptions.map(e => {
           const newItem = { ...e }; // 克隆对象避免污染原数据
           // 1. 处理时间范围字段（如陪诊时间）
            if (newItem.valueType === 'serviceId') {
              newItem.value = this.orderDetails.serviceId || '';
              newItem.text = this.orderDetails.serviceName || newItem.noValue;
            }
            // 添加服务分类回显
            if (newItem.valueType === 'classifyId') {
              newItem.value = this.orderDetails.classifyId || '';
              newItem.text = this.orderDetails.classifyName || newItem.noValue;
            }
           if (newItem.classTimeMap) {
             newItem.text = newItem.classTimeMap
               .map(ele => this.timestampToDateTime(this.orderDetails[ele]))
               .join('~');
             newItem.value = newItem.classTimeMap
               .map(ele => this.timestampToDateTime(this.orderDetails[ele]));
           }
           if (newItem.valueType === 'mode') {
             const modeValue = this.orderDetails.mode;
             newItem.text = typeof modeValue === 'number' && [1, 2].includes(modeValue)
               ? (modeValue === 1 ? '指定派单' : '抢单')
               : newItem.noValue;
             newItem.value = ([1, 2].includes(modeValue)) ? modeValue : '';
             currentMode = modeValue;
           }
          if (newItem.valueType === 'remark') {
            const remarkValue = this.orderDetails.remark || this.orderDetails.remarks; // 兼容不同字段名
            newItem.text = remarkValue ? `${remarkValue}` : newItem.noValue; // 带格式化的文本显示
            newItem.value = remarkValue || '';
          }
          if (newItem.type === 'image') {
            // 确保 value 是字符串后再 split，或者默认空数组
            const originalValue = newItem.value || '';
            newItem.value = typeof originalValue === 'string'
              ? originalValue.split(',')
              : [];
          }
          // 新增医院数据绑定
          if (newItem.valueType === 'hospitalName') {
            newItem.text = this.orderDetails.hospitalName || newItem.noValue;
            newItem.value = this.orderDetails.hospitalName || '';
          }
          // 新增服务商数据绑定
          if (newItem.valueType === 'providerId') {
            newItem.text = this.orderDetails.providerName || newItem.noValue;
            newItem.value = this.orderDetails.providerId || '';
          }
          // 新增就诊人档案查看权限回显
          if (newItem.valueType === 'patientFileSee') {
            const patientFileSee = this.orderDetails.patientFileSee;
            if (patientFileSee) {
              const option = newItem.options.find(opt => opt.value === patientFileSee);
              if (option) {
                newItem.text = option.text;
                newItem.value = option.value;
              }
            }
          }
          // 新增陪诊记录回显
          if (newItem.valueType === 'accompanyOrderIdList') {
            const accompanyOrderIdList = this.orderDetails.accompanyOrderIdList;
            if (accompanyOrderIdList) {
              // 如果是字符串，转换为数组
              const idArray = typeof accompanyOrderIdList === 'string'
                ? accompanyOrderIdList.split(',').filter(id => id)
                : (Array.isArray(accompanyOrderIdList) ? accompanyOrderIdList : []);

              newItem.value = idArray;
              newItem.text = idArray.length > 0 ? `已选择 ${idArray.length} 条记录` : newItem.noValue;
              // 根据 patientFileSee 决定是否显示
              newItem.hidden = this.orderDetails.patientFileSee !== 1;
            }
          }
          return newItem;
         });
         const finalOptions = phase1Options.map(e => {
           const newItem = { ...e };
          if (this.modelType === 'dispatcher' && newItem.valueType === 'employeeId') {
            const isMode1 = currentMode === 1;
            newItem.hidden = !isMode1;
            newItem.text = isMode1 ? (this.orderDetails.employeeName || newItem.noValue) : newItem.noValue;
            newItem.value = isMode1 ? (this.orderDetails.employeeId || '') : '';
          }
           // 处理备注字段
           if (newItem.valueType === 'remark') {
             newItem.text = this.orderDetails.remark || newItem.noValue;
             newItem.value = this.orderDetails.remark || '';
           }
           return newItem;
         });

         // 特殊处理 - 如果patientFileSee为1，确保accompanyOrderIdList可见
         if (this.orderDetails.patientFileSee === 1) {
           const accompanyRecordField = finalOptions.find(item => item.valueType === 'accompanyOrderIdList');
           if (accompanyRecordField) {
             accompanyRecordField.hidden = false;
           }
         }

         // 强制响应式更新
         this.currentOptions.splice(0, this.currentOptions.length, ...finalOptions);
         // 等待DOM更新
         await this.$nextTick();
       } else {
         // 关闭弹窗
         this.$refs.uniPopup.close();
       }
     },
    // 监听orderDetails变化，特别是city变化时更新陪诊师列表
    async orderDetails(newVal, oldVal) {
      // 只在city发生变化时才更新陪诊师
      if (newVal && newVal.city && (!oldVal || newVal.city !== oldVal.city)) {
        // 获取对应城市的陪诊师
        await this.updateAccompanyEmployees(newVal.city);
      }
    },
    async modelType(n) {
      if (n === 'server') {
        return
      }
      if (n === 'code') {
        await this.orderCode();
      }
    },
  },
    data(){
      return {
        serverMap:[
          {title:'就诊城市',value:'',text:'',noValue:'请选择',valueType:'provinceCity',textType:'provinceCity',classTimeMap:['province','city'], required: true},
          {title:'服务商',value:'',text:'',noValue:'请选择',valueType:'providerId',textType:'providerName',hidden:serverOptions.source !== 1, required: true},
          {title:'服务分类',value:'',text:'',noValue:'请选择',valueType:'classifyId',textType:'classifyId', required: true},
          {title:'服务',value:'',text:'',noValue:'请选择',valueType:'serviceId',textType:'serviceName', required: true},
          {title: '服务价格', value: '', text: '', noValue: '请先选择服务',valueType: 'price',textType: 'price',type: 'price-input', inputType: 'number',required: true },
          {title:'医院',value:'',text:'',noValue:'请选择',valueType:'hospitalName',textType:'hospitalName', required: true},
          {title:'陪诊时间',value:'',text:'',noValue:'开始时间-结束时间',valueType:'selectTime',textType:'selectTime',classTimeMap:['startTime','endTime'], required: true},
        ],
        clearMap:[
          {title:'取消原因',value:'',text:'',noValue:'请输入',valueType:'cancelReason',textType:'cancelReason' },
          {title:'退款金额',value:0,text:'',valueType:'refundAmount',textType:'refundAmount'}
        ],
        refreshMap:[
          // {title:'服务',value:'',text:'',noValue:'请选择',valueType:'serviceId',textType:'serviceName', required: true, disabled: true},
          {title:'就诊城市',value:'',text:'',noValue:'请选择',valueType:'provinceCity',textType:'provinceCity',classTimeMap:['province','city'], required: true},
          {title:'医院',value:'',text:'',noValue:'请选择',valueType:'hospitalName',textType:'hospitalName', required: true},
          {title:'陪诊时间',value:'',text:'',noValue:'开始时间-结束时间',valueType:'selectTime',textType:'selectTime',classTimeMap:['startTime','endTime'], required: true},
          {title:'服务商',value:'',text:'',noValue:'请选择',valueType:'providerId',textType:'providerName',hidden:serverOptions.source !== 1, required: true},
          {title:'补充内容',valueType:'backupImg',value:[],textType:'backupImg',type:'image', required: true},
        ],
        dispatcherMap:[
          {title:'派单模式',value:'',text:'',noValue:'请选择',valueType:'mode',textType:'modeName', required: true},
          {title:'陪诊师',value:'',text:'',noValue:'请选择',valueType:'employeeId',textType:'employeeName',hidden:true, required: true},
          {title:'陪诊记录可见范围',value:'',text:'',noValue:'请选择',valueType:'patientFileSee',textType:'patientFileSee',type:'select',options:[
            {text:'手动勾选可查看的陪诊记录',value:1},
            {text:'可查看全部陪诊记录',value:2},
            {text:'不可查看全部陪诊记录',value:3}
          ], required: true},
          {title:'陪诊记录',value:'',text:'',noValue:'请选择',valueType:'accompanyOrderIdList',textType:'accompanyOrderIdList', required: true, hidden: true},
          {title:'备注',miniTitle:'发送给陪诊师查看',value:'',text:'',noValue:'请选择',valueType:'remark',textType:'remark',type:'textarea', required: true},
        ],
        VectorMap:[
          {title:'陪诊师',value:'',text:'',noValue:'请选择',valueType:'employeeId',textType:'employeeName', required: true},
          {title:'就诊城市',value:'',text:'',noValue:'请选择',valueType:'provinceCity',textType:'provinceCity',classTimeMap:['province','city'],hidden:true},
        ],
        finishMap:[
        ],
        refreshOrderMap:[
          // {title:'就诊城市',value:'',text:'',noValue:'请选择',valueType:'provinceCity',textType:'provinceCity',classTimeMap:['province','city']},
          {title:'医院',value:'',text:'',noValue:'请选择',valueType:'hospitalName',textType:'hospitalName',required: true},
          {title:'医院科室',value:'',text:'',noValue:'未选择科室',valueType:'deptName',textType:'deptName'},
          {title:'就诊医生',value:'',text:'',noValue:'未选择医生',valueType:'doctorName',textType:'doctorName'},
          {title:'陪诊时间',value:'',text:'',noValue:'开始时间-结束时间',valueType:'selectTime',textType:'selectTime',classTimeMap:['startTime','endTime'], required: true},
          {title:'补充内容',valueType:'remark', value:'',text:'',noValue:'请输入补充说明',type:'textarea'}
        ],
        // 参数映射
        mappingList:{
          server:'serverMap',
          clear:'clearMap',
          refresh:'refreshMap',
          dispatcher:'dispatcherMap',
          refreshOrder:'refreshOrderMap',
          Vector:'VectorMap',
          finish:'finishMap',
        },
        titleMap:{
          server:'创建服务单',
          clear:'取消订单',
          code:'订单二维码',
          refresh:'更改订单',
          dispatcher:'派单',
          refreshOrder:'更改预约',
          Vector:'更改陪诊师',
          finish:'结束服务',
        },
        buttonTitleMap:{
          server:'发送给用户',
          clear:'确定',
          code:'保存到相册',
          refresh:'确定',
          dispatcher:'确定',
          refreshOrder:'确定',
          Vector:'确定',
          finish:'结束服务',
        },
        classMap:{
          code:'codeBox',
          refresh:'refreshBox',
          dispatcher:'dispatcherBox',
          clear:'clearBox',
          finish:'clearBox',
        },
        btnClassMap:{
          code:'codeBtn'
        },
        apiMap:{
          server:'accompanybookUpdateOrder',
          clear:'accompanybookCancel',
          refresh:'accompanybookUpdateOrder',
          refreshOrder:'accompanybookUpdateOrder',
          dispatcher:'accompanybookDispatchProvider',
          Vector:'accompanybookChangeEmployee',
          finish:'accompanybookFinish',
        },
        modeMap:[
          {text:'指定派单',value:1},
          {text:'抢单',value:2}
        ],
        accompanyemployeeMap:[],
        codeImg:'',
        hospitalQuery:[],
        deptNameQuery: [],
        doctorNameQuery: [],
        provinceMap:[],
        providerId:'',
        openServerFlag:false,
        file_ctx: this.file_ctx,
        iconClose: this.$static_ctx + "image/business/hulu-v2/close-white.png",
        iconRightArrow: this.$static_ctx + "image/business/hulu-v2/icon-right-arrow.png",
        ProviderIdMap:[],
        cancelReason: '',  // 取消原因
        refundAmount: '',  // 退款金额（元）
        showPatientSelectModal: false,
        patientRecords: [], // 就诊人列表
        patientEditIcon: this.$static_ctx + "image/business/hulu-v2/patient-edit.png",
        patientCloseIcon: this.$static_ctx + "image/business/hulu-v2/patient-close.png",
        patientAddIcon: this.$static_ctx + "image/business/hulu-v2/patient-add.png",
        providerOptions:null,
      }
    },
    computed: {
      ...mapState("user", {
        accountId: (state) => state.accountId,
        fansRecord: (state) => state.fansRecord,
      }),
      currentOptions(){
        return this[this.mappingList[this.modelType]];
      }
    },
    async mounted() {
      const userId = serverOptions.getUserId(this);
      if(!userId){
        uni.showToast({title:'请先登录',icon:'none'})
        return;
      }
      let {data:id} = await this.$api.accompanyDoctor.accompanyproviderUserProvider({userId})
      this.providerId = id;

      // 先获取省市数据
      this.provinceMap = await this.accompanyproviderQueryPage();

      // 确保orderDetails和city存在后再获取陪诊师数据
      if (this.orderDetails && this.orderDetails.city) {
        const condition = { auditStatus: 2, city: this.orderDetails.city };
        let {data:{records}} = await this.$api.accompanyDoctor.getAccompanyemployeePage({
          size: 10000,
          providerId: this.providerId,
          condition: condition
        });

        records.map(e => {
          e.text = e.username
          e.value = e.id
        });
        this.accompanyemployeeMap = records;
      } else {
        // 如果没有城市信息，可以先设置为空数组，后续在watch或其他地方更新
        this.accompanyemployeeMap = [];
      }

      // 初始加载就诊人数据
      await this.loadPatientRecords();

      // 监听陪诊记录选择事件
      uni.$on('selectAccompanyRecord', this.handleSelectAccompanyRecord);
    },
    beforeDestroy() {
      // 移除事件监听，防止内存泄漏
      uni.$off('selectAccompanyRecord', this.handleSelectAccompanyRecord);
    },
    methods:{
      // 更新陪诊师列表
      async updateAccompanyEmployees(city) {
        if (!city || !this.providerId) return;

        try {
          const condition = { auditStatus: 2, city: city };
          let {data:{records}} = await this.$api.accompanyDoctor.getAccompanyemployeePage({
            size: 10000,
            providerId: this.providerId,
            condition: condition
          });

          records.map(e => {
            e.text = e.username;
            e.value = e.id;
          });

          this.accompanyemployeeMap = records;
        } catch (error) {
          console.error('获取陪诊师列表失败:', error);
          this.accompanyemployeeMap = [];
        }
      },
      async getdeptName(id) {
        try {
          const { data: departments } = await this.$api.hospital.crawlershospitaldeptQuery({ id });
          this.deptNameQuery = [];
          this.deptNameQuery = departments.map(e=>{
            return {...e,text:e.name,value:e.id}
          })
        } catch (error) {
          console.error('科室加载失败:', error);
        }
      },
      async accompanyproviderQueryPage(){
        // 判断当前是否是平台
        let queryOptions;
        if(serverOptions.source === 1){
          queryOptions = (await this.$api.accompanyDoctor.getAccompanyproviderAll()).data
        }else{
          queryOptions = [(await this.$api.accompanyDoctor.accompanyproviderQueryOne({id:serverOptions.providerId})).data]
        }
        let cityMap = this.getCityMap(queryOptions)
        return [...new Set(cityMap)].filter(e=>e);
      },
      getCityMap(AccompanyproviderAll){
          return AccompanyproviderAll.reduce((acc, {province, city}) => {
              let provinceMap = province.split(',');
              let cityMap = city.split('$');
              provinceMap.map((provinceItem,index)=>{
              let currentCityMap = cityMap[index].split(',').filter(e=>e);
              let prov = acc.find(p => p.value === provinceItem);
              if (!prov) {
                acc.push(prov = {text: provinceItem, value: provinceItem, children: []});
              }
              prov.children.push(...currentCityMap)
              })
              return acc;
          }, []).map(e=>{
              e.children = [...new Set(e.children)];
              e.children = e.children.map(e=>({text:e,value:e}))
              return e;
          });
      },
      // 处理表单输入事件
      handleFormInput({ type, res }) {
        // 找到对应的表单项
        if (type === 'price') {
          const value = Number(res.detail.value.replace(/[^0-9.]/g, '')) || 0
          this.setForm('price', 'value', Number(value))
        }
        const targetIndex = this.currentOptions.findIndex(e => e.textType === type);
        if (targetIndex === -1) return;
        // 创建新对象触发响应式
        const newItem = {
          ...this.currentOptions[targetIndex],
          value: res.detail.value,
          text: res.detail.value || this.currentOptions[targetIndex].noValue
        };
        this.$set(this.currentOptions, targetIndex, newItem);
      },
      getForm(itemName,flag){
        return this.currentOptions.filter(e=>e.textType === itemName)[0] || (flag ? null : {})
      },
      setForm(itemName,className,value){
        this.currentOptions.map(e=>{
          if(e.textType === itemName){
            this.$set(e,className,value)
          }
        })
      },
      timestampToDateTime(timestamp) {
          if(typeof timestamp !== 'number') return timestamp
          // 创建一个 Date 对象
          const date = new Date(timestamp); // 时间戳通常是以秒为单位，Date 对象需要毫秒

          // 获取年、月、日、时、分
          const year = date.getFullYear();
          const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要加1，并补零
          const day = String(date.getDate()).padStart(2, '0');
          const hours = String(date.getHours()).padStart(2, '0');
          const minutes = String(date.getMinutes()).padStart(2, '0');

          // 拼接成所需的格式
          return `${year}-${month}-${day} ${hours}:${minutes}`;
      },
      async trigger(){
        switch (this.modelType){
          case 'code':
            this.downloadCode()
            break;
          default:
            this.confirmActions()
            break;
        }
      },
      // 获取订单二维码
      async orderCode(){
        uni.showLoading({title:'加载中...',mask:true})
        // 插入存储订单二维码
        try {
          // 查询该订单是否有二维码
          let {data:codeData} = await this.$api.accompanyDoctor.minichannellinkQueryOne({
            businessId:this.orderDetails.id,
            businessType:4
          })
          // 如果有则直接返回
          if(codeData.qrcodePath){
            // 确保二维码图片链接是完整的URL
            let qrCodeUrl = codeData.qrcodePath;
            if (qrCodeUrl && !qrCodeUrl.startsWith('http://') && !qrCodeUrl.startsWith('https://')) {
              const baseUrl = getApp().globalData.baseUrl || '';
              qrCodeUrl = baseUrl ? baseUrl + qrCodeUrl : qrCodeUrl;
            }

            // 确保使用https协议
            if (qrCodeUrl.startsWith('http://')) {
              qrCodeUrl = qrCodeUrl.replace('http://', 'https://');
              console.log('已将图片URL从http转换为https:', qrCodeUrl);
            }

            this.codeImg = qrCodeUrl;
            console.log('二维码图片地址:', this.codeImg);
            uni.hideLoading();
            return;
          }
          // 如果没有则插入参数 渲染二维码
          let {data} = await this.$api.common.codeInsert({
            path:'modules/accompany-doctor/service-reservation/index?id='+this.orderDetails.id,
            appid:serverOptions.getoptions().appId,
            name:'订单二维码',
            businessType:4,
            businessId:this.orderDetails.id,
            CustomParameters:`providerId=${this.providerId}`
          });

          // 确保二维码图片链接是完整的URL
          let qrCodeUrl = data.qrcodePath;
          if (qrCodeUrl && !qrCodeUrl.startsWith('http://') && !qrCodeUrl.startsWith('https://')) {
            const baseUrl = getApp().globalData.baseUrl || '';
            qrCodeUrl = baseUrl ? baseUrl + qrCodeUrl : qrCodeUrl;
          }

          // 修改图片链接
          if (qrCodeUrl.startsWith('http://')) {
            qrCodeUrl = qrCodeUrl.replace('http://', 'https://');
            console.log('已将图片URL从http转换为https:', qrCodeUrl);
          }

          this.codeImg = qrCodeUrl;
          console.log('新生成的二维码图片地址:', this.codeImg);

          uni.hideLoading();
        } catch (error) {
          console.error('获取二维码失败:', error);
          uni.hideLoading();
          uni.showToast({
            title: '获取二维码失败',
            icon: 'none'
          });
        }
      },

      // 下载二维码到本地
      downloadCode(){
        if (!this.codeImg) {
          uni.showToast({
            title: '二维码未加载',
            icon: 'none'
          });
          return;
        }
        uni.showLoading({title: '保存中...', mask: true});

        let imageUrl = this.codeImg;

        // 确保URL是完整的
        if (imageUrl && !imageUrl.startsWith('http://') && !imageUrl.startsWith('https://')) {
          const baseUrl = getApp().globalData.baseUrl || '';
          if (baseUrl) {
            imageUrl = baseUrl + imageUrl;
          } else {
            imageUrl = 'https://file.greenboniot.cn/' + imageUrl;
          }
        }

        // 确保使用https协议
        if (imageUrl.startsWith('http://')) {
          imageUrl = imageUrl.replace('http://', 'https://');
        }

        // 直接下载并保存
        uni.downloadFile({
          url: imageUrl,
          success: ({statusCode, tempFilePath}) => {
            // console.log('下载结果: 状态码=', statusCode, '临时路径=', tempFilePath);
            if (statusCode === 200) {
              uni.saveImageToPhotosAlbum({
                filePath: tempFilePath,
                success: () => {
                  uni.hideLoading();
                  uni.showToast({
                    title: '保存成功',
                    icon: 'success'
                  });
                },
                fail: (err) => {
                  uni.hideLoading();
                  uni.showToast({
                    title: '保存失败: ' + (err.msg || '未知错误'),
                    icon: 'none'
                  });
                }
              });
            } else {
              uni.hideLoading();
              uni.showToast({
                title: '下载失败，状态码: ' + statusCode,
                icon: 'none'
              });
            }
          },
          fail: (err) => {
            uni.hideLoading();
            uni.showToast({
              title: '下载失败: ' + (err.msg || '未知错误'),
              icon: 'none'
            });
          }
        });
      },
      async getProviderId(city){
        try {
          const {data} = await this.$api.accompanyDoctor.getAccompanyproviderAll({city});
          if (data && Array.isArray(data)) {
            this.ProviderIdMap = data.map(e => {
              return {...e, text:e.providerName, value:e.id}
            });
          } else {
            this.ProviderIdMap = [];
          }
        } catch (error) {
          this.ProviderIdMap = [];
        }
      },
      async confirmActions() {
        // 新增取消订单验证逻辑
        if (this.modelType === 'clear') {
          if (!this.cancelReason.trim()) {
            uni.showToast({ title: '取消原因不能为空', icon: 'none' });
            return;
          }
          if (this.orderDetails.orderState >= 3) {
            const amount = Number(this.refundAmount);
            const maxRefundAmount = (this.orderDetails.payPrice || 0) / 100;
            if (isNaN(amount) || amount < 0 || amount > maxRefundAmount) {
              uni.showToast({ title: `退款金额需在0-${maxRefundAmount}元之间`, icon: 'none' });
              return;
            }
          }
        }

        // 针对'server'模式特别检查分类和服务是否已经选择
        if (this.modelType === 'server') {
          if (!this.getForm('serviceName').value) {
            uni.showToast({ title: '请选择服务项目', icon: 'none' });
            return;
          }
        }

        // 对于结束服务操作，使用简化的参数
        if (this.modelType === 'finish') {
          const queryOptions = {
            id: this.orderDetails.id
          };

          try {
            const data = await this.$api.providerManagement[this.apiMap[this.modelType]](queryOptions);
            this.$emit('finish');
            uni.showToast({ title: '操作成功', icon: 'none' });
          } catch (error) {
            uni.showToast({ title: error.msg || '操作失败', icon: 'none' });
          } finally {
            this.close();
          }
          return;
        }

        // 获取当前选择的服务商ID
        const selectedProviderId = this.getForm('providerName')?.value;
        const finalProviderId = selectedProviderId || this.providerId;
        // 保持原有参数构造逻辑
        let queryOptions = {
          id: this.orderDetails.id,
          providerId: finalProviderId,
          serviceId: this.orderDetails.serviceId,
          source: serverOptions.source,
          condition: { providerId: finalProviderId },
          ...this.currentOptions.reduce((nex, current) => {
            if (current.valueType === 'price') {
              nex.price = Math.round(current.value * 100);
              return nex;
            }
            if (current.classTimeMap) {
              current.classTimeMap.map((e, index) => {
                nex[e] = current.value[index]
              });
              return nex;
            }
            if (current.type === 'image') {
              nex[current.valueType] = current.value.join(',');
              return nex;
            }
            if (current.valueType === 'employeeId' && this.getForm('modeName', true)) {
              if (this.getForm('modeName').value === 2) {
                nex[current.valueType] = '';
                return nex
              }
            }
            if (current.valueType === 'patientFileSee') {
              // 提交时用patientSeeType字段
              nex['patientSeeType'] = current.value;
              return nex;
            }
            if (current.valueType === 'remark') {
              nex[current.valueType] = current.value || '';
              return nex;
            }
            // 如果是providerId字段，确保使用最终确定的服务商ID
            if (current.valueType === 'providerId') {
              nex[current.valueType] = finalProviderId;
              return nex;
            }
            nex[current.valueType] = current.value
            return nex;
          }, {})
        };
        // 新增取消订单参数合并
        if (this.modelType === 'clear') {
          queryOptions = {
            ...queryOptions,
            cancelReason: this.cancelReason,
            refundAmount: Math.round(this.refundAmount * 100) // 转换为分
          };
        }
        try {
          const data = await this.$api.accompanyDoctor[this.apiMap[this.modelType]](queryOptions);
          this.$emit('finish');
          uni.showToast({ title: '操作成功', icon: 'none' });
        } catch (error) {
          uni.showToast({ title: error.msg || '操作失败', icon: 'none' });
        } finally {
          this.close();
        }
      },
      getPosition(){
        let resFn;
        let promise = new Promise(res=>resFn = res);
        uni.getLocation({
          type: 'wgs84',
          geocode:true,
          	success: async (res)=> {
              console.log('resresres',res);
              let Position = await this.$ext.common.getPosition(res);
              console.log('Position',Position);
              resFn(Position)
          	},
            fail(e) {
             console.log('eee',e);
            }
        });
        return promise
      },
      emit(type){
        console.log('触发',type);
        switch (type){
          case 'serviceName':
            // 在平台端先检查是否选择了服务商
            if(serverOptions.source === 1 && !this.getForm('providerName').value) {
              uni.showToast({
                title:'请先选择服务商',
                icon:'none'
              });
              return;
            }
            this.openServerFlag = true
            // 无论是否为平台端，都需要传递正确的参数
            this.$nextTick(() => {
              // 确保弹窗已打开后再调用getServerData
              if(serverOptions.source === 1) {
                // 平台端传递服务商ID
                const providerId = this.getForm('providerName').value;
                if(providerId) {
                  if(this.$refs.selectChangeServer) {
                    this.$refs.selectChangeServer.lastProviderId = providerId;
                  }
                  // 如果有分类ID，一并传递
                  const classifyId = this.getForm('classifyId').value;
                  if(classifyId) {
                    this.$refs.selectChangeServer.getServerData(true, {providerId, classifyId});
                  } else {
                    this.$refs.selectChangeServer.getServerData(true, {providerId});
                  }
                }
              } else {
                // 非平台端也需要传递分类ID
                const classifyId = this.getForm('classifyId').value;
                if(classifyId && this.$refs.selectChangeServer) {
                  this.$refs.selectChangeServer.getServerData(true, {classifyId});
                } else if(this.$refs.selectChangeServer) {
                  this.$refs.selectChangeServer.getServerData(true);
                }
              }
            });
            break;
          case 'classifyId':
            // 在平台端先检查是否选择了服务商
            if(serverOptions.source === 1 && !this.getForm('providerName').value) {
              uni.showToast({
                title:'请先选择服务商',
                icon:'none'
              });
              return;
            }
            this.openServerFlag = true
            // 无论是否为平台端，都需要传递正确的参数
            this.$nextTick(() => {
              // 确保弹窗已打开后再调用getServerData
              if(serverOptions.source === 1) {
                // 平台端传递服务商ID
                const providerId = this.getForm('providerName').value;
                if(providerId) {
                  if(this.$refs.selectChangeServer) {
                    this.$refs.selectChangeServer.lastProviderId = providerId;
                  }
                  // 如果有分类ID，一并传递
                  const classifyId = this.getForm('classifyId').value;
                  if(classifyId) {
                    this.$refs.selectChangeServer.getServerData(true, {providerId, classifyId});
                  } else {
                    this.$refs.selectChangeServer.getServerData(true, {providerId});
                  }
                }
              } else {
                // 非平台端也需要传递分类ID
                const classifyId = this.getForm('classifyId').value;
                if(classifyId && this.$refs.selectChangeServer) {
                  this.$refs.selectChangeServer.getServerData(true, {classifyId});
                } else if(this.$refs.selectChangeServer) {
                  this.$refs.selectChangeServer.getServerData(true);
                }
              }
            });
            break;
          case 'hospitalName':
            this.$refs.selectHospital.show()
            break;
          case 'selectTime':
            this.$refs.selectTime.show()
            break;
          case 'provinceCity':
            this.$refs.selectCity.show()
            break;
          case 'backupImg':
            this.$refs.upDataImage.uploadImage()
            break;
          case 'modeName':
            this.$refs.selectmode.show()
            break;
          case 'employeeName':
            this.$refs.selectAccompanyemployee.show()
            break;
          case 'providerName':
            this.$refs.selectProviderId.show()
            break;
          case 'deptName':
            this.$refs.selectDept.show()
            break;
          case 'doctorName':
            this.$refs.selectDoctorName.show()
            break;
          case 'patientRecord':
            this.showPatientSelectModal = true;
            // 确保显示弹窗前加载数据
            this.loadPatientRecords();
            break;
          case 'patientFileSee':
            // 显示选择器弹窗
            uni.showActionSheet({
              itemList: this.getForm('patientFileSee').options.map(item => item.text),
              success: (res) => {
                const selectedOption = this.getForm('patientFileSee').options[res.tapIndex];
                this.onchangePatientSeeType({
                  detail: {
                    value: [selectedOption]
                  }
                });
              }
            });
            break;
          case 'accompanyOrderIdList':
            // 获取当前已选择的记录ID
            const recordItem = this.getForm('accompanyOrderIdList');
            // 确保selectedIds是字符串格式
            let selectedIds = '';
            if (recordItem && recordItem.value) {
              // 如果是数组，转为逗号分隔的字符串
              if (Array.isArray(recordItem.value)) {
                selectedIds = recordItem.value.join(',');
              } else if (typeof recordItem.value === 'string') {
                selectedIds = recordItem.value;
              }
            }
            this.$navto.push('accompanyRecord', {
              id: this.orderDetails.id,
              userId: this.orderDetails.userId,
              providerId: this.orderDetails.providerId,
              city: this.orderDetails.city,
              province: this.orderDetails.province,
              from: 'model',
              selectedIds: selectedIds // 传递已选择的记录ID
            });
            break;
          default:
            break;
        }
      },
      changeExchange(flag){
        console.log('flag',flag);
        this.openServerFlag = flag
      },
      selectServer(options){
        // 设置服务信息
        this.setForm('serviceName','text', options.serviceName || '');
        this.setForm('serviceName','value', options.id || '');

        // 设置价格信息
        if (options.price) {
          const priceInYuan = options.price / 100;
          this.setForm('price', 'value', priceInYuan);
          this.setForm('price', 'text', `￥${priceInYuan}`);
        } else {
          this.setForm('price', 'value', '');
          this.setForm('price', 'text', '');
        }

        // 如果服务所属的分类信息可用，也更新分类信息
        if (options.classifyId && options.classifyName) {
          this.setForm('classifyId', 'text', options.classifyName);
          this.setForm('classifyId', 'value', options.classifyId);
        }

        // 如果服务商ID可用，也更新服务商ID
        if (options.providerId) {
          // 只有在未设置providerId时才更新
          if (!this.getForm('providerName')?.value) {
            this.setForm('providerName', 'value', options.providerId);
            // 尝试从ProviderIdMap中找到对应的名称
            const provider = this.ProviderIdMap.find(p => p.value === options.providerId);
            if (provider) {
              this.setForm('providerName', 'text', provider.text);
            }
          }
        }
      },
      onchangeHospital({ detail: { value } }) {
        this.setForm('hospitalName', 'text', value[0].text);
        this.setForm('hospitalName', 'value', value[0].text);
        this.orderDetails.hospitalId = value[0].value;

        // 清空科室和医生的表单值
        this.setForm('deptName', 'text', '');
        this.setForm('deptName', 'value', '');
        this.setForm('doctorName', 'text', '');
        this.setForm('doctorName', 'value', '');

        // 清空科室和医生的查询数据
        this.deptNameQuery = [];
        this.doctorNameQuery = [];

        this.getdeptName(this.orderDetails.hospitalId);
      },
      onchangeDept({ detail: { value } }) {
        this.setForm('deptName', 'text', value[0].text);
        this.setForm('deptName', 'value', value[0].text);

        // 清空医生的表单值
        this.setForm('doctorName', 'text', '');
        this.setForm('doctorName', 'value', '');

        // 清空医生的查询数据
        this.doctorNameQuery = [];

        if (value[0].value) {
          this.getDoctorName({
            deptId: value[0].value,
            hospitalId: this.orderDetails.hospitalId
          });
        }
      },
      onchangeDoctorName({detail:{value}}){
        this.setForm('doctorName','text',value[0].text);
        this.setForm('doctorName','value',value[0].text);
      },
      onchangeCity({detail:{value}}){
        this.setForm('provinceCity','text',value.map(e=>e.text).join('~'));
        this.setForm('provinceCity','value',value.map(e=>e.value));
        let [province,city] = value.map(e=>e.value)
        this.getHospital({province,city})
        this.onchangeHospital({detail:{value:[{text:'',value:''}]}})
        // 在平台小程序下请求兼容数据
        if(serverOptions.source === 1){
          this.setForm('providerName','text','');
          this.setForm('providerName','value','');
          this.getProviderId(city);
        }
      },
      onchangeTime(value){
        this.setForm('selectTime','text',value.join('~'));
        this.setForm('selectTime','value',value);
      },
      onchangeProviderId({detail:{value}}){
        // 保存选择的服务商ID和名称
        const providerId = value[0].value;
        const providerName = value[0].text;

        // 设置表单值
        this.setForm('providerName','text', providerName);
        this.setForm('providerName','value', providerId);

        // 切换服务商时，清空已选择的服务和分类
        this.setForm('classifyId', 'text', '');
        this.setForm('classifyId', 'value', '');
        this.setForm('serviceName', 'text', '');
        this.setForm('serviceName', 'value', '');
        this.setForm('price', 'text', '');
        this.setForm('price', 'value', '');

        // 如果是平台端，则在切换服务商时清空分类和服务，并获取该服务商的分类列表
        if (serverOptions.source === 1) {
          // 先重置changeServer组件的状态
          if (this.$refs.selectChangeServer) {
            this.$refs.selectChangeServer.lastProviderId = providerId; // 使用当前选择的服务商ID
            this.$refs.selectChangeServer.categoryList = [];
            this.$refs.selectChangeServer.currentCategoryId = '';
            this.$refs.selectChangeServer.currentServices = [];
          }

          // 打开服务选择器弹窗
          this.openServerFlag = true;

          // 更新服务列表，传入服务商ID
          this.$nextTick(() => {
            if (this.$refs.selectChangeServer) {
              this.$refs.selectChangeServer.getServerData(true, {providerId});
            }
          });
        } else {
          // 非平台端直接调用getServerData
          this.$refs.selectChangeServer.lastProviderId = providerId; // 确保设置了lastProviderId
          this.$refs.selectChangeServer.getServerData(true, {providerId});
        }
      },
      closeImgage(index){
        this.getForm('backupImg').value.splice(index, 1);
      },
      previewImage(urls){
        uni.previewImage({urls:[urls]});
      },
      imgReturnFn(imageObj){
        let {value:oldValue} = this.getForm('backupImg');
        oldValue.push(...imageObj.map(e=>e.filePath))
        this.setForm('backupImg','value',[...new Set(oldValue)]);
      },
      onchangeMode({ detail: { value } }) {
        const mode = value[0].value;
        this.setForm('modeName', 'text', value[0].text);
        this.setForm('modeName', 'value', mode);
        const employeeField = this.currentOptions.find(
          e => e.valueType === 'employeeId'
        );
        if (employeeField) {
          this.$set(employeeField, 'hidden', mode !== 1);
          if (mode !== 1) {
            this.$set(employeeField, 'value', '');
            this.$set(employeeField, 'text', employeeField.noValue);
          }
        }
      },
      onchangeEmployee({detail:{value}}){
        this.setForm('employeeName','text',value[0].text);
        this.setForm('employeeName','value',value[0].value);
      },
      // 处理就诊人档案选择
      onchangePatientSeeType({detail:{value}}) {
        const selectedValue = value[0].value;
        this.setForm('patientFileSee', 'text', value[0].text);
        this.setForm('patientFileSee', 'value', selectedValue);

        // 根据选择的值控制陪诊记录字段的显示/隐藏
        const accompanyRecordField = this.currentOptions.find(
          e => e.valueType === 'accompanyOrderIdList'
        );
        if (accompanyRecordField) {
          this.$set(accompanyRecordField, 'hidden', selectedValue !== 1);
          if (selectedValue !== 1) {
            // 如果不是手动勾选，清空陪诊记录的值
            this.$set(accompanyRecordField, 'value', '');
            this.$set(accompanyRecordField, 'text', accompanyRecordField.noValue);
          }
        }
      },
      clearInput({detail:{value}}){
        console.log('res',value);
        this.setForm('refundAmount','value',value === '' ? '' : (+value));
      },
      changeShow(res){
        !res.show && this.$emit('change',res.show)
      },
      close(){
        this.$emit('change',false)
      },
      async getDoctorName({hospitalId,deptId}){
        let {data:getDoctorNameMap} = await this.$api.hospital.crawlershospitaldoctor({hospitalId,deptId});
        console.log('getDoctorNameMap',getDoctorNameMap);
        this.doctorNameQuery = [];
        this.doctorNameQuery = getDoctorNameMap.map(e=>{
          return {...e,text:e.name,value:e.id}
        })
      },
      async getHospital({ province, city }) {
        try {
          const { data: { records: hospitalQuery } } = await this.$api.hospital.hospitalQueryPage({
            current: 0,
            size: 1000,
            condition:{
              province:province.replace(/省|市/g,''),
              // 去掉字符串里的市和市辖区还有市市辖区 使用正则祛除
              city:city.replace(/市市辖区|市辖区|市/g,''),
             }
          });
          const processedHospitals = hospitalQuery.map(e => ({
            ...e,
            text: e.hospitalName,
            value: e.id
          }));
          this.hospitalQuery = processedHospitals;
          // 匹配当前医院
          const targetHospital = processedHospitals.find(
            item => item.hospitalName === this.orderDetails.hospitalName
          );
          console.log('targetHospital', targetHospital);
          // 加载科室数据
          if (targetHospital?.id) {
            const id = targetHospital.id
            this.orderDetails.hospitalId = id
            // 调用科室接口
            const { data: departments } = await this.$api.hospital.crawlershospitaldeptQuery({id});
            // 处理科室数据格式
              this.deptNameQuery = departments.map(e=>{
                return {...e,text:e.name,value:e.id}
              })
            console.log('deptNameQuery', this.deptNameQuery);
          }
              // 处理回显科室
              if (this.orderDetails.deptName) {
                const dept = this.deptNameQuery.find(d => d.text === this.orderDetails.deptName);
                if (dept) {
                  // 设置科室表单的值为ID
                  this.setForm('deptName', 'text', dept.name);
                  this.setForm('deptName', 'value', dept.id);
                  this.orderDetails.deptId = dept.id; // 更新当前科室ID
                  // 调用医生接口
                  await this.getDoctorName({
                    hospitalId: targetHospital.id,
                    deptId: dept.id
                  });
                }
              }
        } catch (error) {
          // uni.showToast({ title: '未查询到医院/科室', icon: 'none' });
        }
      },
      // 加载患者档案记录
      async loadPatientRecords() {
        try {
          const codeUserInfo = common.getKeyVal('user', 'codeUserInfo', true);
          if (!codeUserInfo || !codeUserInfo.id) return;

          // 获取当前用户的所有患者档案
          const {data} = await this.$api.accompanyDoctor.accompanypatientQueryAll({providerId: serverOptions.providerId});
          this.patientRecords = data || [];

          if (this.patientRecords.length > 0) {
            console.log('已找到患者档案', this.patientRecords.length, '条');
          } else {
            console.log('未找到患者档案');
          }
        } catch (error) {
          console.error('获取患者档案失败', error);
        }
      },

      // 显示患者选择弹窗
      showPatientSelect() {
        this.loadPatientRecords();
        this.showPatientSelectModal = true;
      },

      // 关闭患者选择弹窗
      closePatientSelectModal() {
        this.showPatientSelectModal = false;
      },

      // 选择患者
      selectPatientItem(patient) {
        this.setForm('patientRecord', 'text', patient.name);
        this.setForm('patientRecord', 'value', patient.id);
        this.closePatientSelectModal();
      },

      // 跳转到编辑患者页面
      goToEditPatient(patient) {
        this.$navto.push('addPatient', {
          id: patient.id,
          type: 'edit'
        });
      },

      // 跳转到新增患者页面
      goToAddPatient() {
        this.$navto.push('addPatient', {
          type: 'add'
        });
      },

      // 格式化手机号
      formatPhone(phone) {
        if (!phone) return '';
        return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
      },

      // 处理选择的陪诊记录
      handleSelectAccompanyRecord(records) {
        // console.log('接收到的陪诊记录数据类型:', typeof records, '内容:', records);
        if (Array.isArray(records)) {
          // 多选情况
          const recordIds = records.map(r => r.id);
          this.setForm('accompanyOrderIdList', 'text', `已选择 ${records.length} 条记录`);
          this.setForm('accompanyOrderIdList', 'value', recordIds);
        } else if (records && records.ids) {
          this.setForm('accompanyOrderIdList', 'text', `已选择 ${records.ids.length} 条记录`);
          this.setForm('accompanyOrderIdList', 'value', records.ids);
        } else if (records && records.id) {
          // 单选情况
          // console.log('单选数据:', records.id);
          this.setForm('accompanyOrderIdList', 'text', records.text);
          this.setForm('accompanyOrderIdList', 'value', [records.id]);
        } else {
          console.error('未识别的陪诊记录数据格式:', records);
        }
      },
    }
  }
</script>

<style lang="scss">
  .lineHide{
    width: 0;
    overflow: hidden;
    height: 0;
  }
  .iconRightArrow{
    width: 32rpx;
    height: 32rpx;
  }
  .clearBox{
    height: 926rpx !important;
  }
  .codeBox{
    width: 750rpx !important;
    height: 1134rpx !important;
    background: #00B484 !important;
    border-radius: 24rpx 24rpx 0rpx 0rpx !important;
  }
  .refreshBox{
    height: 1016rpx !important;
    overflow: scroll;
  }
  // .dispatcherBox{
  //   height: 1004rpx !important;
  // }
  .codeBtn{
    background: #FFFFFF !important;
    color: #00B484 !important;
  }
  .confirm{
    width: 100vw;
    background: #F4F6FA;
    padding: 32rpx;
    box-sizing: border-box;
    border-radius: 24rpx 24rpx 0rpx 0rpx;
    position: fixed;
    bottom: 0;
    .confirmTitle{
      font-weight: 500;
      font-size: 32rpx;
      color: #1D2029;
      text-align: center;
      position: relative;
      .iconClose{
        width: 32rpx;
        height: 32rpx;
        position: absolute;
        right: 0;
        top: 6rpx;
      }
    }

    .clear{
      .clearTitle{
        font-weight: 400;
        font-size: 28rpx;
        color: #1D2029;
      }
      .clearInput{
        font-weight: 400;
        font-size: 28rpx;
        margin-top: 24rpx;
        width: 686rpx;
        height: 88rpx;
        background: #FFFFFF;
        border-radius: 16rpx;
        border: 1rpx solid #D9DBE0;
        padding: 24rpx;
        box-sizing: border-box;
      }
      .miniTitle{
        font-weight: 400;
        font-size: 24rpx;
        color: #4E5569;
        margin-top: 36rpx;
      }
      .clearValue{
        width: 686rpx;
        height: 288rpx;
        background: #FFFFFF;
        border-radius: 16rpx;
        border: 1rpx solid #D9DBE0;
        margin-top: 24rpx;
        padding: 24rpx;
        box-sizing: border-box;
      }
    }
    .code{
      width: 638rpx;
      height: 766rpx;
      background-image: url($static_ctx + 'image/business/hulu-v2/orderCodeBg.png');
      background-size: 100%;
      margin: 48rpx auto;
      padding: 0 32rpx;
      box-sizing: border-box;
      .codeTitle{
        font-weight: 500;
        font-size: 28rpx;
        color: #1D2029;
        height: 136rpx;
        width: 100%;
        line-height: 136rpx;
        text-align: center;
        border-bottom: 2rpx solid #BCDDD4;
      }
      .payCode{
        width: 390rpx;
        height: 390rpx;
        background: #FFFFFF;
        border-radius: 40rpx;
        border: 12rpx solid #9DE3D0;
        margin: 110rpx auto;
        display: flex;
        align-items: center;
        justify-content: center;
        .codeImg{
          width: 90%;
          height: 90%;
          border-radius: 40rpx;
        }
      }
      .codeTip{
        font-weight: 400;
        font-size: 24rpx;
        color: #4E5569;
        text-align: center;
        margin-top: 24rpx;
      }
    }
    .actionsConfirm{
      width: 686rpx;
      height: 88rpx;
      background: #00B484;
      border-radius: 44rpx;
      font-weight: 500;
      font-size: 32rpx;
      color: #FFFFFF;
      text-align: center;
      line-height: 88rpx;
      margin-top: 50rpx;
    }
  }
  .sign {
    color: #FF5500;
    font-weight: 500;
    font-size: 28rpx;
    margin-right: 8rpx;
  }

  .patient-select-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;

    .modal-mask {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
    }

    .patient-select-container {
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      background: #fff;
      border-radius: 24rpx 24rpx 0 0;
      padding: 32rpx;

      .patient-select-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 32rpx;

        .patient-select-title {
          font-size: 32rpx;
          font-weight: 500;
          color: #333;
        }

        .close-icon {
          width: 48rpx;
          height: 48rpx;
          display: flex;
          align-items: center;
          justify-content: center;

          .close-icon-image {
            width: 32rpx;
            height: 32rpx;
          }
        }
      }

      .patient-list {
        max-height: 600rpx;
        overflow-y: auto;

        .patient-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 24rpx 0;
          border-bottom: 1rpx solid #eee;

          .patient-info {
            flex: 1;

            .patient-name {
              font-size: 28rpx;
              color: #333;
              margin-right: 16rpx;
            }

            .patient-phone {
              font-size: 28rpx;
              color: #666;
            }
          }

          .edit-icon {
            width: 48rpx;
            height: 48rpx;
            display: flex;
            align-items: center;
            justify-content: center;

            .edit-icon-image {
              width: 32rpx;
              height: 32rpx;
            }
          }
        }
      }

      .add-btn-wrapper {
        margin-top: 32rpx;

        .add-patient-btn {
          height: 96rpx;
          background: #00B484;
          border-radius: 48rpx;
          display: flex;
          align-items: center;
          justify-content: center;

          .add-patient-btn-text {
            font-size: 32rpx;
            color: #fff;
            font-weight: 500;
            margin-right: 8rpx;
          }

          .add-icon-image {
            width: 32rpx;
            height: 32rpx;
          }
        }
      }

      .indicator-bar {
        width: 40rpx;
        height: 4rpx;
        background: #eee;
        border-radius: 2rpx;
        margin: 16rpx auto 0;
      }
    }
  }
</style>
