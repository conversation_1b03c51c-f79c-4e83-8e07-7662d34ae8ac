/*
 * @Author: <PERSON>
 * @Date:   2019-01-05 09:24:00
 * @Last Modified by:   <PERSON><PERSON>
 * @Last Modified time: 2019-01-10 15:16:39
 */
const calculationObj = new Object()

/**
 * 获取数字小数点后面length
 */
calculationObj.digitLength = (num) => {
  const eSplit = num.toString().split(/[eE]/)
  const len = (eSplit[0].split('.')[1] || '').length
  return len > 0 ? len : 0
}

/**
 * 把小数转成整数
 */
calculationObj.float2Fixed = (num) => {
  if (num.toString().indexOf('e') === -1) {
    return Number(num.toString().replace('.', ''))
  }
  const dLen = calculationObj.digitLength(num)
  return dLen > 0 ? num * Math.pow(10, dLen) : num
}

/**
 * 乘法
 */
calculationObj.times = (num1, num2) => {
  if (!calculationObj.isVal(num1, num2)) {
    return
  }
  num1 = Number(num1)
  num2 = Number(num2)
  const thisChanged = calculationObj.float2Fixed(num1)
  const numChanged = calculationObj.float2Fixed(num2)
  const baseNum = calculationObj.digitLength(num1) + calculationObj.digitLength(num2)
  const leftValue = thisChanged * numChanged
  return leftValue / Math.pow(10, baseNum)
}

/**
 * 加法
 */
calculationObj.plus = (num1, num2) => {
  if (!calculationObj.isVal(num1, num2)) {
    return
  }
  num1 = Number(num1)
  num2 = Number(num2)
  const baseNum = Math.pow(10, Math.max(calculationObj.digitLength(num1), calculationObj.digitLength(num2)))
  return (calculationObj.times(num1, baseNum) + calculationObj.times(num2, baseNum)) / baseNum
}

/**
 * 减法
 */
calculationObj.minus = (num1, num2) => {
  if (!calculationObj.isVal(num1, num2)) {
    return
  }
  num1 = Number(num1)
  num2 = Number(num2)
  const baseNum = Math.pow(10, Math.max(calculationObj.digitLength(num1), calculationObj.digitLength(num2)))
  return (calculationObj.times(num1, baseNum) - calculationObj.times(num2, baseNum)) / baseNum
}

/**
 * 除法
 */
calculationObj.divide = (num1, num2) => {
  if (!calculationObj.isVal(num1, num2)) {
    return
  }
  num1 = Number(num1)
  num2 = Number(num2)
  const num1Changed = calculationObj.float2Fixed(num1)
  const num2Changed = calculationObj.float2Fixed(num2)
  return calculationObj.times((num1Changed / num2Changed), Math.pow(10, calculationObj.digitLength(num2) - calculationObj.digitLength(num1)))
}

/**
 * 四舍五入
 */
calculationObj.round = (num, ratio) => {
  if (!calculationObj.isVal(num, ratio)) {
    return
  }
  num = Number(num)
  ratio = Number(ratio)
  const base = Math.pow(10, ratio)
  return calculationObj.divide(Math.round(calculationObj.times(num, base)), base)
}

/**
 * 判断是否是数字类型
 */
calculationObj.isVal = (num1, num2) => {
  let isValueB = true
  const re = /(^[\-0-9][0-9]*(.[0-9]+)?)$/ // 判断字符串是否为数字 //判断正整数 /^[1-9]+[0-9]*]*$/
  if (!re.test(num1) || !re.test(num2)) {
    isValueB = false
  }
  return isValueB
}

function algorithm(num1, num2, type) {
  if (!calculationObj.isVal(num1, num2)) {
    return
  }
  num1 = Number(num1)
  num2 = Number(num2)
  let value

  const mark = ['+', '-', '*', '/']
  if (!type) {
    alert('请填写第三个参数算法类型(+,-,*,/)')
    return
  } else if (mark.indexOf(type) === -1) {
    alert('第三个参数算法类型(+,-,*,/)')
    return
  }

  switch (type) {
    case '+':
      value = calculationObj.plus(num1, num2)
      break
    case '-':
      value = calculationObj.minus(num1, num2)
      break
    case '*':
      value = calculationObj.times(num1, num2)
      break
    case '/':
      value = calculationObj.divide(num1, num2)
      break
  }
  return calculationObj.round(value, 2)
}

function algorithmToFixed(num1, num2, type) {
  if (!calculationObj.isVal(num1, num2)) {
    return
  }
  num1 = Number(num1)
  num2 = Number(num2)
  let value

  const mark = ['+', '-', '*', '/']
  if (!type) {
    alert('请填写第三个参数算法类型(+,-,*,/)')
    return
  } else if (mark.indexOf(type) === -1) {
    alert('第三个参数算法类型(+,-,*,/)')
    return
  }

  switch (type) {
    case '+':
      value = calculationObj.plus(num1, num2).toFixed(2)
      break
    case '-':
      value = calculationObj.minus(num1, num2).toFixed(2)
      break
    case '*':
      value = calculationObj.times(num1, num2).toFixed(2)
      break
    case '/':
      value = calculationObj.divide(num1, num2).toFixed(2)
      break
  }

  return value
}

export default {

  // 获取小数点后面多少位
  digitLength: calculationObj.digitLength,

  // 乘法
  times: calculationObj.times,

  // 加法
  plus: calculationObj.plus,

  // 减法
  minus: calculationObj.minus,

  // 除法
  divide: calculationObj.divide,

  // 四舍五入
  round: calculationObj.round,

  // Precise calculation精确算法
  algorithm: algorithm,

  // 精确换算四舍五入保留两位
  algorithmToFixed: algorithmToFixed

}
