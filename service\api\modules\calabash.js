import request from '@/common/util/request'
import env from '@/config/env'

/**
 * 社区请求工具类，
 * 注意：API接口只做请求数据和格式化数据的事情，其他事务一概不处理
 */
export default {

  // 积分祈愿弹幕分页
  pointwishbarrageQueryPage(data) {
    const url = env.ctx + `dm/api/v1/pointwishbarrage/query/page`
    return request.postJson(url, data)
  },
   // 积分祈愿弹幕插入
  pointwishbarrageInsert(data) {
    const url = env.ctx + `dm/api/v1/pointwishbarrage/insert`
    return request.postJson(url, data)
  },
   // 积分祈愿快捷语分页列表查询
  pointwishtemplateQueryPage(data) {
    const url = env.ctx + `dm/api/v1/pointwishtemplate/query/page`
    return request.postJson(url, data)
  },
  // 查询用户积分
  pointuserQueryUser(data) {
    const url = env.ctx + `dm/api/v1/pointuser/query/user`
    return request.get(url, data)
  },
  // 葫芦待领积分列表
  pointrecordScoreList(data) {
    const url = env.ctx + `dm/api/v1/pointrecord/scoreList`
    return request.get(url, data)
  },
  // 葫芦领积分
  pointrecordReceivePoint(data) {
    const url = env.ctx + `dm/api/v1/pointrecord/receivePoint`
    return request.get(url, data)
  },
  // 福币积分任务领取记录
  pointrecordQueryPage(data) {
    const url = env.ctx + `dm/api/v1/pointrecord/query/page`
    return request.postJson(url, data)
  },
  // 积分规则-业务类型
  getBusinessTypeList(data) {
    const url = env.ctx + `dm/api/v1/pointrule/businessTypeList`
    return request.get(url, data)
  },
  // 积分规则-事件类型
  getEventTypeList(data) {
    const url = env.ctx + `dm/api/v1/pointrule/eventTypeList`
    return request.get(url, data)
  },
  // 积分规则-福币任务列表
  pointtaskQueryPointList(data) {
    const url = env.ctx + `dm/api/v1/pointtask/query/pointList`
    return request.get(url, data)
  },
  // 事件处理-完成任务
  pointtasklogHandle(data) {
    const url = env.ctx + `dm/api/v1/pointtasklog/handle`
    return request.postJson(url, data)
  },
  // 请求用户
  fansrecordQueryPage(data) {
    const url = env.ctx + `dm/api/v1/fansrecord/query/page`
    return request.postJson(url, data)
  },
  // 新增积分用户
  pointuserInsert(data) {
    const url = env.ctx + `dm/api/v1/pointuser/insert`
    return request.postJson(url, data)
  },
  // 查询用户是否存在
  pointuserIsExist(data) {
    const url = env.ctx + `dm/api/v1/pointuser/query/user/isExist`
    return request.get(url, data)
  },
  // 积分商品分页
  pointgiftQueryPage(data) {
    const url = env.ctx + `dm/api/v1/pointgift/query/page`
    return request.postJson(url, data)
  },
  // 根据id查询商品
  pointgiftQueryOne(data) {
    const url = env.ctx + `dm/api/v1/pointgift/query/one`
    return request.get(url, data)
  },
  // 积分用户地址表分页列表查询
  pointaddressQueryPage(data) {
    const url = env.ctx + `dm/api/v1/pointaddress/query/page`
    return request.postJson(url, data)
  },
  // 保存积分用户地址
  pointaddressInsert(data) {
    const url = env.ctx + `dm/api/v1/pointaddress/insert`
    return request.postJson(url, data)
  },
  // 更新积分用户地址
  pointaddressUpdate(data) {
    const url = env.ctx + `dm/api/v1/pointaddress/update`
    return request.putJson(url, data)
  },
  // 删除积分用户地址
  pointaddressDeleteOne(id) {
    const url = env.ctx + `dm/api/v1/pointaddress/delete/one/${id}`
    return request.delete(url)
  },
  // 根据id积分用户地址表根据主键单一查询
  pointaddressQueryOne(data) {
    const url = env.ctx + `dm/api/v1/pointaddress/query/one`
    return request.get(url, data)
  },
  // 兑换礼物
  pointgiftexchangeInsert(data) {
    const url = env.ctx + `dm/api/v1/pointgiftexchange/insert`
    return request.postJson(url, data)
  },
  // 礼物兑换分页
  pointgiftexchangeQueryPage(data) {
    const url = env.ctx + `dm/api/v1/pointgiftexchange/query/page`
    return request.postJson(url, data)
  },
}
