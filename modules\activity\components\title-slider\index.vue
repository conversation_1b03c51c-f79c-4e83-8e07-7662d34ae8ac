
<template>
  <view class="title-slider clear-float" :class="{'bdt':defaultConfig.bdt,'bdb':defaultConfig.bdb && !child}">
    <view class="l-l" :style="{'color': defaultConfig.titleColor}" :class="defaultConfig.titleClass">
      {{defaultConfig.label}}
      <text class="star" v-if="defaultConfig.required">*</text>
    </view>
    <view class="l-r">
      <!--<input-->
      <!--  v-model="form.data.val"-->
      <!--  @input="returnFn"-->
      <!--/>-->
      <slider :disabled="disabled" :style="config.unitDesc && config.unitDesc != '' ? 'width: calc(100% - 50px)' : ''"  :value="form.data.val" @change="returnFn" :min="config.numberScope[0]" :max="config.numberScope[1]" show-value />
      <!--<slider value="100" @change="sliderChange" min="50" max="200" show-value />-->
      <!--  -->
     <view class="util" v-if="config.unitDesc && config.unitDesc != ''">
        {{config.unitDesc}}
      </view>
    </view>
  </view>
</template>

<script>

export default {
  data() {
    return {
      form: {
        data: {
          val: this.config.numberScope[0]
        }
      },
      array: [],
      index: 0,
      defaultConfig: {
        bdt: false,
        bdb: true,
        titleColor: '#333',
        textColor: '#333',
        label: '单行输入框',
        name: 'input',
        required: false,
        numberScope:[0,20]
      }
    }
  },
  watch: {
    cData: {
      handler(val) {
        this.watchDataMain(val)
      },
      deep: true
    },
    config: {
      handler(val) {
        this.copyConfig()
      },
      deep: true
    }
  },
  props: {
    child:{
      type:Boolean,
      default:false,
    },
    // 是否禁用disable
    disabled: {
      type: Boolean,
      default() {
        return false
      }
    },
    // 初始值传值，用于回显
    cData: {
      type: [String, Number],
      required: false,
      default() {
        return 0
      }
    },
    // 参数设置
    config: {
      type: Object,
      required: false,
      default: () => {
        return {}
      }
    }
  },
  computed: {

  },
  mounted() {
      this.copyConfig()
    console.log("config",this.config)
      if (!this.$validate.isNull(this.cData)) {
        this.watchDataMain(this.cData)
      }
  },
  methods: {
    /**
       * 初始化拷贝config对象
       */
    copyConfig() {
      const that = this
      const obj = that.config
      Object.keys(obj).forEach(function(key) {
        that.defaultConfig[key] = obj[key]
      })
    },
    /**
       * 监听Data主逻辑方法
       */
    watchDataMain(val) {
      if (val.length===0){
        val = this.config.numberScope[0]
      }
      this.form.data.val = val
    },
    /**
       * picker触发选中事件
       * @param e
       */
    returnFn(e) {
      // debugger
      const that = this
      that.form.data.val = e.detail.value
      if (that.disabled) return
      that.$emit('updateForm', { key: '' + that.config.name, value: that.form.data.val })
    }
  }
}
</script>

<style lang="scss" scoped>
  .color-topicC{
    color: $topicC !important;
  }
  .title-slider{
    background-color:#ffffff;
    .l-l{
      line-height: 88upx;
      color: #333333;
      font-weight: 600;
      font-size:30upx;
    }
    .l-l.font36{
      font-size: 36;
    }
    .l-r{
      margin-bottom: 5px;
      width: 100%;
      position: relative;
      // display: flex;
      // align-items: center;
      input {
        height: 80upx;
        line-height: 80upx;
        color: #333;
        font-size: 32upx;
        text-align: left;
        display: inline-block;
        vertical-align: middle;
        width: calc(100% - 64upx);
        border: 2upx solid #ede9e9;
        border-radius: 10upx;
      }

      .util{
        width: 64upx;
        font-size: 28upx;
        overflow: hidden;
        text-align: center;
        position: absolute;
        overflow: hidden;
        right: 0;
        top: 0;
        display: flex;
        align-items: center;
            margin-top: 2px;
      }
    }
  }
  .star{
    color: #F85E4C;
    padding-top: 6upx;
    font-size: 32upx;
    display: inline-block;
    margin-left: 10rpx;
  }
</style>
