<template>
  <view>
    <uni-nav-bar :left-icon="isBack ? 'left' : null" backgroundColor="transparent" :border="false" color="#1D2029" fixed statusBar @clickLeft="back">
      <slot>
        <view class="nav-bar-title">{{ title }}</view>
      </slot>
    </uni-nav-bar>
    <slot name="after" />
  </view>
</template>

<script>
import uniNavBar from '@/components/uni/uni-nav-bar/uni-nav-bar.vue'
export default {
  components: {
    uniNavBar
  },
  props: {
    isBack: {
      type: Boolean,
      default: true
    },
    title: {
      type: String,
      default: ''
    }
  },
  options: { styleIsolation: 'shared' },
  methods: {
    back() {
      if (!this.isBack) return
      let pages = getCurrentPages() // 获取栈实例
      if (pages.length > 1) {
        this.$navto.back()
      } else {
        this.$navto.replaceAll('Index')
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.nav-bar-title {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 32rpx;
  color: #1D2029;
  line-height: 44rpx;
}
</style>