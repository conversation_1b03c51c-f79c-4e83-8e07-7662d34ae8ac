<template>
  <view>
    <m-nav-bar :title="doctorTitle" left-icon="left" @clickLeft="back" />
    <page>
        <view slot="content" class="body-main">
          <view class="m-main-body">
            <scroll-refresh class="scroll-refresh-main" :isShowEmptySwitch="true" :fixed="false" :up="upOption" :down="downOption" @returnFn="returnFn" @scrollInit="scrollInit">
              <view class="depa-doctor-item" v-for="item in indexlist" :key="item.id" @click="handleJump(item.id)"
              >
                <view class="doctor-item-box">
                  <view class="doctor-item-l">
                    <image mode="aspectFit" :src="item.expertPic"></image>
                  </view>
                  <view class="doctor-item-r">
                    <view class="item-r-head">
                      <view class="name">{{ item.name }}<span>{{ item.post }}</span></view>
                    </view>
                    <view class="item-r-bott">
                      擅长领域：{{ item.introduction }}
                    </view>
                  </view>
                </view>
                <view class="line"></view>
              </view>
            </scroll-refresh>
          </view>
        </view>
    </page>
  </view>
</template>

<script>
  import { isDomainUrl } from '@/utils/index.js'
  export default {
    components: {
    },
    data(){
      return{
        file_ctx:this.file_ctx,
        $constant: this.$constant,
        departmentsList:[],
        doctorList:[],
        currentActive:0,
        depaList:[],
        depaActive:0,
        hospitalId:'',
        deptId:null,
        mescroll: null, // mescroll实例对象
        downOption: {
          auto: false // 不自动加载
        },
        upOption: {
          auto: false, // 不自动加载
          empty: {
            top: 0,
            zIndex: 999,
          },
          onScroll: true
        },
        indexlist:[],
        moreFlag:false,
        icnosType:'bottom',
        curIndex:0,
        tabs: [{ name: '科室医生', consultStatus: '' },{ name: '点评', consultStatus: '' },],
        accompanyMap:[],
        cityName:'',
        doctorTitle: '本地名医', // 动态文案，默认值
      }
    },
    onLoad(option){
      console.log('option',option);
      if(option.cityName){
        this.cityName = decodeURIComponent(option.cityName)
      }
      if(option.doctorTitle){
        this.doctorTitle = decodeURIComponent(option.doctorTitle)
      }
      this.init()
    },
    methods:{
      back() {
        this.$navto.back(1)
      },
      returnFn(obj) {
        console.log('this.mescroll3',this.mescroll);
        const that = this
        setTimeout(()=> {
          let params = {
            current: obj.pageNum,
            size: obj.pageSize,
            condition: {
              city: this.cityName?.split('市')[0] || '',
            }
          }
          let defaultUrl = '0/msg-reply/1013082175633223682.png';
          that.$api.hospital.crawlershospitaldoctorQueryPage(params).then(res => {
              let data = res.data.records.map(item=>({...item,expertPic:isDomainUrl(item.expertPic || defaultUrl)})) || []
              if (obj.pageNum === 1) {
                  that.indexlist = []
              }
              that.indexlist = [...that.indexlist, ...data]
              obj.successCallback && obj.successCallback(data)
          })
        }, that.$constant.noun.scrollRefreshTime)
      },

      scrollInit(scroll) {
        scroll.optUp.page.num = 1
        scroll.optUp.page.size = 10
        this.mescroll = scroll
        console.log('this.mescroll1',this.mescroll);
      },

      init() {
        this.$nextTick(() => {
          console.log('this.mescroll2',this.mescroll);
          this.mescroll.triggerDownScroll()
        })
      },
      handleJump(id){
        this.$navto.push('DoctorDetail', {id, doctorTitle: this.doctorTitle})
      },
    },
 }
</script>

<style lang='scss' scoped>
.line{
    display: block;
    width: 100%;
    height: 1upx;
    background-color: #e0e0e0;
    transform: scaleY(.333);
}
.body-main{
    height: 100%;
    background-color: #f5f5f5;
    .m-main-body{
      height: 100%;
      .scroll-refresh-main{
        height: 100%;
        ::v-deep .mescroll-uni{
          .z-paging-content{
            // background-color: #fff !important;
            // background-color: pink !important;
          }
        }
        .depa-doctor-item{
          padding:20upx 40upx;
          margin:0 20upx 0;
          background-color: #fff;
          .doctor-item-box{
            display: flex;
            padding-bottom:20upx;
            .doctor-item-l{
              width: 140upx;
              height: 100upx;
              image{
                width: 100%;
                height: 100%;
              }
            }
              .doctor-item-r{
                display: flex;
                flex-direction: column;
                flex: 1;
                margin-left: 15upx;
                .item-r-head{
                  .name{
                    color:#000;
                  }
                  span{
                    margin-left: 10upx;
                  }
                }
                .item-r-bott{
                  margin-top: 20upx;
                  display: -webkit-box;
                  -webkit-box-orient: vertical;
                  -webkit-line-clamp: 2;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  color:#333;
                }
              }
          }
        }
        .header{
            display: flex;
            align-items: center;
            padding:30upx 20upx;
            margin:0 20upx;
            border-radius: 20upx;
            background-color: #fff;
            .header-l{
                width: 80upx;
                height: 80upx;
                image{
                    width: 100%;
                    height: 100%;
                }
            }
            .header-r{
                margin-left: 10upx;
                .title{
                    font-size: 32upx;
                    font-weight: bold;
                    color:#000;
                }
            }

        }
        .hospital-content{
            padding:20upx;
            margin:20upx 20upx 0;
            background-color: #fff;
            border-radius: 20upx;
            .hospital-head{
                padding:20upx 0;
                h3{
                    color:#000;
                    font-size: 32upx;
                    font-weight: bold;
                }
                .info{
                    padding-top: 15upx;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 3;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    color:#333;
                }
            }
            .outpatient{
                .info{
                    padding:25upx 0;
                    color:#333;
                    span{
                        display: inline-block;
                        color: rgb(253, 80, 62);
                        background-color: rgba(253, 80, 62, 0.1);
                        padding:0 10upx;
                        border-radius: 10upx;
                        margin-right: 10upx;
                    }
                }
            }
            .outpatient-date{
                .info{
                    padding:25upx 0;
                    color:#333;
                }
            }
            .hospital-phone{
                .info{
                    padding:25upx 0;
                    color:#333;
                }
            }
            .hospital-address{
                padding-top:25upx;
                color:#333;
            }
        }
      .accompany-bott{
        display: flex;
        padding:40rpx 0;
        background: #FFFFFF;
        border-radius: 16rpx;
        margin: 20rpx 20rpx 0;
        .accompany-bott-item{
          display: flex;
          flex: 1;
          flex-direction: column;
          align-items: center;
          text-align: center;
          // justify-content: center;
          .img{
            width: 72rpx;
            height: 72rpx;
            image{
              width: 100%;
              height: 100%;
            }
          }
          .title{
            font-size: 24rpx;
            color: #1D2029;
            margin-top: 12rpx;
            width: 120rpx;
            word-wrap: break-word;
            white-space: normal;
            word-break: break-all;
          }
        }
      }
        .hospital-depa{
            margin:20upx 20upx 0;
            border-radius: 20upx 20upx 0 0;
            padding:20upx;
            background-color: #fff;
            // height: 100%;
            .depa-header{
                display: flex;
                width: 100%;
                ::v-deep.mytab{
                  width: 100%;
                  .tabs-sticky{
                    width: 100%;
                    .tabs-sticky-body{
                      .tab{
                        display: flex;
                        flex: 1;
                        .text-width{
                          width: 100%;
                        }
                      }
                    }
                  }
                }
            }
            .depa-title{
                display: flex;
                justify-content: space-between;
                margin:30upx 0 30upx;
                .title-l{
                    color:#333;
                }
                .title-r{
                    color:#000;
                    font-size: 26upx;
                }
            }
            .depa-tag-list{
                .depa-tag-box{
                    display: flex;
                    flex-wrap: wrap;
                    max-height:182upx;
                    overflow: hidden;
                    .depa-tag-item{
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        padding:4upx 7upx;
                        border-radius: 15upx;
                        border:1px solid #8f939c;
                        margin-right: 15upx;
                        margin-bottom: 15upx;
                        overflow: hidden;
                        color:#8f939c;
                    }
                    .active{
                        border-color: #2979ff;
                        color:#2979ff;
                    }
                }
                &::-webkit-scrollbar{
                    height: 0;
                }
                .box-active{
                    max-height:360upx;
                    overflow: auto;
                }
            }
            .more{
              display: block;
              height: 40upx;
              line-height: 40upx;
              width: calc(100% - 40upx);
              font-size: 28upx;
              text-align: center;
              margin: 15upx 20upx 0;
              border-radius: 20upx;
              color: #333;
              .move-active{
                margin-left: 10upx;
              }
            }
        }
      }
    }
}
</style>
