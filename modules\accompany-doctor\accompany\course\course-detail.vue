<template>
  <view class='course-detail'>
    <view class="header">
      <view class="video">
        <template v-if="isShow">
          <image :src="file_ctx + coverImg"></image>
        </template>
        <template v-else>
          <!-- <video id="videoId" :autoplay="true" @fullscreenchange="handleFullScreen" @controlstoggle="handleControlstoggle" v-if="detailChildrenObj.chapterType == 2" :src="detailChildrenObj.chapterContent" object-fit="cover"> -->
            <!-- id="videoId"  -->
          <video 
            :autoplay="true" 
            v-if="detailChildrenObj.chapterType == 2" 
            :src="detailChildrenObj.chapterContent" 
            :controls="true"
            @timeupdate="onTimeUpdate"
            @ended="onEnded"
            @pause="onPause"
            @play="onPlay"
            @seeking="onSeeking" 
            @seeked="onSeeked" 
          >
              <!-- object-fit="cover"  -->
            <!-- 倍速按钮 -->
            <!-- <cover-view v-show="speedState&&controlsToggle" class="speed">
                <text @click="speedNum=true" class="doubleSpeed">倍速</text>
            </cover-view> -->
            <!-- 倍速面板 -->
            <!-- <cover-view v-if="speedNum" class="speedModal" @click.stop="speedNum=false"
                :style="{width:clientHeight+37+'px',height: videoWidth+16+'px'}">
                <view class="speedNumBox" :style="{height: videoWidth+16+'px'}">
                    <text @click.stop="handleSetSpeedRate(0.5)" :class="[0.5 == speedRate?activeClass:errorClass,'number']">0.5</text>
                    <text @click.stop="handleSetSpeedRate(0.75)" :class="[0.75 == speedRate?activeClass:errorClass,'number']">0.75</text>
                    <text @click.stop="handleSetSpeedRate(1)" :class="[1 == speedRate?activeClass:errorClass,'number']">正常</text>
                    <text @click.stop="handleSetSpeedRate(1.25)" :class="[1.25 == speedRate?activeClass:errorClass,'number']">1.25</text>
                    <text @click.stop="handleSetSpeedRate(1.5)" :class="[1.5 == speedRate?activeClass:errorClass,'number']">1.5</text>
                    <text @click.stop="handleSetSpeedRate(2.0)" :class="[2.0 == speedRate?activeClass:errorClass,'number']">2.0</text>
                </view>
            </cover-view> -->
          </video>
          <swiper v-else class="swiper" circular :indicator-dots="true" :autoplay="true" :interval="3000" indicator-color="rgba(255,255,255,0.6)" indicator-active-color="#00B484">
            <swiper-item class="swiper-item" v-for="(item,index) in detailChildrenObj.chapterContent.split(',')" :key="index">
              <image :src="item" @click="handleLookImg(index,detailChildrenObj.chapterContent.split(','))"></image>
            </swiper-item>
          </swiper>
        </template>
      </view>
      <!-- <view class="img-list"></view> -->
      <view class="info">
        <view class="title">{{ courseDetail.courseName }}</view>
        <view class="tag">{{ studyStat.studyCount }} 人已学</view>
      </view>
    </view>
    <view class="detail-content">
      <view class="detail-header">
        <view class="detail-item" v-for="(item,index) in navList" :key="index" @click="handleClickActive(index)">
          <view :class="currentIndex == index ? 'text active' : 'text'">{{ item.name }}</view>
          <view v-if="currentIndex == index" class="active-border"><image :src="file_ctx + 'static/image/business/accompany-doctor/icon-text-active-border.png'"></image></view>
        </view>
      </view>
      <view class="detail-item-content" :style="{'height':`calc(100vh - ${myDataHeight + detailHeight + 20}px)`}" v-if="currentIndex == 0">
        <!-- 编辑器显示图文内容，编辑器显示图文内容， -->
        <rich-text class="name" :nodes="courseDetail.summary" preview :style="{whiteSpace: 'pre-wrap',}"></rich-text>
      </view>
      <view class="catalogue" :style="{'height':`calc(100vh - ${myDataHeight + detailHeight + 20}px)`}" v-if="currentIndex == 1">
        <view class="uni-collapse">
          <view
            class="uni-collapse-item"
            v-for="(item, index) in directoryChapterList"
            :key="index"
            @click="toggle(index)"
          >
            <view class="uni-collapse-title">
              <view class="text">{{ item.directoryTitle }}</view>
              <view class="uni-collapse-title-arrow">
                <uni-icons :type="item.show ? 'top' :'bottom'" size="16" color="#999"></uni-icons>
              </view>
            </view>
            <view class="uni-collapse-content" v-show="item.show">
              <view class="collapse-content-item" v-for="(item2,index2) in item.chapterList" :key="index2" @click.stop.prevent="handleClickDetailChildren(item2,item,index2)">
                <view :class="chapterActive == index2 ? 'item-l active' : 'item-l'">
                  <view class="img" v-if="item2.chapterType == 2"><image :src="file_ctx + 'static/image/business/accompany-doctor/icon-course-detail-live.png'"></image></view>
                  <view class="img" v-else><image :src="file_ctx + `static/image/business/accompany-doctor/icon-course-detail-img-${item2.learned == 1 ? 'active' : 'default'}.png`"></image></view>
                  {{ item2.chapterTitle }}
                </view>
                <view class="item-r" v-if="item2.learned == 1">已学</view>
              </view>
            </view>
          </view>
        </view>
      </view>
      <view class="data-content" :style="{'height':`calc(100vh - ${myDataHeight + detailHeight + 20}px)`}" v-else>
        <view class="data-content-item" v-for="item in courseDataList" :key="item.id" @click="handleLookData(item.infoUrl)">
          <view class="data-title">{{ item.infoName }}</view>
          <view class="data-look">查看</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  import UniIcons from '@/components/uni/uni-icons/uni-icons'
  import common from '@/common/util/main'
  import serverOptions from '@/config/env/options'

  export default {
    components:{
      UniIcons,
    },
    data(){
      return{
        file_ctx:this.file_ctx,
        navList:[{id:1,name:'简介'},{id:2,name:'目录'},{id:3,name:'资料'}],
        currentIndex:0,
        myDataHeight:null,
        detailHeight:null,
        courseDetail:null,
        coverImg:null,
        directoryChapterList:null,
        courseId:null,
        detailChildrenObj:null, // 当前目录子级对象
        detailItemObj:null, // 当前目录对象
        isShow:true,
        userInfo:null, // 用户信息
        studyStat:null, //学习人数统计
        visit:null, //播放时长
        timer:null,
        chapterActive:null,
        queryId:null,



        realPlayTime: 0,     // 记录真实播放时长
        lastTime: 0,         // 记录上次更新时间
        isPlaying: false,    // 判断视频是否在播放
        isSeeking: false,    // 判断是否正在拖拽
        startTime: 0,        // 记录播放开始时的时间
        videoContext:null,
        courseDataList:[], //课程资料
        // speedState:false,
        // controlsToggle:true,
        // speedNum:false,
        // activeClass:'active',
        // errorClass:'noActive',
        // speedRate:1,
        // videoWidth: 0, //设置视频宽度
        // clientHeight: 0, //设备高度
      }
    },
    async onLoad(option){
      const query = this.$validate.isNull(this.$Route.query) ? option : this.$Route.query
      if(query?.id){
        this.queryId = query.id 
        const userId = serverOptions.getUserId(this);
        if(!userId){
          uni.showToast({title:'请先登录',icon:'none'})
          return;
        }
        let {data} = await this.$api.accompanyDoctor.getAccompanyemployeeOneByUserId({userId})
        this.userInfo = data;
        // 课程详情
        this.accompanycourseQueryOne(query.id)
        // 目录
        this.accompanycourseDirectoryList(query.id,this.userInfo.id)

      }
      // 学习统计人数
      this.accompanycoursestudylogStudyStatistic()

      // uni.getSystemInfo({
      //   success: (res)=> {
      //     this.videoWidth = res.screenWidth - 16
      //     this.clientHeight = res.screenHeight
      //     console.log(this.videoWidth,'this.videoWidth0-0000')
      //     console.log(this.clientHeight,'this.clientHeight0-0000')
      //   }
      // });
    },
    onUnload(){
      if(this.timer){
        clearInterval(this.timer)
        this.timer = null
      }
    },
    mounted(){
      // 获取播放器上下文（后面的 this 需要传入，在微信小程序上无法暂停播放拖拽精度，所以需要传入这个）
      // this.videoContext = uni.createVideoContext('videoId', this)
      this.getElementHeight()
    },
    methods:{
      // 查看资料
      handleLookData(url){
        uni.downloadFile({
          url: url,
          success: function (res) {
            var filePath = res.tempFilePath;
            uni.openDocument({
              filePath: filePath,
              showMenu: true,
              success: function (res) {
                console.log('打开文档成功');
              }
            });
          }
        });
      },
      // 开始拖拽时
      onSeeking() {
        this.isSeeking = true;
      },

      // 拖拽结束时
      onSeeked() {
        this.isSeeking = false;
        // 如果视频有拖拽过，重置播放的时间
        this.startTime = Date.now();
      },
      onPlay(){
        clearInterval(this.timer)
        this.isPlaying = true;
        this.startTime = Date.now();  // 记录播放开始时间
        // this.isPlaying = true
        this.timer = setInterval(()=>{
          if(this.visit > 0){
            this.accompanycoursestudylogInsert(this.detailChildrenObj,this.detailItemObj)
          }
        },5000)
      },
      onPause(){
        this.isPlaying = false;
        // 暂停时累加到播放时长
        // this.realPlayTime += (Date.now() - this.startTime) / 1000;  // 将毫秒转换为秒
        this.realPlayTime = 0
        clearInterval(this.timer)
        this.timer = null
      },
      onEnded(){
        this.isPlaying = false;
        this.realPlayTime += (Date.now() - this.startTime) / 1000;  // 播放结束时计算播放时长
        clearInterval(this.timer)
        this.timer = null
      },
      onTimeUpdate(e){
        if (this.isPlaying && !this.isSeeking) {
          // const currentTime = Math.floor(event.target.currentTime);
          this.visit = Math.ceil(e.detail.currentTime)
          if (this.visit > this.lastTime) {
            // 只更新播放时长，如果是正向播放
            this.realPlayTime += (Date.now() - this.startTime) / 1000;
            this.startTime = Date.now();  // 更新开始时间
          }
        }
      },
      //监听进入全屏 和 退出全屏
      // handleFullScreen(e) {
      //   console.log(e,'ee00000')
      //     //e.detail对象的两个属性fullScreen和direction，一个可以得到true/false 另一个得到horizontal/vertical
      //   this.speedState = e.detail.fullScreen;
      //   console.log(this.speedState,'this.speedState0000')
      // },
      // //倍速按钮：显示的条件  1.首先全屏   2.控件（播放/暂停按钮、播放进度、时间）是显示状态
      // handleControlstoggle(e) {
      //   console.log(e,'e11111111')
      //   this.controlsToggle = e.detail.show;
      // },
      // //设置倍速速度
      // handleSetSpeedRate(rate) {
      //   let videoContext = uni.createVideoContext("videoId");
      //   videoContext.playbackRate(rate);
      //   this.speedRate = rate;
      // },

      handleLookImg(index,list){
        uni.previewImage({
          current: list[index], // 当前显示图片的http链接
          urls: list
        })
      },
      async handleClickDetailChildren(itemChildren,item,index){
        this.realPlayTime = 0
        // console.log(this.realPlayTime,'this.realPlayTime00000')
        // this.lastTime = 0
        // this.startTime = 0
        // this.visit = null

        this.chapterActive = index
        this.isShow = false
        this.detailChildrenObj = itemChildren
        this.detailItemObj = item
        if(itemChildren.chapterType == 1){
          // const res = await this.accompanycoursestudylogInsert(itemChildren,item)
          this.accompanycoursestudylogInsert(itemChildren,item)
          itemChildren.learned = 1
          // if(itemChildren.learned == 2 || itemChildren.learned == ""){
          //   this.accompanycourseDirectoryList(this.queryId,this.userInfo.id)
          // }
        }
      },
      // 新增观看记录
      async accompanycoursestudylogInsert(itemChildren,item){
        let params = {
          employeeId:this.userInfo.id,
          courseId:this.courseId,
          directoryId:item.id,
          chapterId:itemChildren.id,
          // visit:itemChildren.chapterType == 1 ? 0 : this.visit,
          visit:itemChildren.chapterType == 1 ? 0 : Math.ceil(this.realPlayTime),
        }
        const res = await this.$api.accompanyDoctor.accompanycoursestudylogInsert(params)
        this.realPlayTime = 0
        this.visit = null
        // return Promise.resolve(res)
      },
      accompanycourseQueryOne(id){
        this.$api.accompanyDoctor.accompanycourseQueryOne({id}).then(res => {
          // console.log(res,'res11111')
          if(res.data !== ""){
            this.courseDetail = res.data
            this.coverImg = res.data.detailImg
          }
        })
      },
      accompanycoursestudylogStudyStatistic(){
        this.$api.accompanyDoctor.accompanycoursestudylogStudyStatistic({}).then(res => {
          if(res.data !== ""){
            this.studyStat = res.data
          }
        })
      },
      accompanycourseDirectoryList(id,employeeId){
        this.$api.accompanyDoctor.accompanycourseDirectoryList({id,employeeId}).then(res => {
          if(res.data !== ""){
            this.courseId = res.data.courseId
            this.directoryChapterList = res.data.directoryChapterList.map(item=>({...item,show:false}))
          }
        })
      },
      toggle(index) {
        this.directoryChapterList[index].show = !this.directoryChapterList[index].show;
      },
      async handleClickActive(index){
        this.currentIndex = index
        if(this.currentIndex == 2){
          const res = await this.$api.accompanyDoctor.accompanycourseinfoQueryInfo({courseId:this.courseId})
          this.courseDataList = res.data
        }
      },
      getElementHeight() {
        let query = uni.createSelectorQuery().in(this);
        query.select('.header').boundingClientRect(data => {
          if (data) {
            this.myDataHeight = data.height
          }
        }).exec();

        query.select('.detail-header').boundingClientRect(data => {
          if (data) {
            this.detailHeight = data.height
          }
        }).exec();
      },
    },
 }
</script>

<style lang='scss' scoped>
.course-detail{
  height: 100vh;
  background: #F4F6FA;
  .header{
    margin-bottom: 20rpx;
    background-color: #fff;
    .video{
      height: 422rpx;
      width: 100%;
      // background-color: pink;
      image{
        width: 100%;
        height: 100%;
      }
      video{
        width: 100%;
        height: 100%;
        // .speed {
        //   position: absolute;
        //   right: 20rpx;
        //   top: 16rpx;

        //   .doubleSpeed {
        //     color: #fff;
        //     font-size: 14rpx;
        //     background-color: rgba(0, 0, 0, 0.6);
        //     padding: 4rpx 6rpx;
        //   }
        // }
        // // 倍速的蒙版
        // .speedModal {
        //     background-color: rgba(0, 0, 0, 0.7);
        // }

        // .speedNumBox {
        //     display: flex;
        //     flex-direction: column;
        //     justify-content: space-around;
        //     align-items: center;
        //     background-color: #2c2c2c;
        //     width: 120rpx;
        //     position: absolute;
        //     right: 0rpx;
        //     top: 0;

        //     .number {
        //         width: 120rpx;
        //         font-weight: 700;
        //         font-size: 14rpx;
        //         padding: 18rpx 0;
        //         display: flex;
        //         justify-content: center;
        //         align-items: center;
        //         text-align: center;
        //     }

        //     .active {
        //         color: red;
        //     }

        //     .noActive {
        //         color: #fff;
        //     }
        // }
      }
      .swiper{
        height: 100%;
        .swiper-item{
          width: 100%;
          height: 100%;
          image{
            width: 100%;
            height: 100%;
          }
        }
      }
    }
    .info{
      padding: 32rpx;
      .title{
        font-size: 36rpx;
        color: #1D2029;
        line-height: 50rpx;
      }
      .tag{
        margin-top: 16rpx;
        font-size: 22rpx;
        color: #00664B;
        padding: 6rpx 12rpx;
        background: #DEF2ED;
        border-radius: 8rpx;
        width:fit-content;
      }
    }
  }
  .detail-content{
    background: #fff;
    .detail-header{
      display: flex;
      justify-content: space-around;
    }
    .detail-item{
      display: flex;
      flex-direction: column;
      align-items: center;
      padding:24rpx 0;
      .text{
        font-size: 32rpx;
        color: #1D2029;
      }
      .active{
        color:#00B484;
      }
      .active-border{
        display: flex;
        width: 38rpx;
        height: 10rpx;
        margin-top: 2rpx;
        image{
          width: 100%;
          height: 100%;
        }
      }
    }
    .detail-item-content{
      padding: 0 32rpx;
    }
    .catalogue,.data-content{
      padding: 0 32rpx;
      .uni-collapse-item {
        display: flex;
        flex-direction: column;
        margin-bottom: 16rpx;
      }
      .uni-collapse-title {
        display: flex;
        flex: 1;
        justify-content: space-between;
        align-items: center;
        border-radius: 16rpx;
        padding: 24rpx 24rpx 22rpx;
        cursor: pointer;
        font-size: 30rpx;
        color: #1D2029;
        background: #F4F6FA;
      }
      .uni-collapse-content{
        .collapse-content-item{
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding:24rpx 0 24rpx 32rpx;
          font-size: 28rpx;
          color: #1D2029;
          border-bottom: 1rpx solid #EAEBF0;
          .item-l{
            display: flex;
            align-items: center;
            .img{
              display: flex;
              width: 26.67rpx;
              height: 26.67rpx;
              // background-color: pink;
              margin-right: 20rpx;
              image{
                width: 100%;
                height: 100%;
              }
            }
          }
          .active{
            color:#00B484;
          }
          .item-r{
            padding: 4rpx 8rpx;
            background: #F4F6FA;
            border-radius: 8rpx;
            font-size: 22rpx;
            color: #4E5569;
          }
          &:last-child{
            border-bottom: none;
          }
        }
      }
    }
    .data-content{
      .data-content-item{
        display: flex;
        justify-content: space-between;
        border-radius: 16rpx;
        padding: 24rpx 24rpx 22rpx;
        cursor: pointer;
        font-size: 30rpx;
        color: #1D2029;
        background: #F4F6FA;
        margin-bottom: 16rpx;
        &:last-child{
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>