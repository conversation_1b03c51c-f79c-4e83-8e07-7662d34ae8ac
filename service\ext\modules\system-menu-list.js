import $env from '@/config/env'
const $static_ctx = $env.static_ctx

const menuList = {
    // 设备运维管理
    // deviceManagement: {
    //     name: '设备运维',
    //     desc: '',
    //     code: '',
    //     isState: true, // 对否收起
    //     isPermissions: false, // 是否直接开放权限
    //     list: [
    //         { imgUrl: $static_ctx + 'image/store/management/icon-logo-one-day-living-template.png', name: '测试出袋', url: 'TestOutBag', obj: {}, code: '' },
    //         { imgUrl: $static_ctx + 'image/store/management/icon-logo-absenteeism.png', name: '设备注册', url: 'DeviceRegistration', obj: {}, code: '' },
    //         { imgUrl: $static_ctx + 'image/store/management/icon-logo-hour-care-service-configuration.png', name: '设备管理', url: '', obj: {}, code: '' },
    //         { imgUrl: $static_ctx + 'image/store/management/icon-logo-preparing-audit.png', name: '设备记录', url: 'DeviceIssueLog', obj: {}, code: '' },
    //         { imgUrl: $static_ctx + 'image/store/management/icon-logo-employee-audit-leave.png', name: '故障日志', url: 'DeviceFaultLog', obj: {}, code: '' }
    //     ]
    // },
    activityManagement: {
    name: '活动管理',
    desc: '',
    code: '',
    isState: true, // 对否收起
    isPermissions: false, // 是否直接开放权限
    list: [
      { imgUrl: $static_ctx + 'image/store/management/icon-logo-case_collect_activity.png', name: '病例征集', url: '', obj: {}, code: '' },
      { imgUrl: $static_ctx + 'image/store/management/icon-logo-research.png', name: '学术调研', url: 'Research', obj: {}, code: '' },
      { imgUrl: $static_ctx + 'image/store/management/icon-logo-execute_task.png', name: '我的任务', url: 'WaitToBeDone', obj:  { tapIndex: 1 }, code: '' },
      { imgUrl: $static_ctx + 'image/store/management/icon-logo-research.png', name: '学术调研', url: 'Research', obj: {}, code: '' },
      { imgUrl: $static_ctx + 'image/store/management/icon-logo-execute_task.png', name: '我的任务', url: 'WaitToBeDone', obj:  { tapIndex: 1 }, code: '' },
      { imgUrl: $static_ctx + 'image/store/management/icon-logo-research.png', name: '学术调研', url: 'Research', obj: {}, code: '' },
      { imgUrl: $static_ctx + 'image/store/management/icon-logo-execute_task.png', name: '我的任务', url: 'WaitToBeDone', obj:  { tapIndex: 1 }, code: '' },
      { imgUrl: $static_ctx + 'image/store/management/icon-logo-research.png', name: '学术调研', url: 'Research', obj: {}, code: '' },
      { imgUrl: $static_ctx + 'image/store/management/icon-logo-execute_task.png', name: '我的任务', url: 'WaitToBeDone', obj:  { tapIndex: 1 }, code: '' },
      { imgUrl: $static_ctx + 'image/store/management/icon-logo-research.png', name: '学术调研', url: 'Research', obj: {}, code: '' },
      { imgUrl: $static_ctx + 'image/store/management/icon-logo-execute_task.png', name: '我的任务', url: 'WaitToBeDone', obj:  { tapIndex: 1 }, code: '' },
    ]
  }
}
for (const a in menuList) {
    for (let b = 0; b < menuList[a].list.length; b++) {
        menuList[a].list[b].module = a
        menuList[a].list[b].eq = b + 1
    }
}
/**
 * 系统所有菜单列表
 */
export default menuList
