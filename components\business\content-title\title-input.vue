<!-- 内容模块标题配件组件 <title-jump :title="xx" :text="xx" @returnFn = "returnFn"></title-jump> -->
<template>
  <view
    class="univalent"
    :class="{ bdt: bdt, bdb: bdb }"
    :style="disabledStyle"
  >
    <text class="l-t"
      >{{ title }}<text class="xingxing" v-if="isRequired">*</text></text
    >
    <view v-if="disabled" class="l-r">
      <view class="l-r-text">
        <view class="l-r-text-l" :class="{ 'width-auto': !rightText }">
          {{ val }}
        </view>
        <view class="l-r-text-r" v-if="rightText">{{ rightText }}</view>
      </view>
    </view>
    <view v-else class="l-r-input">
      <input
        :class="{ 'width-auto': !rightText }"
        :maxlength="maxlength"
        :value="val"
        :type="typeStr"
        :placeholder-class="placeholderClass"
        :placeholder="placeholder"
        :disabled="disabled"
        :adjust-position="true"
        @input="returnFn"
        @blur="returnBlurFn"
        @focus="focusFn"
      />
      <view class="l-r-input-text" v-if="rightText">{{ rightText }}</view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    // type为positive-number、positive-integer时，这个属性是用于控制值的最大值(输入熟悉的值必须是数值，如：99、'99')
    maxNumber: {
      type: [Number, String],
      default() {
        return ''
      }
    },
    // type为positive-number、positive-integer时，这个属性是用于控制值的最小值(输入熟悉的值必须是数值，如：0、'0')
    minNumber: {
      type: [Number, String],
      default() {
        return ''
      }
    },
    // type为positive-number时，这个属性是用于控制保留多少位小数，默认保留两位小时
    digit: {
      type: [Number, String],
      default() {
        return 2
      }
    },
    // 单位字符
    rightText: {
      type: String,
      default() {
        return ''
      }
    },
    // 是否去除空格
    isBlank: {
      type: Boolean,
      default() {
        return true
      }
    },
    // 限制最大值
    maxlength: {
      type: Number,
      default() {
        return -1
      }
    },
    // 初始值
    initialValue: {
      type: [Number, String],
      default() {
        return ''
      }
    },
    // 是否禁用disable
    disabled: {
      type: Boolean,
      default() {
        return false
      }
    },
    disabledStyle: {
      type: Object,
      default() {
        return {}
      }

    },
    // 标题名称
    title: {
      type: String,
      default() {
        return ''
      }
    },
    // 返回content
    placeholder: {
      type: String,
      default() {
        return ''
      }
    },
    placeholderClass: {
      type: String,
      default() {
        return 'f-w-medium'
      }
    },
    // border-top
    bdt: {
      type: Boolean,
      default() {
        return false
      }
    },
    // border-bottom
    bdb: {
      type: Boolean,
      default() {
        return true
      }
    },
    // border-bottom
    type: {
      type: String,
      default() {
        return 'text'
      }
    },
    isRequired: {
      type: Boolean,
      default() {
        return false
      }
    },
    // 数组下标
    arrayIndex: {
      type: [String, Number],
      default() {
        return ''
      }
    },
    // 数组下标
    businessType: {
      type: [String, Number],
      default() {
        return ''
      }
    }
  },
  watch: {
    initialValue: {
      handler(val) {
        this.watchDataMain(val)
      },
      deep: true
    }
  },
  data() {
    return {
      typeStr: 'text',
      val: ''
    }
  },
  created() {
    if (this.type === 'positive-number') {
      this.typeStr = 'digit'
    } else if (this.type === 'positive-integer') {
      this.typeStr = 'number'
    } else if (this.type === 'number') {
      this.typeStr = 'number'
    } else if (this.type === 'idcard') {
      this.typeStr = 'idcard'
    } else if (this.type === 'digit') {
      this.typeStr = 'digit'
    } else {
      this.typeStr = 'text'
    }
  },
  mounted() {
    this.watchDataMain(this.initialValue)
  },
  methods: {
    watchDataMain(val) {
      this.val = val
    },
    returnFn(e) {
      let card = e.target.value
      if (this.isBlank) {
        if (this.typeStr === 'text') {
          card = card.replace(/(^\s+)|(\s+$)/g, '').replace(/\s/g, '')
        }
      }
      if (this.typeStr === 'number') {
        const str = card + ''
        if (str.indexOf('e-') !== -1) {
          card = 1
        }
      }
      this.val = card
      if (this.$validate.isNull(this.arrayIndex)) {
        this.$emit('returnFn', card)
      } else {
        const paramObj = {
          index: this.arrayIndex,
          businessType: this.businessType,
          value: card
        }
        this.$emit('returnFn', paramObj)
      }
    },
    focusFn(e) {
      if (this.type === 'positive-number' && e.target.value == 0) {
        this.val = ''
      } else if (this.type === 'positive-integer' && e.target.value == 0) {
        this.val = ''
      }
    },
    returnBlurFn(e) {
      if (this.type === 'positive-number') {
        // 强制转换为正数（多数用于输入价格，如-13转为13，-13.0585转成13.05保留两位小数），参数1：值(Number, String)；参数二：保留多少位小数,默认保留两位(Number, String)
        e.target.value = this.$common.positiveNumber(e.target.value, this.digit)
        if (!this.$validate.isNull(this.maxNumber) && !isNaN(this.maxNumber)) {
          if (e.target.value > Number(this.maxNumber)) {
            e.target.value = this.maxNumber
          }
        }
        if (!this.$validate.isNull(this.minNumber) && !isNaN(this.minNumber)) {
          if (e.target.value < Number(this.minNumber)) {
            e.target.value = this.minNumber
          }
        }
        this.returnFn(e)
      } else if (this.type === 'positive-integer') {
        // 强制转换为正整数（多数用于输入价格，如-13转为13，-13.0585转成13），传入值必须为number类型
        e.target.value = this.$common.positiveInteger(e.target.value)
        if (!this.$validate.isNull(this.maxNumber)) {
          if (e.target.value > Number(this.maxNumber)) {
            e.target.value = this.maxNumber
          }
        }
        if (!this.$validate.isNull(this.minNumber)) {
          if (e.target.value < Number(this.minNumber)) {
            e.target.value = this.minNumber
          }
        }
        this.returnFn(e)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.width-auto {
  width: 100% !important;
}
.univalent {
  width: 100%;
  box-sizing: border-box;
  padding: 0 30upx;
  overflow: hidden;
  height: 88upx;
  background-color: #ffffff;
  .l-t {
    color: #333333;
    font-size: 32upx;
    height: 88upx;
    line-height: 88upx;
    width: 304upx;
    display: inline-block;
    vertical-align: middle;
    @include ellipsis(1);
  }
  .l-r-input {
    width: calc(100% - 304upx);
    display: inline-block;
    vertical-align: middle;
    input {
      height: 88upx;
      line-height: 88upx;
      color: #333;
      font-size: 32upx;
      text-align: right;
      display: inline-block;
      vertical-align: middle;
      width: calc(100% - 64upx);
    }
    .l-r-input-text {
      height: 88upx;
      line-height: 88upx;
      color: #333;
      font-size: 32upx;
      display: inline-block;
      vertical-align: middle;
      text-align: right;
      width: 64upx;
      overflow: hidden;
    }
  }
  .l-r {
    display: inline-block;
    vertical-align: middle;
    width: calc(100% - 304upx);
    overflow-x: auto;
    font-size: 32upx;
    text-align: right;
    color: #999;
    .l-r-text {
      white-space: nowrap;
      .l-r-text-l {
        height: 88upx;
        line-height: 88upx;
        font-size: 32upx;
        display: inline-block;
        vertical-align: middle;
        text-align: right;
        width: calc(100% - 64upx);
      }
      .l-r-text-r {
        height: 88upx;
        line-height: 88upx;
        font-size: 32upx;
        display: inline-block;
        vertical-align: middle;
        text-align: right;
        width: 64upx;
      }
    }
  }
}
.uni-input-placeholder {
  text-align: right;
}
.xingxing {
  color: #f85e4c;
  padding-top: 6upx;
  font-size: 32upx;
  display: inline-block;
}
.f-w-medium {
  color: #bfbfbf;
  font-weight: Medium;
  font-size: 32upx;
}
</style>
