export default {
  certfType: [{
      "value": "01",
      "text": "身份证"
    },
    // {
    //   "value": "02",
    //   "text": "户口本"
    // },
    // {
    //   "value": "03",
    //   "text": "护照"
    // },
    // {
    //   "value": "06",
    //   "text": "返乡证（港澳居民来往大陆通行证）"
    // },
    // {
    //   "value": "42",
    //   "text": "台胞证（台湾居民来往大陆通行证）"
    // },
    // {
    //   "value": "550",
    //   "text": "港澳台居住证"
    // },
    // {
    //   "value": "12",
    //   "text": "身份证(港澳)"
    // },
    // {
    //   "value": "553",
    //   "text": "外国人永久居留身份证"
    // },
    // {
    //   "value": "335",
    //   "text": "机动车驾驶证"
    // },
    // {
    //   "value": "05",
    //   "text": "驾驶执照"
    // },
    // {
    //   "value": "130",
    //   "text": "无"
    // },
    // {
    //   "value": "04",
    //   "text": "军官证"
    // },
    // {
    //   "value": "07",
    //   "text": "学生证"
    // },
    // {
    //   "value": "10",
    //   "text": "其他证件"
    // },
    // {
    //   "value": "117",
    //   "text": "出生医学证明"
    // }
  ],
  sex:[
    {
      "value": "1",
      "text": "男"
    },
    {
      "value": "2",
      "text": "女"
    }
  ],
  invoiceLine:[
    {
      "value": "pc",
      "text": "普通发票",
      select:true
    },
    // {
    //   "value": "bs",
    //   "text": "增值税专用发票",
    //   select:false
    // }
  ],
  company:[
    {
      "value": false,
      "text": "个人",
      select:true
    },
    {
      "value": true,
      "text": "企业单位",
      select:false
    }
  ]
}
