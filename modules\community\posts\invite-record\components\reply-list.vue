<template>
  <scroll-refresh
    :fixed="false"
    :up="upOption"
    :down="downOption"
    @returnFn="returnFn"
    @scrollInit="scrollInit"
  >
    <view class="main-content">
      <view
        class="item"
        v-for="item in list"
        :key="item.id"
        hover-class="message-hover-class"
      >
        <view class="user-box">
          <image
            class="avatar"
            mode="aspectFill"
            :src="item.postMessageDetailVo.headPath || defaultAvatar"
          ></image>
          <view class="user-box-content" v-if="item.postMessageDetailVo">
            <text class="user-name">{{ item.postMessageDetailVo.nickName }}</text>
            <view class="time-box">
              <text class="data-item">邀请 {{ item.nickName }} 回答</text>
              <text class="data-item">{{ item.inviteDurationText }}</text>
            </view>
          </view>
          <!-- <uni-icons type="closeempty" size="18" color="#ccc"></uni-icons> -->
        </view>
        <view class="data-box">
          <view class="reply-content"
            >{{ item.postMessageDetailVo.title }}</view
          >
          <view class="reply-data-box">
            <view class="posts-data">
              <text class="data-item">{{ item.postMessageDetailVo.showReadNumber }}浏览</text>
              <text class="data-item">{{ item.postMessageDetailVo.showCommentNumber }}回答</text>
              <text class="data-item">{{ item.postMessageDetailVo.showLikeNumber }}关注</text>
            </view>
            <!-- #ifdef H5 -->
            <view>
                <wx-open-launch-weapp
                    style="height:80rpx;"
                    :appid="communityAppid"
                    :path="`modules/community/posts/detail/index?id=${item.postMessageDetailVo.id}`"
                >
                    <script type="text/wxtag-template">
                        <style>
                          .btn {
                            margin: 0;
                            padding: 0 12px;
                            display: flex;
                            align-items: center;
                            background-color: #4cd964;
                            border: none;
                            height: 34px;
                            line-height: 34px;
                            border-radius: 16px;
                            font-size: 14px;
                            color: #fff;
                          }
                        </style>
                        <button class="btn" type="primary" size="mini">
                          查看帖子
                        </button>
                    </script>
                </wx-open-launch-weapp>
            </view>
            <!-- #endif -->
            <!-- #ifndef H5 -->
            <button class="btn" type="primary" size="mini" @tap="checkReply(item)">
              <uni-icons
                style="margin-right: 6rpx"
                type="compose"
                size="22"
                color="#fff"
              ></uni-icons>
              查看帖子
            </button>
            <!-- #endif -->
          </view>
          <view class="reply-comment-box" v-if="item.replyStatus === 1">
            <text class="reply-comment-title">回复内容：</text>
            <text class="reply-comment-content" v-if="item.commentInfo">
              {{ item.commentInfo.content }}
            </text>
            <template v-if="item.commentInfo.imagePathList">
              <image
                v-for="(imgItem, imgIndex) in item.commentInfo.imagePathList"
                :key="imgIndex"
                class="reply-img"
                mode="aspectFill"
                :src="imgItem.indexOf('http') === -1 ? file_ctx + imgItem : imgItem"
                @tap="viewImgs(item.commentInfo.imagePathList, 0)"
              ></image>
            </template>
          </view>
        </view>
      </view>
    </view>
  </scroll-refresh>
</template>

<script>
import UniIcons from '@/components/uni/uni-icons/uni-icons.vue'
import { mapState } from 'vuex'
import serverOptions from '@/config/env/options'

export default {
  components: {
    UniIcons
  },
  props: {
    params: {
      type: Object,
      default: () => {
        return {}
      }
    },
    index: [Number, String]
  },
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $accurateConversion: this.$accurateConversion,
      file_ctx: this.file_ctx,
      $static_ctx: this.$static_ctx,
      $timePlugin: this.$timePlugin,
      $validate: this.$validate,
      mescroll: null, // mescroll实例对象
      downOption: {
        auto: false // 不自动加载
      },
      upOption: {
        auto: false // 不自动加载
      },
      isInit: false, // 列表是否已经初始化
      list: [],
      defaultAvatar: this.file_ctx + 'static/image/system/avatar/icon-default-v2.png',
      communityAppid: serverOptions.title + '陪诊'
    }
  },
  computed: {
    ...mapState('user', {
      accountId: state => state.accountId
    })
  },
  methods: {
    // 查看图片（多张）
    viewImgs (e, eIndex) {
      const imgs = e.map(item => {
        return item.indexOf('http') === -1 ? this.file_ctx + item : item
      })
      uni.previewImage({
        current: eIndex,
        urls: imgs,
        // #ifndef MP-WEIXIN
        indicator: 'number'
        // #endif
      })
    },
    // 查看帖子
    checkReply(e) {
      this.navtoGo('PostsDetail', {
        id: e.postMessageDetailVo.id
      })
    },
    init(val) {
      this.$nextTick(() => {
        this.mescroll.triggerDownScroll()
      })
    },
    scrollInit(scroll) {
      scroll.optUp.page.num = 1
      scroll.optUp.page.size = 7
      this.mescroll = scroll
    },
    returnFn(obj) {
      const that = this
      setTimeout(function () {
        let params = {
          current: obj.pageNum,
          size: obj.pageSize,
          condition: {
            accountId: that.accountId,
          }
        }
        Object.keys(that.params).forEach(key => {
          if (!['name'].includes(key)) {
            params.condition[key] = that.params[key]
          }
        })
        that.$ext.community.cfpostinvitelogQueryPage(params).then(res => {
          const data = res.data.records || []
          if(obj.pageNum === 1) {
            that.list = []
          }
          that.list = that.list.concat(data)
          obj.successCallback && obj.successCallback(data || [])
        })
      }, that.$constant.noun.scrollRefreshTime)
    },
    navtoGo(url, obj = {}) {
      this.$navto.push(url, obj)
    }
  }
}
</script>

<style lang="scss" scoped>
.main-content {
  padding: 0 32rpx;
  background-color: #fff;
}
.item {
  width: 100%;
  display: flex;
  flex-direction: column;
  padding-top: 32rpx;
  &:last-child {
    padding-bottom: 32rpx;
  }
  .user-box {
    height: 90rpx;
    display: flex;
    align-items: center;
    .avatar {
      width: 90rpx;
      height: 90rpx;
      border-radius: 50%;
      overflow: hidden;
      margin-right: 18rpx;
    }
    .user-box-content {
      height: 100%;
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }
    .user-name {
      font-size: 32rpx;
      line-height: 46rpx;
      font-weight: 500;
      color: #000;
    }
  }
  .data-item {
    font-size: 26rpx;
    color: #c8c8c8;
    line-height: 32rpx;
    & + .data-item {
      position: relative;
      padding-left: 32rpx;
      &::before {
        content: "";
        position: absolute;
        left: 16rpx;
        top: 50%;
        transform: translateY(-50%);
        width: 8rpx;
        height: 8rpx;
        background-color: #e4e4e4;
        border-radius: 50%;
      }
    }
  }
  .data-box {
    display: flex;
    flex-direction: column;
    padding: 32rpx 24rpx;
    background-color: #f6f6f6;
    border-radius: 18rpx;
    margin-top: 12rpx;
    .reply-content {
      font-size: 34rpx;
      line-height: 46rpx;
      font-weight: 600;
      padding-bottom: 12rpx;
    }
    .reply-data-box {
      display: flex;
      align-items: center;
      .posts-data {
        flex: 1;
      }
    }
  }
  .btn {
    margin: 0;
    padding: 0 24rpx;
    display: flex;
    align-items: center;
    background-color: #4cd964;
    border: none;
    height: 68upx;
    line-height: 68upx;
    border-radius: 32rpx;
    font-size: 28rpx;
  }

  .reply-comment-box {
    display: flex;
    flex-direction: column;
    .reply-comment-title {
      color: #000;
      font-size: 26rpx;
      line-height: 42rpx;
      padding-top: 12rpx;
    }
    .reply-comment-content {
      color: #333;
      font-size: 26rpx;
      line-height: 42rpx;
    }
    .reply-img {
      height: 200rpx;
      max-width: calc(100% - 56rpx);
      max-height: 300rpx;
    }
  }
}
</style>
