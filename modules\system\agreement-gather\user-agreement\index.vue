<template>
  <page>
    <view class="main-body" slot="content">
      <scroll-view scroll-y="true" class="main">
        <view class="m-main">
          <h3>用户协议</h3>
          <p>【审慎阅读】您在申请注册流程中点击同意本协议之前，应当认真阅读本协议。请您务必审慎阅读、充分理解各条款内容，特别是免除或者限制责任、法律适用和争议管辖等以粗体或下划线标识的重要条款，您应重点阅读。如您对本协议有任何疑问，可向{{registerTitle}}咨询。</p>
          <p>【签约动作】当您按照注册页面提示填写信息、阅读并同意本协议或完成注册程序后，即表示您已充分阅读、充分理解并接受本协议的全部内容，并与我们达成一致，成为{{registerTitle}}的用户。阅读本协议的过程中，如果您不同意本协议或其中任何条款约定，或对协议未充分理解或存有异议，您应立即停止注册程序并退出。</p>
          <h3>一、概述</h3>
          <p>1.{{registerTitle}}及其指定子公司或关联公司(以 下合称“我们”)根据您的服务需求，通过实时的线上线下信息交互，线下资源借助大数据分析的有效匹配，为您提供多样化的陪诊助医服务(以下简称“服务”),具体服务产品包括但不限于现场陪诊、代问诊、代约号、代跑腿、代买药、“绿通”服务等(以实际开通情况为准),您在访问和使用有关网站、应用程序、小程序提供的前述服务功能时适用本服务协议(以下 简称“本协议”)。</p>
          <p>2.在您使用有关网站、应用程序、小程序及接受服务之前，请您认真阅读本协议(尤其是粗体标注部分)。当您点击同意本协议后，即视为您已充分阅读并接受本协议的所有条款，您同意本协议对您和我们具有法律约束力。</p>
          <h3>二、用户注册</h3>
          <p>1.为使用前述助医服务，您须在{{registerTitle}}平台进行注册以及开 通相关服务(以下统称“平台”),注册时您必须保证提供真实 有效的注册资料。您在完成前述注册、开通且点击同意本协议 后即可成为{{registerTitle}}服务用户。</p>
          <p>2.如果您是代表个人签订本协议，您应具备完全民事行为能力(年满18周岁或年满16周岁、以自己的劳动收入为主要生活来源，且精神健康能完全辨认自己的行为)。</p>
          <p>2.1 如您年满12周岁但尚未具备完全民事行为能力，请在征得您 的监护人同意后签署本协议，并由监护人陪同使用本平台及接  受服务，否则{{registerTitle}}平台有权拒绝服务并取消订单。如您未  经监护人同意即签署本协议或坚持独自接受服务，因您已具备  相应的智力、判断水平，本协议仍然有效，因本协议及您的行  为产生的义务和责任将由您或您的监护人承担。对于您的监护  人未妥善履行监护责任的情况下，导致您自身损失或给他人造  成损失的，将由您的监护人承担相应责任。</p>
          <p>2.2如您尚未年满12周岁或存在其他无民事行为能力的情况，请勿签署本协议或在无监护人陪同的情况下接受服务。如平台及服务人员发现您属于上述情况，应当拒绝提供服务。如您违反本条要求，导致自身损失或给他人造成损失的，将由您的监护人承担相应责任。</p>
          <p>3.您应妥善保管自己的账号及密码，不得将其提供给他人使用，或允许不具备完全民事行为能力的人在无监护人陪同的情况下单独预定及接受服务。如因您未妥善保管账号及密码，或 违反本条要求，允许不具备完全民事行为能力的人使用您的账号，由此产生的义务和责任将由您承担。如因此给他人造成损失的，将由您承担相应责任。</p>
          <p>4.如果您是代表法人实体签订本协议，您应获得授权并遵守本协议(并约束该法人实体)。</p>
          <h3>三、服务使用</h3>
          <p>1.根据相关法律法规，我们为您提供服务并承担相应法律责任。您可以通过{{registerTitle}}平台提出服务需求。我们将按照您的需求信息匹配符合您需求的服务人员，按照平台规则代表我们 向您提供相关服务。</p>
          <p>2.我们将做出合理的努力，让您获得所需的助医服务，但这受制于您请求服务之时所在位置周边是否有提供服务的服务人员。</p>
          <p>3.为防止服务人员私自出借账号等行为的发生，您在接受服务前，请仔细核对提供服务人员是否与平台信息所显示的相关信 息一致。如发现服务人员与平台显示的信息不一致，应拒绝接受服务，并可向我们客服投诉，我们核实后可以按照平台规则对服务人员予以追责。</p>
          <p>4.您不得使用本平台从事违反法律法规或医疗机构规定的活动，否则平台及服务人员有权拒绝提供服务，并由您承担由此引起的后果和责任。</p>
          <p>5.您不得在使用服务过程中进行违反平台规则的不文明的行为（包括但不限于大声喧哗、辱骂殴打他人、随地吐痰等），不得进行或要求服务人员违反公序良俗或者逾越法律法规的行为，因您违反禁止行为的原因导致有关部门行政处罚、人员受伤或其他损失的，您应向受损方进行赔偿，由此引发的纠纷诉讼由您承担。</p>
          <p>6.您作为用户，应当保证发布服务需求信息时是自愿的，且不存在意识不清楚、醉酒等情形。若您有意识不清楚、醉酒等情形的，陪同人员代为发布的助医服务需求信息视为您的行为； 在您醉酒或存在其他意识不清楚的情况下，应当由意识清楚的第三方陪同；在您饮酒、醉酒或意识不清楚的情况下，给他人造成损失的，您应当就相应损失予以赔偿。</p>
          <p>7.您作为用户，在为自己或其他人预定服务时，应当保证向平台充分告知就诊人的相关信息，包括但不限于传染疾病史、精神疾病史、危重病史等，对于国家三级甲等医院要求的、必须由法定监护人陪同到场的就诊人，其监护人必须同时陪同就医，平台及服务人员有权拒绝提供服务。本平台及服务人员不接受不承担前述相关人员的监护责任和义务。</p>
          <p>8.在服务现场，您及就诊人不得要求在平台及服务人员在医疗文书或诊治协议上签字。如您及就诊人确需授权服务人员代为签署的，需要您在正式治疗开始前且意识完全清晰时，与服务人员沟通清楚，双方自行签署书面授权。您双方约定与平台无关，平台不承担由此带来的后果。</p>
          <p>9.您不得在未获得服务人员事先同意的情况下，在使用服务过程中进行照相、录音、录像、网络直播等行为。在征得服务人员同意的前提下进行的照相、录音、录像等行为所获得的内容仅能为记录服务过程中的证明信息之目的，不得通过网络传输、线下拷贝等任何方式进行复制、传播、扩散等。</p>
          <p>10.在您使用本服务过程中获得的其他{{registerTitle}}平台用户的个人信息，除相关法律允许外，您不得向任何第三方公开、透露上述个人信息。</p>
          <h3>四 、保证及承诺</h3>
          <p>1.为实现您的助医服务功能，我们有权对您的服务请求进行验证，并有权根据验证结果决定是否向您提供服务。</p>
          <p>2.您使用我们服务或{{registerTitle}}平台，即表示您还同意以下事项：</p>
          <p>a. 您将严格遵守本协议以及平台规则的约定；</p>
          <p>b.您将遵守所有适用的法律法规规定及公序良俗；</p>
          <p>c.您不会将平台服务或应用程序用于非法目的，包括(但不限于)发送或存储任何非法资料或者用于欺诈目的；</p>
          <p>d. 您不会利用平台服务或应用程序侵犯他人合法权利，不会骚扰、妨碍他人或给他人造成不便；</p>
          <p>e.您不会影响本平台网络的正常运行或者尝试危害平台服务或应用程序；</p>
          <p>f. 当本平台提出合理请求时，您会提供账号及请求相关证明资料；</p>
          <p>g. 您认可与我们建立协议关系并接受其服务。</p>
          <p>3.如果您违反以上任一约定，我们保留立即终止向您提供服务的权利。</p>
          <h3>五、付款及发票</h3>
          <p>1.您同意并认可{{registerTitle}}平台现行公示的有关服务价格标准，您可以在网站上或{{registerTitle}}平台上查看有关服务的价格。这些价格可能会根据市场供需情况调整并在按照规定公示后生效， 您须留意服务的价格，并根据自主意愿选择适合您的服务产品。</p>
          <p>2.我们将在您使用服务前，向您收取预定费用。逾期不支付费用的，我们有权拒绝您提供服务。</p>
          <p>3.您在{{registerTitle}}平台可以使用您的第三方电子支付账户(包括但不限于微信支付账户或支付宝支付账户)进行支付。处理您使用服务相关的付款时，除了受到本协议的约束之外，还要受电子支付服务商及信用卡/借记卡发卡行的条款和政策的约束。 我们对于电子支付服务商或银行发生的错误不承担责任。</p>
          <p>4.在您提出开具发票的要求时，我们将为您开具发票，具体发票开具及申请规则，请参见应用程序上所列示的说明或联系客服处理。</p>
          <p>5.您应在每次服务完成后及时查询实际订单情况，如有异议应当在服务完成后24小时内联系我们，我们将对您提出的异议进行解释和处理。</p>
          <h3>六、违约责任</h3>
          <p>1.如您违反本协议约定，我们有权根据您违约的严重程度对您采取暂停服务、终止服务或其他限制措施。如您违反平台用户规则，我们有权依据平台规则追究您的违规责任。</p>
          <p>2.如您违反本协议约定的行为给我们造成损失，您应赔偿我们所受损失及我们因此支出的合理费用。</p>
          <p>3.如因您违反本协议约定，导致我们被第三方主张权利，我们在向第三方承担责任后，有权向您追偿，追偿内容包括我们所受损失及因此支付的合理费用。</p>
          <p>4.如您的行为使第三方遭受损失且您未及时依照法律法规、本协议约定、平台规则规定等承担责任的，该第三方向我们发出通知的，我们保留限制您使用本平台服务的权利，直至您与该第三方沟通解决相关纠纷。同时，出于维护平台秩序、保护用户合法权利的目的，您同意委托我们代您支付上述款项，但您应当偿还我们因此支付的全部费用。</p>
          <p>5.本条款所称“损失”,包括经济损失、商誉损失等。本条款所称“因此支出的合理费用”,包括诉讼费、律师费、鉴定费、公证费、差旅费等。</p>
          <h3>七、责任限制</h3>
          <p>1.在网站或{{registerTitle}}平台上向您提供的信息、推荐的服务仅供您参考。我们将在合理的范围内尽力保证该等信息准确，但无法保证其中没有任何错误、缺陷、恶意软件和病毒。对于因使用(或无法使用)网站或{{registerTitle}}平台导致的任何损害，我们按照适用的法律法规承担相应责任。对于因使用(或无法使用)网站或{{registerTitle}}平台的电子通信手段导致的任何损害，包括但不限于因电子通信传达失败或延时、第三方或用于电子通信的计算机程序对电子通信的拦截或操纵，以及病毒传输导致的损害，由责任人承担相应责任，同时我们按照适用的法律法规承担相应责任。</p>
          <p>2.您知悉并确认，您通过{{registerTitle}}平台可获得的前述相关服务，由我们根据您发出的预定需求信息，经过后台大数据信息处理，在用户终端上提供可使用的服务信息，最终由平台指定服务人员提供前述服务。我们根据适用的法律法规就上述服务承担相应责任。为更好地保障用户权益，我们将通过平台规范的方式约束平台中的参与方严格遵守平台规则，保障用户权益，我们负责平台规则的合理化及监督规则的实施。从需求用户发送订单并由系统成功匹配开始，到订单完成之时，为线上平台信息服务区间，其中从订单预定开始时间或服务人员与您(或就诊人)见面起始，至订单结束时间或服务完成止，为线下服务提供区间。</p>
          <p>3.{{registerTitle}}平台在以上线上平台信息服务区间内，根据适用法律法规的要求承担相应的法律责任。平台参与方在线下服务提供区间内，根据法律法规的要求承担相应的法律责任。同时，为提升服务品质，保障平台参与各方的安全，并更好地承担企业社会责任，我们将通过个性化的安全保障产品为特定受保障用户提供相应的安全保障服务。对于存在侵权责任人、违约责任人的，在提供安全保障服务后，提供安全保障服务的相关方有权向有关实际责任方追偿。</p>
          <h3>八、授权及许可</h3>
          <p>1.在您遵守本协议的前提下，我们授予您有限的、非排他性的、不可转让的如下许可：以将一份应用程序副本下载并安装到您拥有或控制的单台移动设备上，并仅出于您自身用途运行 此应用程序副本。您不得：</p>
          <p>(1)以任何方式许可、再许可、出售、转售、转让、分配、分发服务或应用程序，或以其他方式进行商业开发或提供给任何第三方；</p>
          <p>(2)修改服务或应用程序，或者据此创建衍生产品；</p>
          <p>(3)创建指向服务的互联网“链接”,或在任何其他服务器或基于无线或互联网的设备上“设计”或“镜像”任何应用程序；</p>
          <p>(4)反向工程或访问应用程序设计或构建竞争产品或服务、使用类似于服务或应用程序的设想或图形来设计或构建产品，或抄袭服务或应用程序的任何设想、特点、功能或图形；</p>
          <p>(5)启动自动程序或脚本，每秒发送多个服务器请求或过度加重服务或应用程序负担或妨碍其工作和/或性能的程序。</p>
          <p>2.此外，您不得：</p>
          <p>(1)发送垃圾邮件或者以其他形式违反适用法律的重复或不受欢迎的邮件；</p>
          <p>(2)发送或存储侵权、淫秽、威胁、诽谤或者其他非法或侵权 资料，包括危害儿童或触犯第三方隐私权的资料；</p>
          <p>(3)发送或存储包含软件病毒、蠕虫、木马或其他有害的计算 机代码、文件、脚本、代理或程序的资料；</p>
          <p>(4)阻挠或扰乱网站、应用程序、服务或其所含数据的完整性或性能；</p>
          <p>(5)尝试未经授权地访问网站、应用程序、服务或其相关系统或网络。</p>
          <p>3.在法律允许的范围内，我们将有权调查并起诉任何上述违法 违规行为。我们可参与并协助执法部门起诉违反本协议的用户。如果我们认为任何内容违反本协议或以其他方式危害网站、平台及/或其中的服务或应用程序，我们保留在不另行通知的情况下删除或禁用对这些内容的访问权限的权利。</p>
          <h3>九、知识产权</h3>
          <p>我们遵守各类适用的知识产权法律法规，并希望您也遵守。</p>
          <h3>十、第三方链接</h3>
          <p>本平台上可能保留有第三方网站(或应用程序)或网址的链接 及第三方服务，是否访问这些链接或接受第三方服务将由您自 己做出决定。您知悉，除法律规定我们应尽的基本审核义务外，我们并不就这些链接上所提供的任何信息、数据、观点、图片、陈述或建议的准确性、完整性、充分性和可靠性提供承诺或保证。该等第三方网站各自制定了协议、规则等，您应充分阅读并理解该等网站或网址的内容及活动并承担浏览或访问这些网站或网址可能带来的法律责任及风险。</p>
          <h3>十一、协议期限</h3>
          <p>1.我们和您订立的本协议是无固定期限的。</p>
          <p>2.您有权随时通过永久性删除智能设备上的应用程序或注销平台账号来终止合约，这样您将无法使用{{registerTitle}}平台及其中的应用程序和服务。</p>
          <p>3.如果您做出以下行为，我们有权随时终止本协议并立即生效(即禁止您使用应用程序和服务):</p>
          <p>a.您触犯或违反本协议中的任何条款；</p>
          <p>b. 您发生违反{{registerTitle}}平台规则的行为。</p>
          <p>4.当我们终止本协议时将以短信或弹窗或停止服务的方式通知您，如您有疑问可以致电官方客服进行咨询。</p>
          <h3>十二、不可抗力</h3>
          <p>遭受不可抗力事件的一方可暂行中止履行本协议项下的义务直 至不可抗力事件的影响消除为止，并且无需为此承担违约责任，但应尽最大努力克服该事件，减少损失的扩大。不可抗力指各方不能控制、不可预见或即使预见亦无法避免的事件，该事件足以妨碍、影响或延误任何一方根据本协议履行其全部或 部分义务。该事件包括但不限于自然灾害、战争、政策变化、计算机病毒、黑客攻击或电信机构服务中断造成的事件。</p>
          <h3>十三、其他</h3>
          <p>1.如果本协议的某一(些)条款被认定为无效而其他条款仍能保持有效且其执行不受影响。</p>
          <p>2.我们可以通过以下途径发送通知： </p>
          <p>2.1在{{registerTitle}}平台上发布通知；</p>
          <p>2.2按照您在账户信息中登记的电子邮件地址或电话发送电子邮件或短信。</p>
          <p>3.事先未经我们的书面同意，您不得转让本协议中的任何权利和义务。</p>
          <h3>十四、管辖约定</h3>
          <p>本协议适用中华人民共和国法律。关于本协议的履行、违约、终止、解释或有效性，或者就{{registerTitle}}平台的使用所产生的或与其相关的任何冲突、赔偿或纠纷，协商不成时任何一方均可向广州市天河区人民法院提起诉讼。</p>
        </view>
      </scroll-view>
    </view>
  </page>
</template>

<script>
  import serverOptions from '@/config/env/options'
  import {
    mapState
  } from 'vuex'
  export default {
    components: {

    },
    data() {
      return {
        // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
        $constant: this.$constant,
        $common: this.$common,
        $accurateConversion: this.$accurateConversion,
        file_ctx: this.file_ctx,
        $static_ctx: this.$static_ctx,
        $timePlugin: this.$timePlugin,
        $validate: this.$validate,
        id: undefined,
        idUpdate: true,
        regForm: {}
      }
    },
    computed: {
      ...mapState('user', {

      }),
      ...mapState('system', {
        temporaryStorage: state => state.temporaryStorage
      }),
      registerTitle() {
        return serverOptions.registerTitle 
      }
    },
    // 离开当前页面后执行
    onUnload() {
      // this.$common.setKeyVal('system', 'temporaryStorage', {})
    },
    onLoad(paramsObj) {
      const query = this.$Route.query
      if (!this.$validate.isNull(query)) {
        this.id = query.id
      }
      this.init()
    },
    onShow() {},
    methods: {
      init() {
        this.$nextTick(() => {

        })
      },
      navtoGo(url, obj = {}) {
        this.$navto.push(url, obj)
      },
      getDetail(id) {
        const that = this

      }
    }

  }
</script>

<style lang="scss" scoped>
  h3{
    color: #000;
    font-weight: 600;
  }
  .m-l-16 {
    margin-left: 16upx;
  }

  .star {
    color: #F85E4C;
    font-size: 32upx;
    display: inline-block;
  }

  .main-body {
    height: 100%;

    .main {
      height: 100%;
      background: #fff;

      .m-main {
        background: #fff;
        padding: 30upx;
      }
    }
  }

  .title {
    font-size: 36upx;
    line-height: 54upx;
    text-align: center;
  }

  .text-title {
    font-size: 28upx;
    line-height: 42upx;
    margin-top: 16upx;
    color: #333;
  }

  .text {
    font-size: 24upx;
    line-height: 36upx;
    margin-top: 16upx;
    color: #666;
  }

  .text-indent-40 {
    text-indent: 40upx;
  }

  .p-b-8 {
    padding-bottom: 8upx;
  }
</style>
