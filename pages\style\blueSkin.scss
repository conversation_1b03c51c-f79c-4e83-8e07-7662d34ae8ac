// 定义css变量
:root {
  --file-ctx-header: '';
  --file-path:'';
}
.blueSkin{
  .active{
    color: #489DF0 !important;
  }
}
.index-blueSkin {
  .bgHeader{
    background-image: url(https://file.greenboniot.cn/static/image/business/accompany-doctor/icon-accompany-service-blue-2.png) !important;
    height: 358rpx !important;
    background-repeat: no-repeat;
  }
  .top-nav{
    margin-bottom: 86rpx;
  }
  .accompany-content{
    height: calc(100% - 358rpx) !important;
    display: flex !important;
    flex-wrap: wrap !important;
    .accompany-content-son-1{
        order: 1;
    }
    .accompany-content-son-2{
      order: 2;
    }
    .accompany-content-son-3{
      order: 3;
    }
    .accompany-content-son-4{
      order: 5;
    }
    .accompany-content-son-5{
      order: 4;
    }
    .accompany-content-son-6{
      order: 6;
    }
  }
  .accompany-content-son-5{
    background-image:none !important;
    background-color: rgba(255, 255, 255, 0) !important;
    margin-top: 80rpx !important;
    height: 480rpx;
    padding: 0 20rpx 20rpx !important;
    .accompany-head{
        width: 100%;
        height: 112rpx;
        background: #EDF8FF;
        border-radius: 24rpx 24rpx 0rpx 0rpx;
        border: 2rpx solid #A7D4FF;
        margin-bottom: 0rpx !important;
        border-bottom: none !important;
        box-sizing: border-box;
        .info{
          margin-left: 212rpx !important;
        }
        .teacher{
            width: 170rpx !important;
            height: 218rpx !important;
            left: 62rpx;
            top: -80rpx !important;
            background-image: url(https://file.greenboniot.cn/static/image/business/accompany-doctor/icon-accompany-doctor-specialty-teacher-blue.png) !important;
        }
        .info-t{
            width: 168rpx !important;
            height: 40rpx !important;
          background-image: url(https://file.greenboniot.cn/static/image/business/accompany-doctor/icon-accompany-doctor-specialty-text-blue.png) !important;
        }
        .accompany-head-r button{
          width: 168rpx !important;
          height: 62rpx !important;
          background: linear-gradient( 91deg, #489DF0 0%, #117CE5 100%) !important;
          box-shadow: inset 0rpx 0rpx 8rpx 0rpx #FFFFFF !important;
          border-radius: 36rpx 36rpx 36rpx 36rpx !important;
          font-weight: normal !important;
          margin-right: 44rpx !important;
          font-weight: bold !important;
          font-size: 24rpx !important;
          color: #FFFFFF !important;
        }
    }
    .accompany-bott{
        width: 100%;
        height: 368rpx !important;
        background: linear-gradient( 180deg, #74C5FF 0%, #FFFFFF 100%);
        border-radius: 16rpx 16rpx 16rpx 16rpx;
        border: 2rpx solid #FFFFFF;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);


    }
  }

}
.serverShow-blueSkin{
    .swiper-item{
      .item{
        padding: 0 15rpx!important;
        padding-top: 36rpx !important;
        .accompany-bott-item{
          background-color: rgba(255, 255, 255, 0) !important;
          background-image:none !important;
          width: 143rpx !important;
          margin: 0 0 20rpx 0 !important;
          height: 138rpx !important;
          padding: 0 !important;
          text-align: center;
          margin-right: calc((100% - (143rpx * 4)) / 3) !important;
          &:nth-of-type(4n){
            margin-right: 0 !important;
          }
          .icon{
            display: inline-block !important;
            width: 90rpx !important;
            height: 90rpx !important;
          }
          .text{
            display: none !important;
          }
          .title{
            font-weight: 500 !important;
            font-size: 24rpx !important;
            color: #333333 !important;
            text-align: center !important;
          }
       }
      }
    }
}
.moduleBox-blueSkin{
  display: flex;
  flex-wrap: wrap;
  // 选择该元素的一级子元素
  >view{
    width: 100%!important;
  }
  >view:nth-child(1){
    order: 1;
  }
  >view:nth-child(2){
    order: 3;
  }
  >view:nth-child(3){
    order: 4;
  }
  >view:nth-child(4){
    order: 2;
  }
  >view:nth-child(5){
    order: 5;
  }
  .service-project-head{
    width: 100%;
    .service-project-head-l{
      font-weight: 500 !important;
      font-size: 32rpx !important;
      color: #1D2029 !important;
    }
  }
  .moduleBox-service{
    background-color: rgba(255, 255, 255, 0) !important;
    display: flex;
    flex-wrap: wrap;
    padding:0 !important;
    row-gap: 24rpx;
    column-gap: 18rpx;

    .service-project-item{
      width: 334rpx !important;
      background: #FFFFFF !important;
      border-radius: 16rpx !important;
      display: block !important;
      padding: 0!important;
      .project-item-l{
        width: 334rpx !important;
        height: 334rpx !important;
        margin-bottom: 16rpx!important;
        border-radius: 12rpx 12rpx 0 0!important;
}
      .project-item-r{
        margin-left: 12rpx !important;
        .project-item-r-title{
          font-weight: 500 !important;
          font-size: 30rpx !important;
          color: #1D2029 !important;
          text-align: left !important;
          // 设置子盒子超出显示省略号
          overflow: hidden!important;
          width: 96%!important;
          text-overflow: ellipsis!important;
          white-space: nowrap!important;
        }
        .project-item-r-info{
          font-weight: 400 !important;
          font-size: 22rpx !important;
          color: #868C9C !important;
          margin: 0 !important;
          // 设置子盒子超出显示省略号
          overflow: hidden!important;
          width: 96%!important;
          text-overflow: ellipsis!important;
          white-space: nowrap!important;
        }
        .project-item-r-money{
          font-weight: bold !important;
          font-size: 20rpx !important;
          color: #FF514D !important;
        }
        .project-item-r-money span{
          font-weight: bold !important;
          font-size: 28rpx !important;
          color: #FF514D !important;
        }
          .project-item-r-tag{
            height: 32rpx !important;
            line-height: 32rpx;
            background: rgba(247,145,88,0.15);
            border-radius: 4rpx 4rpx 4rpx 4rpx !important;
            font-weight: 500 !important;
            font-size: 20rpx !important;
            color: #F79158 !important;
            border: none !important;
            width: fit-content !important;
          }
        .project-item-r-box{
          // 设置子盒子超出显示省略号
          display: flex!important;
          overflow: hidden!important;
          width: 90%!important;
          margin-top: 6rpx !important;
          .project-item-r-tag{
            height: 32rpx !important;
            line-height: 32rpx;
            background: rgba(247,145,88,0.15);
            border-radius: 4rpx 4rpx 4rpx 4rpx !important;
            font-weight: 500 !important;
            font-size: 20rpx !important;
            color: #F79158 !important;
            border: none !important;
          }
        }
      }

    }
  }
  .moduleBox-teacher{
    background: #FFFFFF !important;
    border-radius: 16rpx 16rpx 16rpx 16rpx !important;
    padding: 32rpx 24rpx !important;
    .pzsTitleIcon{
      display: none!important;
    }
    .accompany-List{
      display: flex;
      flex-wrap: wrap !important;
    }
    .accompany-box{
      margin-right: 0 !important;
      padding: 20rpx !important;
      box-sizing: border-box!important;
      width: 638rpx !important;
      height: 208rpx !important;
      background: #F4F6FA !important;
      border-radius: 16rpx 16rpx 16rpx 16rpx !important;
      margin-bottom: 20rpx!important;
      flex-direction: row !important;
      justify-content:left !important;
      flex-wrap: wrap;
      .accompany-text{
        width: calc(100% - 168rpx - 40rpx)!important;
        align-items: center;
      }
      .accompany-Icon{
        width: 168rpx !important;
        height: 168rpx !important;
        background: #FFFFFF !important;
        border-radius: 100rpx 100rpx 100rpx 100rpx !important;
        margin-right: 20rpx !important;
        -webkit-border-radius: 100rpx 100rpx 100rpx 100rpx !important;
        -moz-border-radius: 100rpx 100rpx 100rpx 100rpx !important;
        -ms-border-radius: 100rpx 100rpx 100rpx 100rpx !important;
        -o-border-radius: 100rpx 100rpx 100rpx 100rpx !important;
      }
      .accompany-text{
        width: calc(100% - 168rpx - 20rpx)!important;
      }
      .accompany-username{
        height: 36rpx !important;
        width: 100%;
        box-sizing: border-box!important;
        // 设置子盒子超出显示省略号
        overflow: hidden!important;
        text-overflow: ellipsis!important;
        white-space: nowrap!important;
        .pzsNameLogo{
          margin: 0 8rpx !important;
        }
        .certificate-nums{
          width: 104rpx;
          height: 36rpx;
          background: #FFF1D7;
          border-radius: 68rpx 68rpx 68rpx 68rpx;
          border: 2rpx solid #EBA838;
          display: block!important;
          font-weight: 500;
          font-size: 22rpx;
          color: #EBA838;
          text-align: center;
          line-height: 36rpx;
          margin-right: 8rpx;
          box-sizing: border-box;
        }
      }
      .accompany-experience{
        font-weight: 400;
        font-size: 24rpx !important;
        color: #4E5569;
        display: block!important;
        height: 34rpx;
        width: 100%;
        box-sizing: border-box!important;
        // 设置子盒子超出显示省略号
        overflow: hidden!important;
        text-overflow: ellipsis!important;
        white-space: nowrap!important;
      }
      .languageMap{
        height: 36rpx !important;
        width: 100%;
        box-sizing: border-box!important;
        // 设置子盒子超出显示省略号
        overflow: hidden!important;
        text-overflow: ellipsis!important;
        white-space: nowrap!important;
        margin-top: 8rpx !important;
        .languageItem{
          height: 36rpx !important;
          background: rgba(247,145,88,0.15)!important;
          border-radius: 4rpx 4rpx 4rpx 4rpx!important;
          font-weight: 400!important;
          font-size: 22rpx !important;
          color: #F79158 !important;
        }
      }
    }
  }
  .moduleBox-doctor{
    background: linear-gradient( 180deg, #CCE9FF 0%, #FFFFFF 100%) !important;
    border-radius: 16rpx 16rpx 16rpx 16rpx !important;
    display: flex;
    flex-wrap: wrap!important;
    column-gap: 22rpx;
    .service-project-head{
      width: 100%;
    }
    .depa-doctor-item{
      width: 198rpx !important;
      overflow: hidden;
      background: rgba(247,145,88,0)!important;
      .doctor-item-box{
        flex-wrap: wrap;
        .doctor-item-l{
          width: 100%!important;
          height: 198rpx !important;
          background: #D5EAFF;
          border-radius: 100rpx 100rpx 100rpx 100rpx !important;
          overflow: hidden !important;
        }
        .doctor-item-r{
          .item-r-head >.name span:nth-of-type(1){
            font-weight: 500 !important;
            font-size: 30rpx !important;
            color: #333333 !important;
            text-align: left !important;
            display: block!important;
            margin-top: 12rpx!important;
          }
          .item-r-head >.name span:nth-of-type(2){
            font-weight: 400 !important;
            font-size: 24rpx !important;
            color: #777777 !important;
            display: block!important;
            margin-top: 4rpx!important;
          }
          .item-r-bott{
            display: none !important;
          }
          .deptName{
            display: inline-block!important;
            height: 36rpx !important;
            background: rgba(247,145,88,0.15)!important;
            border-radius: 4rpx 4rpx 4rpx 4rpx!important;
            font-weight: 400!important;
            font-size: 22rpx !important;
            color: #F79158 !important;
            width: fit-content;
            padding: 4rpx 6rpx!important;
            margin-top: 8rpx;
            max-width: 100%!important;
            border-radius: 4rpx 4rpx 4rpx 4rpx !important;
            overflow: hidden!important;
            text-overflow: ellipsis!important;
            white-space: nowrap!important;
        }
        }
      }
    }
  }
  .moduleBox-hospital{
    padding-bottom: 32rpx!important;
    .service-project-item{
      width: 638rpx !important;
      overflow: hidden !important;
      height: 184rpx !important;
      background: #F4F6FA !important;
      border-radius: 16rpx 16rpx 16rpx 16rpx !important;
      border-bottom: none!important;
      margin-top: 20rpx!important;
      box-sizing: border-box !important;
      .hospitalImage{
        width: 144rpx!important;
        height: 144rpx!important;
        background: #F4F6FA !important;
        border-radius: 16rpx 16rpx 16rpx 16rpx !important;
        -webkit-border-radius: 16rpx 16rpx 16rpx 16rpx !important;
        -moz-border-radius: 16rpx 16rpx 16rpx 16rpx !important;
        -ms-border-radius: 16rpx 16rpx 16rpx 16rpx !important;
        -o-border-radius: 16rpx 16rpx 16rpx 16rpx !important;
      }
      .project-item-l{
        margin-left: 20rpx;
      }
      .project-item-r{
        display: flex;
        flex-wrap: wrap;
        .project-item-r-title{
          order: 1;
        }
        .project-item-r-box{
          order: 3;
        }
        .project-item-r-address{
          order: 2;
        }
        .project-item-r-hospital{
          background: #FCF0DA;
          border-radius: 4rpx 4rpx 4rpx 4rpx !important;
          padding: 2rpx 10rpx !important;
          font-weight: 400 !important;
          font-size: 20rpx !important;
          color: #693E13 !important;
        }
      }
    }
  }
}
// 服务页面
.service-blueSkin{
  .my-data{
    background-image:url(https://file.greenboniot.cn/static/image/business/accompany-doctor/blueSkin-headerBG.png) !important;
  }
  .l-main{
    background-color: rgba($color: #000000, $alpha: 0) !important;
    .tab {
      .active{
        color: #489DF0!important;
      }
      .text-width{
        background-image:url(https://file.greenboniot.cn/static/image/business/accompany-doctor/blueSkin-icon-accompany-service-head-bg.png) !important;
      }
    }
  }
  .project-item-r-btn{
    border: 1rpx solid #489DF0!important;
    font-size: 26rpx;
    color: #489DF0!important;
  }
  .combo-btn{
    background-color: #489DF0 !important;
    color: #FFFFFF !important;
    border: none !important;
  }
  .accompany-combo-btn{
    background-color: #489DF0 !important;
  }
}
// 订单页面
.order-blueSkin{
  .orderHeader{
  }
  .l-main{
    background-color: rgba($color: #000000, $alpha: 0) !important;
    .tab {
      .active{
        color: #489DF0!important;
      }
      .text-width{
        background-image:url(https://file.greenboniot.cn/static/image/business/accompany-doctor/blueSkin-icon-accompany-service-head-bg.png) !important;
      }
    }
  }
}
// 我的页面
.my-blueSkin{
  .my-data{
    background-image:url(https://file.greenboniot.cn/static/image/business/accompany-doctor/blueSkin-headerBG.png) !important;
    background-repeat: no-repeat!important;
  }
  .title-indicator{
    background-color: #489DF0!important;
  }
  .provider-overview{

  }
  .accompany-workbench-item{

  }
}

/* 分类模块 背景伸缩 */
  /* 默认背景样式 */
  .bgHeader {
    background-image: url(https://file.greenboniot.cn/static/image/business/accompany-doctor/icon-accompany-service.png) !important;
    background-size: 100% !important;
    background-repeat: no-repeat !important;
  }

  /* 蓝色皮肤默认背景 */
  .index-blueSkin .bgHeader {
    background-image: url(https://file.greenboniot.cn/static/image/business/accompany-doctor/icon-accompany-service-blue-2.png) !important;
  }


  /* 当扩展背景激活时，原header不需要背景 */
  .index-blueSkin.extend-bg .accompany-header {
    background-image: none !important;
    background: transparent !important;
  }

  /* 确保accompany-header没有默认背景 */
  .accompany-header {
    background: none;
  }

  .extend-bg:not(.index-blueSkin) {
    background-image: url(https://file.greenboniot.cn/static/image/business/accompany-doctor/icon-accompany-service-2.png) !important;
    background-repeat: no-repeat !important;
    background-size: 100% !important;
    background-position: top !important;
  }

  .index-blueSkin.extend-bg {
    background-image: url(https://file.greenboniot.cn/static/image/business/accompany-doctor/icon-accompany-service-blue-2.png) !important;
    background-repeat: no-repeat !important;
    background-size: 100% !important;
    background-position: top !important;
  }

  /* 当扩展背景激活时，原header不需要背景（所有皮肤通用） */
  .extend-bg .accompany-header {
    background-image: none !important;
    background: transparent !important;
  }
  /* 默认顺序（绿色皮肤）:
   * 1. 分类模块(accompany-content-son-1)
   * 2. 轮播图(accompany-content-son-4)
   * 3. 专业陪诊(accompany-content-son-5)
   * 4. 模块盒子(accompany-content-son-6)
   */
   .scroll-content .accompany-content-son-1 {
    order: 1;
  }

  .scroll-content .accompany-content-son-4 {
    order: 2;
  }

  .scroll-content .accompany-content-son-5 {
    order: 3;
  }

  .scroll-content .accompany-content-son-6 {
    order: 4;
  }

  /* 蓝色皮肤时调整顺序，让专业陪诊在轮播图上方 */
  .index-blueSkin .scroll-content .accompany-content-son-1 {
    order: 1 !important;
  }

  .index-blueSkin .scroll-content .accompany-content-son-4 {
    order: 3 !important;
  }

  .index-blueSkin .scroll-content .accompany-content-son-5 {
    order: 2 !important;
  }

  .index-blueSkin .scroll-content .accompany-content-son-6 {
    order: 4 !important;
  }
