<template>
  <view
    class="main-container"
    :style="{
      paddingTop: headerTop + 'px',
    }"
  >
    <title-header
      :isHome="true"
      :headerobj="headerobj"
      @init="initHeader"
    ></title-header>

    <view class="content">
      <image class="img" :src="file_ctx + 'static/image/business/diagnosis-heart.png'" mode="aspectFill">
      <view class="text-box">
        <text class="text-title">反馈成功</text>
        <text class="text-desc">感谢您的支持，祝您早日康复！</text>
      </view>
      <!-- <text class="content-title">第一时间获取医院反馈</text> -->
      <view class="content-btn-box" @tap="subscribeMessage">
        <button type="primary" class="content-btn">一起点亮健康</button>
        <image :src="urlImg" class="btn-img"></image>
      </view>
    </view>
  </view>
</template>

<script>
import titleHeader from '@/modules/activity/components/title-header/index.vue'
export default {
  data() {
    return {
      $appId: this.$appId,
      file_ctx: this.file_ctx,
      headerTop: 55,
      headerobj: {
        headBgColor: '#0bdaa6',
        titleType: "txt",
        titleTxt: "医院服务满意度调查表",
        currentIndex: 0,
        contentColor: "#fff",
      },
      urlImg: this.$static_ctx + 'image/business/icon-im-hand2.png',
      params: {}
    }
  },
  components: {
    titleHeader
  },
  onLoad(options) {
    const query = this.$Route.query
    const that = this
    this.params = query
  },
  methods: {
    initHeader(height) {
      this.headerTop = height
    },
    async subscribeMessage() {
      this.$navto.replace('Patronsaint')
    }
  }
}
</script>

<style lang="scss" scoped>
.main-container {
  background-color: #fff;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}
.content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding-top: 150rpx;
}
.content-title {
  color: $topicC;
  font-size: 56rpx;
  font-size: 600;
  padding-bottom: 32rpx;
}
.content-btn {
  background-color: $topicC;
  height: 100upx;
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: center;
  border-radius: 50upx;
}
.content-btn-box {
  position: fixed;
  width: 80%;
  bottom: 14%;
}
.btn-img {
  width: 110upx;
  height: 120upx;
  position: absolute;
  top: 40%;
  right: 50upx;
}
.text-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  .text-title {
    font-size: 50rpx;
    color: #504f50;
    font-weight: 550;
    letter-spacing: 5rpx;
    padding-bottom: 12rpx;
  }
  .text-desc {
    font-size: 30rpx;
    color: #868789;
    letter-spacing: 2rpx;
  }
}
</style>
