<template>
  <page>
    <view class="main-body" slot="content">
      <scroll-view scroll-y="true" class="main">
        <view class="m-main">

        </view>
      </scroll-view>
    </view>
  </page>
</template>

<script>
import { mapState } from 'vuex'
export default {
  components: {

  },
  data() {
    return {
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $accurateConversion: this.$accurateConversion,
      file_ctx: this.file_ctx,
      $static_ctx: this.$static_ctx,
      $timePlugin: this.$timePlugin,
      $validate: this.$validate,
      id: undefined,
      idUpdate: true,
      regForm: {}
    }
  },
  computed: {
    ...mapState('user', {

    }),
    ...mapState('system', {
      temporaryStorage: state => state.temporaryStorage
    })
  },
  // 离开当前页面后执行
  onUnload() {
    // this.$common.setKeyVal('system', 'temporaryStorage', {})
  },
  onLoad(paramsObj) {
    const query = this.$Route.query
    if (!this.$validate.isNull(query)) {
      this.id = query.id
    }
    this.init()
  },
  onShow() {},
  methods: {
    init() {
      this.$nextTick(() => {

      })
    },
    navtoGo(url, obj = {}) {
      this.$navto.push(url, obj)
    },
    getDetail(id) {
      const that = this

    }
  }

}
</script>

<style lang="scss" scoped>
  .m-l-16{
    margin-left: 16upx;
  }
  .star{
    color: #F85E4C;
    font-size: 32upx;
    display: inline-block;
  }
  .main-body{
    height: 100%;
    .main{
      height: 100%;
      background: #fff;
      .m-main{
        background: #fff;
        padding: 30upx;
      }
    }
  }
  .title {
    font-size: 36upx;
    line-height: 54upx;
    text-align: center;
  }
  .text-title {
    font-size: 28upx;
    line-height: 42upx;
    margin-top: 16upx;
    color: #333;
  }
  .text {
    font-size: 24upx;
    line-height: 36upx;
    margin-top: 16upx;
    color: #666;
  }
  .text-indent-40 {
    text-indent: 40upx;
  }
  .p-b-8 {
    padding-bottom: 8upx;
  }
</style>
