import ext from '@/service/ext'
export default {
        // 拒绝授权默认数据
        isRefuseAuth:false,
        defaultData:{cityname:'广州市',pname:'广东省'},
        getDefaultData(){
          this.isRefuseAuth = true
          return this.defaultData
        },
        // 获取地理位置权限
        async initLocationPerm(isMust = false) {
          if(!isMust && this.isRefuseAuth) return this.getDefaultData()
          // 1. 检查系统定位是否开启
          const { locationEnabled } = uni.getSystemInfoSync();
          if (!locationEnabled) {
            await this.showLocationTip('手机系统定位未打开！');
            return false;
          }
          try {
            // 2. 检查定位权限
            const { authSetting } = await this.uniPromise('getSetting');
            // 3. 已有权限，直接获取位置
            if (authSetting['scope.userLocation']) {
              return await this.getPosition();
            }
            // 4. 无权限，请求授权
            try {
              await this.uniPromise('authorize', { scope: 'scope.userLocation' });
              return await this.getPosition();
            } catch (error) {
              // 5. 用户拒绝授权，引导打开设置页
              const confirmed = await this.showLocationTip('需要获取地理位置权限，请到小程序设置页面打开授权',!isMust);
              // 如果用户不愿意授权 那么则直接返回默认数据
              console.log('confirmed',confirmed ,isMust,!confirmed && !isMust);
              
              if(!confirmed && !isMust) return this.getDefaultData()
              let settingInfo = await this.uniPromise('openSetting');
              // 判断用户是否授权 如果未授权则重新请求
              let isAuth = settingInfo.authSetting['scope.userLocation'];
              // 如果不授权且不强制要求授权 则直接返回默认数据
              if(!isAuth && !isMust) return this.getDefaultData()
              // 如果不授权且强制要求授权 则重新请求授权
              if(!isAuth) return await this.initLocationPerm(isMust)
              return await this.getPosition();
            }
          } catch (error) {
            console.error('位置权限处理失败:', error);
            return false;
          }
        },
        // 工具方法：显示提示框
        async showLocationTip(content, showCancel = false) {
          const res = await this.uniPromise('showModal', {
            title: '提示',
            content,
            showCancel
          });
          return res.confirm;
        },
        // 工具方法：Promise化uni接口
        uniPromise(name, options = {}) {
          return new Promise((resolve, reject) => {
            uni[name]({
              ...options,
              success: resolve,
              fail: reject
            });
          });
        },
        getPosition(){
          let resFn;
          let retryCount = 0;
          const maxRetries = 3;
  
          let promise = new Promise(res => resFn = res);
  
          const tryGetLocation = () => {
            uni.getLocation({
              type: 'wgs84',
              geocode: true,
              // altitude: true,
              // isHighAccuracy: true,
              success: async (res) => {
                console.log('位置获取成功', res);
                let Position = await ext.common.getPosition(res);
                resFn(Position);
              },
              fail: (err) => {
                console.log('位置获取失败', JSON.stringify(err));
                // 如果是服务器位置错误且未达到最大重试次数
                if (err.errMsg && err.errMsg.includes('ERROR_SERVER_NOT_LOCATION') && retryCount < maxRetries) {
                  retryCount++;
                  console.log(`正在尝试第${retryCount}次重新获取位置`);
  
                  // 延迟1秒后重试
                  setTimeout(tryGetLocation, 1000);
                  return;
                }
                // 重试次数已用完，返回默认数据
                if (retryCount >= maxRetries) {
                  resFn(this.getDefaultData());
                  return;
                }
                // 返回模拟位置数据，防止流程中断
                resFn({
                  latitude: 30.0,  // 使用默认位置
                  lat:30.0,
                  lng:120.0,
                  longitude: 120.0
                });
              }
            });
          };
  
          tryGetLocation();
          return promise
        },
}