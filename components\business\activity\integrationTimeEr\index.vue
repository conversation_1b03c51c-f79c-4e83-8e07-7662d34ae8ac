<template>
  <view class="">
    <view class="animatePage" v-if="showTimeEr">
      <view class="animateBox" :class="{animateBoxR:showRight,animateBoxL:!showRight}">
        <!-- 环形进度条 -->
        <view class="g-container" v-if="!showEndAnimate">
          <view class="coverOver">
            <image class="coverOverImg" :src="giftBox" mode=""></image>
          </view>
          <view class="g-progress" :style="{background:circleBackground}"></view>
          <view class="g-circle"></view>
        </view>
        <view class="g-container" v-else>
          <image class="coverOverGif" :src="goldReceive" mode=""></image>
        </view>
        <!-- 底部预览文字 -->
        <view class="bottom" v-if="tracks>0">再浏览{{Math.ceil(tracks)}}s</view>
        <view class="finishBottom" v-else>获得{{Math.ceil(point)}}个福币</view>
      </view>
    </view>
    <!-- 返回按钮唤起菜单 -->
    <template v-if="backTaskFlag">
      <view>
        <view class="backTask" :style='{top:backTaskTop}' @click="showBackMenu">
          <image class="leftRed" :src="backMenu" mode=""></image>
          <view>返回</view>
        </view>
        <view class="backBtns" @click.stop v-if="showBackFlag" :style="{backgroundImage:'url('+backMenuBg+')'}">
          <view v-for="(item,index) in backRouterMap" :key="index">
            <view class="backBtn" @click="gotoPath(item)">
              <image class="backIcon" :src="item.iconPath" mode=""></image>
              <view class="backText">{{item.title}}</view>
            </view>
            <view class="line" v-if="index < backRouterMap.length - 1"></view>
          </view>
        </view>
        <view v-if="showBackFlag" :style='{top:MaskTop}' @click="showBackFlag = false" class="Mask"></view>
      </view>
    </template>
  </view>
</template>

<script>
  const huluPath = "image/business/hulu-v2/huluPath.png";
  const fubiPath = "image/business/hulu-v2/fubiPath.png";
  const indexPath = "image/business/hulu-v2/indexPath.png";
  export default{
    props:{
      tracks:{
        type:Number,
        default:1
      },
      point:{
        type:Number,
        default:1
      },
      backTaskFlag:{
        type:Boolean,
        default:false
      },
      isBackIndex:{
        type:Boolean,
        default:false
      }
    },
    data(){
      return {
        circleBackgroundHead: 'conic-gradient(#FFD73B 0, #FFD73B ',
        circleBackgroundBody:'%, rgba(255,255,255,0.3) ',
        circleBackgroundEnd: '%, rgba(255,255,255,0.3));',
        initTrack:0,
        giftBox: this.$static_ctx + "image/business/hulu-v2/giftBox.png",
        backMenu: this.$static_ctx + "image/business/hulu-v2/backMenu.png",
        backMenuBg: this.$static_ctx + "image/business/hulu-v2/backMenuBg.png",
        huluPath: this.$static_ctx + huluPath,
        fubiPath: this.$static_ctx + fubiPath,
        indexPath: this.$static_ctx + indexPath,
        // 返回按钮
        backRouterMap:[
          {iconPath:this.$static_ctx + huluPath,title:'祈福许愿',gotoName:'calabashWebview'},
          {iconPath:this.$static_ctx + fubiPath,title:'福币任务',gotoName:'luckyCoinaTask',backTaskFlag:true},
        ],
        // 返回首页参数
        backIndex:{iconPath:this.$static_ctx + indexPath,title:'首页',gotoName:'/pages/index/index',tab:true},
        goldReceive: this.$static_ctx + "image/business/hulu-v2/goldReceive.gif"+'?index='+Math.random(),
        flag:false,
        showEndAnimate:false,
        showTimeEr:false,
        showRightMap:['modules/community/posts/detail/index'],
        showRight:false,
        backTaskTop:'',
        showBackFlag:false,
        MaskTop:''
      }
    },
    methods:{
      showBackMenu(){
        // 展示返回按钮
        this.showBackFlag = !this.showBackFlag;
      },
      gotoPath(urlOptions){
        this.$store.dispatch('system/UpdateBackTaskFlag',!!urlOptions.backTaskFlag)
        if(urlOptions.tab){
          return uni.switchTab({url:urlOptions.gotoName})
        }
        this.$navto.replace(urlOptions.gotoName)
      },
      // 异步获取元素
      getEl (getDiv) {
          return new Promise((resolve, reject) => {
              // #ifdef H5
              resolve(getDiv.getBoundingClientRect())
              // #endif

              // #ifndef H5
              if (getDiv.boundingClientRect) {
                  getDiv.boundingClientRect(data => {
                      console.log(data)
                      resolve(data)
                  }).exec()
              }
              // #endif
          })
      },
    },
    computed:{
      circleBackground(){
        let percentage = (this.initTrack - this.tracks) / this.initTrack * 100
        return `${this.circleBackgroundHead}${percentage}${this.circleBackgroundBody}${percentage}${this.circleBackgroundEnd}`;
      }
    },
    async mounted() {
      let pages = getCurrentPages();
      let currentPath = pages[pages.length - 1].$page.fullPath;
      this.showRightMap.map(router=>{
        if(currentPath.indexOf(router) >= 0){
          this.showRight = true
        }
      })
      if(this.backTaskFlag){
        const query = uni.createSelectorQuery().in(this)
        let backTask = await this.getEl(query.select('.backTask'))
        console.log('backTask',backTask);
        let ClientRect = uni.getMenuButtonBoundingClientRect()
        if(backTask.top == 0){
          this.backTaskTop = ClientRect.top +　'px'
        }
        this.MaskTop = -ClientRect.top +　'px'
      }
      if(this.isBackIndex){
        this.backRouterMap = [this.backRouterMap[0],this.backIndex]
      }
    },
    watch:{
      tracks(n){
        if(n <= 0){
          setTimeout(()=>{
            this.showTimeEr = false
            this.flag = false
            this.showEndAnimate = false
          },2000)
        }
        // 判定是否该进入结束动画
        if(this.showEndAnimate) return
        // 开启结束动画
        if(n<=2.5){
          this.showEndAnimate = true
        }
        if(this.flag) return
        this.flag = true;
        if(n>0){
          this.showTimeEr = true
          this.initTrack = n
        }
      }
    }
  }
</script>

<style lang="scss">
  .backBtns{
    width: 302rpx;
    height: 256rpx;
    background-size: 100% 100%;
    position: fixed;
    top: 169rpx;
    left: 0rpx;
    z-index: 999999999999;
    padding: 22rpx 42rpx;
    box-sizing: border-box;
    .backBtn{
      padding: 26rpx 0;
      font-weight: 400;
      font-size: 30rpx;
      color: #1D2029;
      display: flex;
      .backIcon{
        width: 48rpx;
        height: 50rpx;
        margin-right: 12rpx;
      }
      .backText{
        margin-top: 4rpx;
      }
    }
    .line{
      width: 206rpx;
      height: 1rpx;
      background: #D9DBE0;
    }
  }
  .Mask{
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 999;
  }
  .backTask{
    width: 168rpx;
    height: 64rpx;
    background: rgba(255,255,255,0.6);
    border-radius: 32rpx;
    border: 1rpx solid rgba(0,0,0,0.08);
    font-weight: 400;
    font-size: 30rpx;
    color: #1D2029;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    left: 16rpx;
    z-index: 999999999;
    .leftRed{
      width:32rpx;
      height: 30rpx;
      margin-right: 12rpx;
      margin-top: 2rpx;
    }

  }
  .animatePage{
    width: 142rpx;
    height: 170rpx;
    position: absolute;
  }
  .animateBox{
    position: fixed;
    color: white;
    width: 174rpx;
    z-index: 99999999999999999999999999999999999999999999;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    flex-direction: column;
    .g-container {
        position: relative;
        margin: auto;
        width: 112rpx;
        height: 112rpx;
        .coverOverGif{
          width: 112rpx;
          height: 112rpx;
        }
        .coverOver{
          position: absolute;
          top: 50%;
          left: 50%;
          width: 96rpx;
          height: 96rpx;
          transform: translate(-50%,-50%);
          background: rgba(0,0,0,1);
          z-index: 9;
          border-radius: 50%;
          .coverOverImg{
            width: 96rpx;
            height: 96rpx;
          }
        }
        .g-progress {
            position: relative;
            margin: auto;
            width: 112rpx;
            height: 112rpx;
            border-radius: 50%;

        }
        .g-circle {
            position: absolute;
            top: 0;
            left: 0;

        }
    }
  }
  .animateBoxL{
    top: 284rpx;
    left: 52rpx;
  }
  .animateBoxR{
    top: 156rpx;
    right: 32rpx;
  }
  .bottom{
    width: 142rpx;
    height: 50rpx;
    line-height: 50rpx;
    background: rgba(0,0,0,0.6);
    border-radius: 26rpx;
    border: 2rpx solid rgba(255,255,255,0.3);
    margin-top:6rpx;
    font-weight: 500;
    font-size: 24rpx;
    color: #FFFFFF;
    text-align: center;
  }
  .finishBottom{
    height: 46rpx;
    line-height: 46rpx;
    padding:0 16rpx;
    background: #FF5500;
    border-radius: 23rpx;
    border: 2rpx solid rgba(255,255,255,0.3);
    margin-top:6rpx;
    font-weight: 500;
    font-size: 24rpx;
    color: #FFFFFF;
    text-align: center;
  }
</style>
